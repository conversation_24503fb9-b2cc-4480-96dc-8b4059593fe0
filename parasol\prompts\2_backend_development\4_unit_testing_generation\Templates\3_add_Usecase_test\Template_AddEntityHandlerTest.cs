using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Nut.Results;
using Nut.Results.FluentAssertions;
using Shared.Domain;
using Shared.Messaging;
using Shared.ObjectStorage;
using Shared.Spec;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Domain;
// using SampleService.Domain.Enums;
// using SampleService.Infrastructure.Persistence;
// using SampleService.Infrastructure.Storage;
// using SampleService.Services.Domain;
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.AddEntity;
// using SharedKernel.ExternalApi.MessageContract.Notification;
// using Entities = SampleService.Domain.Entities;

// TODO: 長いジェネリック型の短縮形エイリアス（可読性向上のため）
using EntityRepository = Shared.Domain.IRepository<SampleService.Domain.Entities.Entity>;
using RelatedEntityRepository = Shared.Domain.IRepository<SampleService.Domain.Entities.RelatedEntity, string>;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.AddEntity;

/// <summary>
/// AddEntityHandlerの単体テストクラス
/// </summary>
/// <remarks>
/// 統合テスト（SQLiteインメモリDB使用）を中心に、
/// Add処理のテストを包括的に実装し、コードカバレッジ100%を目指します。
/// 正常系、異常系、外部サービス連携、エラーハンドリングを網羅的にテストします。
/// AddUseCaseは複雑な依存関係と外部サービス連携を持つため、詳細なモック設定を行います。
/// </remarks>
public class AddEntityHandlerTest : IAsyncLifetime
{
    #region Test Constants
    // TODO: テストファイル関連の定数
    private const int TestFileSize = 20;
    private const string TestFileContent1 = "test file content 1";
    private const string TestFileContent2 = "test file content 2";
    private const string TestFileName1 = "testfile1";
    private const string TestFileName2 = "testfile2";
    private const string TestFileDisplayName1 = "テストファイル1.txt";
    private const string TestFileDisplayName2 = "テストファイル2.png";
    #endregion

    private readonly ApplicationDbContext _dbContext;
    private readonly TestCurrentUserService _currentUserService;
    private readonly TestCurrentDateTimeService _currentDateTimeService;
    
    // TODO: 実際の外部サービス依存関係に合わせて以下のモックを調整してください
    private readonly Mock<IMessageSender<NotificationQuery, string>> _notificationSenderMock;
    private readonly Mock<IMessageSender<ExternalServiceQuery, ExternalServiceResult>> _externalServiceSenderMock;
    private readonly Mock<IStorageClientProvider> _storageClientProviderMock;
    private readonly Mock<IObjectStorageClient> _objectStorageClientMock;
    private readonly IFileProcessingService _fileProcessingService;
    private readonly Mock<IImageProcessingService> _imageProcessingServiceMock;
    
    // TODO: 実際のストレージ設定に合わせて変更してください
    private readonly string _containerName = "entity-storage";
    private readonly string _folderName = "entity";

    public AddEntityHandlerTest()
    {
        _currentUserService = new TestCurrentUserService();
        _currentDateTimeService = new TestCurrentDateTimeService();

        _dbContext = TestDbContextBuilder.Build(_currentUserService, _currentDateTimeService);

        // TODO: 実際の外部サービスに合わせてモックを初期化してください
        _notificationSenderMock = new Mock<IMessageSender<NotificationQuery, string>>();
        _notificationSenderMock.Setup(x => x.SendAsync(It.IsAny<NotificationQuery>()))
            .ReturnsAsync(Result.Ok("notification-sent"));

        _externalServiceSenderMock = new Mock<IMessageSender<ExternalServiceQuery, ExternalServiceResult>>();
        _externalServiceSenderMock.Setup(x => x.SendAsync(It.IsAny<ExternalServiceQuery>()))
            .ReturnsAsync(Result.Ok(new ExternalServiceResult()));

        // ストレージ関連のモック初期化
        _storageClientProviderMock = new Mock<IStorageClientProvider>();
        _objectStorageClientMock = new Mock<IObjectStorageClient>();
        _objectStorageClientMock.Setup(m => m.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok);
        _objectStorageClientMock.Setup(m => m.DeleteAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok);
        _storageClientProviderMock.Setup(m => m.CreateAsync(_containerName))
            .ReturnsAsync(_objectStorageClientMock.Object);

        // ファイル処理サービス初期化
        _fileProcessingService = new FileProcessingService(new UnitOfWork(_dbContext), _storageClientProviderMock.Object, _currentDateTimeService);

        // 画像処理サービスのモック初期化
        _imageProcessingServiceMock = new Mock<IImageProcessingService>();
        _imageProcessingServiceMock.Setup(x => x.ProcessImageAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Ok("processed-image-url"));
    }

    public Task InitializeAsync()
        => TestData.CreateAsync(_dbContext);

    public Task DisposeAsync() => Task.CompletedTask;

    #region テスト対象とテストデータの準備

    /// <summary>
    /// テスト対象のハンドラーを作成します
    /// </summary>
    /// <returns>AddEntityHandler インスタンス</returns>
    private AddEntityHandler CreateHandler()
    {
        // TODO: プロジェクトのパターンに合わせてハンドラーの生成方法を調整してください
        var unitOfWork = new UnitOfWork(_dbContext);
        return new AddEntityHandler(
            unitOfWork,
            _currentDateTimeService,
            _currentUserService,
            _notificationSenderMock.Object,
            _externalServiceSenderMock.Object,
            _fileProcessingService,
            _imageProcessingServiceMock.Object
        );
    }

    /// <summary>
    /// 正常なコマンドを作成します
    /// </summary>
    /// <returns>AddEntityCommand インスタンス</returns>
    private static AddEntityCommand CreateValidCommand()
    {
        // TODO: 実際のコマンドプロパティに合わせて調整してください
        return new AddEntityCommand(
            Name: "テストエンティティ",
            Description: "テスト用の説明",
            Status: EntityStatus.Active,
            Priority: 1,
            RelatedEntityId: "related-entity-1",
            Tags: new List<string> { "tag1", "tag2" },
            UploadFiles: null
        );
    }

    #endregion

    #region 異常系テスト（null チェック）

    [Fact]
    public void Ctor_引数にnullを渡すと例外が発生する()
    {
        var unitOfWork = new UnitOfWork(_dbContext);

        // TODO: 実際のコンストラクタ引数に合わせて全パターンのnullチェックを実装してください
        Assert.Throws<ArgumentNullException>("unitOfWork", 
            () => new AddEntityHandler(
                null!, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("currentDateTimeService", 
            () => new AddEntityHandler(
                unitOfWork, 
                null!, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("currentUserService", 
            () => new AddEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                null!, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("notificationSender", 
            () => new AddEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                null!, 
                _externalServiceSenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("externalServiceSender", 
            () => new AddEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                null!, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("fileProcessingService", 
            () => new AddEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                null!, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("imageProcessingService", 
            () => new AddEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _fileProcessingService, 
                null!));
    }

    [Fact]
    public async Task Handle_Requestがnullの場合は例外が発生する()
    {
        var handler = CreateHandler();

        var act = () => handler.Handle(null!, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    #endregion

    #region 正常系テスト

    [Fact]
    public async Task Handle_指定された内容が追加される()
    {
        // Arrange
        var expectedDateTime = new DateTimeOffset(2024, 1, 15, 10, 30, 0, TimeSpan.Zero);
        var expectedUserName = "TestUser";

        _currentDateTimeService.UpdateNowDateTimeOffset(expectedDateTime);
        _currentUserService.UpdateUserName(expectedUserName);

        var command = CreateValidCommand();
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var entityId = result.Get();

        // データベースから追加されたエンティティを取得して詳細検証
        var addedEntity = await _dbContext.Entities.Where(e => e.Id == entityId).FirstOrDefaultAsync();
        
        addedEntity.Should().NotBeNull();
        
        // TODO: 実際のエンティティプロパティに合わせて検証項目を調整してください
        addedEntity.Name.Should().Be(command.Name);
        addedEntity.Description.Should().Be(command.Description);
        addedEntity.Status.Should().Be(command.Status);
        addedEntity.Priority.Should().Be(command.Priority);
        addedEntity.RelatedEntityId.Should().Be(command.RelatedEntityId);
        
        // 共通プロパティの検証
        addedEntity.CreatedAt.Should().Be(expectedDateTime);
        addedEntity.CreatedBy.Should().Be(expectedUserName);
        addedEntity.UpdatedAt.Should().Be(expectedDateTime);
        addedEntity.UpdatedBy.Should().Be(expectedUserName);

        // TODO: 関連エンティティや派生処理がある場合は追加で検証してください
        // 例: キューテーブル、ログテーブル、関連エンティティの更新など
        // var queueEntity = _dbContext.ProcessingQueues.Where(x => x.EntityId == entityId).FirstOrDefault();
        // queueEntity.Should().NotBeNull();
        // queueEntity.ProcessDateTime.Should().Be(expectedDateTime);

        // 外部サービス呼び出しの検証
        _imageProcessingServiceMock.Verify(m => m.ProcessImageAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_関連エンティティが正しく設定される()
    {
        // Arrange
        var command = CreateValidCommand();
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var entityId = result.Get();

        // TODO: 実際の関連エンティティに合わせて検証を実装してください
        var addedEntity = await _dbContext.Entities
            .Include(e => e.RelatedEntity)
            .Include(e => e.Tags)
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        addedEntity.Should().NotBeNull();
        
        // 関連エンティティの検証
        if (command.RelatedEntityId != null)
        {
            addedEntity.RelatedEntity.Should().NotBeNull();
            addedEntity.RelatedEntity.Id.Should().Be(command.RelatedEntityId);
        }

        // タグの検証
        if (command.Tags?.Any() == true)
        {
            addedEntity.Tags.Should().HaveCount(command.Tags.Count);
            foreach (var tag in command.Tags)
            {
                addedEntity.Tags.Should().Contain(t => t.Name == tag);
            }
        }
    }

    [Fact]
    public async Task Handle_ファイルアップロード付きで正しく追加される()
    {
        // Arrange
        var formFiles = CreateTestFiles();
        var command = CreateValidCommand() with { UploadFiles = formFiles };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var entityId = result.Get();

        // ファイルストレージの呼び出し検証
        _objectStorageClientMock.Verify(m => m.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()), Times.Exactly(formFiles.Count));
        
        // TODO: 実際のファイルパスパターンに合わせて調整してください
        foreach (var file in formFiles)
        {
            var expectedPath = $"{_folderName}/{entityId}/{file.FileName}";
            _objectStorageClientMock.Verify(m => m.PostAsync(expectedPath, It.IsAny<Stream>()), Times.Once);
        }

        // データベースのファイル情報検証
        var addedEntity = await _dbContext.Entities
            .Include(e => e.Files)
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        addedEntity.Files.Should().HaveCount(formFiles.Count);
        foreach (var file in formFiles)
        {
            addedEntity.Files.Should().Contain(f => f.FileName == file.FileName);
        }
    }

    [Theory]
    [InlineData(EntityStatus.Active)]
    [InlineData(EntityStatus.Inactive)]
    [InlineData(EntityStatus.Draft)]
    public async Task Handle_異なるステータスで正しく追加される(EntityStatus status)
    {
        // Arrange
        var command = CreateValidCommand() with { Status = status };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var entityId = result.Get();

        var addedEntity = await _dbContext.Entities.Where(e => e.Id == entityId).FirstOrDefaultAsync();
        addedEntity.Should().NotBeNull();
        addedEntity.Status.Should().Be(status);
    }

    #endregion

    #region 境界値・エッジケーステスト

    [Fact]
    public async Task Handle_最小限の情報で追加される()
    {
        // Arrange
        // TODO: 実際の必須フィールドのみに調整してください
        var command = new AddEntityCommand(
            Name: "最小限",
            Description: null,
            Status: EntityStatus.Draft,
            Priority: 0,
            RelatedEntityId: null,
            Tags: null,
            UploadFiles: null
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var entityId = result.Get();

        var addedEntity = await _dbContext.Entities.Where(e => e.Id == entityId).FirstOrDefaultAsync();
        addedEntity.Should().NotBeNull();
        addedEntity.Name.Should().Be("最小限");
        addedEntity.Description.Should().BeNull();
    }

    [Fact]
    public async Task Handle_最大文字数で追加される()
    {
        // Arrange
        // TODO: 実際の文字数制限に合わせて調整してください
        var maxName = new string('あ', 100); // 最大文字数
        var maxDescription = new string('い', 1000);
        
        var command = CreateValidCommand() with 
        { 
            Name = maxName, 
            Description = maxDescription 
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var entityId = result.Get();

        var addedEntity = await _dbContext.Entities.Where(e => e.Id == entityId).FirstOrDefaultAsync();
        addedEntity.Should().NotBeNull();
        addedEntity.Name.Should().Be(maxName);
        addedEntity.Description.Should().Be(maxDescription);
    }

    [Fact]
    public async Task Handle_空のタグリストで追加される()
    {
        // Arrange
        var command = CreateValidCommand() with { Tags = new List<string>() };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var entityId = result.Get();

        var addedEntity = await _dbContext.Entities
            .Include(e => e.Tags)
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        addedEntity.Should().NotBeNull();
        addedEntity.Tags.Should().BeEmpty();
    }

    #endregion

    #region エラーハンドリングテスト

    [Fact]
    public async Task Handle_リポジトリからのエラーはそのまま返る()
    {
        // Arrange
        var unitOfWork = new UnitOfWork(_dbContext);
        var unitOfWorkMock = new Mock<IUnitOfWork>();
        var repositoryMock = new Mock<EntityRepository>();
        
        unitOfWorkMock.Setup(x => x.GetRepository<Entity>())
            .Returns(repositoryMock.Object);

        repositoryMock.Setup(x => x.AddAsync(It.IsAny<Entity>()))
            .ReturnsAsync(Result.Error(new Error("Repository Error")));

        var handler = new AddEntityHandler(
            unitOfWorkMock.Object,
            _currentDateTimeService,
            _currentUserService,
            _notificationSenderMock.Object,
            _externalServiceSenderMock.Object,
            _fileProcessingService,
            _imageProcessingServiceMock.Object
        );
        var command = CreateValidCommand();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Repository Error");
    }

    [Fact]
    public async Task Handle_外部サービスからのエラーはそのまま返る()
    {
        // Arrange
        _externalServiceSenderMock.Setup(x => x.SendAsync(It.IsAny<ExternalServiceQuery>()))
            .ReturnsAsync(Result.Error<ExternalServiceResult>(new Error("External Service Error")));

        var handler = CreateHandler();
        var command = CreateValidCommand();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("External Service Error");
    }

    [Fact]
    public async Task Handle_通知サービスからのエラーはそのまま返る()
    {
        // Arrange
        _notificationSenderMock.Setup(x => x.SendAsync(It.IsAny<NotificationQuery>()))
            .ReturnsAsync(Result.Error<string>(new Error("Notification Error")));

        var handler = CreateHandler();
        var command = CreateValidCommand();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Notification Error");
    }

    [Fact]
    public async Task Handle_画像処理サービスからのエラーはそのまま返る()
    {
        // Arrange
        _imageProcessingServiceMock.Setup(x => x.ProcessImageAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Error<string>(new Error("Image Processing Error")));

        var handler = CreateHandler();
        var command = CreateValidCommand();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Image Processing Error");
    }

    [Fact]
    public async Task Handle_ストレージサービスからのエラーはそのまま返る()
    {
        // Arrange
        var formFiles = CreateTestFiles();
        _objectStorageClientMock.Setup(m => m.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Error(new Error("Storage Error")));

        var handler = CreateHandler();
        var command = CreateValidCommand() with { UploadFiles = formFiles };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Storage Error");
    }

    [Fact]
    public async Task Handle_関連エンティティが存在しない場合のエラーハンドリング()
    {
        // Arrange
        var unitOfWork = new UnitOfWork(_dbContext);
        var unitOfWorkMock = new Mock<IUnitOfWork>();
        var repositoryMock = new Mock<RelatedEntityRepository>();
        
        unitOfWorkMock.Setup(x => x.GetRepository<Entity>())
            .Returns(unitOfWork.GetRepository<Entity>());
        unitOfWorkMock.Setup(x => x.GetRepository<RelatedEntity, string>())
            .Returns(repositoryMock.Object);

        // 関連エンティティが見つからない場合のエラーを設定
        repositoryMock.Setup(x => x.GetAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Error<RelatedEntity>(new DataNotFoundError()));

        var handler = new AddEntityHandler(
            unitOfWorkMock.Object,
            _currentDateTimeService,
            _currentUserService,
            _notificationSenderMock.Object,
            _externalServiceSenderMock.Object,
            _fileProcessingService,
            _imageProcessingServiceMock.Object
        );
        var command = CreateValidCommand() with { RelatedEntityId = "non-existent-id" };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    #endregion

    #region 複雑な業務ロジックテスト（業務固有）

    [Fact]
    public async Task Handle_優先度に応じた処理順序が正しく設定される()
    {
        // Arrange
        var highPriorityCommand = CreateValidCommand() with { Priority = 10 };
        var lowPriorityCommand = CreateValidCommand() with { Priority = 1 };
        var handler = CreateHandler();

        // Act
        var highResult = await handler.Handle(highPriorityCommand, CancellationToken.None);
        var lowResult = await handler.Handle(lowPriorityCommand, CancellationToken.None);

        // Assert
        highResult.Should().BeOk();
        lowResult.Should().BeOk();

        // TODO: 実際の優先度ロジックに合わせて検証を実装してください
        var highEntity = await _dbContext.Entities.Where(e => e.Id == highResult.Get()).FirstOrDefaultAsync();
        var lowEntity = await _dbContext.Entities.Where(e => e.Id == lowResult.Get()).FirstOrDefaultAsync();

        highEntity.Priority.Should().Be(10);
        lowEntity.Priority.Should().Be(1);

        // 処理キューの順序確認など
        // var queues = _dbContext.ProcessingQueues.OrderBy(q => q.Priority).ToList();
        // queues.First().EntityId.Should().Be(highResult.Get());
    }

    [Fact]
    public async Task Handle_ステータス変更時の通知が正しく送信される()
    {
        // Arrange
        var command = CreateValidCommand() with { Status = EntityStatus.Active };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // 通知送信の確認
        _notificationSenderMock.Verify(x => x.SendAsync(It.Is<NotificationQuery>(q => 
            q.Type == NotificationType.EntityAdded && 
            q.EntityId == result.Get())), Times.Once);
    }

    #endregion

    #region ヘルパーメソッド

    /// <summary>
    /// テスト用ファイルを作成します
    /// </summary>
    /// <returns>テスト用ファイルのリスト</returns>
    private static List<IFormFile> CreateTestFiles()
    {
        var formFiles = new List<IFormFile>();
        
        // TODO: 実際のファイルテストパターンに合わせて調整してください
        var file1 = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes(TestFileContent1)), 0, TestFileSize, TestFileName1, TestFileDisplayName1);
        var file2 = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes(TestFileContent2)), 0, TestFileSize, TestFileName2, TestFileDisplayName2);
        
        formFiles.Add(file1);
        formFiles.Add(file2);
        
        return formFiles;
    }

    #endregion
}
