import type { FindIssueProjectDiscussionThreadResultItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'

export const useGetThreadComments = (
  thread: FindIssueProjectDiscussionThreadResultItem,
) => {
  /**
   * 最新のコメントを除外したコメント
   */
  const commentsWithoutLatest = computed(() => {
    if (thread.comments.length < 2) return []
    const comments = [...thread.comments]
    comments.pop()
    return comments
  })

  return {
    commentsWithoutLatest,
  }
}
