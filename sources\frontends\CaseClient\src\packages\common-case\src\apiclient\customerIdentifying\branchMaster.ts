import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

// 主として扱うデータのスキーマを定義します。
export const branchmasterSchema = z.object({
  id: z.string(),
  number: z.string(),
  name: z.string(),
  branchType: z.string(),
  closedAt: z.string().datetime(),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type BranchMaster = z.infer<typeof branchmasterSchema>

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

/**
 * 対象の支店を取得する
 * @param query [] 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindAllBranchMaster() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<BranchMaster[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-old-customer-identifying/v1.0/branchmaster/getAll',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

/**
 * 対象の支店を取得する
 * @param query [] 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindBranchMaster() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<BranchMaster[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-old-customer-identifying/v1.0/branchmaster/getBranch',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}
