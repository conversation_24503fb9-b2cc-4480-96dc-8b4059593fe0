import { useSharedData } from '@ibp/issue-project/src/composables/fileUploader/useSharedData'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type { issueProjectDiscussionFileModelItem } from '@ibp/issue-project/src/models/issueProjectDiscussion/FileUploadType'
import type { DeleteItem } from '@ibp/issue-project/src/models/share/FileUploadType'

export const useFileManagement = () => {
  const { error: errorToast } = useAppToasts()
  const { dataRef, nowUploadFiles } = useSharedData()

  const addFiles = (files: File[] | null) => {
    for (const file of files ?? []) {
      const selectedFile = nowUploadFiles.value?.find(
        (x: File) => x.name === file.name,
      )

      if (selectedFile) {
        errorToast(`同名のファイルが存在します(${file.name})`, { retain: true })
        continue
      }
      nowUploadFiles.value?.push(file)
      dataRef.value.isFormValid = false
    }
  }

  const deleteUploadFile = (fileName: string) => {
    nowUploadFiles.value = nowUploadFiles.value!.filter(
      (x: File) => x.name !== fileName,
    )
    if (nowUploadFiles.value!.length <= 0) {
      dataRef.value.isFormValid = true
    }
  }

  const change = () => {
    addFiles(dataRef.value.inputFiles)
    dataRef.value.inputFiles = []
  }

  const isFileInChipFilesToDelete = (
    file: issueProjectDiscussionFileModelItem,
  ) => {
    return dataRef.value.chipFilesToDelete.some(
      (chipFile: DeleteItem) => chipFile.deleteItemName === file.fileName,
    )
  }

  /**
   * チップの削除方法
   */
  const deleteFromChipSet = (deleteItem: any) => {
    dataRef.value.chipFilesToDelete.push({
      deleteTargetFile: deleteItem,
      deleteItemName: deleteItem.fileName,
      deleteItemVersion: deleteItem.version,
    })
  }

  return {
    addFiles,
    deleteUploadFile,
    isFileInChipFilesToDelete,
    deleteFromChipSet,
    change,
  }
}
