services:
  sqldb:
    image: mcr.microsoft.com/azure-sql-edge:2.0.0
    container_name: customerunderstandingapp_sqldb
    environment:
      ACCEPT_EULA: 'Y'
      SA_PASSWORD: P@ssw0rd!
    ports:
      - 1433:1433
  azurite:
    image: mcr.microsoft.com/azure-storage/azurite:3.31.0
    container_name: customerunderstandingapp_azurite
    ports:
      - 10000:10000
    command: "azurite-blob --blobHost 0.0.0.0 --blobPort 10000"
