import { useNuxtApp } from 'nuxt/app'
import { format, parseJSON } from 'date-fns'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import { isString } from '@hox/base/src/utils/shared/is'

export const customerSchema = z.object({
  id: z.string(),
  customerType: z.number().nullish(),
  branchNumber: z.string().max(3).nullish(),
  cifNumber: z.string().max(8).nullish(),
  nameKana: z.string().max(50),
  nameKanji: z.string().max(50),
  name: z.string(),
  address: z.string().max(50).nullish(),
  birthDate: z.string().datetime().nullish(),
  channel: z.number(),
  userId: z.string().max(50).nullish(),
  loanRating: z.string().max(2).nullish(),
  communicationPlanCount: z.number().nullish(),
  transactionPolicyConfirmedDateTime: z.string().datetime().nullish(),
  lastUpdatedDateTime: z.string().datetime().nullish(),
  industryCode: z.string().max(4).nullish(),
  approachType: z.number().nullish(),
  staffId: z.string().nullish(),
  staffName: z.string().nullish(),
  transactionPolicy: z.string().max(2).nullish(),
})

// Guid Id,
// string BranchNumber,
// string CifNumber,
// string NameKana,
// string NameKanji,
// string? PhoneNumbers,
// DateTimeOffset? BirthDate,
// string? Address,
// string? StaffId,
// string? NameId,
// string? LoanRating,
// string? TransactionPolicy

// 主として扱うデータのデータ型を定義します。
export type Customer = z.infer<typeof customerSchema>

// =====================================================================================================================
// APIクライアントの定義(検索)
// =====================================================================================================================

export type FindCustomerCriteria = {
  name?: string
  branchNumbers?: string[]
  cifNumber?: string
  industryCodes?: string[]
  loanRatings?: string[]
  transactionPolicies?: string[]
  approachTypes?: number[]
  staffIds?: string[]
  channels?: number[]
  pageSizeInput?: number
} & Pagination

export type FindCustomerResultItem = FindCustomerCriteria
// 検索結果の型にページングの情報を追加します。
export type FindCustomerResult = ApiResult<FindCustomerResultItem>

const getCustomerName = (nameKanji: string, nameKana: string) => {
  const nameKanjiIsEmpty = !nameKanji || nameKanji === ''
  return nameKanjiIsEmpty ? nameKana : nameKanji
}

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindCustomer(query: Ref<FindCustomerCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindCustomerResult>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-identifying/v1.0/customer'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          if (Array.isArray(data.items)) {
            for (const item of data.items) {
              if (isString(item.lastUpdatedDateTime)) {
                item.lastUpdatedDateTime = format(
                  parseJSON(item.lastUpdatedDateTime),
                  'yyyy/MM/dd',
                )
              }

              item.name = getCustomerName(item.nameKanji, item.nameKana)
            }
          }
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(取得)
// =====================================================================================================================

// NOTE: このPJで使用していないプロパティはコメントアウトしている
export type CustomerGet = {
  id: string
  // customerType?: number
  branchNumber?: string
  cifNumber?: string
  customerName?: string
  nameKana?: string
  // isNameKanaFixed: boolean
  nameKanji?: string
  // isNameKanjiFixed: boolean
  address?: string
  // isAddressFixed: boolean
  // birthDate?: string
  // isBirthDateFixed: boolean
  phoneNumber?: string
  // email?: string
  // isEmailFixed: boolean
  // channel: number
  // isChannelFixed: boolean
  postalCode?: string
  // version: string
}

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetCustomer(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<CustomerGet>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-identifying/v1.0/customer/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          data.customerName = getCustomerName(data.nameKanji, data.nameKana)
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(顧客識別IDで取得)
// =====================================================================================================================

export type FindCustomerByIdsCriteria = {
  ids: Array<string>
} & Pagination

export const findCustomerByIdSchema = z.object({
  id: z.string(),
  branchNumber: z.string().nullish(),
  cifNumber: z.string().nullish(),
  nameKana: z.string().nullish(),
  nameKanji: z.string().nullish(),
  phoneNumbers: z.string().nullish(),
  birthDate: z.string().datetime().nullish(),
  address: z.string().nullish(),
  staffId: z.string().nullish(),
  staffName: z.string().nullish(),
  NameId: z.string().nullish(),
  loanRating: z.string().max(2).nullish(),
  transactionPolicy: z.string().max(2).nullish(),
  industryCode: z.string().max(4).nullish(),
  name: z.string(),
})

export type FindCustomerByIdResult = z.infer<typeof findCustomerByIdSchema>

/**
 * データをId指定で取得する
 * @param id 顧客識別ID
 * @returns 顧客
 */
export function useFindCustomerByIds(body: Ref<FindCustomerByIdsCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Array<FindCustomerByIdResult>>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-identifying/v1.0/customer/find/byIds`,
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          if (Array.isArray(data)) {
            for (const item of data) {
              item.name = getCustomerName(item.nameKanji, item.nameKana)
            }
          }
          return data
        },
      }),
    ),
  )
}
