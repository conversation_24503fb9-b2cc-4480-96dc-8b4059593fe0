# アグリゲート編集フォームテスト作成

## 役割定義

あなたはアグリゲート編集フォームのテストを実装するベテランエンジニアです。Template_EditAggregate.vueまたはTemplate_CreateAggregate.vueから作成されたアグリゲートコンポーネントに対してVitestを使用した包括的な単体テストを生成してください。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- プロジェクト設定定義：
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- テスト対象コンポーネント：`[指定されたアグリゲート編集フォームのVueファイル]`
- ドメイン言語定義：`parasol\capability\**\ドメイン言語.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- コンポーネントテンプレート：
  - `parasol\prompts\4_Test_case_generation\Templates\aggregate_edit_forms\Template_AggregateEditForm.spec.ts`
  - `parasol\prompts\4_Test_case_generation\Templates\aggregate_edit_forms\Template_AggregateCreateForm.spec.ts`
- Composablesテンプレート：
  - `parasol\prompts\4_Test_case_generation\Templates\aggregate_edit_forms\Template_useGetAggregate.spec.ts`
  - `parasol\prompts\4_Test_case_generation\Templates\aggregate_edit_forms\Template_useUpdateAggregate.spec.ts`
  - `parasol\prompts\4_Test_case_generation\Templates\aggregate_edit_forms\Template_useCreatedAggregateState.spec.ts`
  - `parasol\prompts\4_Test_case_generation\Templates\aggregate_edit_forms\Template_useCreateAggregate.spec.ts`

## 出力定義

出力対象ファイルの出力先ディレクトリや出力ファイル名のフォーマットは以下に従ってください。

- コンポーネントテストファイル出力先ディレクトリ：`test\[テスト対象ファイルのsrc配下の相対パス]`
- Composableテストファイル出力先ディレクトリ：`test\composables\[エンティティ名]\`
- テストファイル名：
  - コンポーネント：`[テスト対象のファイル名].spec.ts`
  - Composables：`[Composable名].spec.ts`

## 制約事項

以下の制約に従ってください。

### 禁止事項

- 定義ファイルを編集する代わりに、必要な内容は出力ファイルに記述してください
- テンプレートファイルを編集する代わりに、テンプレートを参考にしてテストコードを生成してください
- 当プロンプトファイルを編集する代わりに、記載されている指示に従ってテスト実装を行ってください
- テンプレートにないモックを定義する代わりに、テンプレートに記載されているモック定義のみを使用してください

## 指示詳細

### 情報収集

1. 各定義ファイルの読み込みを行ってください。
   - テスト対象のアグリゲート編集フォームVueファイルを読み込み、実装内容を確認してください
   - テスト対象コンポーネントで扱うエンティティに基づいて、該当するドメイン言語ファイルを読み込んでください
   - 適切なテストテンプレートファイルを読み込んでください

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください。

1. コンポーネント種別（編集・作成）に応じてテストファイルを作成してください。
   - 編集コンポーネントの場合：
     - コンポーネントテスト：Template_AggregateEditForm.spec.tsを基に生成
     - Composablesテスト：useGetAggregate、useUpdateAggregate、useCreatedAggregateStateのテストを生成
   - 作成コンポーネントの場合：
     - コンポーネントテスト：Template_AggregateCreateForm.spec.tsを基に生成
     - Composablesテスト：useCreateAggregateのテストを生成

1. テンプレートファイルを基に、エンティティ名に応じた置換を行ってください。
   - import文のコンポーネントパス
   - describe文のテストパス
   - composableのモック設定
   - 変数名・プロパティ名
   - API endpoint
   - 型定義

### 品質保証

1. 作成したテストファイルの検証を行ってください：
   - 出力先・出力ファイル名フォーマットに従って、ファイルが正しく出力されているか
   - エンティティ固有の名前に正しく置換されているか
   - 基本動作テスト（コンポーネント表示、ローディング状態、セクション構成）が含まれているか
   - データフローテスト（データ取得、初期化処理、エラーハンドリング）が含まれているか
   - UIコンポーネントテスト（セクションコンポーネント、プロパティ伝播）が含まれているか
   - Composablesテスト（API通信、データ変換、状態管理、バリデーション）が含まれているか
   - TypeScriptの構文エラーがないか
   - Vitestの記法が正しく使用されているか
   - 必要なimport文がすべて含まれているか
