import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import { interactionCategorykeys } from '@ibp/common-case/src/constants/domain/interactionCategory'
import type { InteractionCategoryType } from '@ibp/common-case/src/constants/domain/interactionCategory'
import type { InteractionCategoryGroupType } from '@ibp/common-case/src/constants/domain/interactionCategoryGroup'

// =====================================================================================================================
// APIクライアントの定義(新規追加)
// =====================================================================================================================

// データのスキーマを定義します。
export const interactionlogSchema = z.object({
  customerIdentificationId: z.string().uuid(),
  interactionCategory: z.enum(
    interactionCategorykeys.map(String) as [string, ...string[]],
  ),
  sourceId: z.string(),
  threadId: z.string().nullish(),
  description1: z.string().nullish(),
  description2: z.string().nullish(),
  creatorId: z.string(),
  creatorName: z.string(),
  createdDateTime: z.date(),
})

// データ型を定義します。
export type InteractionLog = z.infer<typeof interactionlogSchema>

// Resultの型
export type GetInteractionLogResult = {
  id: string
  customerIdentificationId: string
  interactionCategory: InteractionCategoryType
  interactionCategoryGroup: InteractionCategoryGroupType
  sourceId: string
  threadId: string | null
  title: string
  subTitle: string | null
  description1: string | null
  description2: string | null
  creatorId: string
  creatorName: string
  createdDateTime: Date
  displayCreatedDateTime: string
  version: string
}

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostInteractionLog(body: Ref<InteractionLog>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetInteractionLogResult>(
    useFetch(
      () =>
        $endpoints.default.get('/ibp-customer-interaction/v1.0/interactionlog'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}
