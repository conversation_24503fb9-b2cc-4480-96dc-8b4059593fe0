﻿<template>
  <LeaseTable
    v-model:pageIndex="pagination.pageIndex"
    v-model:sort="pagination.sort"
    :items="data?.items"
    :total="data?.total"
    :loading="loading"
    :page-size="pagination.pageSize"
    :search-result-message="searchResultMessage"
    @search="search"
    @edit="edit"
  >
    <template #criteria>
      <LeaseSearchCriteria
        :search-condition="searchCondition"
        :branch-master-options="branchMasterOptions"
        :staff-and-teams="options.staffAndTeams"
        :loading-staff-and-team="loadingStaffAndTeam"
        :error-messages="errorMessages"
        @update:search-condition="updateSearchCondition"
        @validate-item="validateItem"
      />
    </template>
  </LeaseTable>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import LeaseTable from '@/packages/lease/src/components/new-lease/LeaseTable.vue'
import LeaseSearchCriteria from '@/packages/lease/src/components/new-lease/LeaseSearchCriteria.vue'
import { useLeaseSearch } from '@/packages/lease/src/composables/new-lease/useLeaseSearch'
import { useLeaseValidation } from '@/packages/lease/src/composables/new-lease/useLeaseValidation'
import { useLeaseOptions } from '@/packages/lease/src/composables/new-lease/useLeaseOptions'
import { useLeaseNavigation } from '@/packages/lease/src/composables/new-lease/useLeaseNavigation'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { isUndefinedOrNull } from '@hox/base/src/utils/shared/is'
// タイトルとメタデータの設定
useHead({
  title: 'リース案件作業一覧',
})

definePageMeta({
  title: 'リース案件作業一覧',
})

// オプションデータ取得用コンポーザブル
const {
  branchMasterOptions,
  options,
  loadingStaffAndTeam,
  loadAllOptions,
} = useLeaseOptions()

// バリデーションの設定
const validationInstance = useLeaseValidation(toRef(() => searchCondition))
const { errorMessages, validateItem, validateAll } = validationInstance

// 検索関連機能を取得
const {
  searchCondition,
  pagination,
  data,
  searchResultMessage,
  loading,
  criteriaHistory,
  hasState,
  search,
  restoreCriteria,
} = useLeaseSearch(validateAll)

const { error: errorToast } = useAppToasts()

// 検索条件更新用の関数
function updateSearchCondition(newValue: any) {
  // 検索条件の各プロパティを更新
  Object.assign(searchCondition, newValue)
}

// 初期データの読み込み
onMounted(async () => {
  try {
    const hasCriteriaHistory = hasState && !isUndefinedOrNull(criteriaHistory.value)

    // マスタデータとオプション情報を取得
    await loadAllOptions(hasCriteriaHistory, searchCondition)

    if (hasCriteriaHistory) {
      // 検索条件を復元して検索実行
      await restoreCriteria(criteriaHistory.value)
      search({ noStoreCriteria: true })
    } else {
      // 初期化
      data.value = { items: [], total: 0 }
    }
  } catch (error) {
    errorToast('初期データの読み込みに失敗しました。')
  }
})

// ナビゲーション関連機能を取得
const { navigateToEdit } = useLeaseNavigation()

// 編集処理
function edit(e: any) {
  // 遷移前に画面をクリア・ローディング状態にする
  const onBeforeNavigate = () => {
    data.value = { items: [], total: 0 }
    loading.value = true
  }

  // 編集画面に遷移
  navigateToEdit(e, onBeforeNavigate)
}
</script>
