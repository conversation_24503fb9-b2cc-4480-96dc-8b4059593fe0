<!--
===========================================
アグリゲートエンティティListFormコンポーネント テンプレート
===========================================

このテンプレートは、アグリゲートルートエンティティの各プロパティに対応する
Formコンポーネントの基本構造を提供します。

【使用方法】
1. TODOコメントに従って、エンティティ固有の情報に置き換える
2. 不要なフィールドは削除し、必要なフィールドを追加する
3. レイアウト（v-row/v-col）を調整する
-->

<script setup lang="ts">
import type { AppDataTableHeader } from '@hox/base/src/components/shared/types'
import type { SampleEntity } from '../../constants/domain/entities/SampleEntity'
import type { CommonRelatedInformations } from '../../apiclient/relatedinformation'
import type { SampleEntityEditDialogType } from './SampleEntityEditDialog.vue'
import { useRelatedInformation } from '../../composables/useRelatedInformation'

const props = defineProps<{
  relatedInformation: CommonRelatedInformations | null | undefined
  disabled?: boolean
  loading: boolean
}>()

const items = defineModel<SampleEntity[]>({ default: () => [] })

const { toDisplayName } = useRelatedInformation(
  toRef(props, 'relatedInformation'),
)

// TODO: 編集対象のエンティティ型に合わせてdefaultHeaders定義を変更
const defaultHeaders: AppDataTableHeader<SampleEntity>[] = [
  {
    key: 'branchNumber',
    title: '店番',
    sortable: false,
    nowrap: true,
  },
  {
    key: 'cifNumber',
    title: 'CIF',
    sortable: false,
    nowrap: true,
  },
  {
    key: 'SampleEntityName',
    title: '氏名',
    sortable: false,
    nowrap: true,
  },
  {
    key: 'relationship',
    title: '関係',
    sortable: false,
    nowrap: true,
    value(item) {
      return toDisplayName('relationships', item.relationship)
    },
  },
  {
    key: 'relationshipDetail',
    title: '関係詳細',
    sortable: false,
  },
  {
    key: 'isCollateralProvider',
    title: '物上保証',
    sortable: false,
    nowrap: true,
  },
]

const headers = computed<AppDataTableHeader<SampleEntity>[]>(() => props.disabled
  ? defaultHeaders
  : [
      ...defaultHeaders,
      { key: '##edit', title: '', sortable: false, align: 'end' },
    ],
)

const SampleEntityEditDialog = shallowRef<SampleEntityEditDialogType>()

async function editSampleEntity(SampleEntity?: SampleEntity) {
  const { isOk, data } = await SampleEntityEditDialog.value!.open(SampleEntity)
  if (isOk) {
    if (SampleEntity) {
      Object.assign(SampleEntity, data)
    } else {
      items.value = [...items.value, data]
    }
  }
}

function deleteSampleEntity(SampleEntity: SampleEntity) {
  items.value = items.value.filter(g => g !== SampleEntity)
}
</script>
<template>
  <VContainer fluid>
    <!-- TODO : 対象のエンティティ一覧のタイトルを設定 -->
    <VLabel text="保証人一覧" class="label-title mb-2" />
    <AppSimpleDataTable
      :headers
      :items
      :items-per-page="-1"
      :loading
      item-value="customerIdentificationId"
      hide-default-footer
    >
      <template #item.isCollateralProvider="{ value }">
        <VIcon v-if="value" color="primary" icon="mdi-check" />
      </template>
      <template #[`header.##edit`]>
        <AppSubBtn
          type="add"
          :disabled="loading"
          @click="() => editSampleEntity()"
        />
      </template>
      <template #[`item.##edit`]="{ item }">
        <VIcon
          :disabled="loading"
          color="primary"
          @click="() => editSampleEntity(item)"
        >
          mdi-pencil
        </VIcon>
        <VIcon
          :disabled="loading"
          class="ml-2"
          color="error"
          @click="() => deleteSampleEntity(item)"
        >
          mdi-delete
        </VIcon>
      </template>
    </AppSimpleDataTable>
    <!-- TODO : 対象のエンティティを編集するための明細編集ダイアログコンポーネントを実装し、呼び出してください -->
    <SampleEntityEditDialog
      ref="SampleEntityEditDialog"
      :SampleEntitys="items"
    />
  </VContainer>
</template>
