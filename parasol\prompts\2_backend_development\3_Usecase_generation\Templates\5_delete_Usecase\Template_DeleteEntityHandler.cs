using MediatR;
using Nut.Results;
using SampleService.Domain;
using Shared.Results.Errors;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.DeleteEntity;

public class DeleteEntityHandler(IUnitOfWork unitOfWork) : IRequestHandler<DeleteEntityCommand, Result<string>>
{
    public async Task<Result<string>> Handle(DeleteEntityCommand request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request);

        // TODO: 利用するリポジトリに置き換えてください。
        var repository = unitOfWork.GetRepository<Domain.Entities.Entity>();
        // 既存データを取得します。
        var getResult = await repository.GetAsync(request.Id)
            // 既存データが取得できなかった場合は同時実行制御エラーに置き換えます。
            .MapError(e => e is DataNotFoundException ? new ChangeConflictException() : e);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        return await repository
            .DeleteAsync(currentData) // データを削除として設定します。
            .FlatMap(() => unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果として削除したデータのキーを返します。
            .ConfigureAwait(false);
    }
}
