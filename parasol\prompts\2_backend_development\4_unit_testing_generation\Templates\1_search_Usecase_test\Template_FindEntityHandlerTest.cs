using FluentAssertions;
using Nut.Results;
using Nut.Results.FluentAssertions;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Infrastructure.Persistence;
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.FindEntity;

// TODO: 長いジェネリック型の短縮形エイリアス（可読性向上のため）
using EntityRepository = Shared.Domain.IRepository<SampleService.Domain.Entities.Entity, string>;
using EntityResult = Nut.Results.Result<SampleService.Domain.Entities.Entity>;
using PaginatedEntityResult = Nut.Results.Result<Shared.Domain.PaginatedResult<SampleService.Domain.Entities.Entity>>;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.FindEntity;

/// <summary>
/// FindEntityHandlerの単体テストクラス
/// </summary>
/// <remarks>
/// 統合テスト（SQLiteインメモリDB使用）とSpecificationテストを組み合わせて、
/// 検索処理のテストを包括的に実装し、コードカバレッジ100%を目指します。
/// 正常系、異常系、境界値、エッジケースを網羅的にテストします。
/// </remarks>
public class FindEntityHandlerTest : IAsyncLifetime
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TestCurrentUserService _currentUserService;
    private readonly TestCurrentDateTimeService _currentDateTimeService;

    public FindEntityHandlerTest()
    {
        _currentUserService = new TestCurrentUserService();
        _currentDateTimeService = new TestCurrentDateTimeService();

        _dbContext = TestDbContextBuilder.Build(_currentUserService, _currentDateTimeService);
    }

    public Task InitializeAsync()
        => TestData.CreateAsync(_dbContext);

    public Task DisposeAsync() => Task.CompletedTask;
    #region テスト対象とテストデータの準備

    /// <summary>
    /// テスト対象のハンドラーを作成します
    /// </summary>
    /// <returns>FindEntityHandler インスタンス</returns>
    private FindEntityHandler CreateHandler()
    {
        // TODO: プロジェクトのパターンに合わせてハンドラーの生成方法を調整してください
        var unitOfWork = new UnitOfWork(_dbContext);
        return new FindEntityHandler(unitOfWork);
    }

    #endregion

    #region 異常系テスト（null チェック）

    [Fact]
    public void Ctor_引数にnullを渡すと例外が発生する()
    {
        var act = () => new FindEntityHandler(null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public async Task Handle_Requestがnullの場合は例外が発生する()
    {
        var handler = CreateHandler();

        var act = () => handler.Handle(null!, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    #endregion

    #region 正常系テスト

    [Fact]
    public async Task Handle_正常な検索条件でデータが取得できる()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            // TODO: 検索条件のプロパティをエンティティに合わせて設定してください
            Name = "検索対象",
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        
        paginatedResult.Items.Should().NotBeNull();
        paginatedResult.Items.Should().HaveCountLessThanOrEqualTo(query.PageSize);
        
        // TODO: TestDataクラスから期待するテストデータを取得して詳細検証を行ってください
        // var expectedData = TestData.GetEntityTestData().Where(t => !t.IsDeleted).OrderBy(t => t.Id).ToList();
        // foreach (var i in Enumerable.Range(0, Math.Min(expectedData.Count, paginatedResult.Items.Count())))
        // {
        //     paginatedResult.Items.ElementAt(i).Id.Should().Be(expectedData[i].Id);
        //     paginatedResult.Items.ElementAt(i).Name.Should().Be(expectedData[i].Name);
        //     // 他のプロパティも同様に検証
        // }
    }

    [Fact]
    public async Task Handle_引数で渡された条件で検索される()
    {
        // Arrange  
        var query = new FindEntityQuery()
        {
            // TODO: 具体的な検索条件を設定してください
            Name = "特定の条件",
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        
        // TODO: 検索条件に合致するデータのみが返されることを確認してください
        // paginatedResult.Items.Should().OnlyContain(x => x.Name.Contains("特定の条件"));
    }

    [Fact]
    public async Task Handle_検索条件なしで全データが取得できる()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            PageIndex = 1,
            PageSize = 20
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        
        // TODO: TestDataから期待される全件数を取得して比較してください
        // var expectedCount = TestData.GetEntityTestData().Count(t => !t.IsDeleted);
        // paginatedResult.Items.Should().HaveCount(expectedCount);
        // paginatedResult.TotalCount.Should().Be(expectedCount);
    }

    [Theory]
    [InlineData("検索対象1")]
    [InlineData("検索対象2")]
    public async Task Handle_名前検索条件で正しいデータが取得できる(string nameFilter)
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            Name = nameFilter,
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        
        // TODO: 名前フィルターに応じた検証を実装してください
        // paginatedResult.Items.Should().OnlyContain(x => x.Name.Contains(nameFilter));
    }

    [Fact]
    public async Task Handle_期間検索条件で正しいデータが取得できる()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            // TODO: 期間検索のプロパティ名をエンティティに合わせて調整してください
            // PeriodFrom = DateTimeOffset.Now.AddDays(-15),
            // PeriodTo = DateTimeOffset.Now.AddDays(15),
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        
        // TODO: 期間内のデータのみが取得されることを確認してください
        // paginatedResult.Items.Should().OnlyContain(x => 
        //     x.PeriodFrom >= query.PeriodFrom && x.PeriodTo <= query.PeriodTo);
    }

    [Fact]
    public async Task Handle_ページネーション指定で正しい範囲のデータが取得できる()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            PageIndex = 1,
            PageSize = 1 // 1件ずつ取得
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        paginatedResult.Items.Count().Should().BeLessOrEqualTo(1);
        
        // ページネーション情報の検証
        if (paginatedResult.TotalCount > 0)
        {
            paginatedResult.PageIndex.Should().Be(1);
            paginatedResult.PageSize.Should().Be(1);
            paginatedResult.TotalPages.Should().BeGreaterThan(0);
        }
    }

    #endregion

    #region 境界値テスト

    [Fact]
    public async Task Handle_検索結果0件の場合は空のリストが返る()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            // TODO: 存在しないデータの検索条件を設定してください
            Name = "存在しないエンティティ名",
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        paginatedResult.Items.Should().BeEmpty();
        paginatedResult.TotalCount.Should().Be(0);
        paginatedResult.TotalPages.Should().Be(0);
    }

    [Fact]
    public async Task Handle_データが見つからない場合は適切な結果が返る()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            // TODO: 絶対に存在しない条件を設定してください（例：未来の日付など）
            Name = "NONEXISTENT_DATA_12345",
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        paginatedResult.Items.Should().BeEmpty();
        paginatedResult.TotalCount.Should().Be(0);
    }

    [Theory]
    [InlineData(0)] // 境界値: 最小値
    [InlineData(-1)] // 不正値
    public async Task Handle_不正なPageIndexでの動作確認(int invalidPageIndex)
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            PageIndex = invalidPageIndex,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        // SafePaginationOptionが使用されている場合は正常化される
        // そうでない場合はエラーになる可能性があります
        // TODO: プロジェクトの仕様に合わせて期待する動作を設定してください
        if (result.IsError)
        {
            result.Should().BeError();
        }
        else
        {
            result.Should().BeOk();
            var paginatedResult = result.Get();
            paginatedResult.PageIndex.Should().BeGreaterThan(0); // 正常化された値
        }
    }

    [Theory]
    [InlineData(0)] // 境界値: 最小値
    [InlineData(-1)] // 不正値
    public async Task Handle_不正なPageSizeでの動作確認(int invalidPageSize)
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            PageIndex = 1,
            PageSize = invalidPageSize
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        // SafePaginationOptionが使用されている場合は正常化される
        // そうでない場合はエラーになる可能性があります
        // TODO: プロジェクトの仕様に合わせて期待する動作を設定してください
        if (result.IsError)
        {
            result.Should().BeError();
        }
        else
        {
            result.Should().BeOk();
            var paginatedResult = result.Get();
            paginatedResult.PageSize.Should().BeGreaterThan(0); // 正常化された値
        }
    }

    #endregion

    #region エッジケーステスト

    [Theory]
    [InlineData("")]
    [InlineData("   ")] // 空白文字
    [InlineData(null)] // null値
    public async Task Handle_空文字またはnull検索条件での動作確認(string? emptyName)
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            Name = emptyName,
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        // 空文字・nullの場合の動作は仕様に依存します
        var paginatedResult = result.Get();
        paginatedResult.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_特殊文字を含む検索条件での動作確認()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            Name = "Test@#$%Entity",
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        // 特殊文字を含む検索でもエラーにならないことを確認
        var paginatedResult = result.Get();
        paginatedResult.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_大量データでのページネーション動作確認()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            PageIndex = 2, // 2ページ目を取得
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        
        // 大量データが存在する場合のページネーション動作を確認
        if (paginatedResult.TotalCount > 10)
        {
            paginatedResult.PageIndex.Should().Be(2);
            paginatedResult.PageSize.Should().Be(10);
            paginatedResult.TotalPages.Should().BeGreaterThan(1);
        }
    }

    [Fact]
    public async Task Handle_期間検索の境界値での動作確認()
    {
        // Arrange
        var now = DateTimeOffset.Now;
        var query = new FindEntityQuery()
        {
            // TODO: 期間検索のプロパティ名をエンティティに合わせて調整してください
            // PeriodFrom = now.AddDays(-1),
            // PeriodTo = now.AddDays(1),
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        // 期間検索が正常に動作することを確認
        var paginatedResult = result.Get();
        
        // TODO: 期間内のデータのみが取得されることを確認してください
        // paginatedResult.Items.Should().OnlyContain(x => 
        //     x.PeriodFrom >= query.PeriodFrom && x.PeriodTo <= query.PeriodTo);
    }

    [Fact]
    public async Task Handle_From期間がTo期間より後の場合の動作確認()
    {
        // Arrange
        var query = new FindEntityQuery()
        {
            // TODO: 期間検索のプロパティ名をエンティティに合わせて調整してください
            // PeriodFrom = DateTimeOffset.Now.AddDays(10), // Toより後の日付
            // PeriodTo = DateTimeOffset.Now.AddDays(-10),
            PageIndex = 1,
            PageSize = 10
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var paginatedResult = result.Get();
        
        // TODO: 仕様に応じて期待される結果を設定してください
        // 通常は条件に合うデータがないため空のリストが返される
        // paginatedResult.Items.Should().BeEmpty();
    }

    #endregion

    #region Specificationテスト（オプション）

    // TODO: 必要に応じてSpecificationテストを追加してください
    // [Fact]
    // public void FindEntitySpecification_名前検索条件の仕様テスト()
    // {
    //     // Arrange
    //     var targetName = "検索対象";
    //     var specification = new FindEntitySpecification { Name = targetName };
    //     var testEntity = TestData.Entity.CreateValid(targetName, DateTimeOffset.Now, DateTimeOffset.Now);
    //
    //     // Act & Assert
    //     SpecificationTestHelper.TestSpecification(specification, testEntity, shouldMatch: true);
    //     
    //     var nonMatchingEntity = TestData.Entity.CreateValid("別の名前", DateTimeOffset.Now, DateTimeOffset.Now);
    //     SpecificationTestHelper.TestSpecification(specification, nonMatchingEntity, shouldMatch: false);
    // }

    // [Fact]
    // public void FindEntitySpecification_期間検索条件の仕様テスト()
    // {
    //     // Arrange
    //     var periodFrom = DateTimeOffset.Now.AddDays(-10);
    //     var periodTo = DateTimeOffset.Now.AddDays(10);
    //     var specification = new FindEntitySpecification 
    //     { 
    //         PeriodFrom = periodFrom, 
    //         PeriodTo = periodTo 
    //     };
    //
    //     // 期間内のエンティティ
    //     var entityInPeriod = TestData.Entity.CreateValid("テスト", periodFrom.AddDays(1), periodTo.AddDays(-1));
    //     
    //     // 期間外のエンティティ
    //     var entityOutOfPeriod = TestData.Entity.CreateValid("テスト", periodFrom.AddDays(-5), periodFrom.AddDays(-1));
    //
    //     // Act & Assert
    //     SpecificationTestHelper.TestSpecification(specification, entityInPeriod, shouldMatch: true);
    //     SpecificationTestHelper.TestSpecification(specification, entityOutOfPeriod, shouldMatch: false);
    // }

    // [Fact]
    // public void FindEntitySpecification_複合検索条件の仕様テスト()
    // {
    //     // Arrange
    //     var specification = new FindEntitySpecification 
    //     { 
    //         Name = "検索対象",
    //         PeriodFrom = DateTimeOffset.Now.AddDays(-10),
    //         PeriodTo = DateTimeOffset.Now.AddDays(10)
    //     };
    //
    //     // すべての条件に合致するエンティティ
    //     var matchingEntity = TestData.Entity.CreateValid("検索対象エンティティ", 
    //         DateTimeOffset.Now.AddDays(-5), DateTimeOffset.Now.AddDays(5));
    //     
    //     // 名前は合致するが期間が合わないエンティティ
    //     var partialMatchEntity = TestData.Entity.CreateValid("検索対象エンティティ", 
    //         DateTimeOffset.Now.AddDays(-20), DateTimeOffset.Now.AddDays(-15));
    //
    //     // Act & Assert
    //     SpecificationTestHelper.TestSpecification(specification, matchingEntity, shouldMatch: true);
    //     SpecificationTestHelper.TestSpecification(specification, partialMatchEntity, shouldMatch: false);
    // }

    #endregion

    #region エラーハンドリングテスト（高度なカバレッジ）

    // TODO: 必要に応じてリポジトリエラーのテストを追加してください
    // [Fact]
    // public async Task Handle_リポジトリからのエラーはそのまま返る()
    // {
    //     // Arrange
    //     var repositoryMock = new Mock<EntityRepository>();
    //     var query = new FindEntityQuery() { Name = "テスト" };
    //
    //     // リポジトリをモックで変更してErrorが返るようにする
    //     repositoryMock.Setup(x => x.FindWithPaginationAsync(It.IsAny<FindEntitySpecification>(), It.IsAny<PaginationOption>()))
    //         .ReturnsAsync(Result.Error<PaginatedResult<Domain.Entities.Entity>>(new Error("Repository Error")));
    //     
    //     var unitOfWorkMock = new Mock<IUnitOfWork>();
    //     unitOfWorkMock.Setup(x => x.GetRepository<Domain.Entities.Entity, string>()).Returns(repositoryMock.Object);
    //
    //     var handler = new FindEntityHandler(unitOfWorkMock.Object);
    //
    //     // Act
    //     var result = await handler.Handle(query, CancellationToken.None);
    //
    //     // Assert
    //     result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Repository Error");
    // }

    #endregion
}
