# エンティティ生成サブプロンプト

## 役割定義

- 日本人のベテランエンジニアとして、フロントエンドアプリケーションの開発を行います。
- 必要な情報を収集し必要に応じてヒアリングを行いながら、実装を行います。
- 語学が堪能であるためヒアリングの際には適した言語で対応しますが、成果物は日本語で出力します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義：
  - `parasol\architecture\frontend_architecture_hoxbase.md`
  - `parasol\architecture\frontend_architecture_ibpbase.md`
- プロジェクト設定定義：
  - `parasol\architecture\project_settings.md`
- スキーマ定義方法：
  - `parasol\architecture\usage_sample_entity_schema.ts`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- API定義：`parasol\api\**\*_API_definition.md`
- ドメイン言語定義：`parasol\capability\**\ドメイン言語.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- エンティティテンプレート：`parasol\prompts\1_frontend_development\1_API_client_generation\sub_prompts\sub_prompt_entity.md`

## 出力定義

出力対象ファイルの出力先のディレクトリや出力ファイル名のフォーマットは以下に従ってください。

- 出力先 ： `[zodスキーマディレクトリ]/`
- ファイル名フォーマット ： `[扱うエンティティの物理名（camelCase形式）].ts`
   - 例：`employee.ts`

## 制約事項

以下の制約に従ってください。

### 禁止事項

- 定義ファイルを編集する代わりに、出力ファイルに必要な内容を記述してください
- テンプレートファイルを編集する代わりに、テンプレートに基づいて新しいファイルを生成してください
- 当プロンプトファイルを編集する代わりに、指示内容に従って作業を実行してください
- 指示していない内容を編集する代わりに、指定された範囲内で作業を行ってください

### その他ルール

- エンティティの定義はzodを利用して行ってください
- エンティティに値オブジェクトが存在している場合は、値オブジェクトの項目を直接エンティティ内に実装してください
- エンティティの定義の方法は、スキーマ定義方法の`usage_sample_entity_schema.ts`に従ってください

## 指示詳細

### 情報収集

1. 生成対象のエンティティについてドメイン言語ファイルから定義を読み込んでください。
   - 対象のエンティティのプロパティとその型、制約を確認してください。
      - 確認した内容を列挙してください。
2. 収集した情報の整理を行います。
   - 対象のエンティティで利用している列挙型を列挙してください。
   - 対象のエンティティから参照されるエンティティを列挙してください。

### 生成作業

1. 対象エンティティで利用している列挙型に対して、１つずつ以下の処理を繰り返してください。
   1. 必要な列挙型のうち、まだ処理していないものを一つ選択します。
   2. 以下のサブプロンプトを実行し、列挙型の生成を行います。
      - 列挙型生成手順 ： `.\sub_prompt_enum.md`
   3. 列挙型の生成が完了したら作業を一時中断し、「生成した列挙型の確認を行ってください」と出力し、ユーザーの承認を必ず得てください。
      - 承認が得られた場合は後続の処理を行ってください。
      - 承認が得られない場合はユーザーにヒアリングを行い、承認が得られるまで指示された修正を行ってください。
   4. 必要な列挙型一覧に未作成のものがないか確認します。
      - すべての列挙型が生成された場合は、次のステップに進みます。

2. 対象エンティティから参照されているエンティティに対して、１つずつ以下の処理を繰り返してください。
   1. 対象エンティティから参照されているエンティティのうち、まだ生成していないものを一つ選択します。
   2. 以下のサブプロンプトを実行し、エンティティの生成を行います。
      - エンティティ生成手順 ： `.\sub_prompt_entity.md`
   3. エンティティの生成が完了したら作業を一時中断し、「生成したエンティティの確認を行ってください」と出力し、ユーザーの承認を必ず得てください。
      - 承認が得られた場合は後続の処理を行ってください。
      - 承認が得られない場合はユーザーにヒアリングを行い、指示された修正を行ってください。
   4. 対象エンティティから参照されているエンティティ一覧に未作成のものがないか確認します。
      - すべての対象エンティティから参照されているエンティティが生成された場合は、次のステップに進みます。
3. 対象のエンティティをテンプレートファイルを元に実装します。
   - プロパティの定義順序はドメイン言語ファイルに記載されている順序に従ってください。
4. 必要に応じて以下を実装してください：
   - zodのインポート
   - 列挙型のインポート

### 品質保証

1. **機能完全性**
   - 生成したエンティティファイルに必要なプロパティがすべて実装されているか確認してください

1. **アーキテクチャ準拠性**
   - エンティティの定義がzodによって行われているか確認してください
   - エンティティの型定義がエンティティの定義を利用して行われているか確認してください

1. **コード品質**
   - 正しい構文で記述されているか確認してください
   - 実行されないコードや冗長なコードがないか確認してください
   - 改行やインデントが崩れていないか確認してください
   - 言語仕様に準拠したエラーがないか確認してください
   - 必要なインポート文がすべて含まれているか確認してください
   - 命名規則が一貫しているか確認してください

1. 上記で検証した内容に問題がある場合は、必要な修正を行ってください。
