# 非対面情報を確認できる能力 - ドメイン言語定義

## 1. エンティティ（Entities）

### 1.1 連絡先（ContactAddress）
- **ID（ID）**: 連絡先をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 連絡先の対象となる顧客のIBP上の一意の識別子
  - 仕様: `GUID`
- **連絡先顧客識別ID（ContactAddressCustomerIdentificationId）**: 連絡先顧客のIBP上の一意の識別子
  - 仕様: `GUID`
- **氏名（Name）**: 連絡先対象者の氏名
  - 仕様: `KANJI_MAX_20`
- **役職（Position）**: 連絡先者の役職
  - 仕様: `KANJI_MAX_20`
- **実権者（IsPersonOfPower）**: 実権者かどうか
  - 仕様: `BOOLEAN`
- **電話番号（PhoneNumber）**: 電話番号
  - 仕様: `ALPHANUMERIC_MAX_20`
- **メールアドレス（Email）**: 連絡先のメールアドレス
  - 仕様: `EMAIL_FORMAT`
- **営業メール優先送付先（IsPrioritySalesEmail）**: 営業メールを優先的に送付する先
  - 仕様: `BOOLEAN`
- **管理メール優先送付先（IsPriorityManagementEmail）**: 管理メールを優先的に送付する先
  - 仕様: `BOOLEAN`
- **備考（Note）**: その他連絡先に関する補足情報
  - 仕様: `KANJI_MAX_200`
- **更新日時（UpdatedAt）**: 情報の最終更新日時
  - 仕様: `DATE_FORMAT`

### 1.2 管理メール送付内容（ManagementEmailContent）
- **顧客識別ID（CustomerIdentificationId）**: 顧客を一意に特定するIBP上の識別子
  - 仕様: `GUID`
- **決算書授受（FinancialStatement）**: 決算書の授受状況
  - 仕様: `ALPHANUMERIC_MAX_5`
- **モニタリング（業況・伴走）（Monitoring）**: 現時点の業況
  - 仕様: `KANJI_MAX_20`
- **相談初動対応（Consultation）**: 相談時の初動対応内容
  - 仕様: `KANJI_MAX_20`
- **更新日時（UpdatedAt）**: 情報の最終更新日時
  - 仕様: `DATE_FORMAT`


## 2. 値オブジェクト（Value Objects）

## 3. 集約（Aggregates）

### 3.1 連絡先集約（ContactAddressAggregate）
- **ルートエンティティ**: 連絡先
- **含まれるエンティティ**:
- **不変条件**:
  - ID
  - 顧客識別ID
  - 連絡先顧客識別ID

### 3.2 管理メール送付内容集約（ManagementEmailContentAggregate）
- **ルートエンティティ**: 管理メール送付内容
- **含まれるエンティティ**:
- **不変条件**:
  - 顧客識別ID


## 4. ドメインイベント（Domain Events）

## 5. 列挙型（Enumerations）

## 6. 業務ルール（Business Rules）
