<template>
  <CaseTable
    v-model:page-index="pagination.pageIndex"
    v-model:sort="pagination.sort"
    :items="data?.items"
    :total="data?.total"
    :loading="loading"
    :page-size="pagination.pageSize"
    :search-result-message="searchResultMessage"
    @search="search"
    @edit="toDetail"
  >
    <template #criteria>
      <CaseSearchCriteria
        v-model:search-condition="searchCondition"
        :branch-master-options="branchMasterOptions"
        :case-options="caseOptions"
        :loading-staff-and-team="loadingStaffAndTeam"
        :loading="loading"
        :error-messages="errorMessages"
        :total-count="data?.total || 0"
        @update:search-condition="updateSearchCondition"
      />
    </template>
  </CaseTable>
</template>
<script setup lang="ts">
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useCaseValidation } from '@ibp/case-work-flow-optimization/src/composables/case/useCaseValidation'
import { useCaseNavigation } from '@ibp/case-work-flow-optimization/src/composables/case/useCaseNavigation'
import { useCaseSearch } from '@ibp/case-work-flow-optimization/src/composables/case/useCaseSearch'
import { useCaseOptions } from '@ibp/case-work-flow-optimization/src/composables/case/useCaseOptions'
import { isUndefinedOrNull } from '@hox/base/src/utils/shared/is'
import CaseTable from './CaseTable.vue'
import CaseSearchCriteria from './CaseSearchCriteria.vue'

// =====================================================================================================================
// 画面の定義
// =====================================================================================================================

useHead({
  title: '案件一覧',
})
definePageMeta({})

// オプションデータ取得用コンポーザブル
const {
  branchMasterOptions,
  options: caseOptions,
  loadingStaffAndTeam,
  loadAllOptions,
} = useCaseOptions()

// バリデーション定義
const validationInstance = useCaseValidation(toRef(() => searchCondition))
const { errorMessages, validateAll } = validationInstance

// 検索関連機能を取得
const {
  searchCondition,
  pagination,
  data,
  searchResultMessage,
  loading: searchLoading,
  criteriaHistory,
  hasState,
  search,
  restoreCriteria,
} = useCaseSearch(async () => {
  const result = await validateAll()
  return result
}, branchMasterOptions)

const { error: errorToast } = useAppToasts()

/**
 * 検索条件更新用の関数
 */
function updateSearchCondition(newValue: any) {
  // 検索条件の各プロパティを更新
  Object.assign(searchCondition, newValue)
}

// 複数のフラグでローディング状態を管理する場合は useFlagCondition を利用します。
const { hasTrue: loading, addFlag } = useFlagCondition()

// 初期化時のローディング状態
const initializeLoading = ref(false)
addFlag(initializeLoading)

// 検索ローディング状態を追加
addFlag(searchLoading)

// =====================================================================================================================
// 画面初期化処理
// =====================================================================================================================

// 初期データの読み込み
onMounted(async () => {
  initializeLoading.value = true

  try {
    const hasCriteriaHistory = hasState && !isUndefinedOrNull(criteriaHistory.value)

    // マスタデータとオプション情報を取得
    await loadAllOptions(hasCriteriaHistory, searchCondition)

    if (hasCriteriaHistory) {
      // 検索条件を復元して検索実行
      await restoreCriteria(criteriaHistory.value)
      search({
        noStoreCriteria: true,
      })
    } else {
      // 初期化
      data.value = {
        items: [],
        total: 0,
      }
    }
  } catch (error) {
    errorToast('初期データの読み込みに失敗しました。')
  } finally {
    initializeLoading.value = false
  }
})

// ナビゲーション関連機能を取得
const { navigateToEdit, isControlPressed } = useCaseNavigation()

/**
 * 画面状態をリセットする
 */
function resetScreenState() {
  data.value = { items: [], total: 0 }
  initializeLoading.value = true
}

/**
 * 案件編集画面に遷移
 */
function toDetail(event: any) {
  // Ctrlキーが押されていない場合のみ画面状態をリセット（同一タブでの遷移時のみ）
  if (!isControlPressed.value) {
    resetScreenState()
  }
  navigateToEdit(event)
}
</script>
<style>
.case-highlight-row {
  background-color: #f6cfd2 !important;
}
</style>
