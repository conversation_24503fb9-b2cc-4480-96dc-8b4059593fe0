import { z } from 'zod'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import type { Ref } from 'vue'

/**
 * リース案件一覧の検索条件バリデーション
 */
export const useLeaseValidation = (searchCondition: Ref<any>) => {
  // バリデーションスキーマ定義
  const { errorMessages, validate, validateItem } = useValidation(
    z.object({
      // 支店番号
      branchNumbers: z.array(z.string()).nullable(),
      // CIF番号
      cifNumber: z.string().max(8, 'CIF番号は8桁以内で入力してください').nullish(),
      // 氏名
      customerName: z.string().nullish(),
      // 案件ステータス
      caseStatuses: z.array(z.string()).nullable(),
      // リース担当者ID
      leaseStaffIds: z.array(z.string()).nullable(),
      // 担当者ID
      staffIds: z.array(z.string()).nullable(),
      // 見積作成担当者ID
      quotationCreateStaffIds: z.array(z.string()).nullable(),
      // 見積審査担当者ID
      quotationScrutinizeStaffIds: z.array(z.string()).nullable(),
    }),
    searchCondition,
  )

  /**
   * 全てのバリデーションを実行
   * @returns バリデーション結果 (成功: true, 失敗: false)
   */
  const validateAll = async () => {
    const { success } = await validate()
    return success
  }

  /**
   * 項目ごとのバリデーション実行
   * @param key バリデーション対象のキー
   */
  const validateItemWithRelated = async (key: string) => {
    await validateItem(key)
  }

  return {
    errorMessages,
    validate,
    validateItem: validateItemWithRelated,
    validateAll,
  }
}
