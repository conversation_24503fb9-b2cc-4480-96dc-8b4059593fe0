# 案件検索ページ定義

## 基本情報

- ページ名 : 案件一覧
- UIパターン : 検索画面（search）

## 実現対象ハイレベルユースケース

- 案件を管理する
  - 案件情報の検索
  - 案件の一覧表示
  - 案件詳細の確認
  - 案件編集画面への遷移

## 共通処理（ミドルウェア）定義

- [ ] アクセスログ出力
- [ ] アクセス可否チェック
- [ ] 次画面パスチェック

## パラメータ定義

| パラメータ名 | 種別 | 必須 | 型 | 説明 |
|---|---|---|---|---|
| customerIdentificationId | query | | 文字列 | 顧客識別ID（指定時は顧客理解レイアウトで表示） |
| from | query | | 文字列 | 遷移元画面識別子 |
| tab | query | | 文字列 | 表示するタブ（case, issue-project, issue-project-task, issue-project-discussion） |

## UI定義

### タブ表示
- **案件一覧タブ（現在表示中）**
    - 説明 : 一般的な案件の検索・一覧表示
- 課題案件一覧タブ
    - 説明 : 課題案件の検索・一覧表示
- 課題案件タスク一覧タブ
    - 説明 : 課題案件のタスク一覧表示（顧客識別時のみ表示）
- 課題案件協議一覧タブ
    - 説明 : 課題案件の協議一覧表示（顧客識別時のみ表示）

### 検索条件
- 店番
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- CIF番号
    - 入力形式 : テキストボックス
    - バリデーション : NUMERIC_MAX_8
    - 説明 : 完全一致検索、8桁ゼロパディング
- 氏名（漢字 または カナ）
    - 入力形式 : テキストボックス
    - 説明 : 部分一致検索
- 業種
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- セグメント
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- カテゴリ
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示、機能フラグによる表示制御
- ステータス
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 案件担当者
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 顧客担当者
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 案件項目
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 期日From
    - 入力形式 : 日付選択
    - 説明 : "yyyy/MM/dd"形式
- 期日To
    - 入力形式 : 日付選択
    - 説明 : "yyyy/MM/dd"形式
- お気に入り
    - 入力形式 : チェックボックス
- 検索ボタン
    - 説明 : ※イベント処理参照

### 検索結果一覧
- 表示項目
    - 登録日（registeredAt）
        - 説明 : "yyyy/MM/dd"形式で表示
    - 店番（branchNumber）
        - 説明 : 店番マスタから店番名を表示
    - CIF番号（cifNumber）
    - 氏名（customerName）
    - カテゴリ（caseCategory）
        - 説明 : カテゴリマスタから表示名を表示
    - 案件項目（generalTransactionTypeName）
    - 案件名（caseName）
        - 説明 : アクセス制限対象の場合はマスキング表示
    - ステータス（caseStatus）
    - 期日（expiredAt）
        - 説明 : "yyyy/MM/dd"形式で表示、期限切れは行全体を赤色表示
    - 案件担当者（staffName）
    - 顧客担当者（customerStaffName）
    - 最終更新日（caseUpdatedAt）
        - 説明 : "yyyy/MM/dd"形式で表示
    - お気に入り（isFavorite）
        - 説明 : ブールドットインジケータで表示
- 機能
    - 行クリック編集
        - 説明 : 案件カテゴリに応じた編集画面に遷移
    - ページング
        - 説明 : 可変ページサイズ（デフォルト20件）
    - ソート機能
        - ソート可能項目 : 全項目

## イベント処理定義

### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 検索条件選択肢を取得し表示する | ライフサイクルフック mounted |
| 検索ボタン押下 | 入力された条件で案件を検索し結果を表示する | function search |
| 項目バリデーション | CIF番号の入力値検証を実行する | function validate |
| フォームバリデーション | 全体の入力値検証を実行する | function validateForm |
| 編集ボタン押下/行クリック | 案件カテゴリに応じた編集画面に遷移する | function edit |
| 検索条件クリア | 検索条件と結果をクリアする | function clear |
| 検索条件復元 | URLヒストリから検索条件を復元する | function criteriaRestored |

### 初期表示（mounted）
1. 担当者・チーム情報のオプション一覧を取得する。
2. 一般取引種別マスタが未取得の場合は取得する。
3. 検索条件の選択肢を設定する。

### 検索ボタン押下（search）
1. フォーム全体のバリデーションを実行する。
2. 検索条件が一つ以上入力されているかチェックする。
3. 初期検索フラグをチェックし、falseの場合は検索を実行しない。
4. CIF番号を8桁ゼロパディングする。
5. 案件検索APIを呼び出し、結果を取得する。
6. SQLタイムアウトエラーの場合は専用メッセージを表示する。
7. 検索結果を整形し、一覧に表示する。
8. 期限切れ案件は行を赤色でハイライト表示する。
9. アクセス制限対象の案件名はマスキング表示する。
10. カテゴリ・ステータス・店番を表示名に変換する。
11. 検索履歴をブラウザに保存する。

### 項目バリデーション（validate）
1. CIF番号の最大桁数チェックを実行する。
2. バリデーションエラーがあればエラーメッセージを表示する。

### フォームバリデーション（validateForm）
1. 全ての検索条件項目のバリデーションを実行する。
2. 検索条件が一つ以上入力されているかチェックする。
3. バリデーションエラーがあればエラーメッセージを表示して false を返す。

### 編集ボタン押下/行クリック（edit）
1. 案件のカテゴリに応じて適切な編集画面のURLを構築する。
2. ビジネスマッチング系案件は専用の編集画面に遷移する。
3. M&Aマッチング系案件は専用の編集画面に遷移する。
4. 人材マッチング案件は専用の編集画面に遷移する。
5. 資金支援案件は専用の編集画面に遷移する。
6. その他の案件は顧客案件編集画面に遷移する。
7. Ctrlキーが押されている場合は新しいタブで開く。
8. 通常クリックの場合は同じタブで遷移する。

### 検索条件クリア（clear）
1. 検索条件と過去の検索履歴をクリアする。
2. 検索結果一覧をクリアする。

### 検索条件復元（criteriaRestored）
1. URLクエリパラメータから検索条件を復元する。
2. タブが案件一覧以外の場合は初期検索条件を設定して検索を無効化する。
3. 日時条件（fromDate, toDate）をDateオブジェクトに変換する。
4. 復元完了を通知する。
