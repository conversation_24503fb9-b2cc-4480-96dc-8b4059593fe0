<#
.SYNOPSIS
    指定されたコピー元のディレクトリからファイルを検索し、コピー先のディレクトリにコピーします。
.PARAMETER SourceDirectory
    コピー元のディレクトリのパス。
.PARAMETER DestinationDirectory
    コピー先のディレクトリのパス。
.PARAMETER FileNamePattern
    検索するファイル名のパターン。
#>

param(
    [string]$SourceDirectory,
    [string]$DestinationDirectory,
    [string]$FileNamePattern
)

# ソースディレクトリが存在するか確認
if (-Not (Test-Path $SourceDirectory)) {
    Write-Error "Source directory does not exist: $SourceDirectory"
    exit 1
}

# デスティネーションディレクトリが存在しない場合は作成
if (-Not (Test-Path $DestinationDirectory)) {
    New-Item -ItemType Directory -Path $DestinationDirectory | Out-Null
}

# 指定されたファイルパターンに一致するファイルを検索しコピー
Get-ChildItem -Path $SourceDirectory -Recurse -Filter $FileNamePattern | ForEach-Object {
    $destinationPath = Convert-Path -Path $DestinationDirectory
    if ($_.FullName.StartsWith($destinationPath)) {
        return
    }
    $destinationName = Join-Path -Path $destinationPath -ChildPath $_.Name
    Copy-Item -Path $_.FullName -Destination $destinationName
    Write-Output "Copied: $($_.FullName) to $destinationName"
}
