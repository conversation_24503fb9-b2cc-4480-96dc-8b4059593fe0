import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

export const generalTransactionTypeSchema = z.object({
  id: z.string(),
  name: z.string(),
  isRegisterable: z.boolean(),
  order: z.number(),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type GeneralTransactionType = z.infer<
  typeof generalTransactionTypeSchema
>

// =====================================================================================================================
// APIクライアントの定義(idで取得)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetGeneralTransactionType(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GeneralTransactionType>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/generaltransactiontype/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(全件取得)
// =====================================================================================================================

/**
 * データを取得する
 * @param query [] 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetAllGeneralTransactionType() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GeneralTransactionType[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/generaltransactiontype/getAll',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(追加)
// =====================================================================================================================

// 作成用のデータのスキーマを定義します。
export const generalTransactionTypeSchemaForCreate =
  generalTransactionTypeSchema.omit({ id: true, version: true })
// 作成用の型を作成します。
export type GeneralTransactionTypeForCreate = z.infer<
  typeof generalTransactionTypeSchemaForCreate
>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostGeneralTransactionType(
  body: Ref<GeneralTransactionTypeForCreate>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GeneralTransactionType>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/generaltransactiontype',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(更新)
// =====================================================================================================================

// 保存用のデータのスキーマを定義します。
export const generalTransactionTypeSchemaForSave = generalTransactionTypeSchema
// 保存用の型を作成します。
export type GeneralTransactionTypeForSave = z.infer<
  typeof generalTransactionTypeSchemaForSave
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutGeneralTransactionType(
  body: Ref<GeneralTransactionTypeForSave>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GeneralTransactionType>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/generaltransactiontype',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(削除)
// =====================================================================================================================

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteGeneralTransactionType(
  body: Ref<Pick<GeneralTransactionType, 'id' | 'version'>>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/generaltransactiontype',
        ),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}
