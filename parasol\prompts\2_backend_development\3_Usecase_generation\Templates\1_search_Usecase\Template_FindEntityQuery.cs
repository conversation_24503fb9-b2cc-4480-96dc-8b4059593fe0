using MediatR;
using Nut.Results;
using Shared.Application;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.FindEntity;

[WithDefaultBehaviors]
public record FindEntityQuery(
    string? Name = null,
    DateTimeOffset? PeriodFrom = null,
    DateTimeOffset? PeriodTo = null,
    string? MemberName = null
// TODO: 検索条件となるプロパティを追加します。
) : PageQuery, IRequest<Result<PaginatedResult<FindEntityResult>>>;
