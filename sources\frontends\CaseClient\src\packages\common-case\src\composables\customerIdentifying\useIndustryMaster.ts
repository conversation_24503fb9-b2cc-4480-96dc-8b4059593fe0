import { startOfToday } from 'date-fns'
import { computed } from 'vue'
import { useOneDayPersistState } from '@ibp/base/src/composables/shared/useOneDayPersistState'
import {
  useAllGetIndustryMaster as useGetIndustryMasterAllApi,
  type IndustryMaster,
} from '@/packages/common-case/src/apiclient/customerIdentifying/industryMaster'

type IndustryMasterOption = {
  value: string
  title: string
}

/**
 * 業種マスタ
 */
export const useIndustryMaster = () => {
  // 業種マスタの一覧
  const industries = useOneDayPersistState<IndustryMaster[]>('industries', () => [])

  const { data, executeWithResult } = useGetIndustryMasterAllApi()

  // 業種マスタの一覧から業種マスタ選択用の一覧を取得する
  const industryMasterOptions = computed((): IndustryMasterOption[] => {
    return industries.value.state?.map<IndustryMasterOption>((x) => ({
      value: x.code,
      title: x.name,
    })) || []
  })

  // 業種マスタの一覧をAPIで取得する
  const fetchIndustries = async (): Promise<void> => {
    // localStorageにセットした有効期限が今日以前だった場合、新たに業種マスタを取得
    if (industries.value.expiredAt <= startOfToday()) {
      await executeWithResult()
      industries.value.state = data.value || []
    }
  }

  return {
    fetchIndustries,
    industryMasterOptions,
  }
}
