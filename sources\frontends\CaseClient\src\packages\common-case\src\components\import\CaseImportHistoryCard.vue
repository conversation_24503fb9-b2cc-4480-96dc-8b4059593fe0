<template>
  <v-card id="case-csv-import-history-list" outlined class="pa-5 mb-3 mx-2">
    <v-row>
      <v-col>
        <v-card-title
          :class="
            $vuetify.display.xs
              ? 'border-l-3 border-gray-300 pl-3'
              : 'flex-basis-160 flex-shrink-0'
          "
        >
          案件データ取込履歴
        </v-card-title>
      </v-col>
      <v-col sm="4" md="4" lg="4" class="d-flex align-center justify-end">
        <app-sub-btn prepend-icon="mdi-update" @click="fetch()" >
          最新情報に更新
        </app-sub-btn>
      </v-col>
    </v-row>
    <v-row v-if="!loading">
      <v-col>
        <app-search-page-tmpl
          :list-item-key="'id'"
          :list-items="items"
          :list-items-total="itemsTotal"
          :can-pagination="false"
          :can-remove="false"
          :can-search="false"
          :can-add="false"
          :can-edit="false"
          :use-criteria-history="false"
          :list-headers="headers"
          :loading="loading"
          :page-size="20"
        >

          <!-- ファイル名 -->
          <template #list-item.fileName="{ item }">
            <a
              href="#"
              @click.prevent="onClickDownload(item.id, item.fileName)"
            >
              <v-icon color="primary">mdi-download</v-icon>
              <span>{{ item.fileName }}</span>
            </a>
          </template>

          <!-- 案件種別 -->
          <template #list-item.caseCategory="{ item }">
            <span v-text="getCaseCategoryText(item.caseCategory)" />
          </template>

          <!-- 詳細 -->
          <template #list-item.detail="{ item }">
            <div style="white-space: pre-wrap" v-text="item.detail" />
          </template>

          <!-- 処理開始日時 -->
          <template #list-item.startedAt="{ item }">
            {{ item.startedAt ? format(item.startedAt, 'yyyy/MM/dd HH:mm:ss') : '-' }}
          </template>

          <!-- 処理終了日時 -->
          <template #list-item.finishedAt="{ item }">
            {{ item.finishedAt ? format(item.finishedAt, 'yyyy/MM/dd HH:mm:ss') : '-' }}
          </template>

          <!-- 状態 -->
          <template #list-item.csvImportProcessStatus="{ item }">
            <span v-text="getCsvProcessStatusText(item.csvImportProcessStatus)" />
          </template>

          <!-- 削除ボタン -->
          <template #list-item.edit="{ item }">
            <v-icon
              v-if="item.csvImportProcessStatus === 'Succeeded'"
              title="削除する"
              color="red"
              class="ml-2"
              @click="remove(item)"
            >
              mdi-delete
            </v-icon>
            <div v-else />
          </template>
        </app-search-page-tmpl>
        <app-confirm-dialog ref="confirmDialog" />
      </v-col>
    </v-row>
    <v-row v-if="loading">
      <v-col>
        <v-skeleton-loader type="table" />
      </v-col>
    </v-row>
  </v-card>
</template>

<script setup lang="ts">
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type { AppConfirmDialogType } from '@hox/base/src/components/shared/molecules/AppConfirmDialog.vue'
import { saveAs } from 'file-saver'
import { useFindCaseImportHistoryItems, useDownloadCaseImportHistory } from '@ibp/common-case/src/apiclient/customerProposal/caseImportHistory'
import { useBatchDeleteCase } from '@ibp/common-case/src/apiclient/customerProposal/case'
import { format } from 'date-fns'
import { CSV_IMPORT_PROCESS_STATUSES, CASE_CATEGORIES } from '@ibp/common-case/src/constants/domain/case'
import { useGoTo } from 'vuetify'

// =====================================================================================================================
// プロパティの定義
// =====================================================================================================================

interface Props {
  csvUploaded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  csvUploaded: false,
})

// =====================================================================================================================
// イベントの定義
// =====================================================================================================================

interface Emits {
  historyFetched: []
}

const emit = defineEmits<Emits>()

// =====================================================================================================================
// スクロール関連
// =====================================================================================================================
const goTo = useGoTo()

// =====================================================================================================================
// リアクティブデータの定義
// =====================================================================================================================

const loading = ref<boolean>(false)
const items = ref<any[]>([])
const itemsTotal = ref<number>(0)
const downloadRequest = ref<{
  caseImportHistoryId: string
  fileName: string
}>({
  caseImportHistoryId: '',
  fileName: '',
})
const batchDeleteCaseRequest = ref<{
  caseImportHistoryId: string
}>({
  caseImportHistoryId: '',
})

const headers = ref([
  { title: 'ファイル名', key: 'fileName' },
  {
    title: '案件種別',
    key: 'caseCategory',
  },
  { title: '詳細', key: 'detail' },
  {
    title: '処理開始日時',
    key: 'startedAt',
  },
  {
    title: '処理終了日時',
    key: 'finishedAt',
  },
  {
    title: '状態',
    key: 'csvImportProcessStatus',
  },
  {
    title: '',
    key: 'edit',
  },
])

const options = {
  duration: 0,
}

// Toast の設定
const { error: errorToast } = useAppToasts()

// 確認ダイアログの参照
const confirmDialog = ref<AppConfirmDialogType>()

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

const { executeWithResult: findItems } = useFindCaseImportHistoryItems()
const { executeWithResult: downloadHistory } = useDownloadCaseImportHistory(downloadRequest)
const { executeWithResult: batchDeleteCase } = useBatchDeleteCase(batchDeleteCaseRequest)

// =====================================================================================================================
// 監視の定義
// =====================================================================================================================

// csvUploadedプロパティの変更を監視
watch(
  () => props.csvUploaded,
  (newValue) => {
    if (newValue) {
      toTop()
      fetch()
      emit('historyFetched')
    }
  },
)

// =====================================================================================================================
// 外部に公開するメソッドとプロパティ
// =====================================================================================================================

// コンポーネントのrefとして利用するために公開
defineExpose({
  fetch,
})

// =====================================================================================================================
// 初期化処理
// =====================================================================================================================

// コンポーネント作成時にデータを取得
await fetch()

// =====================================================================================================================
// メソッドの定義
// =====================================================================================================================

// CSV処理状態テキストを取得
function getCsvProcessStatusText(value: string): string {
  return CSV_IMPORT_PROCESS_STATUSES.find((x) => x.value === value)?.title || value
}

// 案件カテゴリテキストを取得
function getCaseCategoryText(value: string): string {
  return CASE_CATEGORIES.find((x) => x.value === value)?.title || value
}

// データを取得
async function fetch() {
  try {
    loading.value = true

    const result = await findItems()

    // 結果を設定する
    items.value = createListItems(result.data) // 結果の値をフォーマットします。
    itemsTotal.value = result.data.length
  } catch (error) {
    errorToast('取込履歴の取得に失敗しました')
  } finally {
    loading.value = false
  }
}

// ファイルダウンロード処理
async function onClickDownload(caseImportHistoryId: string, fileName: string) {
  try {
    downloadRequest.value = {
      caseImportHistoryId,
      fileName,
    }
    const result = await downloadHistory()

    if (!result) return

    // Blobオブジェクトの作成
    const blob = new Blob([result.data])

    // ファイルダウンロード
    saveAs(blob, fileName)
  } catch (error: any) {
    const data = error.response?.data || {}
    // Blobのダウンロードで、エラーが起きたとき、application/problem+jsonを返すので、その場合一律ファイルが見つからない旨のエラートーストを表示する
    if (data.type === 'application/problem+json') {
      errorToast('ファイルが見つかりませんでした。')
    } else {
      errorToast('ダウンロードに失敗しました')
    }
  }
}

// 検索結果を整形
function createListItems(items: any[]): any[] {
  return items.map(item => ({
    ...item,
    detail: item.detail || '-',
  }))
}

// ページトップにスクロール
function toTop() {
  goTo('#case-csv-import-history-list', {
    duration: options.duration,
  })
  const element = document.getElementById('case-csv-import-history-list')
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 取込データ削除処理
async function remove(item: any) {
  const confirmResult = await confirmDialog.value?.open({
    title: '削除確認',
    message: '取り込んだ案件を削除します。よろしいですか？',
  },
  )
  if (!confirmResult) return
  batchDeleteCaseRequest.value.caseImportHistoryId = item.id

  try {
    await batchDeleteCase()

    // データを再取得
    await fetch()
  } catch (error) {
    errorToast('削除に失敗しました')
  }
}
</script>

<style scoped>
.flex-basis-160 {
  flex-basis: 160px;
  flex-shrink: 0;
}

.border-l-3 {
  border-left: 3px solid rgba(0, 0, 0, 0.2);
}
</style>
