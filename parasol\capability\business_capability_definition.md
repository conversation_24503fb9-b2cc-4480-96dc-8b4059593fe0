# 会社概要システム - ビジネスケーパビリティ定義
①既存の事業性理解を強化し、価値提供を行っているお客様にさらなる価値を届ける  
②より多くのお客様に価値を届けるために会社を理解する

## 1.  最新の会社基本情報が収集できている能力
(ex : 代表者, 設立年月日, 会社電話番号...etc)

- **説明**:
  - 1.5 ~ 2年以内には必要な情報が更新される状態
  - (事業性理解対象先)リソースをかけてでもしっかりと情報を充実させる
  - (事業性理解対象外)優先順位を付けて, 主要な先だけでも情報を充実に
  - 入力システムが統一されている状態

### 1.1 会社基本情報の収集
- **主要機能（ハイレベルユースケース）**:
  - 会社を理解するために必要な情報を収集する
    - 社内情報から会社の基本情報を把握する
    - 社外情報を把握する
    - お客様から情報提供をしていただく
      - 事業計画書をいただく
      - お客様から決算書をいただく
      - お客様から試算表をいただく
    - お客さまと面談を実施する
      - お客様からいただいた情報を整理する
        - 事業概要を把握する
        - 株主 / 役員情報を把握する
        - グループ会社情報を把握する
        - 事業所情報を把握する

### 1.2 会社基本情報の参照
- **主要機能（ハイレベルユースケース）**:
  - 会社を理解するために必要な情報を参照する
    - 情報を参照する
      - 事業概要を参照する
      - 株主構成 / 役員構成を参照する
      - グループ会社情報を参照する
      - 事業所情報を参照する
      - 基本情報を参照する
      - 連絡先情報を参照する
      - 発信媒体情報を参照する
    - 社内の社員に聞く
      - 前の担当者に聞く
      - 同じ業界の担当者に聞く

## 2.  非対面情報を確認出来る能力 
- **説明**:
  - 連絡先に対して、その人の名前や決裁者かどうかなど、連絡先とほかの情報が紐づいて管理されている状態
  - 蓄積場所が一貫している状態
  - お客様のSNSやブログ等、お客様の発信情報の取得が仕組化されている状態

### 2.1 非対面情報を収集する
- **主要機能（ハイレベルユースケース）**:
  - 非対面情報を収集する
    - お客様と面談・応対を通じて情報を収集する（対面 / 非対面含む）
      - 連絡先情報を把握する
      - 会社・代表者/役員としての発信媒体情報を把握する
    - インターネットにて情報を収集する
      - 会社・代表者/役員としての発信媒体情報を把握する
  - 会社・代表者/役員としての発信媒体の発信内容の情報を取得する

# 事業性理解システム - ビジネスケーパビリティ定義
①顧客の抱える課題を理解し、課題解決に向けた提案・支援を行う  
②案件の進捗管理とタスク実行を通じて効率的な業務運営を実現する  
③チーム内での連携・協議により継続的なサービス品質向上を実現する

## 3. 課題解決に向けた包括的な案件管理・実行・協議ができる能力
(ex : 課題項目、顧客の課題感、現状、あるべき姿、課題の詳細、タスク管理、チーム協議...etc)

- **説明**:
  - 顧客の抱える課題を体系的に分析・整理し、案件として適切に管理する状態
  - 課題の発見から解決まで一貫して追跡可能な状態
  - 案件の進捗状況（提案前、提案中、応諾済み、コンサル中、完了等）が明確に把握できる状態
  - 課題案件に関連するタスクを体系的に管理し、担当者と期日を明確にした状態
  - タスクの進捗状況をリアルタイムで把握できる状態
  - チーム間でのタスク割り当てと協力体制が確立されている状態
  - 課題案件に関する社内協議を構造化して管理する状態
  - チームメンバー間での知識共有と意思決定が効率的に行われる状態
  - 過去の協議内容を検索・参照可能な状態

### 3.1 案件一覧の管理
- **主要機能（ハイレベルユースケース）**:
  - 案件情報を一覧表示する
    - 案件一覧を参照する
    - 課題案件一覧を参照する
    - 案件の検索・絞り込みを行う
    - 案件の状況を把握する
  - 案件の新規作成を行う
    - 新規案件を登録する
    - 課題情報を入力する

### 3.2 課題案件の編集・管理
- **主要機能（ハイレベルユースケース）**:
  - 課題案件の詳細情報を管理する
    - 課題情報（概要、現状、あるべき姿、課題）を登録・更新する
    - 案件情報（担当チーム、担当者、提案内容）を管理する
    - 期日管理（ステータス、次のアクション）を行う
    - 関連ファイルとリンク情報を管理する
  - 案件の進捗を管理する
    - 案件ステータスを自動算出・更新する
    - 提案から契約、完了まで一貫して管理する

### 3.3 課題案件タスクの管理
- **主要機能（ハイレベルユースケース）**:
  - タスクを作成・管理する
    - タスクの新規作成を行う
    - タスク概要と詳細を入力する
    - 担当者と期日を設定する
    - タスクステータス（着手前、進行中、完了済み等）を管理する
  - タスクの進捗を追跡する
    - タスク一覧を参照する
    - 期日による検索・絞り込みを行う
    - タスクの完了状況を把握する
  - 関連ファイルを管理する
    - タスクに関連するファイルをアップロード・管理する

### 3.4 課題案件協議の実施
- **主要機能（ハイレベルユースケース）**:
  - 協議スレッドを管理する
    - 新しい協議を開始する
    - 協議種別（社内、社外等）に応じた投稿を行う
    - 協議内容をテンプレートに従って構造化する
    - メンション機能により関係者に通知する
  - 協議に参加する
    - コメントを投稿・編集する
    - リアクション（いいね等）を追加する
    - ファイルを添付・共有する
  - 協議履歴を管理する
    - 過去の協議内容を検索・参照する
    - 期間や協議種別による絞り込みを行う
    - 重要な協議をピックアップする
