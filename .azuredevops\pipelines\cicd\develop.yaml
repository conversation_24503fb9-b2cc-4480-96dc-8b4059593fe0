trigger:
  branches:
    include:
    - main

variables:
- group: common
- group: develop

stages:
- stage: Build
  jobs:
    - template: CaseClient/build.yaml
      parameters:
        env: develop

    # - template: Api/build.yaml
    #   parameters:
    #     env: develop
#  jobs:
## TODO: WebUI/Api/Batchの定義を展開した場合は次のように参照を追加してください。
#    - template: {project name}/build.yaml
#      parameters:
#        env: develop

- stage: DownloadArtifacts
  jobs:
  - template: CaseClient/download-artifacts.yaml
    parameters:
      env: develop
      pipelineId: $(System.DefinitionId) # Buildのアーティファクトを取得するパイプラインのIDを指定して下さい。
      devopsProjectName: $(System.TeamProject) # BUildのアーティファクトを取得するAzure Pipelineがあるプロジェクト名/GUIDを指定してください。
  # - template: Api/download-artifacts.yaml
  #   parameters:
  #     env: develop
  #     pipelineId: $(System.DefinitionId) # Buildのアーティファクトを取得するパイプラインのIDを指定して下さい。
  #     devopsProjectName: $(System.TeamProject) # BUildのアーティファクトを取得するAzure Pipelineがあるプロジェクト名/GUIDを指定してください。
## TODO: WebUIの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/download-artifacts.yaml
#    parameters:
#      env: develop
#      pipelineId: $(System.DefinitionId) # Buildのアーティファクトを取得するパイプラインのIDを指定して下さい。
#      devopsProjectName: $(System.TeamProject) # BUildのアーティファクトを取得するAzure Pipelineがあるプロジェクト名/GUIDを指定してください。
## TODO: Apiの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/download-artifacts.yaml
#    parameters:
#      env: develop
#      pipelineId: $(System.DefinitionId) # Buildのアーティファクトを取得するパイプラインのIDを指定して下さい。
#      devopsProjectName: $(System.TeamProject) # BUildのアーティファクトを取得するAzure Pipelineがあるプロジェクト名/GUIDを指定してください。

- stage: DeployToSlot
  jobs:
  - template: CaseClient/deploy.yaml
    parameters:
      env: develop
  # - template: Api/deploy.yaml
  #   parameters:
  #     env: develop
#  jobs:
## TODO: WebUIの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/deploy.yaml
#    parameters:
#      env: develop
## TODO: Apiの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/deploy.yaml
#    parameters:
#      env: develop

- stage: SwapSlot
  dependsOn:
  - DeployToSlot
  jobs:
  - template: CaseClient/swap.yaml
    parameters:
      env: develop
  # - template: Api/swap.yaml
  #   parameters:
  #     env: develop
#  jobs:
## TODO: WebUI/Apiの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/swap.yaml
#    parameters:
#      env: develop

## TODO: Batchの定義を展開した場合はDeployステージで参照を追加してください。
# - stage: Deploy
#   dependsOn:
#   - SwapSlot
#   jobs:
#   - template: SampleBatch_tmpl/deploy.yaml
#     parameters:
#       env: develop