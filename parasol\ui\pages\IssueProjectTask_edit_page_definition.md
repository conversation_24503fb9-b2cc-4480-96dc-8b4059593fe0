# 課題案件タスク編集ページ定義

## 基本情報

- ページ名 : 課題案件タスク編集
- UIパターン : 編集画面（edit）

## 実現対象ハイレベルユースケース

- 課題案件に紐づくタスクの詳細管理
- タスクの進捗管理とステータス制御
- ファイル添付による詳細情報の共有

## 共通処理（ミドルウェア）定義

- [ ] アクセスログ出力
- [ ] アクセス可否チェック
- [ ] 次画面パスチェック

## パラメータ定義

| パラメータ名 | 種別 | 必須 | 型 | 説明 |
|---|---|---|---|---|
| id | query | | 文字列 | 課題案件タスクID（未指定の場合は新規追加モード） |
| customerIdentificationId | query | ○ | 文字列 | 顧客識別ID |
| issueProjectId | query | ○ | 文字列 | 課題案件ID |
| from | query | | 文字列 | 遷移元画面識別子（パンくずリスト構築用） |

## UI定義

### 参照情報表示
- 課題概要
    - 表示形式 : 読み取り専用ラベル付きテキスト
    - 説明 : 親課題案件の概要情報を表示
- 案件担当者
    - 表示形式 : 読み取り専用ラベル付きテキスト
    - 説明 : 親課題案件の担当者情報を表示

### 編集フォーム
#### 基本情報
- タスクステータス
    - 入力形式 : プルダウンリスト
    - 必須項目 : ○
    - 制限事項 : 新規追加時は「完了済み」を除外
- タスク担当者
    - 入力形式 : プルダウンリスト
    - 必須項目 : ○
    - 表示順序 : ログインユーザーを最上位、BPOチームメンバーは上位に配置
    - 説明 : ユーザー・チーム選択可能

#### 日付情報
- 依頼日
    - 入力形式 : 日付選択
    - 表示形式 : "yyyy/MM/dd"
    - 説明 : 新規時は当日が初期値
- 期日
    - 入力形式 : 日付選択
    - 必須項目 : ○
    - 表示形式 : "yyyy/MM/dd"
    - バリデーション : 依頼日より後の日付である必要がある

#### 詳細情報
- タスク概要
    - 入力形式 : テキストボックス
    - 必須項目 : ○
    - 文字数制限 : 30文字
- タスク詳細
    - 入力形式 : テキストエリア
    - 文字数制限 : 200文字
    - 機能 : 自動拡張

#### ファイル管理
- ファイルアップロード
    - 機能 : 複数ファイルアップロード対応
    - ファイルサイズ制限あり
- アップロード済みファイル一覧
    - 表示条件 : 編集モード時のみ
    - 機能 : ファイルダウンロード、削除

### アクションボタン
- 保存ボタン
    - 配置 : 画面右上
    - 機能 : 新規登録・更新処理
- 削除ボタン
    - 配置 : 保存ボタン右隣
    - 表示条件 : 編集モード時のみ
    - スタイル : 危険操作ボタン（赤色）

## イベント処理定義

### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | タスクデータと関連情報を取得し表示する | ライフサイクルフック created |
| 保存ボタン押下 | 入力データを登録・更新する | function save |
| 削除ボタン押下 | タスクを削除する | function remove |
| 入力項目変更 | 項目別バリデーションを実行する | function validate |

### 初期表示（created）
1. URLパラメータからタスクID、課題案件ID、顧客識別IDを取得する。
2. 課題案件情報を取得し、課題概要・案件担当者を表示する。
3. タスクステータス選択肢を取得する。
4. 担当者一覧（ユーザー・チーム）を取得する。
5. 編集モード時は既存タスクデータを取得・表示する。
6. 新規追加モード時は初期値（ステータス「着手前」、依頼日当日）を設定する。
7. データ変更監視を開始する。

### 保存ボタン押下（save）
1. 入力データのバリデーションを実行する。
2. アップロードファイルのサイズチェックを行う。
3. FormDataを作成し、ファイルアップロード情報を含める。
4. タスク登録/更新APIを呼び出す。
5. 新規追加時は登録後に編集モードに切り替える。
6. 成功時はトースト通知を表示し、データ変更監視を初期化する。

### 削除ボタン押下（remove）
1. 削除対象のタスク情報を取得する。
2. タスク削除APIを呼び出す。
3. 成功時はトースト通知を表示する。
4. 遷移元に応じた適切な画面に戻る：
   - 顧客詳細→課題案件タスク一覧から：タスク一覧に戻る
   - 課題案件編集から：課題案件編集に戻る

### 入力項目変更（validate）
1. 変更された項目のバリデーションを実行する。
2. エラーがある場合は該当項目にエラーメッセージを表示する。
3. 日付項目の場合は依頼日と期日の前後関係をチェックする。

### その他の処理
#### 担当者一覧作成（getUsers）
1. 全ユーザー一覧を取得する。
2. ログインユーザーを最上位に配置する。
3. BPOチーム情報を取得し、チーム選択肢として追加する。
4. BPOチームメンバーの場合はチームを上位に配置する。

#### 送信データ作成（createSendData）
1. 入力データをFormData形式に変換する。
2. 日付データをISO形式に変換する。
3. ファイルデータを追加する。
4. BPOチーム選択時は関連チームメンバー情報を追加する。
