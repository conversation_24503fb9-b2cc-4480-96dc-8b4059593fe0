using FluentAssertions;
using Moq;
using Nut.Results;
using Nut.Results.FluentAssertions;
using Shared.Results.Errors;
using Shared.Spec;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Infrastructure.Persistence;
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.GetEntity;
// using SampleService.Domain;
// using Shared.Domain;
// using Entities = SampleService.Domain.Entities;

// TODO: 長いジェネリック型の短縮形エイリアス（可読性向上のため）
using EntityRepository = Shared.Domain.IRepository<SampleService.Domain.Entities.Entity, string>;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.GetEntity;

/// <summary>
/// GetEntityHandlerの単体テストクラス
/// </summary>
/// <remarks>
/// 統合テスト（SQLiteインメモリDB使用）を中心に、
/// Get処理のテストを包括的に実装し、コードカバレッジ100%を目指します。
/// 正常系、異常系、データ不存在、エラーハンドリングを網羅的にテストします。
/// </remarks>
public class GetEntityHandlerTest : IAsyncLifetime
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TestCurrentUserService _currentUserService;
    private readonly TestCurrentDateTimeService _currentDateTimeService;
    // TODO: 外部サービスのモックが必要な場合は追加してください
    // private readonly Mock<IExternalService> _externalServiceMock;

    public GetEntityHandlerTest()
    {
        _currentUserService = new TestCurrentUserService();
        _currentDateTimeService = new TestCurrentDateTimeService();

        _dbContext = TestDbContextBuilder.Build(_currentUserService, _currentDateTimeService);

        // TODO: 外部サービスのモック初期化が必要な場合は追加してください
        // _externalServiceMock = new Mock<IExternalService>();
        // _externalServiceMock.Setup(x => x.SomeMethod(It.IsAny<string>()))
        //     .ReturnsAsync(Result.Ok(""));
    }

    public Task InitializeAsync()
        => TestData.CreateAsync(_dbContext);

    public Task DisposeAsync() => Task.CompletedTask;

    #region テスト対象とテストデータの準備

    /// <summary>
    /// テスト対象のハンドラーを作成します
    /// </summary>
    /// <returns>GetEntityHandler インスタンス</returns>
    private GetEntityHandler CreateHandler()
    {
        // TODO: プロジェクトのパターンに合わせてハンドラーの生成方法を調整してください
        var unitOfWork = new UnitOfWork(_dbContext);
        return new GetEntityHandler(unitOfWork);
        
        // 外部サービスが必要な場合の例:
        // return new GetEntityHandler(unitOfWork, _externalServiceMock.Object);
    }

    #endregion

    #region 異常系テスト（null チェック）

    [Fact]
    public void Ctor_引数にnullを渡すと例外が発生する()
    {
        var act = () => new GetEntityHandler(null!);
        act.Should().Throw<ArgumentNullException>();

        // TODO: 複数引数がある場合は全パターンをテストしてください
        // Assert.Throws<ArgumentNullException>("unitOfWork", 
        //     () => new GetEntityHandler(null!, _externalServiceMock.Object));
        // Assert.Throws<ArgumentNullException>("externalService", 
        //     () => new GetEntityHandler(new UnitOfWork(_dbContext), null!));
    }

    [Fact]
    public async Task Handle_Requestがnullの場合は例外が発生する()
    {
        var handler = CreateHandler();

        var act = () => handler.Handle(null!, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    #endregion

    #region 正常系テスト

    [Fact]
    public async Task Handle_引数で渡された値でデータが取得される()
    {
        // Arrange
        // TODO: テストデータに存在するIDを設定してください
        var query = new GetEntityQuery("entity-id-1");
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var value = result.Get();

        // TODO: TestDataクラスから期待するテストデータを取得して詳細検証を行ってください
        var expectedData = TestData.GetEntityTestData().FirstOrDefault(e => e.Id == "entity-id-1")!;

        // プロパティ単位での詳細検証
        value.Id.Should().Be(expectedData.Id);
        value.Name.Should().Be(expectedData.Name);
        // TODO: エンティティの全プロパティを検証してください
        // value.Description.Should().Be(expectedData.Description);
        // value.CreatedDateTime.Should().Be(expectedData.CreatedDateTime);
        // value.UpdatedDateTime.Should().Be(expectedData.UpdatedDateTime);
        // value.CreatorId.Should().Be(expectedData.CreatorId);
        // value.UpdaterId.Should().Be(expectedData.UpdaterId);
        
        // 関連データがある場合の検証例
        // if (expectedData.RelatedEntities?.Any() == true)
        // {
        //     value.RelatedEntities.Should().NotBeNull();
        //     value.RelatedEntities.Should().HaveCount(expectedData.RelatedEntities.Count);
        //     foreach (var relatedEntity in value.RelatedEntities)
        //     {
        //         var expectedRelated = expectedData.RelatedEntities.Single(x => x.Id == relatedEntity.Id);
        //         relatedEntity.Name.Should().Be(expectedRelated.Name);
        //     }
        // }
    }

    [Theory]
    [InlineData("entity-id-1")]
    [InlineData("entity-id-2")]
    public async Task Handle_複数のIDでそれぞれ正しいデータが取得される(string entityId)
    {
        // Arrange
        var query = new GetEntityQuery(entityId);
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var value = result.Get();

        // TODO: 各IDに対応する期待データの検証を実装してください
        var expectedData = TestData.GetEntityTestData().FirstOrDefault(e => e.Id == entityId)!;
        value.Id.Should().Be(expectedData.Id);
        value.Name.Should().Be(expectedData.Name);
    }

    [Fact]
    public async Task Handle_履歴を含む複雑なデータ構造が正しく取得される()
    {
        // Arrange
        // TODO: 履歴やネストしたデータを持つエンティティのIDを設定してください
        var query = new GetEntityQuery("entity-with-history-1");
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeOk();
        var value = result.Get();

        // TODO: 複雑なデータ構造の検証を実装してください
        // var expectedData = TestData.GetEntityTestData().FirstOrDefault(e => e.Id == "entity-with-history-1")!;
        // var expectedHistories = TestData.GetEntityHistoryTestData().Where(e => e.OriginalId == "entity-with-history-1");

        // value.Id.Should().Be(expectedData.Id);
        // value.Histories.Should().NotBeNull();
        // value.Histories.Should().HaveCount(expectedHistories.Count());
        // 
        // foreach (var history in value.Histories)
        // {
        //     var expectedHistory = expectedHistories.Single(x => x.Id == history.Id);
        //     history.Id.Should().Be(expectedHistory.Id);
        //     history.UpdatedDateTime.Should().Be(expectedHistory.UpdatedDateTime);
        //     history.UpdaterId.Should().Be(expectedHistory.UpdaterId);
        //     history.UpdaterName.Should().Be(expectedHistory.UpdaterName);
        //     history.OriginalId.Should().Be(expectedHistory.OriginalId);
        // }
    }

    #endregion

    #region データ不存在テスト

    [Fact]
    public async Task Handle_データが見つからない場合はDataNotFoundErrorが返る()
    {
        // Arrange
        // TODO: 存在しないIDを設定してください
        var query = new GetEntityQuery("NONEXISTENT_ID");
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("INVALID_ID_12345")]
    public async Task Handle_無効なIDまたは存在しないIDでDataNotFoundErrorが返る(string invalidId)
    {
        // Arrange
        var query = new GetEntityQuery(invalidId);
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    [Fact]
    public async Task Handle_削除済みデータのIDでDataNotFoundErrorが返る()
    {
        // Arrange
        // TODO: 論理削除されたデータのIDを設定してください（IsDeleted = trueのデータ）
        var query = new GetEntityQuery("deleted-entity-id");
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    #endregion

    #region エッジケース・特殊条件テスト

    [Fact]
    public async Task Handle_特殊文字を含むIDでの動作確認()
    {
        // Arrange
        // TODO: 特殊文字を含むIDがテストデータにある場合のテスト
        var query = new GetEntityQuery("entity@#$%-id");
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        // データが存在する場合は正常に取得、存在しない場合はDataNotFoundError
        if (result.IsOk)
        {
            var value = result.Get();
            value.Id.Should().Be("entity@#$%-id");
        }
        else
        {
            result.Should().BeError().And.BeOfType<DataNotFoundError>();
        }
    }

    [Fact]
    public async Task Handle_長いIDでの動作確認()
    {
        // Arrange
        var longId = new string('a', 1000); // 1000文字の長いID
        var query = new GetEntityQuery(longId);
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        // 長いIDでもエラーにならず、適切にDataNotFoundErrorが返されることを確認
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    [Fact]
    public async Task Handle_大文字小文字の違いによるID検索の動作確認()
    {
        // Arrange
        // TODO: 大文字小文字が異なるIDでのテスト（データベースの照合順序による）
        var query = new GetEntityQuery("ENTITY-ID-1"); // 小文字のentity-id-1が存在する場合
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        // TODO: データベースの照合順序設定に応じて期待する動作を設定してください
        // 大文字小文字を区別しない場合: result.Should().BeOk();
        // 大文字小文字を区別する場合: result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    #endregion

    #region エラーハンドリングテスト

    [Fact]
    public async Task Handle_リポジトリからのエラーはそのまま返る()
    {
        // Arrange
        var repositoryMock = new Mock<EntityRepository>();
        var query = new GetEntityQuery("entity-id-1");

        // リポジトリをモックで変更してErrorが返るようにする
        repositoryMock.Setup(x => x.GetAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Error<Entities.Entity>(new Error("Repository Error")));

        var unitOfWorkMock = new Mock<IUnitOfWork>();
        unitOfWorkMock.Setup(x => x.GetRepository<Entities.Entity, string>()).Returns(repositoryMock.Object);

        var handler = new GetEntityHandler(unitOfWorkMock.Object);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Repository Error");
    }

    [Fact]
    public async Task Handle_SingleAsyncを使用するケースでのリポジトリエラー()
    {
        // Arrange
        // TODO: SingleAsyncを使用するハンドラーの場合のテスト
        var repositoryMock = new Mock<EntityRepository>();
        var query = new GetEntityQuery("entity-id-1");

        // SingleAsyncをモックで変更してErrorが返るようにする
        repositoryMock.Setup(x => x.SingleAsync(It.IsAny<ISpecification<Entities.Entity>>()))
            .ReturnsAsync(Result.Error<Entities.Entity>(new Error("Specification Error")));

        var unitOfWorkMock = new Mock<IUnitOfWork>();
        unitOfWorkMock.Setup(x => x.GetRepository<Entities.Entity, string>()).Returns(repositoryMock.Object);

        var handler = new GetEntityHandler(unitOfWorkMock.Object);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Specification Error");
    }

    // TODO: 外部サービスを使用する場合のエラーハンドリングテスト
    // [Fact]
    // public async Task Handle_外部サービスからのエラーはそのまま返る()
    // {
    //     // Arrange
    //     var query = new GetEntityQuery("entity-id-1");
    //     
    //     _externalServiceMock.Setup(x => x.SomeMethod(It.IsAny<string>()))
    //         .ReturnsAsync(Result.Error("External Service Error"));
    //
    //     var handler = CreateHandler();
    //
    //     // Act
    //     var result = await handler.Handle(query, CancellationToken.None);
    //
    //     // Assert
    //     result.Should().BeError().And.WithMessage("External Service Error");
    // }

    #endregion

    #region Specificationテスト（オプション）

    // TODO: 必要に応じてSpecificationテストを追加してください
    // [Fact]
    // public void GetEntitySpecification_IDによる検索条件の仕様テスト()
    // {
    //     // Arrange
    //     var targetId = "entity-id-1";
    //     var specification = new GetEntitySpecification(targetId);
    //     var testEntity = TestData.Entity.CreateValid(targetId, "Test Entity");
    //
    //     // Act & Assert
    //     SpecificationTestHelper.TestSpecification(specification, testEntity, shouldMatch: true);
    //     
    //     var nonMatchingEntity = TestData.Entity.CreateValid("different-id", "Different Entity");
    //     SpecificationTestHelper.TestSpecification(specification, nonMatchingEntity, shouldMatch: false);
    // }

    // [Fact]
    // public void GetEntitySpecification_論理削除フィルターの仕様テスト()
    // {
    //     // Arrange
    //     var specification = new GetEntitySpecification("entity-id-1");
    //     
    //     // 削除されていないエンティティ
    //     var activeEntity = TestData.Entity.CreateValid("entity-id-1", "Active Entity");
    //     activeEntity.IsDeleted = false;
    //     
    //     // 削除済みエンティティ
    //     var deletedEntity = TestData.Entity.CreateValid("entity-id-1", "Deleted Entity");
    //     deletedEntity.IsDeleted = true;
    //
    //     // Act & Assert
    //     SpecificationTestHelper.TestSpecification(specification, activeEntity, shouldMatch: true);
    //     SpecificationTestHelper.TestSpecification(specification, deletedEntity, shouldMatch: false);
    // }

    #endregion
}
