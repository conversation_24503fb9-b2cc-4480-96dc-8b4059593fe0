# 登録UseCaseテスト作成

## 役割定義

- 日本人のベテランエンジニアとして、登録UseCaseハンドラーの単体テストの開発を行います。
- 既存のAddHandlerの実装を分析し、高品質で高カバレッジな単体テストを生成します。
- 語学が堪能であるためヒアリングの際には適した言語で対応しますが、成果物は日本語で出力します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 ： 
  - `parasol\architecture\backend_architecture.md`
- 共通処理使用方法リファレンス：
  - `parasol\architecture\usage_sample_backend_shared.md`
- プロジェクト設定定義 : 
  - `parasol\architecture\project_settings.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- 登録UseCaseテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\4_create_Usecase_test\Template_AddEntityHandlerTest.cs`
- 登録UseCaseバリデーターテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\4_create_Usecase_test\Template_AddEntityValidatorTest.cs`

### 参照コード

- 対象Handlerクラス : `[サービスディレクトリ]\UseCases\[Entity名]\Add[Entity名]\Add[Entity名]Handler.cs`
- 対象Validatorクラス : `[サービスディレクトリ]\UseCases\[Entity名]\Add[Entity名]\Add[Entity名]Validator.cs`
- 既存テストコード : `[サービステストディレクトリ]\UseCases\*.cs`

## 出力定義

- 出力先ディレクトリ：`[サービステストディレクトリ]\UseCases\[Entity名]\Add[Entity名]`
- Handlerファイル名フォーマット：`Add[Entity名]HandlerTest.cs`
- Validatorファイル名フォーマット：`Add[Entity名]ValidatorTest.cs`

## 制約事項

### 禁止事項

- テンプレートファイルを編集するのではなく、テンプレートの構造に従って新しいファイルを作成してください
- 当プロンプトファイルを編集するのではなく、指示内容に従って作業を実行してください

## 指示詳細

### 情報収集

1. 呼び出し元のプロンプトで特定された以下の情報を確認してください
    - 取り扱い対象の集約
    - 登録UseCaseで実装されている処理概要
    - 外部API呼び出しの有無
        - 呼び出し対象のAPIのエンドポイント

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください

2. 既存のAdd[Entity名]Handlerの実装を読み込み、以下を分析してください：
   - Handleメソッドの処理フロー
   - 入力データのバリデーション処理
   - 関連データの存在チェック処理
   - エンティティ生成とプロパティ設定処理
   - ID生成処理
   - トランザクション処理（UnitOfWork）
   - 外部API呼び出し処理
   - エラーハンドリング処理

3. 既存のAdd[Entity名]Validatorの実装を読み込み、以下を分析してください：
   - バリデーションルールの実装
   - 必須項目チェック
   - 項目長制限チェック
   - フォーマットチェック
   - ドメイン制約チェック
   - 関連データ存在チェック

4. 各テンプレートを元に、収集した情報にてテストを実装してください。
    - テンプレートファイルの基本構造を維持してEntity定義に合わせて修正
    - TODOコメントを参考に必要な部分を修正
    - 外部API呼び出しがある場合は、モックの設定を適切に行う

5. テンプレート修正時の注意事項：
    - namespace: SampleService を実際のサービス名に変更
    - Entity: 実際のエンティティクラス名に変更
    - AddEntityCommand、AddEntityHandler、AddEntityValidator: 実際のクラス名に変更
    - 外部サービスのモック: 実際の依存関係に合わせて調整
    - プロパティ名: 実際のエンティティプロパティに変更
    - enum型: 実際の列挙型に変更
    - SafeValue()メソッドを実際のコマンドプロパティに合わせて調整
    - CreateErrorValues()の各テストケースを実際のバリデーションルールに合わせて修正

6. 機能に応じたテスト調整：
    - 実際のAddUseCaseの複雑さに応じてテストメソッドを調整
    - 不要なセクションは削除し、必要なセクションを追加
    - ファイルアップロード機能がない場合は関連テストを削除
    - 外部サービス依存関係を実際のものに合わせて調整
    - 不要なフィールドのテストは削除し、必要なフィールドのテストを追加
    - 業務固有のバリデーションルールがある場合は複合バリデーションテストを追加

### 品質保証

以下の検証を実施し、品質に問題がないか確認してください。

1. **機能完全性**
    - **コードカバレッジ100%を目指す**
    - 全ての分岐パス（if-else、switch-case、try-catch）をカバー
    - Handleメソッドの全分岐パスがテストされているか
    - プライベートメソッドが間接的にテストされているか
    - エラーハンドリングが全パターンテストされているか
    - 外部依存関係の全パターンがテストされているか

2. **アーキテクチャ準拠性**
    - 各テストが独立して実行できるか
    - FluentAssertionsを使った適切なアサーション
    - テストデータの適切な設定
    - モックが正しく設定されているか
    - モックの呼び出し検証が適切に行われているか

3. **コード品質**
    - 日本語によるテストメソッド名が適切か
    - `[Fact]`/`[Theory]`属性の適切な使用
    - 既存テストコードとの一貫性
    - バリデーションエラーパターンを網羅
    - トランザクション処理をテスト
    - 外部API呼び出しの成功/失敗パターンをテスト
    - ドメイン制約違反パターンをテスト

    **カバレッジを100%に近づけるためのポイント：**
    - 全コンストラクタ引数のnullチェック
    - 正常系での詳細なプロパティ検証
    - 全外部サービスのエラーハンドリング
    - 境界値・エッジケースのテスト
    - ファイルアップロードなど特殊機能のテスト
    - 業務固有のロジックテスト
    - TestData.CreateAsync()を使用した統合テスト環境
    - MemberData + Theory パターンで大量のバリデーションケースを効率的にテスト
    - 各フィールドの個別テストで詳細な境界値チェック
    - SafeValueパターンで正常値ベースでの異常値テスト
    - 複合バリデーションで業務ルールのテスト
    - FluentValidation.TestHelperの全機能を活用
    - エラーメッセージの詳細検証

4. 上記で検証した内容に問題がある場合は、必要な修正を行ってください。
