using MediatR;
using Nut.Results;
using SampleService.Domain;
using SampleService.Domain.Entities;
using SampleService.Externals.Sample;
using Shared.Domain;
using Shared.Messaging;
using Shared.Results.Errors;
using Shared.Spec;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.UpdateEntity;

public class UpdateEntityHandler(
    IUnitOfWork unitOfWork,
    IIdGenerator<EntityMember, string> projectMemberIdGenerator,
    IMessageSender<UpdateEntityLastModifiedDateTime> entityUpdateMessageSender) : IRequestHandler<UpdateEntityCommand, Result<string>>
{

    public async Task<Result<string>> Handle(UpdateEntityCommand request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request);

        // 既存データを取得します。
        // TODO: 利用するリポジトリに置き換えてください。
        var repository = unitOfWork.GetRepository<Domain.Entities.Entity>();
        // TODO: キー項目が違う場合は条件を修正してください。
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.Entity, string>(request.Id)
                .Include(e => e.Members, m => m.ThenInclude(e => e.Member)));
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (!getResult) return getResult.PreserveErrorAs<string>();

        var data = getResult.Get();
        // TODO: 引数で渡されたデータをセットしてください。
        data.Name = request.Name;
        data.PeriodFrom = request.PeriodFrom;
        data.PeriodTo = request.PeriodTo;
        data.IsAssumption = request.IsAssumption;
        data.Amount = request.Amount;
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        data.Version = request.Version;
        var updateDetailResult = await UpdateMembersAsync(request.Members, data.Members);
        if (updateDetailResult.IsError) return updateDetailResult.PreserveErrorAs<string>();

        var result = await repository
            .UpdateAsync(data) // データを更新として設定します。
            .FlatMap(() => Result.Ok(data.Id)) // 結果として更新したデータのキーを返します。
            .ConfigureAwait(false);

        // TODO: 外部APIの利用箇所（必要に応じて実装）
        // 関連エンティティ最終更新日時の更新
        var updateEntityLastModifiedResult = await UpdateEntityLastModifiedDateTime(data.Id, request.UpdaterId, request.UpdaterName);
        if (updateEntityLastModifiedResult.IsError) return updateEntityLastModifiedResult.PassOnError<string>();

        await unitOfWork.SaveEntitiesAsync(); // データを保存します

        return result;
    }

    /// <summary>
    /// 外部APIの利用箇所（必要に応じて実装）
    ///     /// エンティティ最終更新日時を更新します。
    /// </summary>
    /// <param name="entityId">エンティティID</param>
    /// <param name="updaterId">更新者ID</param>
    /// <param name="updaterName">更新者名</param>
    /// <returns>更新結果</returns>
    private async Task<Result> UpdateEntityLastModifiedDateTime(string entityId, string updaterId, string updaterName)
    {
        var updateResult = await entityUpdateMessageSender.SendAsync(
            new UpdateEntityLastModifiedDateTime()
            {
                EntityId = entityId,
                UpdaterId = updaterId,
                UpdaterName = updaterName,
            }
        );

        return updateResult;
    }

    private async Task<Result> UpdateMembersAsync(
        List<UpdateEntityCommandMember> requestItems,
        List<Domain.Entities.EntityMember> currentItems)
    {
        var memberRepo = unitOfWork.GetRepository<Domain.Entities.Member>();
        var currentItemDict = currentItems.ToDictionary(i => i.Id);
        // Idが設定されていて、Updated が true のものは更新されている
        foreach (var requestDetail in requestItems.Where(r => r.Id is not null && r.Updated))
        {
            var item = currentItemDict!.GetValueOrDefault(requestDetail.Id);
            // 存在しない場合は、すでに更新されているとみなして同時更新エラーにする
            if (item is null) return Result.Error(new ChangeConflictException());

            var currentMember = await memberRepo.GetAsync(requestDetail.MemberId, true);
            if (currentMember.IsError) return currentMember.PreserveErrorAs();
            item.Member = currentMember.Get();
            item.UnitPricePerHour = requestDetail.UnitPricePerHour;
        }

        // リクエストで渡ってきていないIDは削除されたとみなす
        var deleteItems = currentItems.ExceptBy(requestItems.Select(r => r.Id), ci => ci.Id).ToList();
        if (deleteItems.Count != 0)
        {
            var pmRepo = unitOfWork.GetRepository<Domain.Entities.EntityMember>();
            foreach (var deleteItem in deleteItems)
            {
                // リポジトリを利用して削除とマーク
                await pmRepo.DeleteAsync(deleteItem);
                currentItems.Remove(deleteItem);
            }
        }

        // Idが設定されておらず、Updatedが false のものは追加されている
        foreach (var newItem in requestItems.Where(r => r.Id is null && !r.Updated))
        {
            var currentMember = await memberRepo.GetAsync(newItem.MemberId, true);
            if (currentMember.IsError) return currentMember.PreserveErrorAs();
            currentItems.Add(new Domain.Entities.EntityMember()
            {
                Id = await projectMemberIdGenerator.GetAsync(),
                Member = currentMember.Get(),
                UnitPricePerHour = newItem.UnitPricePerHour,
            });
        }

        return Result.Ok();
    }
}
