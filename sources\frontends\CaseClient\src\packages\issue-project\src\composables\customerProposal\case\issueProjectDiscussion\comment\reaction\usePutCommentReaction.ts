import type { issueProjectDiscussionCommentReactionType, FindIssueProjectDiscussionThreadResultItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { usePutIssueProjectDiscussionCommentReaction } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import type { updateCommentreactionSchemaForSave } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'

export const usePutCommentReaction = () => {
  const { error: errorToast, success: successToast } = useAppToasts()
  // データの変更を監視して、画面離脱時に変更があった場合に確認ダイアログを表示します。
  const { init, hasChanged, restart } = useWatchDataChanges()
  const watcher = { init, hasChanged, restart }

  const putCommentReaction = async (
    reaction: issueProjectDiscussionCommentReactionType,
    newReactionType: number,
    threadId: string,
    refThreadsData: FindIssueProjectDiscussionThreadResultItem[],
  ) => {
    const sendData = {
      id: reaction.id,
      reactionType: newReactionType,
      updatedDateTime: new Date(),
      version: reaction.version,
    }

    const {
      data: putCommentReactionData,
      executeWithResult: putCommentReaction,
    } = usePutIssueProjectDiscussionCommentReaction(
      ref<updateCommentreactionSchemaForSave>(sendData),
    )

    const result = await putCommentReaction().catch((e) => {
      // 失敗だった場合の処理を行います。
      if (!e.hasProblemDetails) {
        errorToast('リアクションの更新に失敗しました。')
      }
      if (e.data.type === '/validation-error') {
        errorToast(e.data.errors)
      } else if (e.data.type === '/conflict') {
        errorToast(
          'すでに別のユーザーがデータを登録・変更しているためリアクションを更新できませんでした。',
        )
      } else {
        errorToast('リアクションの更新に失敗しました。')
      }
      return false
    })

    // APIがエラーだった場合は処理を中断します。
    if (!result) return

    // スレッド検索
    const targetThreadIndex = refThreadsData.findIndex((x) => x.id === threadId)

    const targetThread = refThreadsData[targetThreadIndex]
    const targetCommentIndex = targetThread.comments.findIndex(
      (x) => x.id === putCommentReactionData.value.commentId,
    )

    // 更新リアクションをtargetDataに入れる
    const reactionIndex = refThreadsData[targetThreadIndex].comments[
      targetCommentIndex
    ].reactions.findIndex((x) => x.id === putCommentReactionData.value.id)

    refThreadsData[targetThreadIndex].comments[
      targetCommentIndex
    ].reactions.splice(reactionIndex, 1, putCommentReactionData.value)

    successToast('リアクションを更新しました。')
    watcher.init(refThreadsData)
  }

  return {
    putCommentReaction,
  }
}
