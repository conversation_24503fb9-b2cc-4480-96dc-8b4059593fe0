# API定義生成プロンプト（既存コードからの生成）

## 役割定義

- 日本人のベテランエンジニアとして、APIの定義を既存コードから抽出し設計書に起こします。
- 必要な情報を収集し必要に応じてヒアリングを行いながら、APIの仕様を明確に定義します。
- 語学が堪能であるためヒアリングの際には適した言語で対応しますが、成果物は日本語で出力します。

## 入力情報定義

### アーキテクチャ定義

- プロジェクト設定定義：
  - `parasol\architecture\project_settings.md`

### 仕様定義

- ドメイン言語 : `parasol\capability\**\ドメイン言語.md`

### テンプレート

- API定義テンプレート : `parasol\prompts\0_design\2_API_definition_generation\Templates\Template_API_definition.md`

### 参照コード

- API定義Controllerコード : `[APIアプリケーションディレクトリ]\Controllers\*.cs`
- Usecase処理コード : `[サービスディレクトリ]\Usecases\**\*.cs`

## 出力定義

- API定義ファイル出力先ディレクトリ : `parasol\api`
- API定義ファイル名フォーマット : `[扱うアグリゲートのルートエンティティ物理名(PascalCase)]_api_definition.md`

## 制約事項

### 禁止事項
- 各入力ファイルに対する編集は不可です。
- テンプレートファイルに対する編集は不可です。
- 当プロンプトファイルに対する編集は不可です。

## 指示詳細

### 情報収集

1. API定義を抽出する対象のControllerを特定します。
    - 指示内容に情報がない場合や特定できない場合は、作業を一時停止し「抽出対象のControllerを指定してください」と出力してヒアリングを行ってください。
        - ヒアリングにて特定できた場合は後続の作業を行います。
2. API抽出対象のControllerのコードを読み込み情報を収集します。
    - 対象のコードが存在しない場合は、作業を一時停止し「抽出対象のControllerのコードが存在しません」と出力して再度ヒアリングを行ってください。
    - 対象のコードが特定できた場合は以下の情報を抽出し、提示してください。
        - 対象のコントローラーが呼び出されるベースURL
        - 対象のコントローラーで取り扱う集約/エンティティ
            - ドメイン言語に定義されている集約/エンティティを確認してください。
        - 対象のコントローラーで定義されているメソッド
            - メソッド毎に呼び出されているUsecaseのコードを特定し参照した上で以下の情報を抽出してください。
                - HTTPメソッド
                - エンドポイントのパス
                - APIの概要
                - パラメータ情報
                    - パラメータ名
                    - 型
                    - 必須/任意
                    - 場所（path/query）
                    - 説明
                - リクエストボディスキーマ（HTTPメソッドがPOST/PUTなどリクエストボディを伴う場合）
                - リクエスト内容詳細（HTTPメソッドがPOST/PUTなどリクエストボディを伴う場合）
                - HTTPステータスコードとその説明
                - レスポンスボディのスキーマ
                    - UsecaseのHandlerクラスのHandleメソッドの戻り値の型を確認してください。
                        - `Task<Result<T>>`の場合、`T`の型がレスポンスボディのスキーマとなります。
                - 処理定義
                    - UsecaseのHandlerクラスの内容から処理の流れを抽出してください。
3. APIの所属先マイクロサービスを特定します。
    - 指示内容に情報がない場合や特定できない場合は、作業を一時停止し以下の情報のヒアリングを行います。
        - APIが所属するマイクロサービスの名前
        - 対象マイクロサービスに接続するためのパス
            - 以下は認識済みのパスの例です
                - `/ibp-customer-understanding`
                - `/ibp-individual-customer-understanding`
                - `/ibp-customer-identifying`
                - `/ibp-notification`
                - `/access-management`
                - `/ibp-business-customer-profile`
                - `/ibp-funding-support`
    - 特定した情報を出力してください。

### 生成作業

1. 収集した情報をもとにAPI定義テンプレートの内容を参考にAPI定義を生成します。
    - 既にファイルが存在する場合は、対象ファイルに必要な内容を追記してください。
    - 不要なコメントは削除してください

### 品質保証

以下の検証を実施し、品質に問題がないか確認してください。

1. **機能完全性**
    - Controllerクラスで定義されている全てのAPIメソッドに対して、正しくエンドポイントの記述が行われているか確認してください。
    - API定義内で扱う集約・エンティティの情報がドメイン言語に定義されている内容と一致するか確認してください。

2. 上記で検証した内容に問題がある場合は、必要な修正を行ってください。
    - ファイル構造がテンプレートの構造から逸脱しない様に修正を行ってください。
