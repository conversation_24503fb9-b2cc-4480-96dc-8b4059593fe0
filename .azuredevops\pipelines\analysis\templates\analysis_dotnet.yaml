parameters:
  sourcesDirectory: ''
  solutionFullPath: ''
  metricsSolutionFullPath: ''
  scriptDirectory: ''
  analysisResultDirectory: ''
  artifactName: ''
  reportTypes: 'Cobertura'

steps:
- checkout: self
  displayName: 'checkout'
  fetchDepth: -1
  clean: true

- task: Cache@2
  displayName: cache .net sdk
  condition: succeeded()
  inputs:
    key: 'version1 | dotnet | "$(Agent.OS)" | $(System.DefaultWorkingDirectory)/global.json'
    path: $(Agent.ToolsDirectory)/dotnet

- task: UseDotNet@2
  displayName: 'install .net sdk'
  inputs:
    packageType: 'sdk'
    version: '8.x'
    includePreviewVersions: false
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: DotNetCoreCLI@2
  displayName: 'install dotnet tools'
  inputs:
    command: custom
    custom: tool
    arguments: 'restore'
    workingDirectory: '$(System.DefaultWorkingDirectory)'

# https://learn.microsoft.com/en-us/azure/devops/pipelines/packages/nuget-restore?view=azure-devops&tabs=yaml#restore-nuget-packages-from-a-feed-in-the-same-organization
- task: NuGetAuthenticate@1
  displayName: 'NuGet Authenticate'
  inputs:
    forceReinstallCredentialProvider: true

- script: |
    echo ##vso[task.setVariable variable=NUGET_PACKAGES]$(Pipeline.Workspace)/.nuget/packages
  displayName: 'set environment variable for nuget packages'
  
- task: Cache@2
  displayName: cache nuget packages
  condition: succeeded()
  inputs:
    key: 'version1 | nuget | "$(Agent.OS)" | ${{ parameters.sourcesDirectory }}/**/packages.lock.json,!**/bin/**,!**/obj/**' 
    path: $(NUGET_PACKAGES)

- task: DotNetCoreCLI@2
  displayName: 'restore dotnet dependencies'
  inputs:
    command: custom
    custom: restore
    projects: '${{ parameters.solutionFullPath }}'

- task: DotNetCoreCLI@2
  displayName: 'lint dotnet format'
  inputs:
    command: custom
    custom: format
    arguments: '${{ parameters.solutionFullPath }} --verify-no-changes -v d --no-restore'
    workingDirectory: '$(System.DefaultWorkingDirectory)'

- task: DotNetCoreCLI@2
  displayName: 'build dotnet solution'
  inputs:
    command: 'build'
    projects: '${{ parameters.solutionFullPath }}'
    arguments: '--no-restore'

- task: PowerShell@2
  displayName: 'execute dotnet test'
  inputs:
    targetType: 'filePath'
    filePath: '${{ parameters.sourcesDirectory }}/test.ps1'
    workingDirectory: '${{ parameters.sourcesDirectory }}'
    pwsh: true
    arguments: '-reportTypes ${{ parameters.reportTypes }}'

- task: PublishTestResults@2
  displayName: 'publish dotnet test results'
  inputs:
    testResultsFormat: 'VSTest'
    testResultsFiles: '${{ parameters.analysisResultDirectory }}/test/*Tests.trx'
    publishRunAttachments: false

- task: PublishCodeCoverageResults@2
  displayName: 'publish code coverage report'
  inputs:
    summaryFileLocation: '$(System.DefaultWorkingDirectory)/**/coverages/report/Cobertura.xml'

- task: PowerShell@2
  displayName: 'create sln file for code metrics'
  inputs:
    pwsh: true
    filePath: '${{ parameters.scriptDirectory }}/createsln.ps1'
    arguments: '-s "${{ parameters.solutionFullPath }}" -o "${{ parameters.metricsSolutionFullPath }}" -g services'

- task: MSBuild@1
  displayName: 'get dotnet code metrics'
  inputs:
    solution: '${{ parameters.metricsSolutionFullPath }}'
    msbuildArguments: '/t:Metrics'

- task: PowerShell@2
  displayName: 'collect code metrics files'
  inputs:
    pwsh: true
    filePath: '${{ parameters.scriptDirectory }}/find_and_copy_files.ps1'
    arguments: '-SourceDirectory ${{ parameters.sourcesDirectory }} -DestinationDirectory ${{ parameters.analysisResultDirectory }}/metrics -FileNamePattern "*.Metrics.xml"'

# 集計元のXMLファイルをパブリッシュする
- publish: '${{ parameters.analysisResultDirectory }}'
  displayName: 'upload dotnet static analysis results to artifact'
  artifact: ${{ parameters.artifactName }}
  