<template>
  <div>
    <div class="mt-10 font-weight-bold">
      ファイルアップロード
    </div>
    <v-form v-model="isFormValid">
      <div
        class="drop-area px-1 mt-3"
        :class="{ enter: isEnter, noEnter: !isEnter }"
        @dragover.prevent
        @dragenter.stop="isEnter = true"
        @dragleave.stop="isEnter = false"
        @drop.prevent="drop"
        @change="change"
      >
        <v-file-input
          v-model="inputFiles"
          :disabled="!authenticated"
          show-size
          multiple
          density="comfortable"
          label="ドラッグ&ドロップか入力フォームを選択してファイルを登録してください"
        />
      </div>

      <v-col>
        <div
          v-for="file in targetData.uploadFiles"
          :key="file.name"
          class="file-chip-container"
        >
          <v-chip
            color="#00a63c"
            label
            size="small"
            class="me-2 mb-3 upload-file-chip"
          >
            <span class="ellipsis">
              {{ file.name }}
            </span>
            <v-icon
              color="#df6249"
              class="ml-2"
              @click="deleteUploadFile(file.name)"
            >
              mdi-close-circle-outline
            </v-icon>
          </v-chip>
        </div>
      </v-col>

      <div v-if="useSaveBtn && !disableList">
        ※保存するまでファイルはアップロード・削除されません
      </div>
      <v-btn
        v-if="!useSaveBtn && !disableList"
        class="mt-5"
        color="primary"
        variant="elevated"
        :disabled="!hasFilesToUpload"
        prepend-icon="mdi-upload"
        @click="uploadFile"
      >
        アップロード
      </v-btn>
    </v-form>

    <div v-if="(!disableList && useSaveBtn && !isAdd) || !useSaveBtn">
      <div class="mt-10 font-weight-bold">
        ファイル一覧
      </div>
      <v-skeleton-loader
        v-if="loading"
        type="list-item-three-line"
      />

      <v-data-table
        v-else
        :loading="loading"
        :headers="headers"
        :items="uploadedFiles"
        no-data-text="データがありません"
      >
        <template #[`item.updatedDateTime`]="{ item }">
          {{ formattedDateTime(item.updatedDateTime) }}
        </template>

        <template #[`item.download`]="{ item }">
          <v-btn
            size="small"
            color="success"
            variant="flat"
            prepend-icon="mdi-download"
            @click="downloadFile(item)"
          >
            ダウンロード
          </v-btn>
        </template>

        <template #[`item.delete`]="{ item }">
          <v-btn
            size="small"
            color="error"
            variant="flat"
            prepend-icon="mdi-trash-can-outline"
            @click="openDialog(item)"
          >
            削除
          </v-btn>
        </template>
      </v-data-table>
    </div>

    <div
      v-if="disableList"
      class="mb-4"
    >
      <div
        v-if="uploadedFiles.length > 0"
        class="font-weight-bold"
      >
        アップロードしたファイル
      </div>
      <div
        v-for="file in uploadedFiles"
        :key="file.fileName"
        class="file-chip-container"
      >
        <v-chip
          v-if="!isFileInChipFilesToDelete(file)"
          color="#00a63c"
          label
          size="small"
          class="me-2 mb-3 uploaded-file-chip"
        >
          <span class="ellipsis">
            {{ file.fileName }}
          </span>
          <v-icon
            color="#df6249"
            class="ml-2"
            @click="deleteFromChipSet(file)"
          >
            mdi-close-circle-outline
          </v-icon>
        </v-chip>
      </div>
    </div>

    <v-dialog
      v-model="dialog"
      max-width="500px"
      @click:outside="cancel"
      @keydown.esc="cancel"
    >
      <app-simple-dialog-tmpl
        v-if="useSaveBtn && !disableList"
        dialog-title="ファイルを削除しますか？"
        :action-button-text="'削除'"
        @cancel="cancel"
        @action="tempDelete"
      >
        <template #default>
          {{ deleteTargetFile?.fileName }}
        </template>
      </app-simple-dialog-tmpl>
      <app-simple-dialog-tmpl
        v-else
        dialog-title="ファイルを削除しますか？"
        :action-button-text="'削除'"
        @cancel="cancel"
        @action="deleteFile"
      >
        <template #default>
          {{ deleteTargetFile?.fileName }}
        </template>
      </app-simple-dialog-tmpl>
    </v-dialog>

    <confirm-overwrite-dialog ref="confirmOverwriteDialog" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { formatDateYMDHm } from '@ibp/issue-project/src/utils/shared/date'
import ConfirmOverwriteDialog from '@ibp/issue-project/src/components/shared/organisms/ConfirmOverwriteDialog.vue'
import type { UploadedFile, FileToDelete, FileUploadResult } from '@ibp/issue-project/src/models/UploadedFile'

const props = withDefaults(defineProps<{
  loading?: boolean
  disableList?: boolean
  useSaveBtn?: boolean
  isAdd?: boolean
  uploadedFiles?: UploadedFile[]
  tempFilesToDelete?: FileToDelete[]
  authenticated?: boolean
}>(), {
  loading: false,
  disableList: false,
  useSaveBtn: false,
  isAdd: false,
  uploadedFiles: () => [],
  tempFilesToDelete: () => [],
  authenticated: true
})

const emit = defineEmits<{
  'upload-file': [uploadFiles: File[], filesToRemove: FileToDelete[]]
  'download-file': [item: UploadedFile]
  'delete-file': [item: UploadedFile | null]
  'update-uploaded-files': [file: FileToDelete]
}>()

const { error: errorToast } = useAppToasts()

const targetData = ref({
  uploadFiles: [] as File[]
})
const isFormValid = ref(true)
const isEnter = ref(false)
const dialog = ref(false)
const deleteTargetFile = ref<UploadedFile | null>(null)
const deleteItemName = ref<string | null>(null)
const deleteItemVersion = ref<string | null>(null)
const chipFilesToDelete = ref<FileToDelete[]>([])
const sameFileNames = ref<string[]>([])
const inputFiles = ref<File[]>([])
const isOk = ref(false)
const confirmOverwriteDialog = ref()

const hasFilesToUpload = computed(() => {
  return targetData.value.uploadFiles.length > 0
})

const headers = computed(() => [
  { title: '名前', key: 'fileName', sortable: true },
  { title: '更新者', key: 'updatedBy', sortable: true },
  { title: '更新日時', key: 'updatedDateTime', sortable: true },
  { title: '', key: 'download', sortable: false },
  { title: '', key: 'delete', sortable: false }
])

const isFileInChipFilesToDelete = (file: UploadedFile): boolean => {
  return chipFilesToDelete.value.some(
    (chipFile) => chipFile.deleteItemName === file.fileName
  )
}

const deleteUploadFile = (fileName: string): void => {
  targetData.value.uploadFiles = targetData.value.uploadFiles.filter(
    (file: File) => file.name !== fileName
  )
  isFormValid.value = targetData.value.uploadFiles.length === 0
}

const drop = (event: DragEvent): void => {
  isEnter.value = false
  if (event.dataTransfer?.files) {
    addFiles(Array.from(event.dataTransfer.files))
  }
}

const change = (): void => {
  if (inputFiles.value.length > 0) {
    addFiles(inputFiles.value)
    inputFiles.value = []
  }
}

const addFiles = (files: File[]): void => {
  for (const file of files) {
    const selectedFile = targetData.value.uploadFiles?.find(
      (existingFile: File) => existingFile.name === file.name
    )

    if (selectedFile) {
      errorToast(`同名のファイルが存在します(${file.name})`)
      continue
    }

    targetData.value.uploadFiles.push(file)
  }
  isFormValid.value = targetData.value.uploadFiles.length === 0
}

const tempDelete = (): void => {
  if (!deleteTargetFile.value || !deleteItemName.value || !deleteItemVersion.value) {
    return
  }

  const file: FileToDelete = {
    deleteTargetFile: deleteTargetFile.value,
    deleteItemName: deleteItemName.value,
    deleteItemVersion: deleteItemVersion.value
  }

  emit('update-uploaded-files', file)
  dialog.value = false
}

const deleteFromChipSet = (deleteItem: UploadedFile): void => {
  chipFilesToDelete.value.push({
    deleteTargetFile: deleteItem,
    deleteItemName: deleteItem.fileName,
    deleteItemVersion: deleteItem.version?.toString() || ''
  })
}

const openDialog = (deleteItem: UploadedFile): void => {
  dialog.value = true
  deleteTargetFile.value = deleteItem
  deleteItemName.value = deleteItem.fileName
  deleteItemVersion.value = deleteItem.version?.toString() || null
}

const cancel = (): void => {
  dialog.value = false
  deleteTargetFile.value = null
  deleteItemName.value = null
  deleteItemVersion.value = null
}

const confirmOverwrite = async (targetFileNames: string[]): Promise<boolean> => {
  const currentFileNames = props.uploadedFiles.map((file: UploadedFile) => file.fileName)
  sameFileNames.value = targetFileNames.filter((targetFileName) =>
    currentFileNames.includes(targetFileName)
  )

  if (sameFileNames.value.length > 0) {
    const result = await confirmOverwriteDialog.value?.open(sameFileNames.value)
    isOk.value = result?.isOk || false
  } else {
    isOk.value = true
  }

  return isOk.value
}

const getUploadFilesWithConfirmationOverwrite = async (): Promise<FileUploadResult> => {
  const targetFileNames = targetData.value.uploadFiles.map(
    (file: File) => file.name
  )

  let isConfirm: boolean

  if (props.disableList) {
    const filteredUploadFiles = targetData.value.uploadFiles
      .filter((file: File) => {
        const isNotInChipFilesToDelete = !chipFilesToDelete.value.some(
          (chipFile) => chipFile.deleteItemName === file.name
        )
        return isNotInChipFilesToDelete
      })
      .map((file: File) => file.name)
    isConfirm = await confirmOverwrite(filteredUploadFiles)
  } else {
    isConfirm = await confirmOverwrite(targetFileNames)
  }

  if (!isConfirm) {
    targetData.value.uploadFiles = targetData.value.uploadFiles.filter(
      (file: File) => !sameFileNames.value.includes(file.name)
    )
  }

  const filesToRemove = [
    ...chipFilesToDelete.value,
    ...props.tempFilesToDelete
  ]

  const result: FileUploadResult = {
    uploadFiles: [...targetData.value.uploadFiles],
    filesToRemove
  }

  targetData.value.uploadFiles = []
  chipFilesToDelete.value = []
  isFormValid.value = true

  return result
}

const uploadFile = async (): Promise<void> => {
  const targetFileNames = targetData.value.uploadFiles.map(
    (file: File) => file.name
  )

  let isConfirm: boolean

  if (props.disableList) {
    const filteredUploadFiles = targetData.value.uploadFiles
      .filter((file: File) => {
        const isNotInChipFilesToDelete = !chipFilesToDelete.value.some(
          (chipFile) => chipFile.deleteItemName === file.name
        )
        return isNotInChipFilesToDelete
      })
      .map((file: File) => file.name)
    isConfirm = await confirmOverwrite(filteredUploadFiles)
  } else {
    isConfirm = await confirmOverwrite(targetFileNames)
  }

  if (!isConfirm) {
    targetData.value.uploadFiles = targetData.value.uploadFiles.filter(
      (file: File) => !sameFileNames.value.includes(file.name)
    )
  }

  const filesToRemove = [
    ...chipFilesToDelete.value,
    ...props.tempFilesToDelete
  ]

  emit('upload-file', targetData.value.uploadFiles, filesToRemove)

  targetData.value.uploadFiles = []
  chipFilesToDelete.value = []
  isFormValid.value = true
}

const downloadFile = (item: UploadedFile): void => {
  emit('download-file', item)
}

const deleteFile = (): Promise<{ success: boolean, error?: any }> => {
  return new Promise((resolve) => {
    emit('delete-file', deleteTargetFile.value)
    dialog.value = false
    resolve({ success: true })
  })
}

function formattedDateTime(item: Date | string | null): string {
  return formatDateYMDHm(item)
}

const clearInternalParameter = (): void => {
  targetData.value.uploadFiles = []
  chipFilesToDelete.value = []
  isFormValid.value = true
}

onMounted(() => {
  targetData.value = {
    uploadFiles: []
  }
})

defineExpose({
  getUploadFilesWithConfirmationOverwrite,
  clearInternalParameter
})
</script>

<style scoped>
.file-chip-container {
  display: inline;
}

.upload-file-chip,
.uploaded-file-chip {
  border: 1px solid #00a63c;
  font-size: 14px;
  min-height: 32px;
  height: auto;
  white-space: pre-wrap;
  overflow-wrap: anywhere;
}

.enter {
  border: 3px dotted rgb(var(--v-theme-error));
  height: 150px;
  padding-top: 30px;
  background-color: rgb(var(--v-theme-error-lighten-5));
}

.noEnter {
  border: 3px dashed rgb(var(--v-theme-surface-variant));
  height: 150px;
  padding-top: 30px;
}

.drop-area {
  border: 3px dashed rgb(var(--v-theme-surface-variant));
  height: 100px;
  padding-top: 15px;
}

.ellipsis {
  max-width: calc(100% - 1rem);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
