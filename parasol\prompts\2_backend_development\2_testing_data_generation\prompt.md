# テストデータ作成

## 役割定義

バックエンド開発者として、エンティティに対応する初期データをCSV形式で作成し、アプリケーションの埋め込みリソースとして登録します。

## 入力情報定義

### 仕様定義

- エンティティクラスファイル：`Domain\Entities\[エンティティ名].cs`
- エンティティコンフィグレーションファイル：`Infrastructure\Persistence\Configurations\[エンティティ名]Configuration.cs`
- ApplicationDbContextSetup.cs：`Infrastructure\Persistence\ApplicationDbContextSetup.cs`
- プロジェクトファイル：`[プロジェクト名].csproj`

### テンプレート

- 既存のDataInitializer実装例（ApplicationDbContextSetup.cs内のサンプルコード）

## 出力定義

- CSVファイル
  - 出力先ディレクトリ：`Infrastructure\Persistence\SeedData`
  - ファイル名フォーマット：`[エンティティ名].csv`（PascalCase）

## 制約事項

### 禁止事項

- 定義ファイルを編集する代わりに、CSVファイルを作成し必要な設定を追加してください
- テンプレートファイルを編集する代わりに、既存の実装例を参考に設定を追加してください
- 当プロンプトファイルを編集する代わりに、指示内容に従って作業を実行してください

## 指示詳細

### 情報収集

1. エンティティクラスファイルを読み込み、プロパティ構造を把握してください
1. エンティティコンフィグレーションファイルを読み込み、テーブル構造とカラム名を確認してください
1. ApplicationDbContextSetup.csを読み込み、現在のDataInitializer設定を確認してください
1. プロジェクトファイルを読み込み、現在の埋め込みリソース設定を確認してください

### 生成作業

1. 出力先ディレクトリの既存ファイルを確認し、対象エンティティのCSVが既に存在するか確認してください
1. CSVヘッダーに必要なカラム名をエンティティコンフィグレーションファイルから特定してください
   - カラム名はsnake_case形式で記述
   - すべてのプロパティに対応するカラムを含める
1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください
1. CSVファイルを作成し、10件の現実的なサンプルデータを設定してください
1. ApplicationDbContextSetup.csのGetEntityDataInitializers()メソッドにDefaultInitializerを追加してください
1. プロジェクトファイルに埋め込みリソース設定を追加してください

### 品質保証

1. 以下の内容を検証してください
   - 作成したCSVファイルの命名規則、ヘッダー行のカラム名、データ件数
   - ApplicationDbContextSetup.csのDefaultInitializer追加と必要なusing文
   - プロジェクトファイルのEmbeddedResource設定
   - 既存設定の保持と目的外の変更がないこと
   - 不備が見つかった場合は修正してください
