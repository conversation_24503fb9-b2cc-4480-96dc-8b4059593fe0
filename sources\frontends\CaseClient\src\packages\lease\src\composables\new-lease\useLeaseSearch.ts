import { reactive, ref, computed } from 'vue'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { isUndefinedOrNull } from '@hox/base/src/utils/shared/is'
import type { SearchOptions } from '@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue'
import type { SortItem } from '@hox/base/src/components/shared/types'
import { useCriteriaUrlState } from '@hox/base/src/composables/shared/useCriteriaUrlState'
import { useFindNewLeaseCase, type FindNewLeaseCaseCriteria, type FindCaseResult } from '@/packages/common-case/src/apiclient/customerProposal/case'
import type { Pagination } from '@hox/base/src/apiclient/shared/types'
import { getLeaseTableHeaders } from '~/packages/lease/src/constants/new-lease/tableHeaders'

// === マスタデータ・ドメイン定数 ===
import {
  CASE_STATUSES, // 案件ステータス一覧
} from '@/packages/common-case/src/constants/domain/case'

type Criteria = Omit<FindNewLeaseCaseCriteria, keyof Pagination>

/**
 * リース案件検索用のコンポーザブル
 * 検索条件の管理、検索処理、結果の加工を行う
 */
export const useLeaseSearch = (validateAll: () => Promise<boolean>) => {
  // 検索条件を設定
  const searchCondition = reactive<any>({
    branchNumbers: [],
    cifNumber: null,
    customerName: null,
    caseStatuses: [],
    leaseStaffIds: [],
    staffIds: [],
    quotationCreateStaffIds: [],
    quotationScrutinizeStaffIds: [],
    isFavorite: null,
  })

  // 検索条件の保持
  const criteria = ref<Criteria | null>(null)

  // ページング
  const pagination = reactive({
    pageIndex: 1,
    pageSize: 20,
    sort: [] as SortItem[],
  })

  // 検索結果
  const data = ref<FindCaseResult>({ items: [], total: 0 })
  const searchResultMessage = ref<string | undefined>(undefined)

  // 処理完了などをトースト表示するために利用
  const { error: errorToast } = useAppToasts()

  // ローディング状態
  const { hasTrue: loading, addFlag } = useFlagCondition()

  // 指定されているクエリの中から検索条件以外のものを抜き出して保持
  const additionalQueryParameter = (() => {
    const route = useRoute()
    const query = route.query as { [key: string]: string }
    const excludeKeys = Object.keys(searchCondition).concat([
      'q',
      'pageIndex',
      'pageSize',
      'sort',
    ])
    return Object.fromEntries(
      Object.entries(query).filter(([key]) => !excludeKeys.includes(key)),
    )
  })()

  // 検索クエリの定義
  const query = computed(() => {
    const sort = pagination.sort.reduce((prev, curr) => {
      prev.push(`${curr.key}${curr.order === 'desc' ? ' desc' : ''}`)
      return prev
    }, [] as string[])
    const paginationQuery = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      sort,
    }
    const processedCriteria = createRequestSearchCondition(criteria.value)
    return { ...(processedCriteria ?? {}), ...paginationQuery }
  })

  // APIクライアントの設定
  const {
    executeWithResult: findData,
    inProgress: findInProgress,
  } = useFindNewLeaseCase(query)

  // ローディング状態に追加
  addFlag(findInProgress)

  // 検索履歴を積む設定を取得
  const {
    state: criteriaHistory,
    push: pushCriteria,
    onChange,
    hasState,
  } = useCriteriaUrlState<FindNewLeaseCaseCriteria>()

  /**
   * 検索処理
   * @param options 検索オプション
   * @returns 検索成功時はtrue、失敗時はfalse
   */
  async function search(options: SearchOptions = {}) {
    // バリデーションチェック（ソートやページング変更時以外）
    if (!options.isSortChangeRequest && !options.isPaginationRequest) {
      const valid = await validateAll()
      if (!valid) {
        return false
      }
    }

    // ソートの変更かページングの変更の場合で、かつ検索条件が空(まだ一度も検索されていない)の場合は検索しない
    if (
      (options.isSortChangeRequest || options.isPaginationRequest) &&
      !criteria.value
    ) {
      return true
    }

    // ソート/ページング以外の場合は検索条件を更新する
    if (!options.isSortChangeRequest && !options.isPaginationRequest) {
      criteria.value = { ...searchCondition }
    }

    try {
      const result = await findData()
      if (result && result.data) {
        // APIレスポンスの構造を確認
        const resultData = result.data as any // 一時的にany型に変換して処理
        if (resultData.result) {
          // 検索結果あり
          data.value = {
            items: createListItems(resultData.result.items),
            total: resultData.result.total,
          }
        } else {
          // 検索結果なし（result: null）の場合、テーブルを空にして件数を0にする
          data.value = {
            items: [],
            total: 0,
          }
        }
        // エラーメッセージがある場合は表示
        searchResultMessage.value = resultData.findFailedMessage
      }
      if (!options.noStoreCriteria) {
        // 検索条件をURLの履歴に積む
        pushCriteria({ ...query.value, ...additionalQueryParameter })
      }
      return true
    } catch (error) {
      errorToast('検索処理でエラーが発生しました')
      return false
    }
  }

  /**
   * 検索条件をクリア
   */
  function clear() {
    criteria.value = null
    // 検索条件などをクリア
    searchCondition.branchNumbers = []
    searchCondition.cifNumber = null
    searchCondition.customerName = null
    searchCondition.caseStatuses = []
    searchCondition.leaseStaffIds = []
    searchCondition.staffIds = []
    searchCondition.quotationCreateStaffIds = []
    searchCondition.quotationScrutinizeStaffIds = []
    searchCondition.isFavorite = null

    data.value = { items: [], total: 0 }
  }

  /**
   * URLのヒストリをもとに検索条件を復元する処理
   */
  function criteriaRestored(e: { value: any, restoreCompleted: () => void }) {
    // 引数の value プロパティにクエリ文字列をもとにしたオブジェクトが渡される
    const { fromDateUTC, toDateUTC, ...searchConditionFromQuery } = e.value

    // 検索条件の復元
    Object.assign(searchCondition, searchConditionFromQuery)

    e.restoreCompleted()
  }

  /**
     * 検索条件の構築
     */
  function createRequestSearchCondition(condition: any) {
    const newCondition = { ...condition }

    // CIF
    if (newCondition?.cifNumber) {
      newCondition.cifNumber = paddingZeroToLeft(newCondition.cifNumber, 8)
    }

    return newCondition
  }

  /**
   * CIF番号をゼロ埋めする関数
   */
  function paddingZeroToLeft(value: string | number | null | undefined, length: number = 8): string {
    if (value === null || value === undefined || value === '') return ''
    const strValue = String(value)
    return strValue.padStart(length, '0')
  }

  /**
   * 検索結果を整形します
   * @param items 検索結果
   * @returns 整形された検索結果
   */
  function createListItems(items: any) {
    if (isUndefinedOrNull(items)) return items

    // テーブルヘッダーの定義を取得
    const headers = getLeaseTableHeaders()

    for (const item of items) {
      // 表示制限
      if (item.isAccessRestricted) {
        item.isMaskTargetItem = true
      }

      // ステータス
      item.caseStatus = CASE_STATUSES.find(x => x.value === item.caseStatus)?.title ?? '-'
      // 相談票
      item.isConsultationTarget = item.isConsultationTarget ? '●' : '-'

      // 見積作成者
      item.quotationCreateStaffName = item.quotationCreateStaffName?.trim() ? item.quotationCreateStaffName : '-'

      // 各フィールドに対してフォーマット処理を適用
      for (const header of headers) {
        if (header.format && item[header.key] !== undefined) {
          // 元のデータを保存（ソートなどで必要になる場合がある）
          item[`${header.key}Raw`] = item[header.key]
          // フォーマット関数を適用
          item[header.key] = header.format(item[header.key])
        }
      }
    }
    return items
  }

  /**
     * URLのクエリに指定されていた検索条件を復元します
     */
  function restoreCriteria(criteriaFromHistory: any) {
    // criteriaFromHistory が存在しない場合は処理を中断
    if (!criteriaFromHistory) {
      return
    }

    // 検索条件を復元
    if (criteriaFromHistory.branchNumbers) searchCondition.branchNumbers = criteriaFromHistory.branchNumbers
    if (criteriaFromHistory.cifNumber) searchCondition.cifNumber = criteriaFromHistory.cifNumber
    if (criteriaFromHistory.customerName) searchCondition.customerName = criteriaFromHistory.customerName
    if (criteriaFromHistory.caseStatuses) searchCondition.caseStatuses = criteriaFromHistory.caseStatuses
    if (criteriaFromHistory.leaseStaffIds) searchCondition.leaseStaffIds = criteriaFromHistory.leaseStaffIds
    if (criteriaFromHistory.staffIds) searchCondition.staffIds = criteriaFromHistory.staffIds
    if (criteriaFromHistory.quotationCreateStaffIds) searchCondition.quotationCreateStaffIds = criteriaFromHistory.quotationCreateStaffIds
    if (criteriaFromHistory.quotationScrutinizeStaffIds) searchCondition.quotationScrutinizeStaffIds = criteriaFromHistory.quotationScrutinizeStaffIds
    if (criteriaFromHistory.isFavorite) searchCondition.isFavorite = criteriaFromHistory.isFavorite

    // ソート条件を保持されているカタチからオブジェクトのカタチに変換します。
    function convertToSortFormat(querySort: string[] | string | undefined) {
      const sort = Array.isArray(querySort)
        ? querySort
        : !querySort
            ? []
            : [querySort]
      return (sort ?? []).map((s) => {
        const [key, order] = s.split(' ')
        return {
          key,
          order: order === 'desc' ? 'desc' : ('asc' as 'asc' | 'desc'),
        }
      })
    }
    pagination.pageIndex = parseFloat(criteriaFromHistory.pageIndex) || 1
    pagination.pageSize = parseFloat(criteriaFromHistory.pageSize) || 20
    pagination.sort = convertToSortFormat(criteriaFromHistory.sort)
  }

  // 検索履歴が変わった(戻るが押された)際の処理
  onChange((state: FindNewLeaseCaseCriteria | null) => {
    if (!state) {
      // 初回アクセスに戻った場合に undefined になる
      data.value = {
        items: [],
        total: 0,
      }
      return
    }
    // 検索条件を復元し検索を実行する
    restoreCriteria(state)
    search({ noStoreCriteria: true })
  })

  return {
    searchCondition,
    criteria,
    pagination,
    data,
    searchResultMessage,
    loading,
    criteriaHistory,
    hasState,
    search,
    clear,
    criteriaRestored,
    restoreCriteria,
  }
}
