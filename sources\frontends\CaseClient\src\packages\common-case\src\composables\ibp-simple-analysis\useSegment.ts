import { ref, computed } from 'vue'
// import { startOfToday } from 'date-fns'
import { useOneDayPersistState } from '@ibp/base/src/composables/shared/useOneDayPersistState'
import {
  useFindSegments as useGetSegmentsApi,
  type FindSegmentCriteria,
  type FindSegmentResultItem,
} from '@/packages/common-case/src/apiclient/ibp-simple-analysis/segment'

type SegmentOption = {
  value: string
  title: string
}

/**
 * セグメント情報を扱うコンポーザブル
 */
export const useSegment = () => {
  // セグメント情報の一覧
  const segments = useOneDayPersistState<FindSegmentResultItem[]>('segments', () => [])

  // ページング情報
  const query = ref<FindSegmentCriteria>({
    pageIndex: 1,
    pageSize: 100000,
    sort: [] as string[],
  })

  const { data, executeWithResult } = useGetSegmentsApi(query) // セグメント情報の一覧からセグメント選択用の一覧を取得する
  const segmentOptions = computed((): SegmentOption[] => {
    return segments.value.state?.map<SegmentOption>((x) => ({
      value: x.id,
      title: x.segmentName,
    })) || []
  })

  // セグメント情報の一覧をAPIで取得する
  const fetchSegments = async (): Promise<void> => {
    await executeWithResult()
    segments.value.state = data.value?.items || []
  }

  return {
    fetchSegments,
    segmentOptions,
  }
}
