import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import { type Ref } from 'vue'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

const toDoCommentReactionSchema = z.object({
  id: z.string(),
  commentId: z.string(),
  reactionType: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  updatedDateTime: z.string(),
})

const toDoCommentFileSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  commentId: z.string(),
  updatedDateTime: z.string(),
  updaterId: z.string().nullable(),
  updaterName: z.string().nullable(),
})

const toDoCommentSchema = z.object({
  id: z.string(),
  registeredDateTime: z.string(),
  registrant: z.string(),
  registrantId: z.string(),
  description: z.string(),
  threadId: z.string(),
  containCustomerReaction: z.boolean(),
  purpose: z.number(),
  person: z.string().nullable(),
  isPersonOfPower: z.boolean().nullable(),
  reactions: z.array(toDoCommentReactionSchema),
  files: z.array(toDoCommentFileSchema),
  mentionTargetUserIds: z.array(z.string()).nullable(),
  mentionTargetTeamIds: z.array(z.string()).nullable(),
})

const toDoThreadReactionSchema = z.object({
  id: z.string(),
  threadId: z.string(),
  reactionType: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  updatedDateTime: z.string(),
})

const toDoThreadFileSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  threadId: z.string(),
  updatedDateTime: z.string(),
  updaterId: z.string().nullable(),
  updaterName: z.string().nullable(),
})

const toDoThreadSchema = z.object({
  id: z.string(),
  registeredDateTime: z.string(),
  registrant: z.string(),
  registrantId: z.string(),
  title: z.string(),
  description: z.string(),
  toDoId: z.string(),
  comments: z.array(toDoCommentSchema),
  containCustomerReaction: z.boolean(),
  purpose: z.number(),
  person: z.string().nullable(),
  isPersonOfPower: z.boolean().nullable(),
  correspondenceDate: z.string().nullable(),
  reactions: z.array(toDoThreadReactionSchema),
  files: z.array(toDoThreadFileSchema),
  mentionTargetsHtml: z.string().nullable(),
  mentionTargetUserIds: z.array(z.string()).nullable(),
  mentionTargetTeamIds: z.array(z.string()).nullable(),
})

const toDoSchemaForGet = z.object({
  id: z.string(),
  title: z.string().nullable(),
  content: z.string().nullable(),
  staffId: z.string().nullable(),
  staffName: z.string().nullable(),
  expiredAt: z.string(),
  threads: z.array(toDoThreadSchema).nullable(),
  status: z.number().nullable(),
  order: z.number().nullable(),
  registeredDateTime: z.string(),
  updatedDateTime: z.string().nullable(),
  completedDateTime: z.string().nullable(),
  updaterId: z.string().nullable(),
  updaterName: z.string().nullable(),
  registrantId: z.string().nullable(),
  registrantName: z.string().nullable(),
  businessUnderstandingId: z.string().nullable(),
  version: z.string(),
})

type ToDoForGet = z.infer<typeof toDoSchemaForGet>

/**
 * ToDoデータをIDで取得する
 * @param id ToDoID
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetToDo(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<ToDoForGet>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/todo/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}