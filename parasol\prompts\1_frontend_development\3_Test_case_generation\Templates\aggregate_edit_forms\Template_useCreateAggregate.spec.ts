/**
 * ===========================================
 * アグリゲート作成コンポーザブル テストテンプレート
 * ===========================================
 * 
 * このテンプレートは、アグリゲート作成用composableのユニットテストです。
 * 
 * 【テスト観点】
 * - API呼び出しの正常系・異常系テスト
 * - バリデーション処理のテスト
 * - エラーハンドリングのテスト
 * - 状態管理のテスト
 * - トースト表示のテスト
 * 
 * 【使用方法】
 * 1. TODOコメントに従って、エンティティ固有の情報に置き換える
 * 2. 型名とインポートパスを実際のエンティティに変更する
 * 3. 関数名と変数名をエンティティに応じて変更する
 * 4. テストデータを実際のエンティティ構造に合わせて調整する
 */

import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { ref } from 'vue'
import { patchOFetchOnMsw } from '@hox/base/test/testSupport/api'

// TODO: テスト対象composableをインポート - 実際のパスに変更
import { useCreateYourAggregate } from '@your-module/src/composables/yourAggregate/useCreateYourAggregate'

// TODO: 型定義をインポート - 実際のパスに変更
import type { YourAggregateType } from '@your-module/src/constants/domain/entities/yourAggregate'
import type { YourAggregateTypeCase } from '@your-module/src/constants/domain/entities/yourAggregateCase'

// テスト用モックサーバー
const server = setupServer()

function startServer() {
  server.listen({ onUnhandledRequest: 'error' })
  patchOFetchOnMsw()
}

// TODO: 作成API用のモック関数 - エンドポイントを実際のものに変更
function setupCreateYourAggregateAPIMock(shouldSucceed: boolean = true) {
  // TODO: 成功時のレスポンスデータを実際のエンティティ構造に変更
  const successResponse: YourAggregateType = {
    id: 'test-aggregate-id',
    caseId: 'test-case-id',
    customerIdentificationId: '00000000-1111-2222-3333-444444444444',
    conditionRegistrationStatus: 'Registered',
    version: 1,
    // TODO: エンティティ固有のフィールドを追加
    name: 'Test Aggregate',
    description: 'Test Description',
  }

  const errorResponse = {
    type: '/validation-error',
    title: 'Validation Error',
    status: 400,
    errors: {
      name: ['名前は必須です'],
    },
  }

  const serverHandler = vi.fn(() =>
    shouldSucceed
      ? HttpResponse.json(successResponse)
      : HttpResponse.json(errorResponse, { status: 400 })
  )

  // TODO: APIエンドポイントを実際のものに変更
  server.use(http.post('/your-module/v1.0/your-aggregates', serverHandler))

  return {
    mockHandler: serverHandler,
    successResponse,
    errorResponse,
  }
}

// TODO: ケース取得API用のモック関数 - エンドポイントを実際のものに変更
function setupGetYourAggregateCaseAPIMock() {
  // TODO: ケースデータを実際の構造に変更
  const caseData: YourAggregateTypeCase = {
    id: 'test-case-id',
    customerIdentificationId: '00000000-1111-2222-3333-444444444444',
    // TODO: ケース固有のフィールドを追加
    status: 'Active',
    createdAt: new Date().toISOString(),
  }

  const serverHandler = vi.fn(() => HttpResponse.json(caseData))

  // TODO: ケースAPIエンドポイントを実際のものに変更
  server.use(http.get('/your-module/v1.0/your-aggregate-cases/:caseId', serverHandler))

  return {
    mockHandler: serverHandler,
    caseData,
  }
}

// TODO: 顧客取得API用のモック関数
function setupGetCustomerAPIMock() {
  const customerData = {
    customerIdentificationId: '00000000-1111-2222-3333-444444444444',
    branchNumber: '001',
    cifNumber: '1234567',
    nameKanji: 'テスト顧客',
  }

  const serverHandler = vi.fn(() => HttpResponse.json(customerData))
  server.use(http.get('/ibp-base/v1.0/customers/:customerIdentificationId', serverHandler))

  return {
    mockHandler: serverHandler,
    customerData,
  }
}

// TODO: テストスイート名をエンティティに応じて変更
describe('useCreateYourAggregate', () => {
  beforeEach(() => {
    clearNuxtState()
    startServer()
  })

  afterEach(() => {
    vi.restoreAllMocks()
    server.resetHandlers()
    server.close()
  })

  describe('createYourAggregate', () => {
    test('正常系: アグリゲートの作成が成功する', async () => {
      // TODO: 必要なAPIモックを設定
      const { mockHandler: createMockHandler, successResponse } = setupCreateYourAggregateAPIMock(true)
      setupGetCustomerAPIMock()

      const caseId = ref('test-case-id')
      const customerIdentificationId = ref('00000000-1111-2222-3333-444444444444')

      // TODO: composable関数名を実際のものに変更
      const {
        yourAggregateModel,
        createYourAggregate,
        inProgress,
      } = useCreateYourAggregate({
        caseId,
        customerIdentificationId,
      })

      // TODO: テストデータを実際のエンティティ構造に設定
      yourAggregateModel.value = {
        name: 'Test Aggregate',
        description: 'Test Description',
        customerIdentificationId: customerIdentificationId.value,
        // TODO: 必要なフィールドを追加
      } as YourAggregateType

      // 作成処理を実行
      const result = await createYourAggregate()

      // アサーション
      expect(createMockHandler).toHaveBeenCalledTimes(1)
      expect(result.success).toBe(true)
      expect(result.data).toEqual(successResponse)
      expect(inProgress.value).toBe(false)
    })

    test('異常系: バリデーションエラーが発生する', async () => {
      const { mockHandler: createMockHandler } = setupCreateYourAggregateAPIMock(false)
      setupGetCustomerAPIMock()

      const caseId = ref('test-case-id')
      const customerIdentificationId = ref('00000000-1111-2222-3333-444444444444')

      const {
        yourAggregateModel,
        createYourAggregate,
        errorMessages,
      } = useCreateYourAggregate({
        caseId,
        customerIdentificationId,
      })

      // TODO: 不正なテストデータを設定
      yourAggregateModel.value = {
        name: '', // 空文字でバリデーションエラーを発生させる
        customerIdentificationId: customerIdentificationId.value,
      } as YourAggregateType

      // 作成処理を実行
      const result = await createYourAggregate()

      // アサーション
      expect(result.success).toBe(false)
      expect(result.data).toBeUndefined()
      expect(errorMessages.value).toBeDefined()
    })

    test('異常系: 排他制御エラーが発生する', async () => {
      // 競合エラーのモック
      const conflictErrorResponse = {
        type: '/conflict',
        title: 'Conflict',
        status: 409,
        detail: 'データが更新されています',
      }

      const serverHandler = vi.fn(() =>
        HttpResponse.json(conflictErrorResponse, { status: 409 })
      )
      server.use(http.post('/your-module/v1.0/your-aggregates', serverHandler))
      setupGetCustomerAPIMock()

      const caseId = ref('test-case-id')
      const customerIdentificationId = ref('00000000-1111-2222-3333-444444444444')

      const {
        yourAggregateModel,
        createYourAggregate,
      } = useCreateYourAggregate({
        caseId,
        customerIdentificationId,
      })

      // TODO: 有効なテストデータを設定
      yourAggregateModel.value = {
        name: 'Test Aggregate',
        description: 'Test Description',
        customerIdentificationId: customerIdentificationId.value,
      } as YourAggregateType

      // 作成処理を実行
      const result = await createYourAggregate()

      // アサーション
      expect(serverHandler).toHaveBeenCalledTimes(1)
      expect(result.success).toBe(false)
    })
  })

  describe('draftYourAggregate', () => {
    test('正常系: アグリゲートの一時保存が成功する', async () => {
      const { mockHandler: createMockHandler, successResponse } = setupCreateYourAggregateAPIMock(true)
      setupGetCustomerAPIMock()

      const caseId = ref('test-case-id')
      const customerIdentificationId = ref('00000000-1111-2222-3333-444444444444')

      const {
        yourAggregateModel,
        draftYourAggregate,
      } = useCreateYourAggregate({
        caseId,
        customerIdentificationId,
      })

      // TODO: テストデータを設定
      yourAggregateModel.value = {
        name: 'Draft Aggregate',
        description: 'Draft Description',
        customerIdentificationId: customerIdentificationId.value,
      } as YourAggregateType

      // 一時保存処理を実行
      const result = await draftYourAggregate()

      // アサーション
      expect(createMockHandler).toHaveBeenCalledTimes(1)
      expect(result.success).toBe(true)
      expect(result.data).toEqual(successResponse)
    })
  })

  describe('fetchYourAggregateCase', () => {
    test('正常系: ケース情報の取得が成功する', async () => {
      const { mockHandler: caseMockHandler, caseData } = setupGetYourAggregateCaseAPIMock()

      const caseId = ref('test-case-id')
      const customerIdentificationId = ref('00000000-1111-2222-3333-444444444444')

      const {
        fetchYourAggregateCase,
      } = useCreateYourAggregate({
        caseId,
        customerIdentificationId,
      })

      // ケース取得処理を実行
      const result = await fetchYourAggregateCase()

      // アサーション
      expect(caseMockHandler).toHaveBeenCalledTimes(1)
      expect(result).toEqual(caseData)
    })
  })

  describe('applyYourAggregateCase', () => {
    test('正常系: ケース情報の適用が成功する', async () => {
      const caseId = ref('test-case-id')
      const customerIdentificationId = ref('00000000-1111-2222-3333-444444444444')

      const {
        yourAggregateModel,
        applyYourAggregateCase,
      } = useCreateYourAggregate({
        caseId,
        customerIdentificationId,
      })

      // TODO: ケースデータを実際の構造に変更
      const caseData: YourAggregateTypeCase = {
        id: 'test-case-id',
        customerIdentificationId: '00000000-1111-2222-3333-444444444444',
        status: 'Active',
        // TODO: ケース固有のフィールドを追加
        name: 'Case Name',
        description: 'Case Description',
      }

      // ケース情報の適用
      applyYourAggregateCase(caseData)

      // アサーション
      expect(yourAggregateModel.value.customerIdentificationId).toBe(caseData.customerIdentificationId)
      expect(yourAggregateModel.value.conditionRegistrationStatus).toBe('Unregistered')
      // TODO: ケースから適用される他のフィールドの検証を追加
    })
  })

  describe('バリデーション機能', () => {
    test('必須項目のバリデーション', async () => {
      const caseId = ref('test-case-id')
      const customerIdentificationId = ref('00000000-1111-2222-3333-444444444444')

      const {
        yourAggregateModel,
        validateItem,
        errorMessages,
      } = useCreateYourAggregate({
        caseId,
        customerIdentificationId,
      })

      // TODO: 空のテストデータを設定
      yourAggregateModel.value = {
        name: '',
        customerIdentificationId: customerIdentificationId.value,
      } as YourAggregateType

      // 個別項目のバリデーション
      await validateItem('name')

      // アサーション
      expect(errorMessages.value.name).toBeDefined()
      expect(errorMessages.value.name).toContain('必須')
    })
  })
})

/*
===========================================
テンプレート使用時の変更点チェックリスト
===========================================

【必須変更事項】
□ TODO: composableのインポートパスを実際のものに変更
□ TODO: 型定義のインポートパスを実際のものに変更
□ TODO: APIエンドポイントを実際のものに変更
□ TODO: テストデータを実際のエンティティ構造に変更
□ TODO: 関数名と変数名をエンティティに応じて変更
□ TODO: テストスイート名を実際のcomposable名に変更

【オプション変更事項】
□ TODO: エンティティ固有のテストケースの追加
□ TODO: 特別なバリデーションルールのテスト追加
□ TODO: ビジネス固有のエラーケースのテスト追加
□ TODO: パフォーマンステストの追加
*/
