# api client prompt

- this is the prompt for creating nuxt 3 api client using a template file.

## input file

- src\packages\template\src\apiclient\employee_template.ts
- apiclient javascript file of Nuxt2 project

## prompt

- Please update `API CLIENT FILE NAME of Nuxt2` and `RELATIVE PATH` to actual name and path.

```md
Please generate a TypeScript API client file for use in a Nuxt 3 project using the Composition API.

## Requirements:

- The client should be implemented using Nuxt 3's Composition API conventions.
- Replicate the functionality and structure of an existing Nuxt 2 API client named `API CLIENT FILE NAME of Nuxt2`. Please ensure that all methods and interfaces are equivalent in purpose and behavior.
- Use the file `employee_template.ts` as a template or reference for code style and structure.
- Save the generated file to the following relative path: `RELATIVE PATH`.

## Notes:

- The output should include all necessary imports, types, and Composition API utilities.
- If any assumptions need to be made due to missing context from the original `API CLIENT FILE NAME of Nuxt2`, add TODO comments in the relevant sections.
```

