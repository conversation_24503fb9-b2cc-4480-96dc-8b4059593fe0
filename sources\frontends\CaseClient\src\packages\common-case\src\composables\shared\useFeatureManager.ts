import { useGetFeatureFlag as useGetFeatureFlagApi } from '@/packages/common-case/src/apiclient/featureManager/featureManager'
type FeatureFlag = {
  name: string
  flag: boolean
}

/**
 * 機能フラグ
 */
export const useFeatureManager = () => {
  // 機能フラグの一覧
  const featureFlags = useState<FeatureFlag[]>('featureFlags', () => [])
  const featureFlagNames = [
    'IBP-D/IndividualCustomerUnderstanding',
    'IBP-A/RecruitMatchingFeatureFlag',
    'IBP-ALL/IsCustomerPortalEnabled',
    'IBP-C/FundingSupport',
  ]
  const fetchFeatureFlag = async () => {
    for (const name of featureFlagNames) {
      const paramId = computed(() => toValue(name))
      const { data, executeWithResult } = useGetFeatureFlagApi(paramId)
      await executeWithResult()
      featureFlags.value.push({
        name,
        flag: data.value,
      })
    }
  }

  // コンピューテッドプロパティとして定義することで、featureFlagsの変更に反応するようにします
  const getIndividualCustomerUnderstandingFeatureFlag = computed(() =>
    featureFlags.value.find(
      (x) => x.name === 'IBP-D/IndividualCustomerUnderstanding',
    )?.flag,
  )

  const getRecruitMatchingFeatureFlag = computed(() =>
    featureFlags.value.find(
      (x) => x.name === 'IBP-A/RecruitMatchingFeatureFlag',
    )?.flag,
  )

  const getIsCustomerPortalEnabledFeatureFlag = computed(() =>
    featureFlags.value.find(
      (x) => x.name === 'IBP-ALL/IsCustomerPortalEnabled',
    )?.flag,
  )

  const getFundingSupportFeatureFlag = computed(() =>
    featureFlags.value.find(
      (x) => x.name === 'IBP-C/FundingSupport',
    )?.flag,
  )

  return {
    fetchFeatureFlag,
    getIndividualCustomerUnderstandingFeatureFlag,
    getRecruitMatchingFeatureFlag,
    getIsCustomerPortalEnabledFeatureFlag,
    getFundingSupportFeatureFlag,
  }
}
