import { reactive, ref, computed } from 'vue'
import { startOfDay } from 'date-fns'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { isUndefinedOrNull } from '@hox/base/src/utils/shared/is'
import type { SearchOptions } from '@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue'
import type { SortItem } from '@hox/base/src/components/shared/types'
import type { Pagination } from '@hox/base/src/apiclient/shared/types'
import { useFindLoanAndLeaseCase, type FindLoanAndLeaseCaseCriteria, type FindCaseResult } from '../../apiclient/customerProposal/case'
import { useCriteriaUrlState } from '@hox/base/src/composables/shared/useCriteriaUrlState'
import { DEFAULT_SELECTED_CASE_STATUSES } from '@/packages/common-case/src/constants/domain/case'
import { getLoanAndLeaseTableHeaders } from '@/packages/common-case/src/constants/loan-and-lease/tableHeaders'

type Criteria = Omit<FindLoanAndLeaseCaseCriteria, keyof Pagination>

/**
 * 融資リース案件検索用のコンポーザブル
 * 検索条件の管理、検索処理、結果の加工を行う
 */
export const useLoanAndLeaseSearch = (validateAll: () => Promise<boolean>) => {
  // 検索条件を設定
  const searchCondition = reactive<Criteria>({
    branchNumbers: [],
    cifNumber: '',
    customerName: '',
    staffIds: [],
    caseCategories: [],
    caseStatuses: [], // 初期値は外部から設定
    preConsultationStandardTarget: '',
    isFavorite: false,
    isOnSiteConfirmationNotFinished: false,
    industryCodes: [],
    loanRatings: [],
    transactionPolicies: [],
    subjectTypes: [],
    useOfFundsTypes: [],
    fromDate: null,
    toDate: null,
    customerStaffIds: [],
    segmentIds: [],
    fromAmount: null,
    toAmount: null,
    fromInterestRate: null,
    toInterestRate: null,
  })

  // 検索条件の保持
  const criteria = ref<Criteria | null>(null)

  // ページング
  const pagination = reactive({
    pageIndex: 1,
    pageSize: 20,
    sort: [] as SortItem[],
  })

  // 検索結果
  const data = ref<FindCaseResult>({ items: [], total: 0 })
  const searchResultMessage = ref<string | undefined>(undefined)

  // 処理完了などをトースト表示するために利用
  const { error: errorToast } = useAppToasts()
  // ローディング状態
  const { hasTrue: loading, addFlag } = useFlagCondition()

  // 指定されているクエリの中から検索条件以外のものを抜き出して保持
  const additionalQueryParameter = (() => {
    const route = useRoute()
    const query = route.query as { [key: string]: string }
    const excludeKeys = Object.keys(searchCondition).concat([
      'q',
      'pageIndex',
      'pageSize',
      'sort',
    ])
    return Object.fromEntries(
      Object.entries(query).filter(([key]) => !excludeKeys.includes(key)),
    )
  })()

  // 検索クエリの定義
  const query = computed(() => {
    const sort = pagination.sort.reduce((prev, curr) => {
      prev.push(`${curr.key}${curr.order === 'desc' ? ' desc' : ''}`)
      return prev
    }, [] as string[])
    const paginationQuery = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      sort,
    }
    const processedCriteria = createRequestSearchCondition(criteria.value)
    return { ...(processedCriteria ?? {}), ...paginationQuery }
  })

  // APIクライアントの設定
  const {
    executeWithResult: findData,
    inProgress: findInProgress,
  } = useFindLoanAndLeaseCase(query)

  // ローディング状態に追加
  addFlag(findInProgress)

  // 検索履歴を積む設定を取得
  const {
    state: criteriaHistory,
    push: pushCriteria,
    onChange,
    hasState,
  } = useCriteriaUrlState<FindLoanAndLeaseCaseCriteria>()

  /**
   * 検索処理
   * @param options 検索オプション
   * @returns 検索成功時はtrue、失敗時はfalse
   */
  async function search(options: SearchOptions = {}) {
    // バリデーションチェック（ソートやページング変更時以外）
    if (!options.isSortChangeRequest && !options.isPaginationRequest) {
      const valid = await validateAll()
      if (!valid) {
        return false
      }
    }

    // ソートの変更かページングの変更の場合で、かつ検索条件が空(まだ一度も検索されていない)の場合は検索しない
    if (
      (options.isSortChangeRequest || options.isPaginationRequest) &&
      !criteria.value
    ) {
      return true
    }

    // ソート/ページング以外の場合は検索条件を更新する
    if (!options.isSortChangeRequest && !options.isPaginationRequest) {
      criteria.value = { ...searchCondition }
    }

    try {
      const result = await findData()
      if (result && result.data) {
        // APIレスポンスの構造を確認
        const resultData = result.data as any // 一時的にany型に変換して処理
        if (resultData.result) {
          // 検索結果あり
          data.value = {
            items: createListItems(resultData.result.items),
            total: resultData.result.total,
          }
        } else {
          // 検索結果なし（result: null）の場合、テーブルを空にして件数を0にする
          data.value = {
            items: [],
            total: 0,
          }
        }
        // エラーメッセージがある場合は表示
        searchResultMessage.value = resultData.findFailedMessage
      }
      if (!options.noStoreCriteria) {
        // 検索条件をURLの履歴に積む
        pushCriteria({ ...query.value, ...additionalQueryParameter })
      }
      return true
    } catch (error) {
      errorToast('検索処理でエラーが発生しました')
      return false
    }
  }

  /**
   * クリア処理
   */
  function clear() {
    criteria.value = null
    // 検索条件などをクリア
    searchCondition.branchNumbers = []
    searchCondition.cifNumber = ''
    searchCondition.customerName = ''
    searchCondition.staffIds = []
    searchCondition.caseCategories = []
    searchCondition.caseStatuses = DEFAULT_SELECTED_CASE_STATUSES || []
    searchCondition.preConsultationStandardTarget = ''
    searchCondition.isFavorite = false
    searchCondition.isOnSiteConfirmationNotFinished = false
    searchCondition.industryCodes = []
    searchCondition.loanRatings = []
    searchCondition.transactionPolicies = []
    searchCondition.subjectTypes = []
    searchCondition.useOfFundsTypes = []
    searchCondition.fromDate = null
    searchCondition.toDate = null
    searchCondition.customerStaffIds = []
    searchCondition.segmentIds = []
    searchCondition.fromAmount = null
    searchCondition.toAmount = null
    searchCondition.fromInterestRate = null
    searchCondition.toInterestRate = null

    data.value = { items: [], total: 0 }
  }

  /**
   * URLのヒストリをもとに検索条件を復元する処理
   */
  function criteriaRestored(e: { value: any, restoreCompleted: () => void }) {
    // 引数の value プロパティにクエリ文字列をもとにしたオブジェクトが渡される
    const { fromDateUTC, toDateUTC, ...searchConditionFromQuery } = e.value

    // 時間条件の復元
    searchConditionFromQuery.fromDate = fromDateUTC ? new Date(fromDateUTC) : null
    searchConditionFromQuery.toDate = toDateUTC ? new Date(toDateUTC) : null

    // 検索条件の復元
    Object.assign(searchCondition, searchConditionFromQuery)

    e.restoreCompleted()
  }

  /**
   * 検索条件の構築
   */
  function createRequestSearchCondition(condition: any) {
    const newCondition = { ...condition }

    // CIF
    if (newCondition?.cifNumber) {
      newCondition.cifNumber = paddingZeroToLeft(newCondition.cifNumber, 8)
    }

    // 日付フィールドをUTC文字列に変換
    if (newCondition?.fromDate) {
      const fromDate = startOfDay(new Date(newCondition.fromDate))
      // 元の日付を保持しつつ、UTC文字列も追加
      newCondition.fromDateUTC = fromDate.toISOString()
    }

    if (newCondition?.toDate) {
      const toDate = startOfDay(new Date(newCondition.toDate))
      // 元の日付を保持しつつ、UTC文字列も追加
      newCondition.toDateUTC = toDate.toISOString()
    }

    return newCondition
  }

  /**
   * CIF番号をゼロ埋めする関数
   */
  function paddingZeroToLeft(value: string | number | null | undefined, length: number = 8): string {
    if (value === null || value === undefined || value === '') return ''
    const strValue = String(value)
    return strValue.padStart(length, '0')
  }

  /**
   * 検索結果の整形処理
   */
  function createListItems(items: any[]) {
    if (isUndefinedOrNull(items)) return items

    // テーブルヘッダーの定義を取得
    const headers = getLoanAndLeaseTableHeaders()

    const today = startOfDay(new Date())
    for (const item of items) {
      // 期日が過ぎている && ステータスが完了していない場合は行ごと赤色にする
      if (
        !isUndefinedOrNull(item.expiredAt) &&
        today > startOfDay(new Date(item.expiredAt)) &&
        DEFAULT_SELECTED_CASE_STATUSES?.includes(item.caseStatus)
      ) {
        item.class = 'loan-lease-highlight-row'
      }
      // 表示制限
      if (item.isAccessRestricted) {
        item.isMaskTargetItem = true
      }

      // 各フィールドに対してフォーマット処理を適用
      for (const header of headers) {
        if (header.format && item[header.key] !== undefined) {
          // 元のデータを保存（ソートなどで必要になる場合がある）
          item[`${header.key}Raw`] = item[header.key]
          // フォーマット関数を適用
          item[header.key] = header.format(item[header.key])
        }
      }
    }
    return items
  }

  /**
   * URLのクエリに指定されていた検索条件を復元します
   */
  function restoreCriteria(criteriaFromHistory: any) {
    // criteriaFromHistory が存在しない場合は処理を中断
    if (!criteriaFromHistory) {
      return
    }
    // 検索条件を復元
    if (criteriaFromHistory.branchNumbers) searchCondition.branchNumbers = criteriaFromHistory.branchNumbers
    if (criteriaFromHistory.cifNumber) searchCondition.cifNumber = criteriaFromHistory.cifNumber
    if (criteriaFromHistory.customerName) searchCondition.customerName = criteriaFromHistory.customerName
    if (criteriaFromHistory.staffIds) searchCondition.staffIds = criteriaFromHistory.staffIds
    if (criteriaFromHistory.caseCategories) searchCondition.caseCategories = criteriaFromHistory.caseCategories
    if (criteriaFromHistory.caseStatuses) searchCondition.caseStatuses = criteriaFromHistory.caseStatuses
    if (criteriaFromHistory.preConsultationStandardTarget) searchCondition.preConsultationStandardTarget = criteriaFromHistory.preConsultationStandardTarget
    if (criteriaFromHistory.isFavorite) searchCondition.isFavorite = criteriaFromHistory.isFavorite
    if (criteriaFromHistory.isOnSiteConfirmationNotFinished) searchCondition.isOnSiteConfirmationNotFinished = criteriaFromHistory.isOnSiteConfirmationNotFinished
    if (criteriaFromHistory.industryCodes) searchCondition.industryCodes = criteriaFromHistory.industryCodes
    if (criteriaFromHistory.loanRatings) searchCondition.loanRatings = criteriaFromHistory.loanRatings
    if (criteriaFromHistory.transactionPolicies) searchCondition.transactionPolicies = criteriaFromHistory.transactionPolicies
    if (criteriaFromHistory.subjectTypes) searchCondition.subjectTypes = criteriaFromHistory.subjectTypes
    if (criteriaFromHistory.useOfFundsTypes) searchCondition.useOfFundsTypes = criteriaFromHistory.useOfFundsTypes
    if (criteriaFromHistory.fromDateUTC) searchCondition.fromDate = new Date(criteriaFromHistory.fromDateUTC)
    if (criteriaFromHistory.toDateUTC) searchCondition.toDate = new Date(criteriaFromHistory.toDateUTC)
    if (criteriaFromHistory.customerStaffIds) searchCondition.customerStaffIds = criteriaFromHistory.customerStaffIds
    if (criteriaFromHistory.segmentIds) searchCondition.segmentIds = criteriaFromHistory.segmentIds
    if (criteriaFromHistory.fromAmount) searchCondition.fromAmount = Number(criteriaFromHistory.fromAmount)
    if (criteriaFromHistory.toAmount) searchCondition.toAmount = Number(criteriaFromHistory.toAmount)
    if (criteriaFromHistory.fromInterestRate) searchCondition.fromInterestRate = Number(criteriaFromHistory.fromInterestRate)
    if (criteriaFromHistory.toInterestRate) searchCondition.toInterestRate = Number(criteriaFromHistory.toInterestRate)

    // ソート条件を保持されているカタチからオブジェクトのカタチに変換します。
    function convertToSortFormat(querySort: string[] | string | undefined) {
      const sort = Array.isArray(querySort)
        ? querySort
        : !querySort
            ? []
            : [querySort]
      return (sort ?? []).map((s) => {
        const [key, order] = s.split(' ')
        return {
          key,
          order: order === 'desc' ? 'desc' : ('asc' as 'asc' | 'desc'),
        }
      })
    }
    pagination.pageIndex = parseFloat(criteriaFromHistory.pageIndex) || 1
    pagination.pageSize = parseFloat(criteriaFromHistory.pageSize) || 20
    pagination.sort = convertToSortFormat(criteriaFromHistory.sort)
  }

  // 検索履歴が変わった(戻るが押された)際の処理
  onChange((state: FindLoanAndLeaseCaseCriteria | null) => {
    if (!state) {
      // 初回アクセスに戻った場合に undefined になる
      data.value = {
        items: [],
        total: 0,
      }
      return
    }
    // 検索条件を復元し検索を実行する
    restoreCriteria(state)
    search({ noStoreCriteria: true })
  })

  return {
    searchCondition,
    criteria,
    pagination,
    data,
    searchResultMessage,
    loading,
    criteriaHistory,
    hasState,
    search,
    clear,
    criteriaRestored,
    restoreCriteria,
  }
}
