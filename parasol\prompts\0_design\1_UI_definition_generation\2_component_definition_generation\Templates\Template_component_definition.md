# ダイアログ定義テンプレート

## 基本情報
<!--
  コンポーネント名とUIパターンを指定します 
    - コンポーネント名 : 適切なコンポーネント名を記述してください
    - UIパターン : UIパターン定義に記述されているコンポーネントパターンを指定してください
-->

- コンポーネント名 : 部署検索
- UIパターン : 検索ダイアログ（search_dialog）

## 呼び出し元情報
<!--
  このコンポーネントを呼び出す画面・コンポーネントの情報を記載します 
-->

- 呼び出し元・目的 : 
    - 従業員集約登録フォームコンポーネント : 従業員の所属部署を選択する
    - 従業員集約編集フォームコンポーネント : 従業員の所属部署を変更する

## UI定義
<!--
UIパターン定義のUI構成に定義されている構成要素について、表示項目などの詳細を定義します。

各項目の定義内容は以下に従ってください。
- 入力フィールドの場合
    - 入力形式 : 入力フィールドの形式
    - 必須 : 対象の入力が必須かどうか。必須でない場合記述不要
    - バリデーション : 入力チェック仕様
    - 説明 : 補足で必要な情報を記述する。特に記述するものがない場合不要
- 表示フィールドの場合
    - 説明 : 補足で必要な情報を記述する。特に記述するものがない場合不要。
        - 例 : 編集フォーマット（"yyyy/MM/dd"形式、"#,###,##0"形式など）

入力形式は以下の種類とする
- テキストボックス
- ラジオボタン
- プルダウンリスト
- チェックボックス
- テキストエリア
- ファイル指定
- 検索ダイアログ

バリデーションは以下の種類とする
- ALPHANUMERIC : アルファベット・数値のみ入力可
- ALPHABET : アルファベットのみ入力可
- NUMERIC : 数値のみ入力可
- MIN_99 : 最小文字数（他と組み合わせる場合は"_MIN_5"などの表現とする。例 : ALPHANUMERIC_MIN_5）
- MAX_99 : 最大文字数（他と組み合わせる場合は"_MAX_20"などの表現とする。例 : ALPHANUMERIC_MIN_5_MAX_10）
- PATTERN : 正規表現でのチェック（正規表現は説明に記述する）
-->

### 検索条件

- 部署コード
    - 入力形式 : テキストボックス
    - バリデーション : ALPHANUMERIC_MAX_10
    - 説明 : 前方一致で絞り込みを行う
- 部署名
    - 入力形式 : テキストボックス
    - バリデーション : MAX_50
    - 説明 : 部分一致で絞り込みを行う
- 事業所
    - 入力形式 : プルダウンリスト
    - 説明 : 
        - 全事業所、東京、大阪、名古屋から選択
        - 初期値は「全事業所」

### 検索結果一覧

- 表示項目
    - 部署コード
    - 部署名
    - 事業所名
    - 部門長名
    - 在籍人数
- 選択方式
    - 単一選択
- ソート機能
    - デフォルト: 部署コード昇順
    - ソート可能項目: 部署コード、部署名、事業所名

<!--
例）資格明細編集ダイアログ
```
### 入力エリア

- 資格名
    - 入力形式 : プルダウンリスト
    - 必須
    - 説明 : 資格マスタから選択
- 取得日
    - 入力形式 : 日付選択
    - 必須
    - 説明 : "yyyy/MM/dd"形式で表示
- 有効期限
    - 入力形式 : 日付選択
    - 説明 : 
        - 有効期限がある資格の場合のみ入力
        - "yyyy/MM/dd"形式で表示
- 備考
    - 入力形式 : テキストエリア
    - バリデーション : MAX_200
    - 説明 : 資格に関する補足情報を入力
```
-->

## イベント処理定義
<!-- UIパターン定義の定義イベント概要に従って処理内容を定義します 
- 定義されているイベントの概要を表形式にて記述します。
    - コンポーネントを呼び出す場合は、コンポーネント単位で記述します。
- 一覧表の下にイベント処理の詳細を記述します。
-->

### 一覧

| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| ダイアログ表示 | 検索条件を初期化し、ダイアログを表示する | function open |
| 検索ボタン押下 | 入力された条件にてデータの検索を行う | function search |
| 選択ボタン押下 | 選択されたデータを呼び出し元に返却し、ダイアログを閉じる | function select |
| キャンセルボタン押下 | ダイアログを閉じる | function cancel |

### ダイアログ表示

1. 事業所リストを取得し、プルダウンリストに設定する。
2. 初期フォーカスを部署コードに設定する。

### 検索ボタン押下

1. 入力された条件にてデータの検索を行う。
2. 検索結果が0件の場合、メッセージを表示する。

### 選択ボタン押下

1. 選択されたデータを呼び出し元に返却し、ダイアログを閉じる。

### キャンセルボタン押下 : 
1. ダイアログを閉じる。

<!--
例）資格明細編集ダイアログ
```
### 一覧

| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| ダイアログ表示 | 対象データを設定し、ダイアログを表示する | function open |
| OKボタン押下 | 入力された情報を呼び出し元に返し、ダイアログを閉じる | function action |
| キャンセルボタン押下 | 入力された情報を破棄し、ダイアログを閉じる | function cancel |


### ダイアログ表示時

1. 資格マスタを取得し、プルダウンリストに設定する。
2. 編集モードの場合、渡された資格情報を各フィールドに設定する。

### OKボタン押下

1. 入力値のバリデーションを実行する。
2. 取得日が有効期限より後の場合、エラーメッセージを表示する。
3. 入力された情報を呼び出し元に返却し、ダイアログを閉じる。

### キャンセルボタン押下

1. 入力された情報を破棄し、ダイアログを閉じる。
```
-->

## データ連携仕様
<!-- 呼び出し元とのデータ連携について詳細を記載します -->

### 受領データ
<!--
  呼び出し元からダイアログに渡されるデータを記載します
  - 受領データのスキーマを記述します
  - 受領データがない場合は「なし」と記述します
-->

```typescript
{
  searchCondition: {
    departmentCode: string; // 部署コード
    departmentName: string; // 部署名
  }
}
```

<!--
例）資格明細編集ダイアログ
```typescript
{
  mode: string;
  qualificationInfo: {
    qualificationId: string;
    qualificationName: string;
    acquisitionDate: Date;
    expirationDate: Date;
    remarks: string;
  }
}
```
-->

### 戻り値仕様
<!--
  ダイアログから呼び出し元に返却するデータを記述します
  - 返却データのスキーマを記述します
-->

```typescript
{
  isOk: boolean;  // ダイアログ処理結果
  data: {
    id: string;   // 選択された部署のID
    name: string; // 選択された部署の名称
  }
}
```

<!--
例）資格明細編集ダイアログ
```typescript
{
  isOk: boolean;  // ダイアログ処理結果
  data: {
    qualificationId : String;   // 資格ID
    qualificationName : String; // 資格名
    acquisitionDate : Date;     // 取得日
    expirationDate : Date;      // 有効期限
    remarks : String;           // 備考
  }
}
```
-->

## その他仕様
<!-- ダイアログ特有の仕様や注意事項があれば記載 -->

- 特記事項 : 
    - ESCキーでキャンセル処理を実行
    - ダイアログ外クリックでは閉じない
    - 検索結果は最大100件まで表示
