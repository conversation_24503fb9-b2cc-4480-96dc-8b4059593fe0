<template>
  <v-dialog
    v-model="dataRef.dialog"
    max-width="1000px"
    @click:outside="cancel"
    @keydown.esc="cancel"
  >
    <AppSimpleDialogTmpl
      :dialog-title="dialogTitle"
      action-button-text="保存"
      :loading="dataRef.loading"
      @cancel="cancel"
      @action="save"
    >
      <!-- 協議種別 -->
      <AppAutocomplete
        v-model="discussionTypeRef"
        :menu-props="{ maxHeight: 'none', nudgeTop: '200px' }"
        label="協議種別"
        :items="dataRef.options.discussionTypeList"
        chips
        clearable
        closable-chips
        :disabled="!dataRef.isAdd || dataRef.loading"
        :error-messages="errorMessages?.discussionType"
        @update:model-value="updateDiscussionType"
      />

      <div v-if="dataRef.loading">
        <v-skeleton-loader
          v-for="n of 3"
          :key="n"
          type="list-item-three-line"
        />
      </div>
      <div v-else>
        <div v-if="dataRef.targetData.purposeTypeOfTemplate">
          <div v-if="discussionTypeRef" class="pb-7">
            <span class="text-caption">To</span>
            <RichTextEditor
              v-model="dataRef.targetData.mentionTargetsHtml"
              :can-mention="true"
              hint-text="※顧客担当者・課題案件担当者には自動で更新通知が届きます"
              :staffs="props.staffs"
              :teams="teams"
            />
          </div>
          <!-- 目的 -->
          <AppAutocomplete
            v-model="purpose"
            :items="issueProjectDiscussionPurposeType"
            label="目的"
            no-data-text="該当なし"
            required-mark
            :disabled="dataRef.loading || !isInternalOrExternal"
            :error-messages="errorMessages?.purpose"
            @blur="validateItem('purpose')"
            @update:model-value="
              ($event) => (dataRef.targetData.purpose = $event)
            "
          />
          <!-- 相手 -->
          <AppTextField
            v-if="isExternal || (isEditPersonType && !dataRef.isAdd)"
            v-model="dataRef.targetData.person"
            label="相手"
            rows="auto"
            :disabled="dataRef.loading"
            :error-messages="errorMessages?.person"
          />
          <!-- 実権者 -->
          <v-checkbox
            v-if="isExternal"
            v-model="dataRef.targetData.isPersonOfPower"
            label="実権者"
            :disabled="dataRef.loading"
          />

          <!-- formテンプレート -->
          <div v-for="form in dataRef.formTemplates" :key="form.key">
            <!-- タイトル -->
            <div
              v-if="
                form.inputType === InputType.title &&
                  dataRef.targetData.descriptions[form.order]
              "
            >
              <AppTextField
                v-model="dataRef.targetData.descriptions[form.order].value"
                :disabled="!form.enabled"
                :label="form.key"
                :error-messages="errorMessages?.[form.key]"
                @blur="requiredValidation(form)"
              />

            </div>

            <!-- テキストボックス -->
            <div
              v-if="
                form.inputType === InputType.textBox &&
                  dataRef.targetData.descriptions[form.order]
              "
            >
              <AppTextField
                v-model="dataRef.targetData.descriptions[form.order].value"
                :label="form.key"
                :error-messages="errorMessages?.[form.key]"
                @blur="requiredValidation(form)"
              />
            </div>

            <!-- テキストエリア -->
            <div
              v-else-if="
                form.inputType === InputType.textArea &&
                  dataRef.targetData.descriptions[form.order]
              "
              class="mt-1"
            >
              <AppTextarea
                v-model="dataRef.targetData.descriptions[form.order].value"
                variant="outlined"
                auto-grow
                :label="form.key"
                rows="auto"
                :error-messages="errorMessages?.[form.key]"
                @blur="requiredValidation(form)"
              />

              <!-- アノテーション -->
              <div
                v-if="
                  form.key === RecoveryAnnotation ||
                    form.key === CommunicationPlanForDelvingIntoNewChallenges ||
                    form.key === CommunicationPlanForAddressingTheNextChallenges
                "
                class="annotation"
              >
                <div
                  v-for="(annotation, index) in addAnnotation(form)"
                  :key="index"
                >
                  <div class="text-caption">{{ annotation }}</div>
                </div>
              </div>
            </div>

            <!-- 日付 -->
            <div
              v-else-if="
                form.inputType === InputType.dateTime &&
                  dataRef.targetData.descriptions[form.order]
              "
            >
              <AppPopupDatePicker
                v-model="dataRef.targetData.descriptions[form.order].value"
                :label="form.key"
                display-text-format="yyyy/MM/dd"
                :error-messages="errorMessages?.[form.key]"
                @blur="requiredValidation(form)"
                @update:model-value="requiredValidation(form)"
              />
            </div>

            <!-- 整数 -->
            <div
              v-else-if="
                form.inputType === InputType.int &&
                  dataRef.targetData.descriptions[form.order]
              "
            >
              <AppNumberField
                v-model="dataRef.targetData.descriptions[form.order].value"
                :label="form.key"
                :suffix="form.suffix"
                min="0"
                max="999999999"
                counter="11"
                :error-messages="errorMessages?.[form.key]"
                @blur="intFieldBlurEvent(form)"
              />
            </div>

            <!-- 実数 -->
            <div
              v-else-if="
                form.inputType === InputType.decimal &&
                  dataRef.targetData.descriptions[form.order]
              "
            >
              <AppNumberField
                v-model="dataRef.targetData.descriptions[form.order].value"
                :label="form.key"
                :suffix="form.suffix"
                :step="dataRef.addition"
                min="0"
                max="999999999"
                counter="11"
                :error-messages="errorMessages?.[form.key]"
                @blur="decimalFieldBlurEvent(form)"
              />
            </div>

            <!-- ラジオボタン -->
            <div
              v-else-if="
                form.inputType === InputType.radio &&
                  dataRef.targetData.descriptions[form.order]
              "
            >
              <v-radio-group
                v-model="dataRef.targetData.descriptions[form.order].value"
                :label="form.key"
                :error-messages="errorMessages?.[form.key]"
                inline
              >
                <div
                  v-for="item in dataRef.radioButtonForms[form.key]"
                  :key="item.key"
                >
                  <v-radio
                    :label="item.key"
                    :value="item.value"
                    @blur="requiredValidation(form)"
                  />
                </div>
              </v-radio-group>
            </div>

            <!-- ドロップダウンリスト -->
            <div
              v-else-if="
                form.inputType === InputType.dropDownList &&
                  dataRef.targetData.descriptions[form.order]
              "
            >
              <AppAutocomplete
                v-model="dataRef.targetData.descriptions[form.order].value"
                :items="dataRef.autocompleteForms[form.key]"
                :label="form.key"
                :error-messages="errorMessages?.[form.key]"
                @blur="requiredValidation(form)"
              />
            </div>

            <!-- チェックボックス -->
            <div
              v-else-if="
                form.inputType === InputType.checkbox &&
                  dataRef.targetData.descriptions[form.order]
              "
            >
              <v-checkbox
                v-model="dataRef.targetData.descriptions[form.order].value"
                :label="form.key"
                :error-messages="errorMessages?.[form.key]"
                @blur="requiredValidation(form)"
              />
            </div>
            <!-- ドロップダウンリスト -->
            <div
              v-else-if="
                form.inputType === InputType.userSelector &&
                  dataRef.targetData.descriptions[form.order] &&
                  dataRef.autocompleteForms[form.key]
              "
            >
              <AppAutocomplete
                v-model="dataRef.targetData.descriptions[form.order].value"
                :items="dataRef.autocompleteForms[form.key]"
                :label="form.key"
                :error-messages="errorMessages?.[form.key]"
                @blur="requiredValidation(form)"
              />
            </div>
          </div>

          <!-- ファイルアップローダー -->
          <FileUploader
            ref="fileUploader"
            v-model:upload-files="uploadFiles"
            :uploaded-files="dataRef.uploadedFiles"
            :disable-list="true"
            :use-save-btn="true"
            :is-add="dataRef.isAdd"
            @upload-file="uploadFile"
          />
        </div>
      </div>
    </AppSimpleDialogTmpl>
  </v-dialog>
</template>

<script setup lang="ts">
import { z } from 'zod'
import { parseISO } from 'date-fns'
import type { Team } from '@ibp/common-case/src/apiclient/customerProposal/team'
import {
  useGetIssueProjectDiscussionOptions,
  useGetOptionsIncludeInactive,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionType'
import type { IssueProjectDiscussionTypeItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionType'
import type { FindIssueProjectDiscussionThreadResultItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import { confirmBeforeUnload } from '@hox/base/src/utils/shared/confirmBeforeUnload'
import { useFindIssueProjectDiscussionFormTemplate } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionFormTemplate'
import type {
  FindIssueProjectDiscussionFormTemplateCriteria,
  IssueProjectDiscussionFormTemplate,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionFormTemplate'
import { issueProjectDiscussionPurposeType } from '@ibp/issue-project/src/constants/domain/issueProject'
import { useGetIssueProject } from '@ibp/issue-project/src/apiclient/customerProposal/issueProject'
import type { IssueProject } from '@ibp/issue-project/src/apiclient/customerProposal/issueProject'
import { useFindUser } from '@ibp/common-case/src/apiclient/myCareer/user'
import { defaultInstance } from '@hox/base/src/utils/shared/zodDefaultInstance'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import { targetDataSchema } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type { DeleteItem } from '@ibp/issue-project/src/models/share/FileUploadType'
import type { issueProjectDiscussionFileModelItem } from '@ibp/issue-project/src/models/issueProjectDiscussion/FileUploadType'
import type fileUploaderComponent from '@ibp/common-case/src/components/organisms/FileUploader.vue'
import { validateFileSize } from '@ibp/common-case/src/utils/fileUploadValidator'
import { getMentionTargetIds } from '@ibp/common-case/src/utils/quillEditorUtil'

const RecoveryAnnotation = 'リカバリーに向けたコミュニケーションプラン'
const CommunicationPlanForDelvingIntoNewChallenges =
  '新たな課題深堀へのコミュニケーションプラン'
const CommunicationPlanForAddressingTheNextChallenges =
  '次の課題取組みへのコミュニケーションプラン'

type IssueProjectDiscussionType = {
  title: string
  value: string
}

type descriptionsType = {
  key: string
  value?: any
}

type RadioButtonForm = {
  key: string
  value: number
}

type radioButtonFormsType = {
  [key: string]: Array<RadioButtonForm> | null | undefined
}

type autocompleteForms = {
  title: string
  value: string | number
}

type autocompleteFormsType = {
  [key: string]: Array<autocompleteForms>
}

// 画面で使用するデータのデフォルトを作ります。
function createDefaultViewData() {
  // 表示する型に変更してください。
  const result = reactive(
    defaultInstance<typeof targetDataSchema>(targetDataSchema),
  )
  return result
}

// 画面で使用するデータを定義します。
const intiTargetData = createDefaultViewData()

type targetDataType = z.infer<typeof targetDataSchema>

const dataRef = ref<{
  dialog: boolean
  loading: boolean
  isAdd: boolean | undefined
  targetData: targetDataType
  resolve: any
  formTemplates: Array<IssueProjectDiscussionFormTemplate>
  cancelPressed: boolean
  showUploader: boolean
  options: {
    discussionTypeList: Array<IssueProjectDiscussionType>
  }
  selectedFiles: Array<any> // アップロード予定のファイルを管理する(inputFilesからselectedFilesにデータが追加されていく) // TODO:anyの確認
  threadFileNames: Array<string> // selectedFilesとuploadedFilesのファイル名を管理する
  inputFiles: [] // アップローダーの選択ダイアログでの選択またはドロップで発火するイベントで扱われるファイルを管理する
  uploadedFiles: Array<issueProjectDiscussionFileModelItem>
  filesLoading: boolean
  issueProject: IssueProject | undefined
  formValue: string | number | null | Date | undefined
  radioButtonForms: radioButtonFormsType // ラジオボタンのフォームのリスト
  autocompleteForms: autocompleteFormsType // DropDownListのフォームのリスト
  addition: number
}>({
  dialog: false,
  loading: false,
  isAdd: undefined,
  targetData: intiTargetData,
  resolve: undefined,
  formTemplates: [],
  cancelPressed: false,
  showUploader: false,
  options: {
    discussionTypeList: [],
  },
  selectedFiles: [],
  threadFileNames: [],
  inputFiles: [],
  uploadedFiles: [],
  filesLoading: true,
  issueProject: undefined,
  formValue: undefined,
  radioButtonForms: {},
  autocompleteForms: {},
  addition: 0,
})

const DiscussionType = {
  internalDiscussion: '1', // 社内協議
  preContractCustomerMeeting: '2', // 契約前顧客面談
  proposalReview: '3', // 提案書確認
  contractApprovalAndStampApplicationForCloudSign: '4', // 契約締結稟議申請兼捺印申請
  contractApprovalAndStampApplicationForWrittenContract: '5', // 契約締結稟議申請兼捺印申請
  executionSupportContentConfirmation: '6', // 実行支援内容確認
  intermediateReview: '7', // 中間レビュー
  projectSummary: '8', // 案件サマリー
  afterFollowUp: '9', // アフターフォロー
  other: '01HJ7A5SV9MX1PZ7S3W4KE3083', // その他
  inContractCustomerMeeting: '01HKVGNJHE35SPDQ4A86E2BCDA', // 契約中顧客面談
}

const InputType = {
  title: 1,
  textBox: 2,
  textArea: 3,
  int: 4,
  decimal: 5,
  dateTime: 6,
  radio: 7,
  dropDownList: 8,
  userSelector: 9,
  checkbox: 10,
}

const { error: errorToast } = useAppToasts()

const dialogTitle = computed(() => {
  return dataRef.value.isAdd ? 'スレッドの登録' : 'スレッドの編集'
})

// v-modelに入れる用
const discussionTypeRef = toStringArray(
  toRef(dataRef.value.targetData, 'discussionType'),
)
const purpose = toStringArray(toRef(dataRef.value.targetData, 'purpose'))

type staffType = {
  id: string
  value: string
  romaji: string
}

const props = withDefaults(
  defineProps<{
    staffs: Array<staffType>
    teams: Array<Team>
  }>(),
  {
    staffs: () => [],
    teams: () => [],
  },
)

// =====================================================================================================================
// バリデーション定義
// =====================================================================================================================
const { errorMessages, validate, validateItem } = useValidation(
  z.object({
    discussionType: z.string(),
    purpose: z.string(),
    person: z.string().nullish(),
  }),
  toRef(dataRef.value.targetData),
)

/**
 * 必須チェック
 */
const requiredValidation = (form: IssueProjectDiscussionFormTemplate) => {
  if (!form.isRequired) return true
  if (!errorMessages.value) return
  errorMessages.value[form.key] = []

  const value = dataRef.value.targetData.descriptions[form.order].value
  if (!value) {
    errorMessages.value[form.key] = [`${form.key}は必須入力です`]
    return false
  }

  if (form.inputType === InputType.checkbox && !value) {
    errorMessages.value[form.key] = [`${form.key}は必須入力です`]
    return false
  }

  if (form.inputType === InputType.dateTime && !value) {
    errorMessages.value[form.key] = [`${form.key}は必須入力です`]
    return false
  }

  return true
}

// データの変更を監視して、画面離脱時に変更があった場合に確認ダイアログを表示します。
const { init, hasChanged, restart } = useWatchDataChanges()
const watcher = { init, hasChanged, restart }
confirmBeforeUnload(hasChanged, '')

// =====================================================================================================================
// モーダル
// =====================================================================================================================
const fileUploader = ref<InstanceType<typeof fileUploaderComponent>>()

// =====================================================================================================================
// 画面初期化処理
// =====================================================================================================================

// 画面変更の検知を開始します。
watcher.init(dataRef.value.targetData)

// =====================================================================================================================
// APIクライアントの定義(issueProjectDiscussionTypeのgetOptions)
// =====================================================================================================================
const {
  data: getIssueProjectDiscussionOptionsData,
  executeWithResult: getIssueProjectDiscussionOptions,
} = useGetIssueProjectDiscussionOptions()

// =====================================================================================================================
// APIクライアントの定義(getOptionsIncludeInactive)
// =====================================================================================================================
const {
  data: optionsIncludeInactiveData,
  executeWithResult: getOptionsIncludeInactive,
} = useGetOptionsIncludeInactive()

// =====================================================================================================================
// APIクライアントの定義(users)
// =====================================================================================================================
const { data: userData, executeWithResult: fineUser } = useFindUser()

//
// ダイアログを開く
//
async function open(
  threadData: FindIssueProjectDiscussionThreadResultItem | null,
): Promise<{ isOk: boolean, data?: any } | undefined> {
  dataRef.value.loading = true
  // リトライの場合は表示させるが、通常は初期表示では表示しない
  dataRef.value.showUploader = false
  errorMessages.value = {}

  // ドロップダウンリストの設定
  const optionsResult = await getIssueProjectDiscussionOptions()
  if (!optionsResult) {
    return
  }

  dataRef.value.options.discussionTypeList =
    getIssueProjectDiscussionOptionsData.value.map(
      (val: IssueProjectDiscussionTypeItem) => {
        return {
          title: val.text,
          value: val.value,
        }
      },
    )

  // 添付ファイル関連の初期化
  dataRef.value.selectedFiles = []
  dataRef.value.uploadedFiles = []
  dataRef.value.threadFileNames = []
  dataRef.value.inputFiles = []

  if (threadData) {
    // 編集元データを受け取った場合は内容を反映させる
    mapTargetData(threadData)
    dataRef.value.uploadedFiles = threadData.files
    for (const file of dataRef.value.uploadedFiles) {
      dataRef.value.threadFileNames.push(file.fileName)
    }

    dataRef.value.isAdd = false
    await getFormTemplate()
    await applyOriginalData(threadData)
    dataRef.value.filesLoading = false

    // 非活性化となった協議種別の場合
    const discussionType = dataRef.value.options.discussionTypeList.find(
      (x) => x.value === threadData.discussionType.id,
    )

    if (!discussionType) {
      const inActiveOptionsResult = await getOptionsIncludeInactive()
      if (!inActiveOptionsResult) return
      dataRef.value.options.discussionTypeList =
        optionsIncludeInactiveData.value.map(
          (val: IssueProjectDiscussionTypeItem) => {
            return {
              title: val.text,
              value: val.value,
            }
          },
        )
    }
    // threadDataがなければ
  } else {
    // 編集元データが存在しない場合は表示を初期化する
    dataRef.value.targetData.discussionType = ''
    dataRef.value.targetData.purpose = undefined
    dataRef.value.formTemplates = []
    dataRef.value.isAdd = true
    dataRef.value.targetData.mentionTargetsHtml = ''
  }

  // ダイアログを表示します。
  dataRef.value.dialog = true
  dataRef.value.loading = false
  watcher.init(dataRef.value.targetData)
  // 呼び出し元に Promise を返します。
  return new Promise((_resolve) => {
    dataRef.value.resolve = _resolve
  })
}

// 編集元データを受け取った場合は内容を反映させる
const mapTargetData = (
  threadData: FindIssueProjectDiscussionThreadResultItem,
) => {
  dataRef.value.targetData.id = threadData.id
  dataRef.value.targetData.version = threadData.version
  dataRef.value.targetData.discussionType = threadData.discussionType.id
  dataRef.value.targetData.mentionTargetsHtml = threadData.mentionTargetsHtml
  dataRef.value.targetData.templateVersion = threadData.templateVersion
  dataRef.value.targetData.purposeTypeOfTemplate =
    threadData.purposeTypeOfTemplate
  dataRef.value.targetData.purpose = threadData.purpose
  dataRef.value.targetData.person = threadData.person
  dataRef.value.targetData.isPersonOfPower = threadData.isPersonOfPower
}

function confirmLeave() {
  if (dataRef.value.dialog && hasChanged) {
    return window.confirm('編集中のものは保存されませんが、よろしいですか？')
  }
  return true
}

function cancel() {
  if (hasChanged.value) {
    // 確認ダイアログを表示
    if (!confirmLeave()) {
      // ユーザーがキャンセルを選択した場合、ダイアログを閉じない
      return
    }
  }

  fileUploader.value?.clearInternalParameter()

  uploadFiles.value = []
  dataRef.value.targetData.purposeTypeOfTemplate = ''
  dataRef.value.targetData.purpose = ''
  dataRef.value.targetData.person = undefined
  dataRef.value.targetData.isPersonOfPower = undefined
  dataRef.value.cancelPressed = true

  dataRef.value.resolve({ isOk: false })
  dataRef.value.showUploader = false
  dataRef.value.dialog = false
}

//
// 保存
//
const saveIsValid = ref<boolean>(true)

async function save() {
  const { success } = await validate()
  if (!success) return

  // ファイルアップロード
  await fileUploader.value?.uploadFile()

  // ファイルサイズチェック
  const fileSizeValidateResult: { isValid: boolean, message: string } = validateFileSize(
    dataRef.value.targetData.filesToUpload,
  )
  if (!fileSizeValidateResult.isValid) {
    errorToast(fileSizeValidateResult.message)
    return
  }

  saveIsValid.value = true
  // 数値入力チェック
  for (const form of dataRef.value.formTemplates) {
    if (!numericValidation(form)) {
      saveIsValid.value = false
    }
  }

  // 必須入力チェック
  for (const form of dataRef.value.formTemplates) {
    const result = requiredValidation(form)

    if (!result) {
      saveIsValid.value = false
    }
  }

  if (!saveIsValid.value) return

  // descriptionsをJSONに変換
  dataRef.value.targetData.descriptionJson = formConvertToJsonString(
    dataRef.value.targetData.descriptions,
  )

  // メンション実装時に行う
  dataRef.value.targetData.mentionTargetUserIds = getMentionTargetIds(
    dataRef.value.targetData.mentionTargetsHtml!,
    'staff',
  )

  dataRef.value.targetData.mentionTargetTeamIds = getMentionTargetIds(
    dataRef.value.targetData.mentionTargetsHtml!,
    'team',
  )

  dataRef.value.targetData.mentionTargetTeamMemberUserIds = []

  const mentionTargetTeams = props.teams.filter((x) =>
    dataRef.value.targetData.mentionTargetTeamIds.includes(x.id),
  )

  if (!mentionTargetTeams) return
  for (let index = 0; index < mentionTargetTeams.length; index++) {
    dataRef.value.targetData.mentionTargetTeamMemberUserIds.push(
      ...mentionTargetTeams[index].teamMembers.map((x) => x.staffId),
    )
  }

  // 社内協議を選択したときは相手・実権者をクリア(面談者が存在する協議以外)
  if (dataRef.value.targetData.purpose === 'Internal' && !isEditPersonType.value) {
    dataRef.value.targetData.person = undefined
    dataRef.value.targetData.isPersonOfPower = undefined
  }

  dataRef.value.resolve({
    isOk: true,
    data: Object.assign({}, dataRef.value.targetData),
  })

  uploadFiles.value = []
  dataRef.value.targetData.purposeTypeOfTemplate = ''
  dataRef.value.targetData.purpose = ''
  dataRef.value.targetData.person = undefined
  dataRef.value.targetData.isPersonOfPower = undefined
}

// =====================================================================================================================
// 入力フォームのテンプレート関連
// =====================================================================================================================
const route = useRoute()
const issueProjectId = route.query.issueProjectId

const { data: issueProjectData, executeWithResult: getIssueProjectById } =
  useGetIssueProject(ref<string>(String(issueProjectId)))

/**
 * 入力フォームのテンプレートを取得する
 */
const getFormTemplate = async (discussionType?: string) => {
  dataRef.value.cancelPressed = false

  if (discussionType) {
    dataRef.value.targetData.discussionType = discussionType
  }

  if (!dataRef.value.targetData.discussionType) {
    dataRef.value.formTemplates = []
  }

  // タイトル(協議内容)が選択されているか確認
  const { success } = await validateItem('discussionType')
  if (!success) return

  // データ取得
  try {
    dataRef.value.loading = true

    // ==========c===========================================================================================================
    // APIクライアントの定義(FormTemplate)
    // =====================================================================================================================
    const {
      data: formTemplatData,
      executeWithResult: findIssueProjectDiscussionFormTemplat,
    } = useFindIssueProjectDiscussionFormTemplate(
      ref<FindIssueProjectDiscussionFormTemplateCriteria>({
        discussionTypeId: dataRef.value.targetData.discussionType,
        templateVersion: dataRef.value.isAdd
          ? null
          : dataRef.value.targetData.templateVersion,
      }),
    )

    const result = await findIssueProjectDiscussionFormTemplat()
    if (!result) return
    dataRef.value.formTemplates = formTemplatData.value

    // 協議目的の設定
    if (dataRef.value.isAdd) {
      dataRef.value.targetData.purposeTypeOfTemplate =
        dataRef.value.formTemplates[0].discussionType.purposeTypeOfTemplate

      if (
        dataRef.value.formTemplates[0].discussionType.purposeTypeOfTemplate ===
        'Internal'
      ) {
        dataRef.value.targetData.purpose = 'Internal'
      } else if (
        dataRef.value.formTemplates[0].discussionType.purposeTypeOfTemplate ===
        'External'
      ) {
        dataRef.value.targetData.purpose = 'External'
      }
    }

    // 「提案書確認」または「契約締結稟議申請兼捺印申請」を新規登録する場合
    if (dataRef.value.isAdd) {
      if (
        dataRef.value.targetData.discussionType ===
        DiscussionType.proposalReview ||
        dataRef.value.targetData.discussionType ===
        DiscussionType.contractApprovalAndStampApplicationForCloudSign ||
        dataRef.value.targetData.discussionType ===
        DiscussionType.contractApprovalAndStampApplicationForWrittenContract
      ) {
        const issueProjectFindResult = await getIssueProjectById().catch(
          (e: any) => {
            if (!e.hasProblemDetails) {
              errorToast('課題案件の取得に失敗しました。')
            }
            return false
          },
        )

        if (!issueProjectFindResult) return
        dataRef.value.issueProject = issueProjectData.value
      }
    }

    // 取得したテンプレート項目の加工
    dataRef.value.targetData.descriptions = []
    for (const form of dataRef.value.formTemplates) {
      dataRef.value.targetData.templateVersion = form.templateVersion

      // 課題案件から値をコピーしてくる項目
      if (dataRef.value.issueProject) {
        switch (form.key) {
          case '提案予定日':
            dataRef.value.formValue =
              dataRef.value.issueProject.projectInformation.proposalDate
            break
          case '提案金額（税抜）':
            dataRef.value.formValue =
              dataRef.value.issueProject.projectInformation.proposalFee
            break
          case '契約日・契約書締結予定日':
            if (
              typeof dataRef.value.issueProject.projectInformation
                .consultingContractDate === 'string'
            ) {
              dataRef.value.formValue = parseISO(
                dataRef.value.issueProject.projectInformation
                  .consultingContractDate,
              )
            } else {
              dataRef.value.formValue =
                dataRef.value.issueProject.projectInformation.consultingContractDate
            }
            break
          case '契約金額（税抜）':
            dataRef.value.formValue =
              dataRef.value.issueProject.projectInformation.contractFee
            break
          case '契約期間(From)':
            dataRef.value.formValue =
              dataRef.value.issueProject.projectInformation.consultingStartDate
            break
          case '契約期間(To)':
            dataRef.value.formValue =
              dataRef.value.issueProject.projectInformation.dueDate
            break
          default:
            dataRef.value.formValue = undefined
        }
      }
      dataRef.value.targetData.descriptions.push({
        key: form.key,
        value: dataRef.value.formValue,
      })

      // ================================================================
      // 「契約前顧客面談」と「アフターフォロー」の協議種別の「面談日」は入力当日を初期表
      // ================================================================
      if (form.key === '面談日') {
        dataRef.value.targetData.descriptions[form.order].value = new Date()
        continue
      }

      // デフォルト文字列の設定
      if (form.inputDefaultText) {
        if (form.inputType === InputType.dateTime) {
          dataRef.value.targetData.descriptions[form.order].value = parseISO(
            form.inputDefaultText,
          )
        } else {
          dataRef.value.targetData.descriptions[form.order].value =
            form.inputDefaultText
        }
        continue
      }

      // ラジオボタンの場合
      if (form.inputType === InputType.radio) {
        // 初期値
        if (form.optionDefaultValue) {
          dataRef.value.targetData.descriptions[form.order].value =
            form.optionDefaultValue
        }
        // ラジオボタンの選択リスト
        dataRef.value.radioButtonForms[form.key] = form.option
        continue
      }

      // ドロップダウンリストの場合
      if (form.inputType === InputType.dropDownList) {
        // 初期値
        if (form.optionDefaultValue) {
          dataRef.value.targetData.descriptions[form.order].value =
            form.optionDefaultValue
        }
        // ドロップダウンリストの選択肢
        dataRef.value.autocompleteForms[form.key] = []
        for (const ddlItem of form.option ?? []) {
          dataRef.value.autocompleteForms[form.key].push({
            title: ddlItem.key,
            value: ddlItem.value,
          })
        }
        continue
      }

      // ドロップダウンリスト（社員選択）の場合
      if (form.inputType === InputType.userSelector) {
        // 初期値
        if (form.optionDefaultValue) {
          dataRef.value.targetData.descriptions[form.order].value =
            form.optionDefaultValue
        }
        dataRef.value.autocompleteForms[form.key] = []
        const staffResult = await fineUser()
        if (!staffResult) return

        const staffs = userData.value.map((x) => {
          return {
            key: x.nameKanji,
            value: x.userId,
          }
        })
        for (const staff of staffs) {
          dataRef.value.autocompleteForms[form.key].push({
            title: staff.key,
            value: staff.value,
          })
        }
      }

      // チェックボックスの場合
      if (form.inputType === InputType.checkbox) {
        // 初期値
        if (form.optionDefaultValue) {
          dataRef.value.targetData.descriptions[form.order].value =
            form.optionDefaultValue
        }
      }
    }
  } finally {
    dataRef.value.loading = false
    if (!dataRef.value.cancelPressed) {
      dataRef.value.showUploader = true
    }
  }
}

const applyOriginalData = (
  threadData: FindIssueProjectDiscussionThreadResultItem,
) => {
  dataRef.value.targetData.descriptions = []

  threadData.descriptions.forEach((desc) => {
    const template = dataRef.value.formTemplates.find(
      (item: IssueProjectDiscussionFormTemplate) => item.key === desc.key,
    )
    if (!template) return

    if (template?.inputType === InputType.dateTime) {
      dataRef.value.targetData.descriptions[template.order] = {
        key: template.key,
        value: parseISO(desc.value),
      }
    } else if (
      template?.inputType === InputType.radio ||
      template?.inputType === InputType.dropDownList
    ) {
      dataRef.value.targetData.descriptions[template.order] = {
        key: template.key,
        value: parseInt(desc.value),
      }
    } else if (template?.inputType === InputType.int) {
      dataRef.value.targetData.descriptions[template.order] = {
        key: template.key,
        value: parseInt(desc.value),
      }
    } else if (template?.inputType === InputType.checkbox) {
      dataRef.value.targetData.descriptions[template.order] = {
        key: template.key,
        value: Boolean(desc.value),
      }
    } else if (template?.inputType === InputType.decimal) {
      dataRef.value.targetData.descriptions[template.order] = {
        key: template.key,
        value: parseFloat(desc.value),
      }
    } else {
      dataRef.value.targetData.descriptions[template.order] = {
        key: template.key,
        value: desc.value,
      }
    }
  })
}

const isExternal = computed(() => {
  return dataRef.value.targetData.purpose === 'External'
})

const isInternalOrExternal = computed(() => {
  return dataRef.value.targetData.purposeTypeOfTemplate === 'InternalOrExternal'
})

// 面談者を持っている協議種別
const isEditPersonType = computed(() => {
  const editPersonType = [
    DiscussionType.preContractCustomerMeeting,
    DiscussionType.afterFollowUp,
    DiscussionType.inContractCustomerMeeting,
  ]

  if (editPersonType.includes(dataRef.value.targetData.discussionType)) {
    return true
  }
  return false
})

const addAnnotation = (form: IssueProjectDiscussionFormTemplate) => {
  const annotation: Array<string> = []

  // 注釈パターン1
  const msgPattern1: Array<string> = []
  msgPattern1.push('※1:誰が、誰に、何を、いつまでに行うのかを記載 ')
  msgPattern1.push('※2:事業性理解のコミュニケーションプランにも登録')

  switch (dataRef.value.targetData.discussionType) {
    // 中間レビュー
    case DiscussionType.intermediateReview:
      if (form.key === RecoveryAnnotation) {
        annotation.push(...msgPattern1)
      }
      if (form.key === CommunicationPlanForDelvingIntoNewChallenges) {
        annotation.push(...msgPattern1)
      }
      break

    // 案件サマリ―
    case DiscussionType.projectSummary:
      if (form.key === CommunicationPlanForAddressingTheNextChallenges) {
        annotation.push(...msgPattern1)
      }
      break
  }

  return annotation
}

/**
 * 整数に変換する
 */
const toInteger = (form: IssueProjectDiscussionFormTemplate) => {
  const inputData = dataRef.value.targetData.descriptions[form.order].value
  // 0を除く数値ではない値(NaNやUndefined)の場合nullにする
  if (!inputData && inputData !== 0) {
    dataRef.value.targetData.descriptions[form.order].value = undefined
    dataRef.value.addition = 1
    return
  }

  const numberData = Number(inputData)
  dataRef.value.targetData.descriptions[form.order].value = numberData
}

/**
 * 整数入力欄のブラーイベント
 */
const intFieldBlurEvent = (form: IssueProjectDiscussionFormTemplate) => {
  toInteger(form)
  if (!numericValidation(form)) return
  requiredValidation(form)
}

/**
 * 数値チェック
 */
const numericValidation = (form: IssueProjectDiscussionFormTemplate) => {
  // 整数または実数以外はチェック対象外
  if (
    form.inputType !== InputType.int &&
    form.inputType !== InputType.decimal
  ) {
    return true
  }

  if (!errorMessages.value) return
  errorMessages.value[form.key] = []

  // 999,999,999より大きいまたは0より小さい場合はエラーにする
  const value = Number(dataRef.value.targetData.descriptions[form.order].value)
  if (value < 0 || value > 999999999) {
    errorMessages.value[form.key] = [
      `${form.key}は0~999,999,999の値を入力してください`,
    ]
    return false
  }
  return true
}

/**
 * 実数入力欄のブラーイベント
 */
const decimalFieldBlurEvent = (form: IssueProjectDiscussionFormTemplate) => {
  toDecimal(form)
  if (!numericValidation(form)) return
  requiredValidation(form)
}
/**
 * 実数に変換する
 */
const toDecimal = (form: IssueProjectDiscussionFormTemplate) => {
  const inputData = dataRef.value.targetData.descriptions[form.order].value
  // 0を除く数値ではない値(NaNやUndefined)の場合nullにする
  if (!inputData && inputData !== 0) {
    dataRef.value.targetData.descriptions[form.order].value = null
    dataRef.value.addition = 1
    return
  }

  // 小数点第4位を四捨五入
  const round = Math.round(inputData * 1000)
  dataRef.value.targetData.descriptions[form.order].value = round / 1000

  // 上下ボタンでの加算値(step)を最小桁に合わせる
  const numbers = String(
    dataRef.value.targetData.descriptions[form.order].value,
  ).split('.')
  dataRef.value.addition = numbers[1] ? Math.pow(10, -1 * numbers[1].length) : 1
}

/**
 * キーと値をJSONに成型して返す
 */
const formConvertToJsonString = (descriptions: Array<descriptionsType>) => {
  const keyValuePairs = []
  for (const template of dataRef.value.formTemplates) {
    const description = descriptions.find((x) => x.key === template.key)
    const key = description?.key
    let value = description?.value ?? ''

    const isText = [
      InputType.title,
      InputType.textBox,
      InputType.textArea,
    ].includes(template.inputType)

    if (template.inputType === InputType.dateTime) {
      const date = dataRef.value.targetData.descriptions[template.order].value
      if (date && date instanceof Date) {
        value = date.toISOString()
      }
    } else if (isText) {
      // 自由入力可能な文字列の場合、\(バックスラッシュ)と"(ダブルクォーテーション)はエスケープする
      // \をエスケープすれば、他の\始まりの特殊文字の影響は生じない
      if (typeof value !== 'string') return
      value = value.replace(/[\\]/g, '\\\\').replace(/["]/g, '\\"')
    }

    keyValuePairs.push(`{ "Key": "${key}", "Value": "${value}" }`)
  }
  return `[${keyValuePairs.toString()}]`
}

// ダイアログを閉じる(親コンポーネントから呼び出す)
const close = (): void => {
  dataRef.value.dialog = false
}

// lodingフラグの設定(親コンポーネントから呼び出す)
const setLoading = (value: boolean): void => {
  dataRef.value.loading = value
}

// ================================================================
// ファイル関連
// ================================================================
const uploadFiles = ref<File[]>([])

/**
 * ファイルアップロード
 */
const uploadFile = (items: File[], filesToRemove: DeleteItem[]) => {
  dataRef.value.targetData.filesToUpload = items
  dataRef.value.targetData.filesToRemove = filesToRemove
}

/**
 * アップロード済ファイルも含めた全ファイル情報を返す
 */
const getUploadTargetFiles = () => {
  // 選択したファイルの配列をクローン
  const uploadTargetFiles = [...dataRef.value.selectedFiles]

  // アップロード済ファイル情報を追加(ファイル名のみの空ファイルとして送る)
  for (const uploadedFile of dataRef.value.uploadedFiles) {
    uploadTargetFiles.push(new File([], uploadedFile.fileName))
  }

  return uploadTargetFiles
}

// ================================================================
// 協議種別
// ================================================================

// 協議種別更新
const updateDiscussionType = (discussionType: string) => {
  dataRef.value.targetData.discussionType = discussionType
  getFormTemplate(dataRef.value.targetData.discussionType)
}

defineExpose({
  open,
  close,
  setLoading,
  getUploadTargetFiles,
  dataRef,
})
</script>

<style scoped>
.annotation {
  margin-top: -20px;
  margin-bottom: 5px;
  color: rgba(0, 0, 0, 0.6);
}
</style>
