using FluentAssertions;
using FluentValidation.TestHelper;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Domain.Enums;
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.UpdateEntity;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.UpdateEntity;

/// <summary>
/// UpdateEntityValidatorの単体テストクラス
/// </summary>
/// <remarks>
/// FluentValidationを使用したバリデーションロジックの包括的テストを実装します。
/// UpdateコマンドではID・Version検証が必須となるため、これらを重点的にテストします。
/// 可能な限りテストケースを網羅的に用意し、コードカバレッジを100%に近づけます。
/// 正常値・異常値・境界値・null/空文字・文字数制限・Enum値の全パターンを検証します。
/// </remarks>
public class UpdateEntityValidatorTest
{
    #region テスト用定数

    /// <summary>
    /// null値のテスト用定数
    /// </summary>
    private static readonly string NullString = null!;

    /// <summary>
    /// 空文字のテスト用定数
    /// </summary>
    private static readonly string EmptyString = string.Empty;

    /// <summary>
    /// 単一スペースのテスト用定数
    /// </summary>
    private static readonly string SingleSpace = " ";

    /// <summary>
    /// 複数スペースのテスト用定数
    /// </summary>
    private static readonly string MultipleSpaces = "  ";

    /// <summary>
    /// タブ文字のテスト用定数
    /// </summary>
    private static readonly string TabCharacter = "\t";

    /// <summary>
    /// 改行文字のテスト用定数
    /// </summary>
    private static readonly string NewLineCharacter = "\n";

    /// <summary>
    /// 改行文字（CRLF）のテスト用定数
    /// </summary>
    private static readonly string CarriageReturnNewLine = "\r\n";

    /// <summary>
    /// NULL文字のテスト用定数
    /// </summary>
    private static readonly string NullCharacter = "\0";

    #endregion

    #region テストデータ作成

    /// <summary>
    /// エラーが発生するテストデータを生成します
    /// Update特有のID・Version検証を含む包括的なエラーパターン
    /// </summary>
    /// <returns>エラーテストケースの配列</returns>
    public static IEnumerable<object[]> CreateErrorValues() => new List<object[]>
    {
        #region 必須フィールド検証（Update特有）
        
        // ID検証（Update UseCaseでは必須）
        (SafeValue() with { Id = NullString }).ToObjectArray(),
        (SafeValue() with { Id = EmptyString }).ToObjectArray(),
        (SafeValue() with { Id = SingleSpace }).ToObjectArray(),
        (SafeValue() with { Id = MultipleSpaces }).ToObjectArray(),
        (SafeValue() with { Id = TabCharacter }).ToObjectArray(),
        (SafeValue() with { Id = NewLineCharacter }).ToObjectArray(),
        
        // Version検証（楽観的ロック用、Update UseCaseでは必須）
        (SafeValue() with { Version = NullString }).ToObjectArray(),
        (SafeValue() with { Version = EmptyString }).ToObjectArray(),
        (SafeValue() with { Version = SingleSpace }).ToObjectArray(),
        (SafeValue() with { Version = MultipleSpaces }).ToObjectArray(),
        (SafeValue() with { Version = TabCharacter }).ToObjectArray(),
        (SafeValue() with { Version = NewLineCharacter }).ToObjectArray(),
        
        #endregion
        
        #region 基本フィールド検証
        
        // TODO: 実際のエンティティプロパティに合わせて調整してください
        
        // Name検証
        (SafeValue() with { Name = NullString }).ToObjectArray(),
        (SafeValue() with { Name = EmptyString }).ToObjectArray(),
        (SafeValue() with { Name = SingleSpace }).ToObjectArray(),
        (SafeValue() with { Name = new string('a', 101) }).ToObjectArray(), // 最大文字数超過
        (SafeValue() with { Name = new string('あ', 101) }).ToObjectArray(), // 日本語での最大文字数超過
        
        // Description検証（最大文字数）
        (SafeValue() with { Description = new string('a', 1001) }).ToObjectArray(),
        (SafeValue() with { Description = new string('あ', 1001) }).ToObjectArray(),
        (SafeValue() with { Description = new string('🎯', 1001) }).ToObjectArray(), // 絵文字での境界値
        
        #endregion
        
        #region 数値フィールド検証
        
        // TODO: 実際の数値プロパティに合わせて調整してください
        
        // Priority検証
        (SafeValue() with { Priority = -1 }).ToObjectArray(), // 最小値未満
        (SafeValue() with { Priority = 101 }).ToObjectArray(), // 最大値超過
        (SafeValue() with { Priority = int.MinValue }).ToObjectArray(),
        (SafeValue() with { Priority = int.MaxValue }).ToObjectArray(),
        
        // Amount検証（金額など）
        (SafeValue() with { Amount = -0.01m }).ToObjectArray(), // 負値
        (SafeValue() with { Amount = 10000000000m }).ToObjectArray(), // 最大値超過
        (SafeValue() with { Amount = decimal.MinValue }).ToObjectArray(),
        (SafeValue() with { Amount = decimal.MaxValue }).ToObjectArray(),
        
        #endregion
        
        #region 日付フィールド検証
        
        // TODO: 実際の日付プロパティに合わせて調整してください
        
        // 開始日・終了日の論理的妥当性
        (SafeValue() with { StartDate = new DateTimeOffset(2024, 12, 31, 0, 0, 0, TimeSpan.Zero), EndDate = new DateTimeOffset(2024, 1, 1, 0, 0, 0, TimeSpan.Zero) }).ToObjectArray(), // 開始日 > 終了日
        (SafeValue() with { StartDate = DateTimeOffset.MinValue }).ToObjectArray(), // 過去の日付
        (SafeValue() with { StartDate = new DateTimeOffset(2100, 1, 1, 0, 0, 0, TimeSpan.Zero) }).ToObjectArray(), // 未来すぎる日付
        
        #endregion
        
        #region 関連エンティティID検証
        
        // TODO: 実際の関連エンティティに合わせて調整してください
        
        // RelatedEntityId検証
        (SafeValue() with { RelatedEntityId = string.Empty }).ToObjectArray(), // 空文字（nullは許可される場合が多い）
        (SafeValue() with { RelatedEntityId = " " }).ToObjectArray(),
        (SafeValue() with { RelatedEntityId = new string('a', 101) }).ToObjectArray(), // ID形式として長すぎる
        
        // CategoryId検証
        (SafeValue() with { CategoryId = string.Empty }).ToObjectArray(),
        (SafeValue() with { CategoryId = " " }).ToObjectArray(),
        
        #endregion
        
        #region コレクション検証
        
        // TODO: 実際のコレクションプロパティに合わせて調整してください
        
        // Tags検証
        (SafeValue() with { Tags = new List<string> { NullString } }).ToObjectArray(), // null要素
        (SafeValue() with { Tags = new List<string> { EmptyString } }).ToObjectArray(), // 空文字要素
        (SafeValue() with { Tags = new List<string> { SingleSpace } }).ToObjectArray(), // 空白要素
        (SafeValue() with { Tags = new List<string> { new string('a', 51) } }).ToObjectArray(), // 要素の最大文字数超過
        (SafeValue() with { Tags = Enumerable.Range(1, 11).Select(i => $"tag{i}").ToList() }).ToObjectArray(), // 要素数上限超過
        
        // MentionTargetUserIds検証
        (SafeValue() with { MentionTargetUserIds = new List<string> { NullString } }).ToObjectArray(),
        (SafeValue() with { MentionTargetUserIds = new List<string> { string.Empty } }).ToObjectArray(),
        (SafeValue() with { MentionTargetUserIds = new List<string> { " " } }).ToObjectArray(),
        (SafeValue() with { MentionTargetUserIds = Enumerable.Range(1, 51).Select(i => $"user{i}").ToList() }).ToObjectArray(), // 上限超過
        
        #endregion
        
        #region 複合オブジェクト検証
        
        // TODO: 実際の複合オブジェクトに合わせて調整してください
        
        // 複合オブジェクト内の必須プロパティ
        (SafeValue() with { DetailInformation = CreateDetailInformation(Title: null!) }).ToObjectArray(),
        (SafeValue() with { DetailInformation = CreateDetailInformation(Title: string.Empty) }).ToObjectArray(),
        (SafeValue() with { DetailInformation = CreateDetailInformation(Title: " ") }).ToObjectArray(),
        (SafeValue() with { DetailInformation = CreateDetailInformation(Title: new string('a', 201)) }).ToObjectArray(),
        (SafeValue() with { DetailInformation = CreateDetailInformation(Content: new string('a', 2001)) }).ToObjectArray(),
        
        // 複合オブジェクト内のEnum値
        // (SafeValue() with { DetailInformation = CreateDetailInformation(Priority: (DetailPriority)999) }).ToObjectArray(), // 不正なEnum値
        
        #endregion
        
        #region ファイル関連検証
        
        // TODO: ファイル関連のバリデーションがある場合は調整してください
        
        // FilesToRemove検証
        (SafeValue() with { FilesToRemove = new List<string> { null! } }).ToObjectArray(),
        (SafeValue() with { FilesToRemove = new List<string> { string.Empty } }).ToObjectArray(),
        (SafeValue() with { FilesToRemove = new List<string> { " " } }).ToObjectArray(),
        (SafeValue() with { FilesToRemove = Enumerable.Range(1, 21).Select(i => $"file{i}.txt").ToList() }).ToObjectArray(), // 削除ファイル数上限
        
        #endregion
        
        #region 特殊文字・エンコード検証
        
        // 特殊文字での境界値テスト
        (SafeValue() with { Name = "🎯".PadRight(101, '🎯') }).ToObjectArray(), // 絵文字での文字数超過
        (SafeValue() with { Name = "あいうえお".PadRight(101, 'あ') }).ToObjectArray(), // 全角文字での文字数超過
        (SafeValue() with { Description = "🚀📊💡".PadRight(1001, '🎯') }).ToObjectArray(), // 複数絵文字での文字数超過
        
        #endregion

    }.ToArray();

    /// <summary>
    /// 正常値のテストデータを生成します
    /// </summary>
    /// <returns>正常値テストケースの配列</returns>
    public static IEnumerable<object[]> CreateSafeValues() => new List<object[]>
    {
        // 基本的な正常値
        SafeValue().ToObjectArray(),
        
        #region null許可フィールドの境界値
        
        // TODO: null許可フィールドの正常パターンを追加してください
        (SafeValue() with { Description = null }).ToObjectArray(),
        (SafeValue() with { Description = string.Empty }).ToObjectArray(),
        (SafeValue() with { RelatedEntityId = null }).ToObjectArray(),
        (SafeValue() with { CategoryId = null }).ToObjectArray(),
        (SafeValue() with { Tags = null }).ToObjectArray(),
        (SafeValue() with { Tags = new List<string>() }).ToObjectArray(), // 空コレクション
        (SafeValue() with { MentionTargetUserIds = null }).ToObjectArray(),
        (SafeValue() with { FilesToRemove = null }).ToObjectArray(),
        
        #endregion
        
        #region 文字数境界値（正常）
        
        // 最大文字数ちょうど
        (SafeValue() with { Name = new string('a', 100) }).ToObjectArray(), // 最大文字数
        (SafeValue() with { Description = new string('a', 1000) }).ToObjectArray(),
        (SafeValue() with { Name = new string('あ', 100) }).ToObjectArray(), // 日本語での最大文字数
        
        // 最小値
        (SafeValue() with { Priority = 0 }).ToObjectArray(),
        (SafeValue() with { Amount = 0m }).ToObjectArray(),
        
        // 最大値
        (SafeValue() with { Priority = 100 }).ToObjectArray(),
        (SafeValue() with { Amount = 9999999999m }).ToObjectArray(),
        
        #endregion
        
        #region コレクション境界値（正常）
        
        // 最大要素数
        (SafeValue() with { Tags = Enumerable.Range(1, 10).Select(i => $"tag{i}").ToList() }).ToObjectArray(), // 最大要素数
        (SafeValue() with { MentionTargetUserIds = Enumerable.Range(1, 50).Select(i => $"user{i}").ToList() }).ToObjectArray(),
        (SafeValue() with { FilesToRemove = Enumerable.Range(1, 20).Select(i => $"file{i}.txt").ToList() }).ToObjectArray(),
        
        // 1要素
        (SafeValue() with { Tags = new List<string> { "single-tag" } }).ToObjectArray(),
        (SafeValue() with { MentionTargetUserIds = new List<string> { "single-user" } }).ToObjectArray(),
        (SafeValue() with { FilesToRemove = new List<string> { "single-file.txt" } }).ToObjectArray(),
        
        #endregion
        
        #region 日付境界値（正常）
        
        // TODO: 実際の業務日付制約に合わせて調整してください
        (SafeValue() with { StartDate = new DateTimeOffset(2024, 1, 1, 0, 0, 0, TimeSpan.Zero), EndDate = new DateTimeOffset(2024, 12, 31, 23, 59, 59, TimeSpan.Zero) }).ToObjectArray(),
        (SafeValue() with { StartDate = DateTimeOffset.Now, EndDate = DateTimeOffset.Now.AddDays(1) }).ToObjectArray(),
        
        #endregion
        
        #region Enum値の全パターン
        
        // TODO: 実際のEnum値に合わせて調整してください
        // (SafeValue() with { Status = EntityStatus.Draft }).ToObjectArray(),
        // (SafeValue() with { Status = EntityStatus.Active }).ToObjectArray(),
        // (SafeValue() with { Status = EntityStatus.Inactive }).ToObjectArray(),
        // (SafeValue() with { Priority = EntityPriority.Low }).ToObjectArray(),
        // (SafeValue() with { Priority = EntityPriority.Medium }).ToObjectArray(),
        // (SafeValue() with { Priority = EntityPriority.High }).ToObjectArray(),
        
        #endregion
        
        #region 特殊文字・エンコード（正常）
        
        // 絵文字・特殊文字（制限内）
        (SafeValue() with { Name = "テスト🎯エンティティ📊" }).ToObjectArray(),
        (SafeValue() with { Description = "詳細な説明💡\n改行込み\tタブ込み" }).ToObjectArray(),
        
        // 多言語文字
        (SafeValue() with { Name = "Test Entity テスト 测试 тест" }).ToObjectArray(),
        
        #endregion

    }.ToArray();

    /// <summary>
    /// 正常値となるUpdateEntityCommandを作成します
    /// </summary>
    /// <returns>正常値のUpdateEntityCommand</returns>
    private static UpdateEntityCommand SafeValue()
        => new(
            Id: "entity-1", // Update特有：必須
            Name: "テストエンティティ",
            Description: "テストエンティティの詳細説明",
            // TODO: 実際のプロパティに合わせて調整してください
            Status: EntityStatus.Active,
            Priority: 50,
            Amount: 1000000m,
            StartDate: new DateTimeOffset(2024, 1, 1, 0, 0, 0, TimeSpan.Zero),
            EndDate: new DateTimeOffset(2024, 12, 31, 0, 0, 0, TimeSpan.Zero),
            RelatedEntityId: "related-entity-1",
            CategoryId: "category-1",
            Tags: new List<string> { "tag1", "tag2", "tag3" },
            MentionTargetUserIds: new List<string> { "user1", "user2" },
            DetailInformation: CreateDetailInformation(),
            Version: "version-1", // Update特有：楽観的ロック用
            UploadFiles: null,
            FilesToRemove: null
        );

    /// <summary>
    /// 複合オブジェクトのテストデータを作成します
    /// </summary>
    /// <param name="Title">タイトル</param>
    /// <param name="Content">内容</param>
    /// <param name="Priority">優先度</param>
    /// <returns>DetailInformationCommand</returns>
    private static DetailInformationCommand CreateDetailInformation(
        string Title = "詳細タイトル",
        string Content = "詳細内容",
        DetailPriority Priority = DetailPriority.Medium)
        => new(Title, Content, Priority);

    #endregion

    #region バリデーションテスト

    /// <summary>
    /// 異常値でバリデーションエラーが発生することを検証します
    /// </summary>
    /// <param name="request">テスト対象のコマンド</param>
    [Theory]
    [MemberData(nameof(CreateErrorValues))]
    public void エラーが発生する(UpdateEntityCommand request)
    {
        // Arrange
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.Errors.Should().NotBeEmpty();
    }

    /// <summary>
    /// 正常値でバリデーションエラーが発生しないことを検証します
    /// </summary>
    /// <param name="request">テスト対象のコマンド</param>
    [Theory]
    [MemberData(nameof(CreateSafeValues))]
    public void エラーが発生しない(UpdateEntityCommand request)
    {
        // Arrange
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.Errors.Should().BeEmpty();
    }

    #endregion

    #region 特定フィールドのバリデーション詳細テスト

    /// <summary>
    /// ID必須検証の詳細テスト（Update特有）
    /// </summary>
    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("  ")]
    [InlineData("\t")]
    [InlineData("\n")]
    [InlineData("\r\n")]
    public void ID必須検証_異常値でエラーが発生する(string invalidId)
    {
        // Arrange
        var request = SafeValue() with { Id = invalidId };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    /// <summary>
    /// Version必須検証の詳細テスト（Update特有）
    /// </summary>
    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("  ")]
    [InlineData("\t")]
    [InlineData("\n")]
    [InlineData("\r\n")]
    public void Version必須検証_異常値でエラーが発生する(string invalidVersion)
    {
        // Arrange
        var request = SafeValue() with { Version = invalidVersion };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Version);
    }

    /// <summary>
    /// Name文字数制限の詳細テスト
    /// </summary>
    [Theory]
    [InlineData(101)] // 境界値+1
    [InlineData(150)] // 明らかな超過
    [InlineData(1000)] // 大幅な超過
    public void Name文字数制限_上限超過でエラーが発生する(int length)
    {
        // Arrange
        var request = SafeValue() with { Name = new string('a', length) };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    /// <summary>
    /// Name文字数制限の正常値テスト
    /// </summary>
    [Theory]
    [InlineData(1)]   // 最小値
    [InlineData(50)]  // 中間値
    [InlineData(100)] // 最大値
    public void Name文字数制限_正常値でエラーが発生しない(int length)
    {
        // Arrange
        var request = SafeValue() with { Name = new string('a', length) };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    /// <summary>
    /// 数値範囲検証の詳細テスト
    /// </summary>
    [Theory]
    [InlineData(-1)]        // 最小値未満
    [InlineData(101)]       // 最大値超過
    [InlineData(int.MinValue)] // 極値
    [InlineData(int.MaxValue)] // 極値
    public void Priority範囲検証_範囲外でエラーが発生する(int invalidPriority)
    {
        // Arrange
        var request = SafeValue() with { Priority = invalidPriority };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Priority);
    }

    /// <summary>
    /// 日付論理チェックの詳細テスト
    /// </summary>
    [Fact]
    public void 日付論理チェック_開始日が終了日より後でエラーが発生する()
    {
        // Arrange
        var request = SafeValue() with 
        { 
            StartDate = new DateTimeOffset(2024, 12, 31, 0, 0, 0, TimeSpan.Zero),
            EndDate = new DateTimeOffset(2024, 1, 1, 0, 0, 0, TimeSpan.Zero)
        };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.StartDate)
              .Or.ShouldHaveValidationErrorFor(x => x.EndDate);
    }

    /// <summary>
    /// コレクション要素検証の詳細テスト
    /// </summary>
    [Theory]
    [InlineData(11)]  // 上限超過
    [InlineData(50)]  // 大幅超過
    [InlineData(100)] // 極端な超過
    public void Tags要素数制限_上限超過でエラーが発生する(int count)
    {
        // Arrange
        var request = SafeValue() with { Tags = Enumerable.Range(1, count).Select(i => $"tag{i}").ToList() };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Tags);
    }

    /// <summary>
    /// 複合オブジェクトバリデーションの詳細テスト
    /// </summary>
    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    public void 複合オブジェクト内必須フィールド_異常値でエラーが発生する(string invalidTitle)
    {
        // Arrange
        var request = SafeValue() with { DetailInformation = CreateDetailInformation(Title: invalidTitle) };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("DetailInformation.Title");
    }

    #endregion

    #region エラーメッセージテスト

    /// <summary>
    /// 必須フィールドのエラーメッセージが正しいことを検証します
    /// </summary>
    [Fact]
    public void ID必須エラー_正しいメッセージが返る()
    {
        // Arrange
        var request = SafeValue() with { Id = null! };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        var error = result.Errors.FirstOrDefault(e => e.PropertyName == nameof(UpdateEntityCommand.Id));
        error.Should().NotBeNull();
        // TODO: 実際のエラーメッセージ仕様に合わせて調整してください
        // error!.ErrorMessage.Should().Contain("必須");
    }

    /// <summary>
    /// 文字数制限のエラーメッセージが正しいことを検証します
    /// </summary>
    [Fact]
    public void Name文字数制限エラー_正しいメッセージが返る()
    {
        // Arrange
        var request = SafeValue() with { Name = new string('a', 101) };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        var error = result.Errors.FirstOrDefault(e => e.PropertyName == nameof(UpdateEntityCommand.Name));
        error.Should().NotBeNull();
        // TODO: 実際のエラーメッセージ仕様に合わせて調整してください
        // error!.ErrorMessage.Should().Contain("100文字以下");
    }

    #endregion

    #region 特殊ケース・エッジケーステスト

    /// <summary>
    /// 絵文字を含む文字列の文字数制限テスト
    /// </summary>
    [Theory]
    [InlineData("🎯🎯🎯🎯🎯", 5)]   // 絵文字のみ
    [InlineData("Test🎯Test🎯", 10)] // 混在
    [InlineData("あああ🎯あああ", 8)]   // 日本語と絵文字
    public void 絵文字含み文字列_文字数が正しく計算される(string text, int expectedLength)
    {
        // Arrange
        text.Length.Should().Be(expectedLength, "テスト前提の確認");
        
        var request = SafeValue() with { Name = text };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert - 100文字以下なのでエラーにならない
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    /// <summary>
    /// 複数バリデーションエラーが同時に発生する場合のテスト
    /// </summary>
    [Fact]
    public void 複数フィールドエラー_すべてのエラーが返る()
    {
        // Arrange - 複数フィールドで同時にエラーを発生
        var request = SafeValue() with 
        { 
            Id = null!,                      // IDエラー
            Name = new string('a', 101),     // Name文字数エラー
            Priority = -1,                   // Priority範囲エラー
            Version = null!                  // Versionエラー
        };
        var validator = new UpdateEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert - 複数エラーが発生することを確認
        result.ShouldHaveValidationErrorFor(x => x.Id);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        result.ShouldHaveValidationErrorFor(x => x.Priority);
        result.ShouldHaveValidationErrorFor(x => x.Version);
        
        result.Errors.Should().HaveCount(4);
    }

    /// <summary>
    /// カスタムバリデーションルールのテスト
    /// </summary>
    [Fact]
    public void カスタムルール_業務ロジック検証が正しく動作する()
    {
        // TODO: 実際のカスタムバリデーションルールに合わせて実装してください
        // 例：特定の条件でのみ必須となるフィールド、相関チェックなど
        
        // Arrange
        // var request = SafeValue() with { /* カスタムルールに反する値 */ };
        // var validator = new UpdateEntityValidator();

        // Act
        // var result = validator.TestValidate(request);

        // Assert
        // result.ShouldHaveValidationErrorFor(/* カスタムルール対象フィールド */);
        
        Assert.True(true, "TODO: カスタムバリデーションルールがある場合は実装してください");
    }

    #endregion
}
