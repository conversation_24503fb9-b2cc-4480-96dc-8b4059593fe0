<#
.SYNOPSIS
    プルリクエストの解析結果(全体)をコメントとして投稿します。
.PARAMETER accessToken
    Azure DevOpsのアクセストークン。
.PARAMETER title
    プルリクエスト解析結果(全体)のタイトル。
.RETURNS
    投稿されたコメントのスレッドIDを返します。
#>

param(
    [string]$accessToken,
    [string]$title
)

Import-Module (Join-Path $PSScriptRoot "get_file_change_stats_func.ps1") -Force
Import-Module (Join-Path $PSScriptRoot "get_file_changes_message_func.ps1") -Force

$fileChangeStats = Get-File-Changes-Stats "origin/$env:SYSTEM_PULLREQUEST_TARGETBRANCHNAME"
$fileChangesMessage = Get-File-Changes-Message $fileChangeStats

$collectionUri = "$($env:SYSTEM_TEAMFOUNDATIONCOLLECTIONURI)"
$project = "$($env:SYSTEM_TEAMPROJECT)"
$repository = "$($env:BUILD_REPOSITORY_NAME)"
$pullRequestId = "$($env:SYSTEM_PULLREQUEST_PULLREQUESTID)"

$uri = "$collectionUri$project/_apis/git/repositories/$repository/pullRequests/$pullRequestId/threads?api-version=7.0"

$message = @"
## プルリクエスト解析結果 ($title)
$fileChangesMessage
"@

# JSON形式のリクエストボディを作成
$body = @{
    "comments" = @(
        @{
            "parentCommentId" = 0
            "content"         = $message
            "commentType"     = "text"
        }
    )
    "status"   = 1
} | ConvertTo-Json

# 新しいスレッドにプルリクエスト解析結果コメントを投稿
$response = Invoke-RestMethod -Uri $uri -Method Post -Headers @{
    Authorization = "Bearer $accessToken"
} -Body $body -ContentType "application/json"

Write-Host ($response | ConvertTo-Json)

return $response.id