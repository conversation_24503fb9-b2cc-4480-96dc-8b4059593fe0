import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type {
  ApiResult,
  Pagination,
} from '@hox/base/src/apiclient/shared/types'
import { caseCategoryKeys, type CaseCategoryType } from '@/packages/common-case/src/constants/domain/caseCategory'

// =====================================================================================================================
// APIクライアントの定義(検索)
// =====================================================================================================================

// 検索するデータのスキーマを定義します
export const findCaseByCustomerSchema = z.object({
  id: z.string(),
  customerIdentificationId: z.string().uuid(),
  caseCategory: z.enum(caseCategoryKeys.map(String) as [string, ...string[]]),
  caseName: z.string(),
  caseStatus: z.string().nullish(),
  expiredAt: z.string().datetime().nullish(),
  staffId: z.string().nullish(),
  staffName: z.string().nullish(),
  generalTransactionTypeName: z.string().nullish(),
  registeredAt: z.string().datetime(),
  caseUpdatedAt: z.string().datetime(),
})

// 検索するデータのデータ型を定義します
export type FindCaseByCustomer = z.infer<typeof findCaseByCustomerSchema>

// 検索条件の型を定義します。
export type FindCaseByCustomerCriteria = {
  customerIdentificationId: string
  caseCategories?: Array<CaseCategoryType>
  caseStatuses?: string[]
  fromDate?: Date | null
  toDate?: Date | null
  staffIds?: string[]
  generalTransactionTypeIds?: string[]
} & Pagination

// 検索結果の型を定義します。主として扱うデータの型をOmitやUnionなどで編集してください。
export type FindFindByCustomerResultItem = FindCaseByCustomer
// 検索結果の型にページングの情報を追加します。
export type FindFindByCustomerResult = ApiResult<FindFindByCustomerResultItem>

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindByCustomer(query: Ref<FindCaseByCustomerCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindFindByCustomerResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/case/find-by-customer',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
