{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../src/*", "../src/*"], "@/*": ["../src/*", "../src/*"], "~~/*": ["../*", "../*"], "@@/*": ["../*", "../*"], "nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "vue-router": ["../node_modules/vue-router"], "vue-router/auto-routes": ["../node_modules/vue-router/vue-router-auto-routes"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "~": ["../src"], "@": ["../src"], "~~": ["./.."], "@@": ["./.."], "#shared": ["../shared"], "assets": ["../src/assets"], "public": ["../src/public"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/nuxt/dist/core/runtime/nitro/paths"], "#vue-router": ["./vue-router"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../node_modules/@nuxt/eslint/runtime/server", "../../../../../ibp-case-app/runtime/server", "../node_modules/@nuxt/devtools/runtime/server", "../node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../src/server/**/*"], "exclude": ["../node_modules", "../../../../node_modules", "../node_modules/nuxt/node_modules", "../src/packages/common-case/node_modules", "../node_modules/@ibp/base/node_modules", "../node_modules/@hox/base/node_modules", "../src/packages/issue-project/node_modules", "../src/packages/case-work-flow-optimization/node_modules", "../src/packages/lease/node_modules", "../node_modules/@nuxt/eslint/node_modules", "../node_modules/@nuxt/test-utils/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../dist"]}