import { useNuxtApp } from 'nuxt/app'
import { parseISO } from 'date-fns'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import { isString } from '@hox/base/src/utils/shared/is'
import type {
  ApiResult,
  Pagination,
} from '@hox/base/src/apiclient/shared/types'

// 明細のスキーマ
export const employeeQualificationSchema = z.object({
  id: z.string(),
  name: z.string(),
})
// 明細のデータ型
export type EmployeeQualification = z.infer<typeof employeeQualificationSchema>

// TODO: 主として扱うデータのスキーマを定義します。
export const employeeSchema = z.object({
  id: z.string(),
  // TODO: 必要なプロパティを追加してください。
  // name: z.string().max(30),
  nameKanji: z.string().max(30),
  nameKana: z.string().max(30),
  address: z.string().max(100),
  birthDate: z.date(),
  branchId: z.string(),
  branchName: z.string(),
  qualifications: z.array(employeeQualificationSchema).min(1),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type Employee = z.infer<typeof employeeSchema>

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

// TODO: 検索条件の型を定義します。
export type FindEmployeeCriteria = {
  // TODO: 必要なプロパティを追加してください。
  // name?: string
  name?: string
  address?: string
} & Pagination

// TODO: 検索結果の型を定義します。主として扱うデータの型をOmitやUnionなどで編集してください。
export type FindEmployeeResultItem = Omit<
  Employee,
  'branchId' | 'qualifications'
>
// export type FindEmployeeResultItem = Employee

// 検索結果の型にページングの情報を追加します。
export type FindEmployeeResult = ApiResult<FindEmployeeResultItem>

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindEmployee(query: Ref<FindEmployeeCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindEmployeeResult>(
    useFetch(
      () => $endpoints.default.get('/v1.0/employee'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: FindEmployeeResult) {
          // TODO: データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          if (Array.isArray(data.items)) {
            for (const item of data.items) {
              if (isString(item.birthDate)) {
                item.birthDate = parseISO(item.birthDate)
              }
            }
          }
          return data
        },
      }),
    ),
  )
}

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetEmployee(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Employee>(
    useFetch(
      () => $endpoints.default.get(`/v1.0/employee/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: Employee) {
          // TODO: データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          if (isString(data.birthDate)) {
            data.birthDate = parseISO(data.birthDate)
          }
          return data
        },
      }),
    ),
  )
}

/**
 * 明細を取得する
 * @param id Ref<T> 明細を保持している親のキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetEmployeeQualifications(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<EmployeeQualification[]>(
    useFetch(
      () => $endpoints.default.get(`/v1.0/employee/${id.value}/qualifications`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

export const employeeQualificationSchemaForSave =
  employeeQualificationSchema.merge(
    z.object({
      id: z.string().optional(),
      updated: z.boolean().optional(),
    }),
  )

// 明細は、更新時には全件送信する必要があるため、更新用のプロパティを追加する
export type EmployeeQualificationForSave = z.infer<
  typeof employeeQualificationSchemaForSave
>

// 保存用のデータのスキーマを定義します。
// TODO: 保存時にデータのスキーマが変わる場合は、ここで定義します。
export const employeeSchemaForSave = employeeSchema.merge(
  z.object({
    qualifications: z.array(employeeQualificationSchemaForSave).min(1),
  }),
)

// 保存用の型を作成します。
export type EmployeeForSave = z.infer<typeof employeeSchemaForSave>

// 作成用のデータのスキーマを定義します。
// TODO: 作成時にデータのスキーマが変わる場合は、ここで定義します。
export const employeeSchemaForCreate = employeeSchemaForSave.omit({
  id: true,
  version: true,
})
// 作成用の型を作成します。
export type EmployeeForCreate = z.infer<typeof employeeSchemaForCreate>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostEmployee(body: Ref<EmployeeForCreate>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Employee>(
    useFetch(
      () => $endpoints.default.get('/v1.0/employee'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: Employee) {
          // TODO: データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          if (isString(data.birthDate)) {
            data.birthDate = parseISO(data.birthDate)
          }
          return data
        },
      }),
    ),
  )
}

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutEmployee(body: Ref<EmployeeForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Employee>(
    useFetch(
      () => $endpoints.default.get('/v1.0/employee'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: Employee) {
          // #if(!isInTemplateExpanding || includeSample)
          if (isString(data.birthDate)) {
            data.birthDate = parseISO(data.birthDate)
          }
          // #endif
          return data
        },
      }),
    ),
  )
}

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteEmployee(body: Ref<Pick<Employee, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () => $endpoints.default.get('/v1.0/employee'),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 指定されたデータの一括削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型の配列
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteAllEmployee(
  body: Ref<Pick<Employee, 'id' | 'version'>[]>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () => $endpoints.default.get('/v1.0/employee/:deleteAll'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}
