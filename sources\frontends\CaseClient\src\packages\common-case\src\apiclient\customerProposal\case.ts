import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type {
  ApiResult,
  Pagination,
} from '@hox/base/src/apiclient/shared/types'
import type { CaseCategoryType } from '@ibp/common-case/src/constants/domain/caseCategory'

// =====================================================================================================================
// スキーマと型定義
// =====================================================================================================================

// TODO: 適切なスキーマを定義する必要があります。元の case.js には詳細なスキーマ定義がないため、
// 実際のデータ構造に合わせて適宜調整してください。
export const caseSchema = z.object({
  id: z.string(),
  // TODO: 実際のケースオブジェクトのプロパティを追加
  version: z.string(),
})

export type Case = z.infer<typeof caseSchema>

// 各種ケースタイプのスキーマ
// TODO: 実際のケースタイプに合わせてスキーマを定義してください
export const generalTransactionCaseSchema = caseSchema.extend({
  // TODO: 一般取引ケース特有のプロパティを追加
})

export const newLoanCaseSchema = caseSchema.extend({
  // TODO: 新規ローンケース特有のプロパティを追加
})

export const otherLoanCaseSchema = caseSchema.extend({
  // TODO: その他ローンケース特有のプロパティを追加
})

export const newLeaseCaseSchema = caseSchema.extend({
  // TODO: 新規リースケース特有のプロパティを追加
})

export const externallyCompletedLeaseCaseSchema = caseSchema.extend({
  // TODO: 外部完了リースケース特有のプロパティを追加
})

export const otherLeaseCaseSchema = caseSchema.extend({
  // TODO: その他リースケース特有のプロパティを追加
})

// 各ケースタイプの型定義
export type GeneralTransactionCase = z.infer<typeof generalTransactionCaseSchema>
export type NewLoanCase = z.infer<typeof newLoanCaseSchema>
export type OtherLoanCase = z.infer<typeof otherLoanCaseSchema>
export type NewLeaseCase = z.infer<typeof newLeaseCaseSchema>
export type ExternallyCompletedLeaseCase = z.infer<
  typeof externallyCompletedLeaseCaseSchema
>
export type OtherLeaseCase = z.infer<typeof otherLeaseCaseSchema>

// =====================================================================================================================
// 検索条件と結果の型定義
// =====================================================================================================================

export type FindCaseCriteria = {
  branchNumbers?: string[]
  cifNumber?: string
  customerName?: string
  industryCodes?: string[]
  caseCategories?: Array<CaseCategoryType>
  caseStatuses?: string[]
  fromDate?: Date | null
  toDate?: Date | null
  staffIds?: string[]
  customerStaffIds?: string[]
  isFavorite?: boolean
  generalTransactionTypeIds?: string[]
} & Pagination

export type FindNewLeaseCaseCriteria = {
  branchNumbers?: string[]
  cifNumber?: string | null
  customerName?: string | null
  caseStatuses?: string[]
  leaseStaffIds?: string[]
  staffIds?: string[]
  quotationCreateStaffIds?: string[]
  quotationScrutinizeStaffIds?: string[]
  isFavorite?: boolean
} & Pagination

export type FindLoanAndLeaseCaseCriteria = {
  branchNumbers?: string[]
  cifNumber?: string | null
  customerName?: string | null
  industryCodes?: string[]
  caseCategories?: string[]
  caseStatuses?: string[] // 初期値は外部から設定
  fromDate?: Date | null
  toDate?: Date | null
  loanRatings?: string[]
  transactionPolicies?: string[]
  fromAmount?: number | null
  toAmount?: number | null
  fromInterestRate?: number | null
  toInterestRate?: number | null
  subjectTypes?: []
  useOfFundsTypes?: []
  preConsultationStandardTarget?: string | null
  isFavorite?: boolean | null
  isOnSiteConfirmationNotFinished?: boolean | null
  customerStaffIds: []
  staffIds?: string[]
  segmentIds?: []

} & Pagination

export type FindCaseResultItem = Omit<Case, 'version'>
export type FindCaseResult = ApiResult<FindCaseResultItem>

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

/**
 * ケースデータを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindCase(query: Ref<FindCaseCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindCaseResult>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: FindCaseResult) {
          // TODO: データの変換などを行います
          return data
        },
      }),
    ),
  )
}

/**
 * 新規リースケースデータを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindNewLeaseCase(query: Ref<FindNewLeaseCaseCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindCaseResult>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/new-lease-case'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: FindCaseResult) {
          // TODO: データの変換などを行います
          return data
        },
      }),
    ),
  )
}

/**
 * ローンおよびリースケースデータを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindLoanAndLeaseCase(query: Ref<FindLoanAndLeaseCaseCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindCaseResult>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/loan-and-lease-case'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: FindCaseResult) {
          // TODO: データの変換などを行います
          return data
        },
      }),
    ),
  )
}

/**
 * IDによるケースデータの取得
 * @param id Ref<string> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetCaseById(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Case>(
    useFetch(
      () => $endpoints.default.get(`/ibp-customer-proposal/v1.0/case/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: Case) {
          // TODO: データの変換などを行います
          return data
        },
      }),
    ),
  )
}

/**
 * IDによるケースカテゴリデータの取得
 * @param id Ref<string> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetCaseCategoryById(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<unknown>(
    useFetch(
      () => $endpoints.default.get(`/ibp-customer-proposal/v1.0/case/case-category/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: unknown) {
          return data
        },
      }),
    ),
  )
}

/**
 * IDによる一般取引ケースデータの取得
 * @param id Ref<string> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetGeneralTransactionCaseById(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GeneralTransactionCase>(
    useFetch(
      () => $endpoints.default.get(`/ibp-customer-proposal/v1.0/case/general-transaction-case/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: GeneralTransactionCase) {
          return data
        },
      }),
    ),
  )
}

/**
 * IDによる新規ローンケースデータの取得
 * @param id Ref<string> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetNewLoanCaseById(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<NewLoanCase>(
    useFetch(
      () => $endpoints.default.get(`/ibp-customer-proposal/v1.0/case/new-loan-case/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: NewLoanCase) {
          return data
        },
      }),
    ),
  )
}

/**
 * IDによるその他ローンケースデータの取得
 * @param id Ref<string> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetOtherLoanCaseById(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<OtherLoanCase>(
    useFetch(
      () => $endpoints.default.get(`/ibp-customer-proposal/v1.0/case/other-loan-case/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: OtherLoanCase) {
          return data
        },
      }),
    ),
  )
}

/**
 * IDによる新規リースケースデータの取得
 * @param id Ref<string> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetNewLeaseCaseById(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<NewLeaseCase>(
    useFetch(
      () => $endpoints.default.get(`/ibp-customer-proposal/v1.0/case/new-lease-case/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: NewLeaseCase) {
          return data
        },
      }),
    ),
  )
}

/**
 * IDによる外部完了リースケースデータの取得
 * @param id Ref<string> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetExternallyCompletedLeaseCaseById(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<ExternallyCompletedLeaseCase>(
    useFetch(
      () => $endpoints.default.get(`/ibp-customer-proposal/v1.0/case/externally-completed-lease-case/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: ExternallyCompletedLeaseCase) {
          return data
        },
      }),
    ),
  )
}

/**
 * IDによるその他リースケースデータの取得
 * @param id Ref<string> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetOtherLeaseCaseById(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<OtherLeaseCase>(
    useFetch(
      () => $endpoints.default.get(`/ibp-customer-proposal/v1.0/case/other-lease-case/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: OtherLeaseCase) {
          return data
        },
      }),
    ),
  )
}

/**
 * 顧客によるケースデータの取得
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetCaseByCustomer(query: Ref<unknown>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<unknown>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/find-by-customer'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

// 保存用のスキーマ定義
// TODO: 保存用のスキーマ定義を追加

export type CaseForSave = Case
export type GeneralTransactionCaseForSave = GeneralTransactionCase
export type NewLoanCaseForSave = NewLoanCase
export type OtherLoanCaseForSave = OtherLoanCase
export type NewLeaseCaseForSave = NewLeaseCase
export type ExternallyCompletedLeaseCaseForSave = ExternallyCompletedLeaseCase
export type OtherLeaseCaseForSave = OtherLeaseCase

/**
 * ケースデータの削除
 * @param body Ref<T> 削除対象のデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteCase(body: Ref<Pick<Case, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case'),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * ケースデータの追加
 * @param body Ref<T> 追加するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useAddCase(body: Ref<Omit<Case, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Case>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 一般取引ケースデータの追加
 * @param body Ref<T> 追加するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useAddGeneralTransactionCase(body: Ref<Omit<GeneralTransactionCase, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GeneralTransactionCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/general-transaction-case'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 新規ローンケースデータの追加
 * @param body Ref<T> 追加するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useAddNewLoanCase(body: Ref<Omit<NewLoanCase, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<NewLoanCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/new-loan-case'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * その他ローンケースデータの追加
 * @param body Ref<T> 追加するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useAddOtherLoanCase(body: Ref<Omit<OtherLoanCase, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<OtherLoanCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/other-loan-case'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 新規リースケースデータの追加
 * @param body Ref<T> 追加するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useAddNewLeaseCase(body: Ref<Omit<NewLeaseCase, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<NewLeaseCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/new-lease-case'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 外部完了リースケースデータの追加
 * @param body Ref<T> 追加するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useAddExternallyCompletedLeaseCase(body: Ref<Omit<ExternallyCompletedLeaseCase, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<ExternallyCompletedLeaseCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/externally-completed-lease-case'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * その他リースケースデータの追加
 * @param body Ref<T> 追加するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useAddOtherLeaseCase(body: Ref<Omit<OtherLeaseCase, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<OtherLeaseCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/other-lease-case'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * ケースデータの更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUpdateCase(body: Ref<CaseForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Case>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 一般取引ケースデータの更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUpdateGeneralTransactionCase(body: Ref<GeneralTransactionCaseForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GeneralTransactionCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/general-transaction-case'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 新規ローンケースデータの更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUpdateNewLoanCase(body: Ref<NewLoanCaseForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<NewLoanCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/new-loan-case'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 新規ローン現地現物確認データの更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUpdateNewLoanOnSitePhysicalConfirm(body: Ref<unknown>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<unknown>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/new-loan-case-on-site-physical-confirm'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * その他ローンケースデータの更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUpdateOtherLoanCase(body: Ref<OtherLoanCaseForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<OtherLoanCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/other-loan-case'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 新規リースケースデータの更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUpdateNewLeaseCase(body: Ref<NewLeaseCaseForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<NewLeaseCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/new-lease-case'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 外部完了リースケースデータの更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUpdateExternallyCompletedLeaseCase(body: Ref<ExternallyCompletedLeaseCaseForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<ExternallyCompletedLeaseCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/externally-completed-lease-case'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * その他リースケースデータの更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUpdateOtherLeaseCase(body: Ref<OtherLeaseCaseForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<OtherLeaseCase>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/other-lease-case'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * スタッフの一括更新
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useBatchUpdateStaff(body: Ref<unknown>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<unknown>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/batch-update-staff'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * CSVによるアップロード
 * @param body Ref<T> アップロードするデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUploadByCsv(body: Ref<FormData>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FormData>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/upload-by-csv'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 一括削除
 * @param body Ref<T> 削除するデータの配列
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useBatchDeleteCase(body: Ref<{ caseImportHistoryId: string }>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<unknown>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/case/batch-delete'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}
