// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack/types": [
        "../node_modules/nitropack/types"
      ],
      "nitropack/runtime": [
        "../node_modules/nitropack/runtime"
      ],
      "nitropack": [
        "../node_modules/nitropack"
      ],
      "h3": [
        "../node_modules/h3"
      ],
      "consola": [
        "../node_modules/consola"
      ],
      "ofetch": [
        "../node_modules/ofetch"
      ],
      "@unhead/vue": [
        "../node_modules/@unhead/vue"
      ],
      "@vue/runtime-core": [
        "../node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../node_modules/@vue/compiler-sfc"
      ],
      "vue-router": [
        "../node_modules/vue-router"
      ],
      "vue-router/auto-routes": [
        "../node_modules/vue-router/vue-router-auto-routes"
      ],
      "unplugin-vue-router/client": [
        "../node_modules/unplugin-vue-router/client"
      ],
      "@nuxt/schema": [
        "../node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../node_modules/nuxt"
      ],
      "vite/client": [
        "../node_modules/vite/client"
      ],
      "~": [
        "../src"
      ],
      "~/*": [
        "../src/*"
      ],
      "@": [
        "../src"
      ],
      "@/*": [
        "../src/*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "#shared": [
        "../shared"
      ],
      "assets": [
        "../src/assets"
      ],
      "public": [
        "../src/public"
      ],
      "#app": [
        "../node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "#vue-router": [
        "../node_modules/vue-router"
      ],
      "#imports": [
        "./imports"
      ],
      "#app-manifest": [
        "./manifest/meta/dev"
      ],
      "#components": [
        "./components"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ]
    },
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "module": "preserve",
    "noEmit": true,
    "lib": [
      "ESNext",
      "dom",
      "dom.iterable",
      "webworker"
    ],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": [
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals",
      "vitest/globals"
    ],
    "moduleResolution": "Bundler",
    "useDefineForClassFields": true,
    "noImplicitThis": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "./nuxt.d.ts",
    "../.config/nuxt.*",
    "../**/*",
    "../src/**/*",
    "../node_modules/@ibp/base/src/**/*",
    "../node_modules/@hox/base/src/**/*",
    "../node_modules/@nuxt/eslint/runtime",
    "../node_modules/@nuxt/eslint/dist/runtime",
    "../../../../../ibp-case-app/runtime",
    "../../../../../ibp-case-app/dist/runtime",
    "../node_modules/@nuxt/devtools/runtime",
    "../node_modules/@nuxt/devtools/dist/runtime",
    "../node_modules/@nuxt/telemetry/runtime",
    "../node_modules/@nuxt/telemetry/dist/runtime",
    ".."
  ],
  "exclude": [
    "../dist",
    "../node_modules",
    "../../../../node_modules",
    "../node_modules/nuxt/node_modules",
    "../src/packages/common-case/node_modules",
    "../node_modules/@ibp/base/node_modules",
    "../node_modules/@hox/base/node_modules",
    "../src/packages/issue-project/node_modules",
    "../src/packages/case-work-flow-optimization/node_modules",
    "../src/packages/lease/node_modules",
    "../node_modules/@nuxt/eslint/node_modules",
    "../node_modules/@nuxt/test-utils/node_modules",
    "../node_modules/@nuxt/devtools/node_modules",
    "../node_modules/@nuxt/telemetry/node_modules",
    "../node_modules/@nuxt/eslint/runtime/server",
    "../node_modules/@nuxt/eslint/dist/runtime/server",
    "../../../../../ibp-case-app/runtime/server",
    "../../../../../ibp-case-app/dist/runtime/server",
    "../node_modules/@nuxt/devtools/runtime/server",
    "../node_modules/@nuxt/devtools/dist/runtime/server",
    "../node_modules/@nuxt/telemetry/runtime/server",
    "../node_modules/@nuxt/telemetry/dist/runtime/server",
    "../.output"
  ]
}