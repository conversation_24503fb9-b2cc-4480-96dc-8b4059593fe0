# CaseClient Project Guidelines

This document defines coding guidelines and architecture patterns for the CaseClient application. GitHub Copilot should adhere to these when providing code suggestions.

## Project Overview

CaseClient is a front-end project for a web application that supports various back-office operations related to various cases.
CaseClient is one of multiple frontends in the IBP project, serving as one service. It is built with the Nuxt 3 framework, featuring a modular structure and Azure AD authentication capabilities.


## Architecture
This project uses Nuxt Layers to achieve a modular structure. The application is organized into the following layers:
  - node-modules
- Domain packages (e.g., `issue-project`) - Business packages organized by bounded contexts

## Common Packages
Components shared with other frontends are managed in the following packages, which are maintained in separate repositories:
- @hox/base
- @ibp/base

### Layer Structure and Relationships
The root `nuxt.config.ts` specifies the layers in its `extends` array. Dependencies between layers follow these rules:
- Avoid circular references between packages
- Domain packages (like `issue-project`) should only depend on the @hox/base and @ibp/base packages, not on other domain packages


## Coding Standards

### Code Style and Linting

- Follow the ESLint configuration defined in the project:
  - Use 2 spaces for indentation in all files
  - Use single quotes for strings instead of double quotes
  - Do not use semicolons at the end of statements
  - Use the 1tbs (One True Brace Style) for all code blocks
  - In Vue templates, use double quotes for HTML attributes
  - Always include spaces in mustache interpolation: `{{ value }}`
  - Always use keys with v-for directives
  - Do not use v-if together with v-for on the same element
  - Avoid using console.log statements (use only for temporary debugging)
  - Prefix private class members with underscore (_)

### Date Formatting Standards

- Use date-fns for all date formatting operations

### TypeScript Guidelines

- Use TypeScript for all new code
- Prefer strict typing with interfaces and type definitions
- Use optional chaining (`?.`) and nullish coalescing (`??`) operators when appropriate
- Define model interfaces in a `models` directory within each package
- Use type assertions only when necessary
- Utilize TypeScript's utility types (e.g., `Partial<T>`, `Pick<T>`, `Omit<T>`) when appropriate

### Vue/Nuxt Guidelines

- Use the Composition API with `<script setup lang="ts">`
- Use typed `ref` and `reactive` for state management
- Organize components, composables, and utilities in their respective directories
- Follow the official Nuxt directory structure guidelines
- Use Nuxt's built-in utilities like `useRoute`, `useRouter`, `useState`, etc.
- For complex state management, use composables

### Component Structure

- Keep components small and focused on a single responsibility
- Use props with type definitions and defaults
- Document complex components with comments
- Use the provided UI components from Vuetify (v3.x)
- Follow naming conventions:
  - Components: PascalCase (e.g., `AppSideMenu.vue`)
  - Composables: camelCase with `use` prefix (e.g., `useAuth.ts`)

### API Client and Schema Structure

API clients and schemas should follow these consistent patterns:

3. **Schema Implementation**:
   - Define within apiclient
   - Use Zod for API request/response type definitions
   - Clearly distinguish between entity schemas and search criteria/result schemas
   - Use derived types from base entity schemas (Omit, Pick, etc.)
   - Export type definitions using Zod's `z.infer<typeof schemaName>`

4. **API Client Implementation**:
   - Implement API clients in corresponding `[domain].ts` files
   - Structure API clients as composable functions (e.g. `useGetCustomer`)
   - Ensure proper typing for all API functions
   - Include transform functions to format response data
   - Follow consistent error handling patterns

### API Client Usage in Pages

1. **Import Pattern**:
   - Import API clients directly from their domain packages

   ```typescript
   import { useGetCustomer } from '../../apiclient/customer'
   ```

2. **API Call Pattern**:
   - For dependencies, fetch main data first, then fetch other data in parallel
   - Define query parameters as computed properties
   - Use inProgress flags from API clients to manage UI loading states

3. **Error Handling**:
   - API clients return result objects, so validation of the result should be used instead of relying solely on try-catch blocks
   - Implement error handling for each API call in the following order:
     1. Check if result is null/undefined (API call failure)
     2. Check for the existence of result.error (API-level error response)
     3. Validate the existence and structure of result.data
   - Use the `useAppToasts` composable to display user-friendly error messages
   - Provide detailed messages based on error types
   - Include detailed error information in development environments while showing only user-friendly summaries in production
   - Use consistent error message patterns like "Failed to retrieve [resource] data"
   - Example:
  
     ```typescript
     async function loadEntityData(): Promise<EntityType | null> {
       try {
         const result = await getEntity()
         
         // Check if result doesn't exist (API call failure)
         if (!result) {
           errorToast('Could not retrieve data.')
           return null
         }
         
         // Check for API-level errors
         if (result.error) {
           const errorMessage = 
             result.error.message || 'An error occurred while retrieving data.'
           errorToast(`Failed to retrieve data: ${errorMessage}`)
           return null
         }
         
         return result.data || null
       } catch (error) {
         // Fallback for unexpected errors (network errors, etc.)
         let errorMessage = 'An error occurred while retrieving data.'
         
         // Messages based on error type
         if (error instanceof TypeError) {
           errorMessage = 'Invalid data format.'
         } else if (error instanceof Error && error.message) {
           // Include detailed error message only in development environment
           if (process.env.NODE_ENV === 'development') {
             errorMessage += ` (${error.message})`
           }
         }
         
         errorToast(errorMessage)
         return null
       }
     }
     ```

### Edit/Detail Page Structure

When implementing object reference or edit pages, follow these patterns:

1. **Page Organization**:
   - Use the `app-edit-page-tmpl` component as the main container
   - Organize sections using the `sections` array with `key` and `title` properties
   - Use `app-edit-item-group-tmpl` components to group related fields within sections
   - Include appropriate user role checks for data visibility

2. **Data Loading Pattern**:
   - Define async data loading functions at the component's setup level
   - Avoid using `onMounted` lifecycle hooks for initial data loading
   - Instead, create separate async functions and await them directly at the top level of the setup function
   - Example:

     ```typescript
     // Define loading functions
     async function loadEntityData(): Promise<EntityType | null> {
       try {
         const result = await getEntity()
         return result?.data || null
       } catch (error) {
         console.error('Error loading entity data:', error)
         return null
       }
     }

     // Execute at setup level (not in onMounted)
     await dataLoad()
     ```

3. **Loading State Management**:
   - Use the `useFlagCondition` composable for managing multiple loading states
   - Set individual loading flags before API calls and clear them after completion
   - Pass loading flags to the page template for proper loading indication
   - Always handle loading states in both success and error scenarios with try/finally

4. **URL Change Detection**:
   - Use `useUrlChangeDetector` composable to reload data when relevant URL parameters change
   - Implement a dedicated reload handler to reset data and loading states

5. **Data Formatting**:
   - Use computed properties for derived display values

6. **UI Patterns**:
   - Use read-only form fields for detail views
   - Use consistent layout for form fields and data tables
   - Implement proper row click handlers for navigating to related entities
   - Add proper type annotations for event handlers

7. **Error Handling**:
   - Use `useAppToasts` composable for user-facing error messages
   - Include proper error logging with context information
   - Add fallback UI for missing or failed data

8. **Role-Based Access Control**:
   - Use checkAccessible middleware from @ibp/base

## Styling Standards

- Use Vuetify 3.x for UI components
- Follow the existing design patterns for consistent UI
- Use SCSS for custom styling when needed
- Maintain responsive design principles

## Dependencies

- Packages used by multiple or all layers should be installed at the root level
- Packages used by a single layer should be installed within that layer
- Install packages with explicit versions to maintain consistency

## Performance Considerations

- Optimize component rendering to prevent unnecessary re-renders
- Lazy load routes and components where appropriate
- Follow Vue best practices for performance optimization

## Test Standards

- Write unit tests using Vitest
- Test components, composables, and utilities
- Maintain good test coverage for critical functionality
- Mock API calls and dependencies in tests

## Documentation

- Add comments for complex logic
- Document functions and components with JSDoc style comments
- Keep inline documentation up-to-date when modifying code

## Error Handling

- Handle errors gracefully with appropriate error messages
- Use try/catch blocks for async operations
- Implement proper error boundaries in components
- Provide user-friendly error messages

## Security

- Never store sensitive information in client-side code
- Use the authentication mechanisms provided
- Validate input data before submission
- Follow security best practices for web applications
