import { z } from 'zod'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import { isUnusable } from '@hox/base/src/utils/shared/is'
import type { Ref } from 'vue'

const NO_SEARCH_CONDITION_MESSAGE = '検索条件を一つ以上入力してください。'

/**
 * ケース検索条件バリデーション
 */
export const useCaseValidation = (searchCondition: Ref<any>) => {
  /**
   * 検索条件が入力されているかチェック
   */
  const hasSearchCondition = () => {
    const condition = searchCondition.value

    // 配列型の条件をチェックするヘルパー関数
    const hasArrayValue = (arr: any[] | null | undefined) =>
      !isUnusable(arr) && arr!.length > 0

    // 文字列型の条件をチェックするヘルパー関数
    const hasStringValue = (str: string | null | undefined) =>
      !isUnusable(str) && str !== '' && str!.trim() !== ''

    // 各検索条件をチェック
    return (
      hasArrayValue(condition.branchNumbers) ||
      hasStringValue(condition.cifNumber) ||
      hasStringValue(condition.customerName) ||
      hasArrayValue(condition.industryCodes) ||
      hasArrayValue(condition.caseCategories) ||
      hasArrayValue(condition.caseStatuses) ||
      hasArrayValue(condition.staffIds) ||
      hasArrayValue(condition.customerStaffIds) ||
      hasArrayValue(condition.generalTransactionTypeIds) ||
      hasStringValue(condition.fromDate) ||
      hasStringValue(condition.toDate) ||
      condition.isFavorite === true
    )
  }
  /**
   * 検索条件バリデーション用のリファイン関数
   * 検索条件がある場合はエラーメッセージを初期化する
   */
  const refineSearchCondition = (fieldName: string) => {
    const hasCondition = hasSearchCondition()
    if (hasCondition && errorMessages.value) {
      // 検索条件が存在する場合、該当フィールドのエラーメッセージを初期化
      errorMessages.value[fieldName] = []
    }
    return hasCondition
  }

  // バリデーションスキーマ定義
  const { errorMessages, validate, validateItem } = useValidation(
    z.object({
      // 店番
      branchNumbers: z
        .array(z.string())
        .nullish()
        .refine(() => refineSearchCondition('branchNumbers'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // CIF番号
      cifNumber: z
        .string()
        .max(8)
        .nullish()
        .refine(() => refineSearchCondition('cifNumber'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // 氏名
      customerName: z
        .string()
        .nullish()
        .refine(() => refineSearchCondition('customerName'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // 業種
      industryCodes: z
        .array(z.string())
        .nullish()
        .refine(() => refineSearchCondition('industryCodes'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // カテゴリ
      caseCategories: z
        .array(z.string())
        .nullish()
        .refine(() => refineSearchCondition('caseCategories'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // ステータス
      caseStatuses: z
        .array(z.string())
        .nullish()
        .refine(() => refineSearchCondition('caseStatuses'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // 案件担当者
      staffIds: z
        .array(z.string())
        .nullish()
        .refine(() => refineSearchCondition('staffIds'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // 顧客担当者
      customerStaffIds: z
        .array(z.string())
        .nullish()
        .refine(() => refineSearchCondition('customerStaffIds'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // お気に入り
      isFavorite: z
        .boolean()
        .nullish()
        .refine(() => refineSearchCondition('isFavorite'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // 案件項目
      generalTransactionTypeIds: z
        .array(z.string())
        .nullish()
        .refine(() => refineSearchCondition('generalTransactionTypeIds'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // 期日From
      fromDate: z
        .date()
        .nullish()
        .refine(() => refineSearchCondition('fromDate'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
      // 期日To
      toDate: z
        .date()
        .nullish()
        .refine(() => refineSearchCondition('toDate'), {
          message: NO_SEARCH_CONDITION_MESSAGE,
        }),
    }),
    searchCondition,
  )
  /**
   * 全てのバリデーションを実行
   * @returns バリデーション結果 (成功: true, 失敗: false)
   */
  const validateAll = async () => {
    const { success } = await validate()
    return success
  }

  return {
    errorMessages,
    validate,
    validateItem,
    validateAll,
    hasSearchCondition,
  }
}
