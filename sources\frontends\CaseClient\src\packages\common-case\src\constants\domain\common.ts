﻿import { reactive } from 'vue'

/** 格付 */
export const loanRatings = reactive([
  { title: 'A1：正常先', value: 'A1' },
  { title: 'A2：正常先', value: 'A2' },
  { title: 'A3：正常先', value: 'A3' },
  { title: 'A4：正常先', value: 'A4' },
  { title: 'A5：正常先', value: 'A5' },
  { title: 'A9：正常先', value: 'A9' },
  { title: 'B1：要注意先', value: 'B1' },
  { title: 'B2：要注意先', value: 'B2' },
  { title: 'B3：要注意先', value: 'B3' },
  { title: 'B9：要注意先', value: 'B9' },
  { title: 'C：破綻懸念先', value: 'C' },
  { title: 'D：実質破綻先', value: 'D' },
  { title: 'E：破綻先', value: 'E' },
])

/** 取引方針 */
export const transactionPolicies = reactive([
  { title: 'T1：積極関与', value: 'T1' },
  { title: 'T2：柔軟対応', value: 'T2' },
  { title: 'T3：経営強化', value: 'T3' },
  { title: 'T4：現状維持', value: 'T4' },
  { title: 'T5：個別協議(含む撤退)', value: 'T5' },
])
