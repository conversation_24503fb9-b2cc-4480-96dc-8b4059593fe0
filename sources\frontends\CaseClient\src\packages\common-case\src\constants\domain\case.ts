/**
 * 案件に関する定数
 */
import { reactive } from 'vue'

// 条件登録につながる融資案件カテゴリー
export const FUNDING_SUPPORT_CASE_CATEGORIES = reactive([
  {
    value: 'DeedLoan',
    title: '融資案件（証書貸付）',
  },
  {
    value: 'CommercialBill',
    title: '融資案件（商業手形）',
  },
  {
    value: 'AcceptanceAndGuarantee',
    title: '融資案件（支払承諾）',
  },
  {
    value: 'ForeignExchangeTransaction',
    title: '融資案件（外為取引）',
  },
  {
    value: 'CreditLimit',
    title: '融資案件（極度）',
  },
  {
    value: 'LoanRestructuring',
    title: '融資案件（条件変更）',
  },
  {
    value: 'OtherApproval',
    title: '融資案件（その他稟議）',
  },
])

/** 融資、リースの案件カテゴリー */
export const LEASE_LOAN_CASE_CATEGORIES = reactive([
  ...FUNDING_SUPPORT_CASE_CATEGORIES,
  {
    value: 'NewLoan',
    title: '融資案件（新規）',
  },
  {
    value: 'OtherLoan',
    title: '融資案件（その他）',
  },
  {
    value: 'UnorganizedLoan',
    title: '融資案件（未整理）',
  },
  {
    value: 'NewLease',
    title: 'リース案件（新規）',
  },
  {
    value: 'ExternallyCompletedLease',
    title: 'リース案件（他社満了）',
  },
])

export const CASE_CATEGORIES = reactive([
  {
    value: 'IssueProject',
    title: '課題案件',
  },
  ...LEASE_LOAN_CASE_CATEGORIES,
  {
    value: 'CorporateLifeInsurance',
    title: '法人保険案件（生保）',
  },
  {
    value: 'CorporateNonlifeInsurance',
    title: '法人保険案件（損保）',
  },
  {
    value: 'NewInvestment',
    title: '投資案件（新規）',
  },
  {
    value: 'BusinessMatchingBuy',
    title: 'ビジネスマッチング(買)',
  }, {
    value: 'BusinessMatchingSell',
    title: 'ビジネスマッチング(売)',
  },
  {
    value: 'MAndAMatchingBuy',
    title: 'M&Aマッチング(買)',
  },
  {
    value: 'MAndAMatchingSell',
    title: 'M&Aマッチング(売)',
  },
  {
    value: 'GeneralTransaction',
    title: '総合取引案件',
  },
])

/**
 * 案件に関する定数短縮版
 */
/** 融資、リースの案件カテゴリー */
export const LEASE_LOAN_CASE_SHORT_CATEGORIES = reactive([
  {
    value: 'NewLoan',
    title: '融資（新規）',
  },
  {
    value: 'OtherLoan',
    title: '融資（その他）',
  },
  {
    value: 'UnorganizedLoan',
    title: '融資（未整理）',
  },
  {
    value: 'NewLease',
    title: 'リース（新規）',
  },
  {
    value: 'ExternallyCompletedLease',
    title: 'リース（他社満了）',
  },
])

export const CASE_SHORT_CATEGORIES = reactive([
  {
    value: 'IssueProject',
    title: '課題',
  },
  ...LEASE_LOAN_CASE_SHORT_CATEGORIES,
  {
    value: 'GeneralTransaction',
    title: '総合取引',
  },
])

export const CASE_STATUSES = reactive([
  {
    value: 'BeforeProposal',
    title: '提案前',
  },
  {
    value: 'UnderProposal',
    title: '提案中',
  },
  {
    value: 'Accepted',
    title: '応諾済み',
  },
  {
    value: 'Finished',
    title: '完了',
  },
  {
    value: 'CancelBeforeProposal',
    title: '提案前取下',
  },
  {
    value: 'CancelAfterProposal',
    title: '提案後失注',
  },
  {
    value: 'CancelWithOurCircumstance',
    title: '謝絶',
  },
])

// 検索用のステータス(CaseStatusとIssueStatusの混合)
export const SEARCH_CASE_STATUSES = reactive([
  {
    value: 'BeforeProposal',
    title: '提案前',
  },
  {
    value: 'UnderProposal',
    title: '提案中',
  },
  {
    value: 'Accepted',
    title: '応諾済み',
  },
  {
    value: 'UnderConsulting',
    title: 'コンサル中',
  },
  {
    value: 'Finished',
    title: '完了',
  },
  {
    value: 'CancelBeforeProposal',
    title: '提案前取下',
  },
  {
    value: 'CancelAfterProposal',
    title: '提案後失注',
  },
  {
    value: 'CancelWithOurCircumstance',
    title: '謝絶',
  },
  {
    value: 'NULL',
    title: 'ステータス未入力',
  },
])

/** 案件取込状態 */
export const CSV_IMPORT_PROCESS_STATUSES = reactive([
  {
    value: 'InProgress',
    title: '処理中',
  },
  {
    value: 'Succeeded',
    title: '成功',
  },
  {
    value: 'Failed',
    title: '失敗',
  },
  {
    value: 'Deleted',
    title: '削除済',
  },
])

/** 案件一覧の検索初期ステータス */
export const DEFAULT_SELECTED_CASE_STATUSES = reactive([
  'BeforeProposal',
  'UnderProposal',
  'Accepted',
])

/** 課題案件にも対応した、案件一覧の検索初期ステータス */
export const DEFAULT_SELECTED_CASE_STATUSES_WITH_ISSUE_PROJECT = reactive([
  ...DEFAULT_SELECTED_CASE_STATUSES,
  'UnderConsulting',
  'NULL',
])

export const CANCEL_CASE_STATUSES = reactive([
  'CancelBeforeProposal',
  'CancelAfterProposal',
  'CancelWithOurCircumstance',
])

export const CUSTOMER_CANCELLED_CASE_STATUSES = reactive([
  'CancelBeforeProposal',
  'CancelAfterProposal',
])

/** 行動記録で"完了"状態と判定される案件ステータス */
export const COMPLETE_CASE_STATUS_OF_INTERACTION = reactive([
  'Finished',
  'CancelBeforeProposal',
  'CancelAfterProposal',
  'CancelWithOurCircumstance',
])

/** 科目 */
export const SUBJECT_TYPES = reactive([
  {
    value: 'DeedLoanByOurCompany',
    title: '証貸(プロパー）',
  },
  {
    value: 'DeedLoanByGuaranteeCorporation',
    title: '証貸（協会）',
  },
  {
    value: 'Overdraft',
    title: '当座貸越',
  },
  {
    value: 'BillDiscount',
    title: '商手・でんさい割引',
  },
  {
    value: 'PaymentAuthorization',
    title: '支払承諾',
  },
  {
    value: 'Other',
    title: 'その他',
  },
])

/** 資金使途 */
export const USE_OF_FUNDS_TYPES = reactive([
  {
    value: 'WorkingCapital',
    title: '運転資金',
  },
  {
    value: 'CapitalExpenditure',
    title: '設備資金',
  },
  {
    value: 'Other',
    title: 'その他',
  },
])

/** 返済方法 */
export const REPAYMENT_METHOD_TYPES = reactive([
  {
    value: 'LumpSum',
    title: '期日一括',
  },
  {
    value: 'Installments',
    title: '分割',
  },
  {
    value: 'Other',
    title: 'その他',
  },
])

/** 流入経路 */
export const TRAFFIC_SOURCES = reactive([
  {
    value: 'FieldSales',
    title: '対面（FS）',
  },
  {
    value: 'Forms',
    title: 'Forms',
  },
  {
    value: 'InsideSales',
    title: '受電（IS）・メール',
  },
  {
    value: 'OtherDepartment',
    title: '他部署連携',
  },
])

/** 協議スレッド目的 */
export const PURPOSE = reactive([
  {
    value: 'Internal',
    title: '社内協議',
  },
  {
    value: 'External',
    title: '社外協議',
  },
])

/** 不成約区分 */
export const CANCEL_TYPES = reactive([
  {
    value: 'SelfFinancing',
    title: '自己資金対応',
  },
  {
    value: 'PostponementOrCancell',
    title: '設備投資延期/中止',
  },
  {
    value: 'LeaseByOther',
    title: '他社リース',
  },
  {
    value: 'FinancingByOurCompany',
    title: '融資対応(当社）',
  },
  {
    value: 'FinancingByOther',
    title: '融資対応（他社）',
  },
  {
    value: 'Other',
    title: 'その他',
  },
])

/** 不成約区分(融資) */
export const CANCEL_TYPES_OF_LOAN = reactive([
  {
    value: 'SelfFinancing',
    title: '自己資金',
  },
  {
    value: 'LoanFromOtherBanksByRelationships',
    title: '他行調達（リレーション）',
  },
  {
    value: 'LoanFromOtherBanksByInterestRates',
    title: '他行調達（金利）',
  },
  {
    value: 'LoanFromOtherBanksByOther',
    title: '他行調達（他条件：詳細は取下・失注・謝絶理由欄に記載）',
  },
  {
    value: 'LeaseByOurCompany',
    title: '当社グループリース',
  },
  {
    value: 'LeaseByOther',
    title: '他社リース',
  },
  {
    value: 'PostponementOrCancell',
    title: '設備投資延期/中止',
  },
  {
    value: 'Other',
    title: 'その他',
  },
])

/** 設置場所 */
export const INSTALLATION_LOCATIONS = reactive([
  {
    value: 'Office',
    title: '本社',
  },
  {
    value: 'Other',
    title: 'その他',
  },
])

/** リース案件区分 */
export const LEASE_CASE_TYPES = reactive([
  {
    value: 'FromBank',
    title: '銀行→総合リース',
  },
  {
    value: 'OnlyLease',
    title: '総合リース単独',
  },
])

/** リース状況 */
export const LEASE_SITUATIONS = reactive([
  {
    value: 'SmallAmount',
    title: '少額案件応諾済',
  },
  {
    value: 'SwitchFromOther',
    title: '他社満了からの切替案件',
  },
])

/** 支払いサイクル */
export const PAYMENT_CYCLES = reactive([
  {
    value: 'EndOfNextMonth',
    title: '検収翌月末',
  },
  {
    value: 'Shortest',
    title: '最短支払',
  },
  {
    value: 'Special',
    title: '手形支払等（特殊）',
  },
])

/** 物件種類 */
export const PROPERTY_CATEGORIES = reactive([
  {
    value: 'General',
    title: '一般',
  },
  {
    value: 'Car',
    title: '自動車',
  },
])

/** 物件契約種別 */
export const PROPERTY_CONTRACT_TYPES = reactive([
  {
    value: 'ResidualValueSetting',
    title: '残価設定',
  },
  {
    value: 'Maintenance',
    title: 'メンテナンス',
  },
  {
    value: 'DeferredPayment',
    title: '延払（割賦）',
  },
  {
    value: 'Subsidy',
    title: '補助金関連',
  },
  {
    value: 'TransferOfOwnership',
    title: '所有権移転',
  },
])

/** 物件状態 */
export const PROPERTY_STATUSES = reactive([
  {
    value: 'New',
    title: '新品',
  },
  {
    value: 'Used',
    title: '中古',
  },
])

/** 見積調達 */
export const QUOTATION_PROCUREMENTS = reactive([
  {
    value: 'ByUser',
    title: 'ユーザ調達',
  },
  {
    value: 'ByOurCompany',
    title: '自社調達',
  },
])

/** 残価設定 */
export const RESIDUAL_VALUE_SETTINGS = reactive([
  {
    value: 'None',
    title: 'なし（フルペイ扱い）',
  },
  {
    value: 'AppraisedValue',
    title: 'オープン（査定額と約同額）',
  },
  {
    value: 'AboutFive',
    title: 'オープン（約５％）',
  },
  {
    value: 'Close',
    title: 'クローズ（車両のみ選択可）',
  },
])

/** 作業状況 */
export const WORK_STATUSES = reactive([
  {
    value: 'InProgress',
    title: '作業中',
  },
  {
    value: 'Complete',
    title: '完了',
  },
])

export const PROPOSAL_RESULTS = reactive([
  {
    value: 'Acceptance',
    title: '応諾',
  },
  {
    value: 'LostOrder',
    title: '失注',
  },
])

/** 相談票進捗 */
export const CONSULTATION_STATUSES = reactive([
  {
    value: 'InApplication',
    title: '申請中',
  },
  {
    value: 'Approval',
    title: '承認',
  },
])

// 型定義とキー配列
export type CaseStatusesType = keyof typeof SEARCH_CASE_STATUSES

export const CASE_STATUSES_KEYS = Object.keys(SEARCH_CASE_STATUSES) as CaseStatusesType[]

export type LeaseLoanCaseShortCategoriesType =
  | 'NewLoan'
  | 'OtherLoan'
  | 'UnorganizedLoan'
  | 'NewLease'
  | 'ExternallyCompletedLease'

export const LEASE_LOAN_CASE_SHORT_CATEGORIES_KEYS =
  LEASE_LOAN_CASE_SHORT_CATEGORIES.map(
    (item) => item.value,
  ) as LeaseLoanCaseShortCategoriesType[]

export type CaseShortCategoriesType =
  | 'IssueProject'
  | 'NewLoan'
  | 'OtherLoan'
  | 'UnorganizedLoan'
  | 'NewLease'
  | 'ExternallyCompletedLease'
  | 'GeneralTransaction'

export const CASE_SHORT_CATEGORIES_KEYS = CASE_SHORT_CATEGORIES.map(
  (item) => item.value,
) as CaseShortCategoriesType[]
