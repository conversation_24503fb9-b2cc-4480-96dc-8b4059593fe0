<template>
  <v-container>
    <div v-if="threadDataRef.loading">
      <v-skeleton-loader v-for="n of 3" :key="n" type="article" />
    </div>

    <div v-else>
      <v-hover v-slot="{ isHovering, props }">
        <v-card
          :class="{ 'highlighted-card': dataRef.isHighlighted }"
          class="custom-outlined-card"
          :elevation="isHovering ? 16 : 2"
          v-bind="props"
          variant="outlined"
        >
          <v-card v-if="isHovering" class="icon-container">
            <v-row>
              <!-- 削除アイコン -->
              <v-col
                v-if="showHover(isHovering, definedProps.type)"
                cols="auto"
              >
                <v-icon
                  title="削除"
                  icon="mdi-delete"
                  color="red"
                  :class="{ icon: !canDeleteThread, nodelete: canDeleteThread }"
                  @click="canDeleteThread ? undefined : removeThread()"
                />
              </v-col>
              <!-- 編集アイコン -->
              <v-col
                v-if="showHover(isHovering, definedProps.type)"
                cols="auto"
                @click="openThreadEditor"
              >
                <v-icon title="編集" icon="mdi-pencil" class="icon" />
              </v-col>
              <!-- リンクコピーアイコン -->
              <v-col>
                <v-icon
                  title="スレッドのリンクをコピー"
                  icon="mdi-link"
                  class="icon"
                  @click="copyThreadLink"
                />
              </v-col>
              <!-- リアクションアイコン -->
              <v-col
                v-for="reactionType in reactionTypes"
                :key="reactionType"
                cols="auto"
              >
                <v-icon
                  v-if="reactionType !== 4"
                  :title="getReactionDisplayName(reactionType)"
                  :color="getReactionIcon(reactionType)?.color"
                  :icon="getReactionIcon(reactionType)?.icon"
                  :class="{
                    iconborder: dataRef.selectedReactionType === reactionType,
                  }"
                  class="pr-1 icon"
                  @click="saveReaction(reactionType)"
                />
                <font-awesome-icon
                  v-if="reactionType === 4"
                  :title="getReactionDisplayName(reactionType)"
                  :color="getReactionIcon(reactionType)?.color"
                  :icon="getReactionIcon(reactionType)?.icon"
                  class="font-awesome-icon"
                  :class="{
                    iconborder: dataRef.selectedReactionType === reactionType,
                  }"
                  @click="saveReaction(reactionType)"
                />
              </v-col>
            </v-row>
          </v-card>
          <v-card-title class="thread-title">
            {{ definedProps.thread.title }}
          </v-card-title>

          <div
            ref="threadBody"
            class="py-1 px-3"
            :class="{ 'hidden-text-container': !dataRef.shownDetail }"
          >
            <v-row dense>
              <v-col cols="auto" class="d-flex align-center">
                <!-- 登録者・登録日時 -->
                <div ref="creatorDiv">
                  <span class="text-subtitle-1 pr-1">
                    {{ definedProps.thread.registrant }}
                  </span>
                  <span class="pl-1 text-subtitle-2 time-color">
                    {{
                      format(
                        definedProps.thread.registeredDateTime,
                        'yyyy/MM/dd HH:mm',
                      )
                    }}
                  </span>
                </div>

                <!-- リアクション -->
                <ReactionView
                  :reactions="definedProps.thread.reactions"
                  :type="definedProps.type"
                  @show-reaction-members="showReactionMembers"
                  @update-reaction="updateReaction"
                  @add-reaction="addReaction"
                  @delete-reaction="deleteReaction"
                />

                <!-- リアクションしたメンバーの表示 -->
                <div ref="reactionMemberDiv" class="reactionMember">
                  <ReactionMember
                    v-if="dataRef.reactionTypeOnHover > 0"
                    :reactions="definedProps.thread.reactions!"
                    :reaction-type="dataRef.reactionTypeOnHover"
                  />
                </div>
              </v-col>
            </v-row>

            <v-row
              v-if="mentionTargets.length > 0"
              dense
              align="center"
              class="pb-1"
            >
              <span class="text-subtitle-2 ml-2"> To: </span>
              <span
                v-for="(mentionTarget, mentionIndex) in mentionTargets"
                :key="mentionIndex"
                class="text-subtitle-2 ml-1"
              >
                <v-chip
                  size="small"
                  :color="
                    mentionTarget.mentionType === 'staff'
                      ? 'light-green lighten-4'
                      : 'blue lighten-4'
                  "
                  :ripple="false"
                >
                  <span class="mention-target">
                    {{ mentionTarget.value }}
                  </span>
                </v-chip>
              </span>
            </v-row>

            <!-- スレッド内容 -->
            <v-row class="my-0 py-0">
              <v-col class="my-0 py-0 text-subtitle-1">
                <div>
                  <p class="mt-1 mb-0">【目的】{{ purposeText }}</p>
                  <p
                    v-if="definedProps.thread.person || isExternal"
                    class="mt-1 mb-0"
                  >
                    【相手】{{ definedProps.thread.person }}
                  </p>
                  <p
                    v-if="definedProps.thread.isPersonOfPower"
                    class="mt-1 mb-0"
                  >
                    【実権者】☑
                  </p>
                </div>
              </v-col>
            </v-row>

            <!-- 協議内容 -->
            <v-row
              v-for="description in displayDescriptions"
              :key="description"
              dense
            >
              <v-col>
                <span style="white-space: pre-wrap">{{ description }}</span>
              </v-col>
            </v-row>
          </div>

          <!-- 詳細表示・簡易表示 -->
          <div v-if="dataRef.shownDetailButton">
            <v-btn
              variant="text"
              size="x-small"
              color="primary"
              @click="toggleShownDetailState"
            >
              {{ dataRef.shownDetail ? '簡易表示' : '詳細表示' }}
            </v-btn>
          </div>

          <div v-if="definedProps.thread.files">
            <!-- 添付ファイル -->
            <div
              v-for="file in definedProps.thread.files"
              :key="file.fileName"
              style="display: inline"
            >
              <v-chip
                color="green"
                label
                class="me-2 mb-3 ms-3 py-2 file-border file-chip"
                @click="downloadThreadFile(file.fileName)"
              >
                <v-icon color="#6495ef" class="mr-2"> mdi-download </v-icon>
                <span class="file-name"> {{ file.fileName }} </span>
              </v-chip>
            </div>
          </div>
        </v-card>
      </v-hover>

      <!-- 全件表示・折りたたみ切り替えボタン -->
      <div v-if="commentsWithoutLatest.length > 0">
        <div v-if="commentDataReactive.shownComments">
          <div ref="closeCommentsButton" @click="toggleShownCommentsState">
            <v-icon
              color="blue darken-1"
            >
              mdi-minus-box-outline
            </v-icon>
            <span class="text-caption">
              コメントを折りたたむ
            </span>
          </div>
        </div>
        <div v-else>
          <div @click="toggleShownCommentsState">
            <v-icon
              color="blue darken-1"
            >
              mdi-plus-box-outline
            </v-icon>
            <span class="text-caption" >
              コメントを全件表示する
            </span>
          </div>
        </div>
      </div>

      <!-- 返信コメント -->
      <div
        v-if="
          commentDataReactive.shownComments && commentsWithoutLatest.length > 0
        "
      >
        <!-- 返信コメント全件表示状態 -->
        <IssueProjectDiscussionComment
          v-for="comment in commentsWithoutLatest"
          :id="`comment-${comment.id}`"
          ref="issueProjectDiscussionComments"
          :key="comment.id"
          class="comments"
          :comment="comment"
          :thread-id="thread.id"
          :type="definedProps.type"
          @add-comment-reaction="addCommentReaction"
          @update-comment-reaction="updateCommentReaction"
          @delete-comment-reaction="deleteCommentReaction"
          @remove-comment="removeComment"
          @download="downloadCommentFile"
          @update-comment="updateComment"
        />
      </div>

      <div v-if="latestComment">
        <!-- 返信コメント折りたたみ状態 -->
        <IssueProjectDiscussionComment
          :id="`comment-${latestComment.id}`"
          ref="latestCommentRef"
          class="comments"
          :loading="threadDataRef.loading"
          :comment="latestComment"
          :thread-id="thread.id"
          :type="type"
          @update-comment="updateComment"
          @remove-comment="removeComment"
          @download="downloadCommentFile"
          @add-comment-reaction="addCommentReaction"
          @update-comment-reaction="updateCommentReaction"
          @delete-comment-reaction="deleteCommentReaction"
        />
      </div>

      <!-- 返信ボタン -->
      <AppBtn
        v-if="type === 'customer-identified'"
        class="my-0 py-0"
        color="white"
        :disabled="!authUserId"
        @click="openCommentEditor()"
      >
        <v-icon class="text-subtitle-1"> mdi-keyboard-return </v-icon>
        <span class="text-caption"> 返信 </span>
      </AppBtn>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { format } from 'date-fns'
import { ref, onMounted, computed } from 'vue'
import { REACTION_TYPE } from '@ibp/issue-project/src/constants/domain/reactionType'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type {
  FindIssueProjectDiscussionThreadResultItem,
  issueProjectDiscussionThreadReactionType,
  issueProjectDiscussionCommentReactionType,
  issueProjectDiscussionCommentType,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useMycareer } from '@ibp/common-case/src/composables/shared/useMycareer'
import type { IssueProjectDiscussionThreadReactionForDelete } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThreadReaction'
import ReactionMember from '@ibp/issue-project/src/components/shared/molecules/Reaction/ReactionMember.vue'
import { useCommentShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/useCommentShareData'
import { useGetThreadComments } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/thread/useGetThreadComments'
import type { IssueProjectDiscussionCommentReactionForDelete } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import { useThreadShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/thread/useThreadShareData'
import IssueProjectDiscussionComment from '@ibp/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionComment.vue'

const { threadDataRef } = useThreadShareData()
const { $auth } = useNuxtApp()
const userId = $auth.getUser()?.userId

const dataRef = ref<{
  isHighlighted: boolean // リアクションしたメンバーを表示する位置のオフセット
  shownDetail: boolean // 詳細を表示しているか
  shownDetailButton: boolean // 詳細表示(簡易表示)ボタンを表示するか
  reactionMemberOffsetY: number // リアクションしたメンバーを表示する位置のオフセット
  reactionTypeOnHover: number // マウスホバーしたときのリアクションタイプ
  selectedReactionType: number | undefined // 選択されたリアクションタイプを追跡
}>({
  isHighlighted: false,
  shownDetail: false,
  shownDetailButton: false,
  reactionMemberOffsetY: 60,
  reactionTypeOnHover: 0,
  selectedReactionType: undefined,
})

const { error: errorToast, success: successToast } = useAppToasts()

// props
const definedProps = defineProps<{
  type: string
  thread: FindIssueProjectDiscussionThreadResultItem
}>()

// emit
const emit = defineEmits([
  'download-thread-file',
  'add-thread-reaction',
  'update-thread-reaction',
  'delete-thread-reaction',
  'remove-thread',
  'update-thread',
  'add-comment-reaction',
  'update-comment-reaction',
  'delete-comment-reaction',
  'remove-comment',
  'add-comment',
  'download-comment-file',
  'update-comment',
])

// threadBodyの参照を保持
const threadBody = ref<HTMLDivElement | null>(null)

onMounted(() => {
  setShownDetailButton()

  // 最後のコメントは簡易表示にする
  nextTick(async () => {
    while (!threadBody.value) {
      await new Promise((resolve) => setTimeout(resolve, 100)) // 100ミリ秒待機
    }
    changeLatestCommentShownDetailState(false)
  })

  // 自分が押したリアクションの判別
  const reaction = definedProps.thread.reactions?.find(
    (x) => x.staffId === userId,
  )
  dataRef.value.selectedReactionType = reaction?.reactionType
})

// =====================================================================================================================
// リアクション関連
// =====================================================================================================================
const reactionTypes = computed(() => {
  return definedProps.type !== 'cross-customer'
    ? {
        good: 1,
        congratulations: 2,
        ok: 3,
        surprise: 4,
      }
    : null
})

const getReactionDisplayName = (reactionType: number) => {
  const displayNames = [
    '', // インデックス0は空文字
    REACTION_TYPE[1], // いいね・確認しました
    REACTION_TYPE[2], // おめでとう・契約お疲れ様
    REACTION_TYPE[3], // ok
    REACTION_TYPE[4], // 驚き
  ]

  return displayNames[reactionType] || ''
}

const getReactionIcon = (type: number) => {
  const reactionTypeValues = reactionTypes.value
  if (!reactionTypeValues) {
    return undefined
  }

  const reactionIcons = {
    [reactionTypeValues.good]: {
      color: '#7CB342',
      icon: 'mdi-thumb-up',
    },
    [reactionTypeValues.congratulations]: {
      color: '#D81B60',
      icon: 'mdi-hand-clap',
    },
    [reactionTypeValues.ok]: {
      color: '#FB8C00',
      icon: 'mdi-hand-okay',
    },
    [reactionTypeValues.surprise]: {
      color: '#039BE5',
      icon: 'far fa-regular fa-face-surprise 2xl',
    },
  }

  return reactionIcons[type] || undefined
}

// リアクション関連
const saveReaction = (newReactionType: number) => {
  const reactions = definedProps.thread.reactions || []

  // 同じリアクションが2回連続押されたらborder-bottomを解除
  dataRef.value.selectedReactionType =
    dataRef.value.selectedReactionType === newReactionType
      ? undefined
      : newReactionType

  // リアクションユーザーのIDを取得
  const reactionUsers = reactions.map((x) => x.staffId)
  const selectReactionUsers = reactions
    .filter((x) => Number(x.reactionType) === newReactionType)
    .map((x) => x.staffId)

  // ユーザーがリアクションを持っているか確認
  if (reactionUsers.includes(userId)) {
    const targetReaction = reactions.find((x) => x.staffId === userId)

    if (selectReactionUsers.includes(userId)) {
      // 登録済みのリアクションを選択すると削除
      if (targetReaction) {
        deleteReaction({
          id: targetReaction.id,
          version: targetReaction.version,
        })
      }
    } else if (targetReaction) {
      // 別のリアクションを選択すると更新
      updateReaction(targetReaction, newReactionType)
    }
  } else {
    // リアクションを追加
    addReaction(newReactionType)
  }
}

const mentionTargets = computed(() => {
  if (!definedProps.thread.mentionTargetsHtml) {
    return []
  }
  const mentionTargetsDelta = htmlToQuillDelta(
    definedProps.thread.mentionTargetsHtml,
  )

  const targets = []
  for (let index = 0; index < mentionTargetsDelta.ops.length; index++) {
    if (mentionTargetsDelta.ops[index].insert?.mention) {
      targets.push(mentionTargetsDelta.ops[index].insert?.mention)
    }
  }
  return targets
})

// =====================================================================================================================
// スレッド関連
// =====================================================================================================================

/**
 * スレッド編集・削除アイコンの表示・非表示を判定
 */
const showHover = (isHovering: boolean, type: string) => {
  if (!isHovering) return false

  const { $auth } = useNuxtApp()
  const userId = $auth.getUser()?.userId

  // スレッド登録者確認
  if (definedProps.thread.registrantId !== authUserId.value) {
    return false
  }

  // ログイン状態確認
  if (!userId || !isMyCareerEnabled.value) return false
  if (type === 'cross-customer') return false

  // 稟議スレッドで削除/更新不可状態か確認
  if (
    definedProps.thread.discussionType.isApprovalDiscussion &&
    definedProps.thread.comments.length > 0
  ) {
    return false
  }

  return true
}

// 目的
const purposeText = computed(() => {
  if (definedProps.thread.purpose === 'Internal') {
    return '社内協議'
  }
  return '社外協議'
})

const isExternal = computed(() => {
  return definedProps.thread.purpose === 'External'
})

/**
 * スレッドの内容
 */
const displayDescriptions = computed(() => {
  const displayDescriptions = definedProps.thread.displayDescriptions

  // 本来はバックエンドでやることだが、フロントのバージョンアップ中なのでフロント側で対処
  return displayDescriptions.map((x) => {
    if (x) {
      // trueが含まれていたらtrueを☑に置き換え
      x = x.replace(/true/g, '☑')
    }
    return x
  })
})

/**
 * 詳細表示の状態を反転する
 */
const toggleShownDetailState = () => {
  dataRef.value.shownDetail = !dataRef.value.shownDetail
}

/**
 * 表示内容が長い場合は本文を省略して詳細ボタンを表示
 */

const setShownDetailButton = () => {
  nextTick(async () => {
    while (!threadBody.value) {
      await new Promise((resolve) => setTimeout(resolve, 100)) // 100ミリ秒待機
    }

    // レンダリング完了を待つためさらに100ms待機
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 9emをpxに変換
    const fontSize = getComputedStyle(document.documentElement).fontSize // fontSizeの取得
    const maxHeight = 9 * parseFloat(fontSize) // max-height: 9em; (.hidden-text-containerより)

    if (threadBody.value.clientHeight >= maxHeight) {
      dataRef.value.shownDetailButton = true
    } else {
      dataRef.value.shownDetailButton = false
    }
  })
}

watch(
  () => threadBody.value,
  () => {
    setShownDetailButton()
  },
)

// =====================================================================================================================
// ファイル関連
// =====================================================================================================================
const downloadThreadFile = (fileName: string) => {
  emit('download-thread-file', definedProps.thread.id, fileName)
}

// =====================================================================================================================
// ユーザー関連
// =====================================================================================================================
const isMyCareerEnabled = computed(() => {
  // Myキャリアにアクセスできる状態か否か
  const { getIsMyCareerAccessSucceed } = useMycareer()
  return getIsMyCareerAccessSucceed()
})

const authUserId = computed(() => {
  const { $auth } = useNuxtApp()
  return $auth.getUser()?.userId
})

// =====================================================================================================================
// スレッドのリアクション関連
// =====================================================================================================================
/**
 * スレッドのリアクションを追加
 */
const addReaction = (newReactionType: number) => {
  dataRef.value.selectedReactionType = newReactionType
  emit('add-thread-reaction', newReactionType, definedProps.thread.id)
}

/**
 * スレッドのリアクションを更新
 */
const updateReaction = (
  sendData: issueProjectDiscussionThreadReactionType,
  newReactionType: number,
) => {
  dataRef.value.selectedReactionType = newReactionType
  emit('update-thread-reaction', sendData, newReactionType)
}

/**
 * スレッドのリアクションを削除
 */
const deleteReaction = (
  sendData: IssueProjectDiscussionThreadReactionForDelete,
) => {
  dataRef.value.selectedReactionType = undefined
  emit('delete-thread-reaction', sendData, definedProps.thread.id)
}

/**
 * リアクションしたメンバーの表示
 */
// div要素のcreatorの参照を保持
type createrType = {
  clientWidth: number
}
const creatorDiv = ref<createrType>()

// div要素のreactionMemberの参照を保持
type reactionMemberType = {
  style: {
    top: string
    left: string
  }
}
const reactionMemberDiv = ref<reactionMemberType>()

const showReactionMembers = async (reactionType: number, index: number) => {
  while (!creatorDiv.value) {
    await new Promise((resolve) => setTimeout(resolve, 100)) // 100ミリ秒待機
  }
  const creatorWidth = creatorDiv.value.clientWidth

  while (!reactionMemberDiv.value) {
    await new Promise((resolve) => setTimeout(resolve, 100)) // 100ミリ秒待機
  }

  const reactionMember = reactionMemberDiv.value

  // 表示位置
  reactionMember.style.top = dataRef.value.reactionMemberOffsetY + 'px'
  reactionMember.style.left = creatorWidth + index * 36 + 'px' // 36 ⇒ アイコンの横幅(px)

  dataRef.value.reactionTypeOnHover = reactionType
}

/**
 * スレッドのリンクコピー
 */
const copyThreadLink = () => {
  const currentUrl = new URL(window.location.href)
  currentUrl.searchParams.set('threadId', definedProps.thread.id)
  currentUrl.searchParams.delete('commentId')
  navigator.clipboard
    .writeText(currentUrl.href)
    .then(() => {
      const activeElement = document.activeElement as HTMLElement
      activeElement?.blur()
      successToast('リンクをコピーしました')
    })
    .catch((error) => {
      errorToast('リンクのコピーに失敗しました:', error)
    })
}

/**
 * スレッドをハイライト表示する
 */
const highlight = () => {
  dataRef.value.isHighlighted = true
  setTimeout(() => {
    dataRef.value.isHighlighted = false
  }, 4000) // 4秒後にハイライトを解除する
}

/**
 * スレッドの削除ボタン押下時の処理
 */
const removeThread = () => {
  emit('remove-thread', definedProps.thread)
}

// コメントが1件以上あると削除が押せない判断
const canDeleteThread = computed(() => {
  return !(definedProps.thread.comments.length < 1)
})

// スレッド編集ダイアログを開く
const openThreadEditor = async () => {
  await emit('update-thread', definedProps.thread, false)
}

// =====================================================================================================================
// コメント関連
// =====================================================================================================================
const { dataRef: commentDataReactive } = useCommentShareData()
const { commentsWithoutLatest } = useGetThreadComments(definedProps.thread)

/**
 * コメントのリアクションを追加
 */
const addCommentReaction = (newReactionType: number, commentId: string) => {
  emit(
    'add-comment-reaction',
    newReactionType,
    definedProps.thread.id,
    commentId,
  )
}

/**
 * コメントのリアクションを更新
 */
const updateCommentReaction = (
  sendData: issueProjectDiscussionCommentReactionType,
  newReactionType: number,
) => {
  emit(
    'update-comment-reaction',
    sendData,
    newReactionType,
    definedProps.thread.id,
  )
}

/**
 * コメントのリアクションを削除
 */
const deleteCommentReaction = (
  sendData: IssueProjectDiscussionCommentReactionForDelete,
  commentId: string,
) => {
  emit('delete-comment-reaction', sendData, definedProps.thread.id, commentId)
}

/**
 * コメントの削除
 */
const removeComment = (emitComment: issueProjectDiscussionCommentType) => {
  emit('remove-comment', emitComment)
}

/**
 * コメントの更新
 */
const updateComment = (comment: issueProjectDiscussionCommentType) => {
  emit('update-comment', comment)
}

/**
 * コメントダイアログオープン
 */
const openCommentEditor = () => {
  const setShownComment = () => {
    commentDataReactive.value.shownComments = true
  }
  emit(
    'add-comment',
    definedProps.thread.id,
    setShownComment,
    changeLatestCommentShownDetailState,
  )
}

/**
 * コメントのファイルをダウンロード
 */
const downloadCommentFile = (commentId: string, fileName: string) => {
  emit('download-comment-file', commentId, fileName)
}

/**
 * 全件表示・折りたたみ切り替え
 */
/**
 * コメントの全件表示状態を反転する
 */
const toggleShownCommentsState = () => {
  commentDataReactive.value.shownComments =
    !commentDataReactive.value.shownComments

  // 返信コメントを全件表示する場合
  if (commentDataReactive.value.shownComments) {
    // 最新の返信コメントを詳細表示にする
    changeLatestCommentShownDetailState(true)
  }
}

/**
 * 最新のコメント
 */
const latestComment = computed(() => {
  if (definedProps.thread.comments.length === 0) return null
  return [...definedProps.thread.comments].pop()
})

/**
 * 最新の返信コメントを詳細表示にするか簡易表示にするかを設定する
 */

/**
 * 詳細表示の状態を変更する
 */
const latestCommentRef = ref<InstanceType<typeof IssueProjectDiscussionComment>>()
const changeLatestCommentShownDetailState = async (isShownDetail: boolean) => {
  if (!definedProps.thread.comments) return
  if (definedProps.thread.comments.length > 0 && latestCommentRef.value) {
    latestCommentRef.value.changeShownDetailState(isShownDetail)
  }
}

defineExpose({
  definedProps,
  toggleShownCommentsState,
  toggleShownDetailState,
  highlight,
})
</script>

<style scoped>
.thread-title {
  background-color: #c8e6c9;
}

.icon-container {
  position: absolute;
  background-color: #f5f5f5;
  max-width: 300px;
  min-width: 50px;
  padding-top: 5px;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 3px;
  text-align: center;
  right: 0px;
  display: inline-block;
}

.font-awesome-icon {
  height: 22px;
}

.font-awesome-icon:hover {
  opacity: 0.5;
}

.highlighted-card {
  background: #ec6d1f12;
  border: 1.5px solid #ec6c1f;
  animation: fadeOut 5s; /* アニメーションの適用 */
}

.hidden-text-container {
  max-height: 9em;
  overflow: hidden;
}

.reactionMember {
  z-index: 100;
  position: absolute;
}

.iconborder {
  border-bottom: 2px solid;
}

.icon:hover {
  opacity: 0.5;
}

.nodelete {
  opacity: 0.5;
}

.time-color {
  color: #616161;
}

.custom-outlined-card {
  border-color: gray;
}

.mention-target {
  color: black;
}

.file-chip {
  height: auto !important;
  white-space: normal !important;
}

.file-name {
  word-break: break-all;
  white-space: normal;
  display: inline-block;
}
</style>
