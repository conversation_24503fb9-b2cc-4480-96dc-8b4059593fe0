import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type {
  Pagination,
  ApiResult,
} from '@hox/base/src/apiclient/shared/types'

// 主として扱うデータのスキーマを定義します。
export const issueprojectdiscussioncommentfileSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  updater: z.string(),
  updaterId: z.string(),
  updatedDateTime: z.string().datetime(),
  commentId: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type IssueProjectDiscussionCommentFile = z.infer<
  typeof issueprojectdiscussioncommentfileSchema
>

// =====================================================================================================================
// APIクライアントの定義(検索)
// =====================================================================================================================

// 検索条件の型を定義します。
export type FindIssueProjectDiscussionCommentFileCriteria = {
  commentId?: string
} & Pagination

// 検索結果の型を定義します。主として扱うデータの型をOmitやUnionなどで編集してください。
export type FindIssueProjectDiscussionCommentFileResultItem =
  IssueProjectDiscussionCommentFile
// 検索結果の型にページングの情報を追加します。
export type FindIssueProjectDiscussionCommentFileResult =
  ApiResult<FindIssueProjectDiscussionCommentFileResultItem>

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindIssueProjectDiscussionCommentFile(
  query: Ref<FindIssueProjectDiscussionCommentFileCriteria>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindIssueProjectDiscussionCommentFileResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncommentfile',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(ファイルアップロード)
// =====================================================================================================================

// IFormFileのスキーマ
export const IFormFileSchema = z.object({
  contentType: z.string(),
  length: z.number().int(),
  name: z.string(),
  fileName: z.string(),
})

// UploadIssueProjectDiscussionCommentFilesCommandのスキーマ
const uploadIssueProjectDiscussionCommentFilesCommandSchema = z.object({
  commentId: z.string(),
  updater: z.string(),
  updaterId: z.string(),
  uploadFiles: z.array(IFormFileSchema),
  versions: z.array(z.string()),
  filesToRemove: z.array(z.string()),
})

// ファイルアップロード用のデータのスキーマを定義します。
export const issueprojectdiscussioncommentfileSchemaForCreate =
  uploadIssueProjectDiscussionCommentFilesCommandSchema
// ファイルアップロード用の型を作成します。
export type IssueProjectDiscussionCommentFileForCreate = z.infer<
  typeof issueprojectdiscussioncommentfileSchemaForCreate
>

// Resultの型
export type UploadIssueProjectDiscussionCommentFileResult = {
  id: string
  fileName: string
  commentId: string
  updatedDateTime: Date
  updaterId: string
  updaterName: string
}

/**
 * ファイルアップロード処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostIssueProjectDiscussionCommentFile(
  body: Ref<IssueProjectDiscussionCommentFileForCreate>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<UploadIssueProjectDiscussionCommentFileResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncommentfile/upload-files',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(ダウンロード)
// =====================================================================================================================

// ダウンロードの型を定義します。
export type downloadIssueProjectDiscussionCommentFile = {
  commentId: string
  fileName: string
}

// Resultのスキーマを定義します。
export type ResultFileSchema = {
  data: Blob
}

/**
 * ダウンロードする
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDownloadIssueProjectDiscussionCommentFile(
  query: Ref<downloadIssueProjectDiscussionCommentFile>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Blob>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncommentfile/download',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}
