# UI pattern

ソフトウェアアーキテクチャにて実装テンプレートが用意されているUIパターンは以下の通り。
- 画面（page）パターン
    - 検索画面（search）
    - 登録画面（add）
    - 編集画面（edit）
    - 複数集約表示画面（multi）
- コンポーネント（component）パターン
    - 検索ダイアログ（search_dialog）
    - 明細編集ダイアログ（edit_dialog）
    - アグリゲート登録フォームコンポーネント（aggregate_add_form）
    - アグリゲート編集フォームコンポーネント（aggregate_edit_form）
    - アグリゲート一覧表示コンポーネント（aggregate_list）
    - アグリゲート詳細表示コンポーネント（aggregate_detail）

## パターン詳細説明

### 検索画面（search）

指定されたドメインモデルのデータを検索するための画面です。　　
検索したデータを一覧にて表示し、追加・編集ボタンにより編集画面への遷移元となります。

#### UI構成
- ボタンエリア : 画面で必要な処理ボタンを定義します。
    - デフォルトでは以下のボタンを表示するものとします。
        - 削除ボタン
        - 追加ボタン
- 検索条件 : 検索条件の入力フィールドと検索ボタンを定義します。
- 検索結果一覧 : 検索結果の一覧について定義します。
    - 必要に応じて各行に削除用チェックボックスと編集ボタンを表示します。
    - ヘッダをクリックすることでソートを行うことができるため、ソート対象についての記述も行います。

#### 定義イベント概要（デフォルト処理）

| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 検索条件を表示するのに必要なデータを取得する | ライフサイクルフック onMounted |
| 検索ボタン押下 | 入力された条件にてデータの検索を行う | function search |
| 削除ボタン押下 | 選択されたデータの削除を行う | function remove |
| 追加ボタン押下 | 登録画面に遷移する | function add |
| 編集ボタン押下 | 一覧にて選択されたデータの編集画面に遷移する | function edit |

---
### 登録画面（add）

単一のアグリゲートを登録するための画面です。
必要に応じてURLやパターンから情報を取得し、登録データとして設定します。
入力された内容にてアグリゲートの登録を行います。

#### UI構成

アグリゲート登録フォームコンポーネントの呼び出しを行います。
（※）詳細はアグリゲート登録フォームコンポーネントの説明を参照してください。

#### 定義イベント概要（デフォルト処理）

（※）詳細はアグリゲート登録フォームコンポーネントの説明を参照してください。

---
### 編集画面（edit）

単一のアグリゲートを更新するための画面です。  
URLやパラメータからキー情報を取得し、対象のアグリゲートのデータを取得して表示します。
必要に応じてURLやパターンから情報を取得し、更新データとして設定します。
編集された内容にてアグリゲートの更新を行います。

#### UI構成

アグリゲート編集フォームコンポーネントの呼び出しを行います。
（※）詳細はアグリゲート編集フォームコンポーネントの説明

#### 定義イベント概要（デフォルト処理）

（※）詳細はアグリゲート編集フォームコンポーネントの説明を参照してください。

---
### 複数集約表示画面（multi）

複数集約表示画面は、複数の集約の表示を行う画面です。  
表示対象の各集約はコンポーネントとして実装され、画面上で組み合わせて表示されます。

#### UI構成

複数のコンポーネントパターンを組み合わせて構成します。  

#### 定義イベント概要（デフォルト処理）

イベントについては、各コンポーネントパターンの定義に従います。

---
### 検索ダイアログ（search_dialog）

データを検索し、一覧にて選択されたデータを呼び出し元に返却するダイアログです。

#### UI構成
- 検索条件 : 検索条件の入力フィールドと検索ボタンを定義します。
- 検索結果一覧 : 検索結果を表示します。
    - 一覧表示項目に選択ボタンを含みます。

#### 定義イベント概要（デフォルト処理）

| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| ダイアログ表示 | 検索条件を初期化し、ダイアログを表示する | function open |
| 検索ボタン押下 | 入力された条件にてデータの検索を行う | function search |
| 選択ボタン押下 | 選択されたデータを呼び出し元に返却し、ダイアログを閉じる | function select |

---
### 明細編集ダイアログ（edit_dialog）

呼び出し元にて選択されたエンティティの明細データの編集を行うためのダイアログです。

#### UI構成
- 入力エリア : 明細情報の入力フィールドを表示します。

#### 定義イベント概要（デフォルト処理）

| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| ダイアログ表示 | 対象データを設定し、ダイアログを表示する | function open |
| OKボタン押下 | 入力された情報を呼び出し元に返し、ダイアログを閉じる | function action |
| キャンセルボタン押下 | 入力された情報を破棄し、ダイアログを閉じる | function cancel |

---
### アグリゲート登録フォームコンポーネント（aggregate_add_form）

単一のアグリゲートを登録するためのフォームコンポーネントです。
複数のセクションに分かれた入力フォームを提供し、関連情報を取得して初期表示に反映します。
入力された内容の検証と保存処理を行います。

#### UI構成
- メインアクション : 任意のボタンを表示します。
    - デフォルトでは以下のボタンを表示します
        - 保存ボタン
        - キャンセルボタン
- セクション型入力フォーム : アグリゲートに含まれる各エンティティの情報を入力するためのセクションを表示します。
    - 各セクションごとにフォームを表示するサブコンポーネントが定義されます
    - 基本情報セクション : アグリゲートのルートエンティティの情報を入力します。
    - その他セクション : アグリゲートの子エンティティの情報を入力します。セクション名はエンティティ名に基づきます。

#### 定義イベント概要（デフォルト処理）

| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 関連情報を取得し、フォームの初期化とデータ変更監視を開始する | ライフサイクルフック onMounted |
| ※各種ボタンイベント | ※定義されたボタンに応じた処理を定義する | ※任意のfunction |
| 項目検証 | 入力項目の検証を行い、エラーメッセージを表示する | function validateItem |

---
### アグリゲート編集フォームコンポーネント（aggregate_edit_form）

単一のアグリゲートを編集するためのフォームコンポーネントです。
複数のセクションに分かれた入力フォームを提供し、既存データを取得して表示します。
編集された内容の検証と更新処理を行い、承認フローが必要な場合は対応する操作を提供します。

#### UI構成
- メインアクション : 任意のボタンを表示します。
    - デフォルトでは以下のボタンを表示します
        - 保存ボタン
        - キャンセルボタン
- セクション型入力フォーム : アグリゲートに含まれる各エンティティの情報を編集するためのセクションを表示します。
  - 各セクションごとにサブコンポーネントが定義されます
  - 基本情報セクション : アグリゲートのルートエンティティの情報を表示・編集します。
  - その他セクション : アグリゲートの子エンティティの情報を表示・編集します。セクション名はエンティティ名に基づきます。

#### 定義イベント概要（デフォルト処理）

| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 既存データと関連情報を取得し、フォームの初期化とデータ変更監視を開始する | ライフサイクルフック onMounted |
| ※各種ボタンイベント | ※定義されたボタンに応じた処理を定義する | ※任意のfunction |
| 項目検証 | 入力項目の検証を行い、エラーメッセージを表示する | function validateItem |

