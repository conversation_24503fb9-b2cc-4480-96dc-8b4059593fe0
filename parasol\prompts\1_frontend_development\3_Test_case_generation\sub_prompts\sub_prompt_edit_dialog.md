# 編集ダイアログテスト作成

## 役割定義

あなたは編集ダイアログのテストを実装するベテランエンジニアです。指定された編集ダイアログコンポーネントに対してVitestを使用した単体テストを生成してください。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- プロジェクト設定定義：
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- テスト対象コンポーネント：`[指定された編集ダイアログのVueファイル]`
- ドメイン言語定義：`parasol\capability\**\ドメイン言語.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- テストテンプレート：`parasol\prompts\4_Test_case_generation\Templates\edit_dialog\Template_EntityEditDialog.spec.ts`

## 出力定義

出力対象ファイルの出力先ディレクトリや出力ファイル名のフォーマットは以下に従ってください。

- テストファイル出力先ディレクトリ：`test\[テスト対象ファイルのsrc配下の相対パス]`
- テストファイル名：`[テスト対象のファイル名].spec.ts`

例：`src\components\employee\organisms\EntityEditDialog.vue` → `test\components\employee\organisms\`

## 制約事項

以下の制約に従ってください。

### 禁止事項

- 定義ファイルを編集する代わりに、必要な内容は出力ファイルに記述してください
- テンプレートファイルを編集する代わりに、テンプレートを参考にしてテストコードを生成してください
- 当プロンプトファイルを編集する代わりに、記載されている指示に従ってテスト実装を行ってください
- テンプレートにないモックを定義する代わりに、テンプレートに記載されているモック定義のみを使用してください

## 指示詳細

### 情報収集

1. 各定義ファイルの読み込みを行ってください。
   - テスト対象の編集ダイアログVueファイルを読み込み、実装内容を確認してください
   - テスト対象コンポーネントで扱うエンティティに基づいて、該当するドメイン言語ファイルを読み込んでください
   - テストテンプレートファイルを読み込んでください

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください。

1. テストファイルを作成してください。
   - テンプレートファイルを基に内容を生成してください
   - ファイル名は「[コンポーネント名].spec.ts」としてください
   - 初期表示を含む全てのイベント処理についてテストを作成してください
   - 各イベントについて必要なパターンのテストを定義してください

### 品質保証

1. 作成したテストファイルの検証を行ってください：
   - 出力先・出力ファイル名フォーマットに従って、ファイルが正しく出力されているか
   - 初期表示を含む全てのイベント処理にテストが作成されているか
   - テンプレートに従ったモック定義が使用されているか
   - TypeScriptの構文エラーがないか
   - Vitestの記法が正しく使用されているか
   - 必要なimport文がすべて含まれているか