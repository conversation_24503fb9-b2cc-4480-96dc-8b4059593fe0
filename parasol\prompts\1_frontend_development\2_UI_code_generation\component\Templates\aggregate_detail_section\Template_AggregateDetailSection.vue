<!--
===========================================
エンティティ詳細セクション テンプレート
===========================================

このテンプレートは、計算値・参照値の表示パターンのセクションコンポーネントを提供します。

【適用対象】
- 単一エンティティの詳細情報表示
- 複数フィールドを組み合わせた計算値表示
- 参照値やマスタデータとの結合表示
- 読み取り専用での情報表示

【参考実装】
- ListingInformationSection.vue: 上場情報（上場先/上場区分の組み合わせ表示）
- BusinessOverviewSection.vue: 事業概要（概要+沿革の表示）
- BusinessBackgroundSection.vue: 取引経緯の表示

【使用方法】
1. TODOコメントに従って、エンティティ固有の情報に置き換える
2. 計算値のロジックを実装する
3. 表示フィールドを実際のエンティティに変更する
4. 必要に応じて編集ボタンを実装する
-->

<script setup lang="ts">
// TODO: コンポーネント固有のcomposablesをインポート
import { useGetYourEntityByCustomerIdentificationId } from './useGetYourEntityByCustomerIdentificationId'
// TODO: 必要に応じて変換関数やユーティリティをインポート
// import {
//   toDisplayName,
//   toStatusName,
// } from '~/constants/domain/YourEntity'

// 共通のprops定義
// TODO: 識別子名をエンティティに応じて変更
const props = defineProps<{
  title: string
  customerIdentificationId: string
}>()

const customerIdentificationId = computed(() => props.customerIdentificationId)

// TODO: エンティティ固有のcomposableを使用
const {
  fetchedYourEntity,        // 単体データ
  hasYourEntity,            // データ存在判定
  inProgressFind,           // ローディング状態
  getYourEntity,            // データ取得関数
} = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

// TODO: 計算値の定義（複数フィールドの組み合わせ表示）
const computedDisplayValue = computed(() => {
  // 【上場先と上場区分の組み合わせ例】
  /*
  return [
    toDisplayName(fetchedYourEntity.value.field1),
    toStatusName(fetchedYourEntity.value.field2),
  ]
    .filter(Boolean)
    .join(' / ')
  */
  
  // 【金額と通貨の組み合わせ例】
  /*
  if (!fetchedYourEntity.value.amount) return ''
  return `${fetchedYourEntity.value.amount.toLocaleString()} ${fetchedYourEntity.value.currency || ''}`
  */
  
  // 【日付範囲の表示例】
  /*
  const startDate = fetchedYourEntity.value.startDate
  const endDate = fetchedYourEntity.value.endDate
  if (startDate && endDate) {
    return `${formatDate(startDate, 'yyyy/MM/dd')} - ${formatDate(endDate, 'yyyy/MM/dd')}`
  }
  return startDate ? formatDate(startDate, 'yyyy/MM/dd') : ''
  */
  
  // TODO: 実際の計算ロジックに置き換え
  return `${fetchedYourEntity.value.field1 || ''} ${fetchedYourEntity.value.field2 || ''}`.trim()
})
</script>

<template>
  <!-- 
  TODO: セクション枠組みコンポーネントを選択してください
  汎用的なセクション枠組みの場合：app-edit-item-group-tmpl
  プロジェクト固有の場合：your-project-edit-item-group-tmpl
  -->
  <app-edit-item-group-tmpl>
    <template #title>{{ title }}</template>
    
    <!-- TODO: 更新日時表示が必要な場合 -->
    <!--
    【更新日時表示の実装例】
    <template #subtitle>
      <div v-if="fetchedUpdatedDateTimeSummary?.yourEntityUpdatedDateTime" class="updated-date-label">
        最終更新日: {{ formatDate(fetchedUpdatedDateTimeSummary.yourEntityUpdatedDateTime, 'yyyy年MM月dd日') }}
      </div>
    </template>
    -->
    
    <div>
      <v-row>
        <v-col>
          <!-- メイン表示フィールド（計算値） -->
          <!-- TODO: 実際のラベルに変更 -->
          <app-text-field
            :model-value="computedDisplayValue"
            label="計算値フィールド"
            :loading="inProgressFind"
            readonly
          />
          
          <!-- 通常の表示フィールド -->
          <!-- TODO: 実際のフィールド名とラベルに変更 -->
          <app-text-field
            v-model="fetchedYourEntity.comment"
            label="備考"
            :loading="inProgressFind"
            readonly
          />
          
          <!-- TODO: リンク表示が必要な場合 -->
          <!--
          【リンク表示の実装例】
          <app-text-field
            :model-value="fetchedYourEntity.url"
            label="関連URL"
            :loading="inProgressFind"
            readonly
          >
            <template #append>
              <div class="mr-1">
                <nuxt-link
                  v-if="fetchedYourEntity.url"
                  :to="fetchedYourEntity.url"
                  target="_blank"
                >
                  リンク<v-icon class="ml-1" size="small" icon="mdi-launch" />
                </nuxt-link>
                <span v-else class="text-grey cursor-default">
                  リンク未設定
                </span>
              </div>
            </template>
          </app-text-field>
          -->
          
        </v-col>
        
        <!-- 操作ボタンエリア -->
        <v-col cols="auto" class="pr-5">
          <!-- TODO: 必要に応じて作成/更新ボタンを実装してください -->
          <!--
          【作成ボタンの実装例】
          <create-your-entity-button
            v-if="!hasYourEntity"
            :in-progress-find="inProgressFind"
            @refetch="refetch"
          />
          
          【更新ボタンの実装例】
          <update-your-entity-button
            v-else
            :fetched-your-entity="fetchedYourEntity"
            :in-progress-find="inProgressFind"
            @refetch="refetch"
          />
          -->
        </v-col>
      </v-row>
    </div>
    
  </app-edit-item-group-tmpl>
</template>

<style scoped>
.updated-date-label {
  font-size: 0.875rem;
  color: var(--v-theme-on-surface-variant);
}

.text-grey {
  color: var(--v-theme-on-surface-variant);
}

.cursor-default {
  cursor: default;
}
</style>

<!--
===========================================
テンプレート使用時の変更点チェックリスト
===========================================

【必須変更事項】
□ エンティティの型定義をインポート（YourEntity → ActualEntity）
□ Composable名を実際のエンティティに変更（useGetYourEntityBy... → useGetActualEntityBy...）
□ props の識別子名を変更（customerIdentificationId → 実際の識別子）
□ 計算値のロジックを実際の業務ロジックに変更（computedDisplayValue）
□ 表示フィールドを実際のエンティティフィールドに変更
□ フィールドラベルを実際の表示名に変更
□ 必要に応じて作成/更新ボタンコンポーネントを実装

【計算値の実装】
□ 複数フィールドの組み合わせ表示ロジックの実装
□ フォーマット処理の実装（日付、金額、パーセンテージ等）
□ 条件分岐による表示制御の実装
□ マスタデータとの結合表示の実装

【オプション実装】
□ 更新日時管理が必要な場合は関連コメントアウトを解除
□ プロジェクト固有のセクション枠組みコンポーネントに変更
□ ステータス表示（アイコン付き）の実装
□ リンク表示機能の実装
□ 追加情報や警告メッセージの表示実装
□ 変換関数やユーティリティのインポート

【表示フィールドの調整】
□ テキストフィールドからテキストエリアへの変更
□ 数値フィールドの追加
□ 日付フィールドの追加
□ セレクトフィールドの追加（読み取り専用）

【Composable実装】
□ 対応するComposableファイル（useGetYourEntityByCustomerIdentificationId.ts）の実装
□ API呼び出し処理の実装
□ エラーハンドリングの実装

【ボタンコンポーネント実装（必要に応じて）】
□ CreateYourEntityButton.vue の実装
□ UpdateYourEntityButton.vue の実装
-->
