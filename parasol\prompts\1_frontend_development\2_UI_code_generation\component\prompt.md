# コンポーネントコード生成

## 役割定義

あなたはフロントエンドアプリケーションを開発するベテランエンジニアです。  
指定された入力情報を元に、フロントエンドのプログラム実装を行います。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 : 
    - `parasol\architecture\frontend_architecture_hoxbase.md`
    - `parasol\architecture\frontend_architecture_ibpbase.md`
- コンポーネント使用方法リファレンス : 
    - `parasol\architecture\usage_sample_hoxbase.vue`
    - `parasol\architecture\usage_sample_ibpbase.vue`
- UIパターン定義 : 
    - `parasol\architecture\UI_pattern.md`
- プロジェクト設定定義 : 
    - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- ドメイン言語定義 : `parasol\capability\**\ドメイン言語.md`
- UI定義ファイル : `parasol\ui\**\*.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

#### 検索ダイアログコンポーネント用

- `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\Template_SearchDialog.vue`

#### 明細編集ダイアログコンポーネント用

- `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\Template_EditDialog.vue`

#### アグリゲート登録/編集フォームコンポーネント用

- メインコンポーネント : 
    - 登録フォーム : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_edit_form\Template_CreateAggregate.vue`
    - 編集フォーム : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_edit_form\Template_EditAggregate.vue`
- サブコンポーネント : 
    - 単票入力フォーム : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_edit_form\forms\Template_AggregateEntityForm.vue`
    - 一覧入力フォーム : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_edit_form\forms\Template_AggregateEntityListForm.vue`
- 関数定義ファイル : 
    - 集約登録関数 : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_edit_form\Template_useCreateAggregate.ts`
    - 集約更新関数 : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_edit_form\Template_useUpdateAggregate.ts`
    - 集約取得関数 : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_edit_form\Template_useGetAggregate.ts`
    - 集約登録情報授受関数 : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_edit_form\Template_useCreatedAggregateState.ts`

#### アグリゲート一覧表示コンポーネント用

- メインコンポーネント : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_detail_section\Template_AggregateDetailSection.vue`
- 関数定義ファイル : 
    - 集約取得関数 : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_detail_section\Template_useGetAggregate.ts`

#### アグリゲート詳細表示コンポーネント用

- メインコンポーネント : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_list_section\Template_AggregateListSection.vue`
- 関数定義ファイル : 
    - 集約取得関数 : `parasol\prompts\1_frontend_development\2_UI_code_generation\component\Templates\aggregate_list_section\Template_useGetAggregateList.ts`

### 利用コード

- APIクライアント : 
    - `[APIクライアントディレクトリ]\[操作対象集約/エンティティ名].ts`
    - `[APIクライアントディレクトリ]\shared\*.ts`
- 関数 : 
    - `[関数ディレクトリ]\*.ts`
- 列挙型 : 
    - `[列挙型ディレクトリ]\*.ts`
- zodスキーマ定義 : 
    - `[zodスキーマディレクトリ]\[操作対象集約/エンティティ名].ts`
    - `[zodスキーマディレクトリ]\shared\*.ts`

## 出力定義

出力対象ファイルの出力先のディレクトリや出力ファイル名のフォーマットは以下に従ってください。  
プロジェクトの詳細なディレクトリ構成は`parasol\architecture\project_settings.md`を参照してください。

- **※ `[操作対象集約名]`は、作成対象のコンポーネントで取り扱う集約名を指します。**
    - あくまで作成対象のコンポーネントで取り扱う集約名であり、呼び出し元とは異なる場合があります。

### 検索ダイアログコンポーネント

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\`
- ファイル名 : `[操作対象集約名(Pascalケース)]SearchDialog.vue`

### 明細編集ダイアログコンポーネント

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\`
- ファイル名 : `[操作対象集約名(Pascalケース)]EditDialog.vue`

### アグリゲート登録/編集フォームコンポーネント

#### メインコンポーネント

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\`
- ファイル名 : `Create[操作対象集約名(Pascalケース)].vue`

#### サブコンポーネント

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\forms\`
- ファイル名 : 
    - 各サブコンポーネントの取り扱うエンティティがそれぞれ異なる場合
        - 操作対象のエンティティがルートエンティティの場合 : `[操作対象集約名(Pascalケース)]Form.vue`
        - それ以外 : `[操作対象集約名(Pascalケース)][エンティティ名(Pascalケース)]Form.vue`
    - 各サブコンポーネントの取り扱うエンティティが同じで複数存在する場合
        - `[操作対象集約名(Pascalケース)][エンティティ名(Pascalケース)][セクション名(Pascalケース)]Form.vue`

#### 関数定義ファイル

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\`
- ファイル名 : 
    - 集約登録関数 : `useCreate[操作対象集約名(Pascalケース)].ts`
    - 集約更新関数 : `useUpdate[操作対象集約名(Pascalケース)].ts`
    - 集約取得関数 : `useGet[操作対象集約名(Pascalケース)].ts`
    - 集約登録情報授受関数 : `useCreated[操作対象集約名(Pascalケース)]State.ts`

#### 出力ファイルの例

例）サンプル集約を取り扱う場合（※配下にHogeエンティティ、Fugaエンティティ一覧を持つ場合）
```
[コンポーネントディレクトリ]\sample\
    ├── CreateSample.vue
    ├── forms\
    │   ├── SampleForm.vue
    │   ├── SampleHogeForm.vue
    │   └── SampleFugaListForm.vue
    ├── useCreateSample.ts
    ├── useUpdateSample.ts
    ├── useGetSample.ts
    └── useCreatedSampleState.ts
```

### アグリゲート一覧表示コンポーネント

#### メインコンポーネント

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\`
- ファイル名 : `[操作対象集約名(Pascalケース)]ListSection.vue`

#### 関数定義ファイル

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\`
- ファイル名 : `useGet[操作対象集約名(Pascalケース)]List.ts`

#### 出力ファイルの例

例）サンプル集約を取り扱う場合
```
[コンポーネントディレクトリ]\sample\
    ├── SampleListSection.vue
    └── useGetSampleList.ts
```

### アグリゲート詳細表示コンポーネント

#### メインコンポーネント

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\`
- ファイル名 : `[操作対象集約名(Pascalケース)]DetailSection.vue`

#### 関数定義ファイル

- 出力先 : `[コンポーネントディレクトリ]\[操作対象集約名(ケバブケース)]\`
- ファイル名 : `useGet[操作対象集約名(Pascalケース)].ts`

#### 出力ファイルの例

例）サンプル集約を取り扱う場合
```
[コンポーネントディレクトリ]\sample\
    ├── SampleDetailSection.vue
    └── useGetSample.ts
```

## 制約事項

以下の制約に従ってください。

### 禁止事項
- 定義ファイル、テンプレートファイル、当プロンプトファイルの編集は行わないでください。
- 指示していない内容の編集は行わないでください。
- 作業効率を重視した作業内容の省略は行わず、各作業を正しく丁寧に完了させてください。

### その他ルール
- 各フィールドについては、Vuetifyコンポーネントの直接使用を避け、@hox/baseまたは@ibp/baseで提供されるコンポーネントを使用してください。
- コンポーネントの使用方法は、コンポーネント使用方法リファレンスを参照してください。

## 指示詳細

### 情報収集

1. 指定されたUI定義ファイルを読み込ます。
    - 作成対象のコンポーネントで取り扱う集約を特定してください。
        - 特定した集約の情報を提示してください。
        - 取り扱う集約が決まると出力先のディレクトリが決まるため、出力先を提示してください。
    - 作成対象のコンポーネントで定義されている項目とバリデーションルールを抽出します。
    - 定義されているイベント処理を抽出します。
    - コンポーネントパターンとUI定義ファイルの内容から生成すべきファイルを特定します。
        - テンプレートで定義されているものを参考に、生成するファイルを決定します。
        - 生成するファイルの一覧を列挙してください。

2. ドメイン言語ファイルを読み込み、必要なエンティティとフィールド情報を取得します。
    - 対象画面で取り扱う集約やエンティティの情報を読み込みます。

3. アーキテクチャ定義とコンポーネント使用方法リファレンスを読み込み、利用可能なコンポーネントとその使用方法を把握します。

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成します。

2. 必要な関数定義ファイルをテンプレートファイルを元に実装します。
    - 集約登録、更新、取得、登録情報授受の各関数を生成します。
    - APIクライアントを使用する場合、実在するコードを参照し適切な内容で実装を行ってください。
    - 仮実装・暫定実装は行わないでください。

3. 必要なサブコンポーネントをテンプレートファイルを元に実装します。
    - UI定義の内容に沿って単票入力フォームコンポーネント・一覧入力フォームコンポーネントを必要に応じて生成します。
        - 表示項目の順序はUI定義ファイルに従ってください。
    - 既に同内容のコンポーネントが存在する場合は、既存のコンポーネントを再利用します。
    - 対象コンポーネントのimportでエラーが発生する場合以下の対応を行ってください。
        - 対象ファイルが存在するか検索し、存在する場合はimport文を書き換えてください
        - 対象ファイルが存在しない場合、一旦エラーのままで出力してください。

4. 必要な関数定義ファイル・サブコンポーネントが作成されているか確認します。
    - 作成されていない場合は、手順を戻って必要なファイルを生成してください。

5. メインコンポーネントをテンプレートファイルを元に実装します。

6. 必要なイベント処理の内容を実装します。

### 品質検証

以下の検証を実施し、品質に問題がないか確認してください。

1. **機能完全性**
    - ページ定義との整合性を確認してください。
        - 表示項目
        - バリデーションルール
        - イベント処理

2. **アーキテクチャ準拠性**
    - アーキテクチャ定義に従った実装が行われているか確認してください。
        - @hox/base、@ibp/baseコンポーネントの正しいインポート
        - `app-`プレフィックスコンポーネントの使用
        - Vuetifyコンポーネント直接使用の回避
        - 使用方法ファイルに従ったプロパティ指定

3. **コード品質**
    - ビルドエラーが無いことを確認してください。
    - コード品質（インデント、不要コード除去、適切なコメント）が保たれていることを確認してください。

4. 上記で検証した内容に問題がある場合は、必要な修正を行ってください。
