# ページ定義テンプレート

## 基本情報
<!--
  ページ名とUIパターンを指定します 
    - ページ名 : 適切なページ名を記述してください
    - UIパターン : UIパターン定義に記述されている画面パターンを指定してください
-->

- ページ名 : 従業員検索
- UIパターン : 検索画面（search）

## 実現対象ハイレベルユースケース
<!--
  ケイパビリティ定義のハイレベルユースのうち、この画面で実現したいものを列挙します
  また、各ハイレベルユースケースに対して、分解されたユースケースを列挙します
-->

- 従業員を管理する
  - 従業員情報の検索
  - 従業員情報の登録
  - 従業員情報の更新
  - 従業員情報の削除

## 共通処理（ミドルウェア）定義
<!--
  画面アクセス時に共通的に実行される処理を定義します
  チェックボックスにチェックを入れることで、該当する処理が実装されます
  必要なものにチェック（[x]）を入れてください
-->

- [x] アクセスログ出力
- [ ] アクセス可否チェック
- [ ] 次画面パスチェック

## パラメータ定義
<!-- 画面に渡されるパラメータを定義します -->
<!--
表形式にて記述します。
- パラメータ名 : パラメータの名前
- 種別 : path(パスパラメータ)、query(クエリパラメータ)
- 型 : 数値、文字列、ブール値など
- 説明 : パラメータの説明
- 必須 : パラメータが必須かどうか
-->

| パラメータ名 | 種別 | 必須 | 型 | 説明 |
|---|---|---|---|
| customerIdentificationId | path | ✓ | 文字列 | 顧客を一意に識別する識別子 |

## UI定義
<!--
UIパターン定義のUI構成に定義されている構成要素について、表示項目などの詳細を定義します。

各項目の定義内容は以下に従ってください。
- 入力フィールドの場合
    - 入力形式 : 入力フィールドの形式
    - 必須 : 対象の入力が必須かどうか。必須でない場合記述不要
    - バリデーション : 入力チェック仕様
    - 説明 : 補足で必要な情報を記述する。特に記述するものがない場合不要
- 表示フィールドの場合
    - 説明 : 補足で必要な情報を記述する。特に記述するものがない場合不要。
        - 例 : 編集フォーマット（"yyyy/MM/dd"形式、"#,###,##0"形式など）

入力形式は以下の種類とする
- テキストボックス
- ラジオボタン
- プルダウンリスト
- チェックボックス
- テキストエリア
- ファイル指定
- 検索ダイアログ

バリデーションは以下の種類とする
- ALPHANUMERIC : アルファベット・数値のみ入力可
- ALPHABET : アルファベットのみ入力可
- NUMERIC : 数値のみ入力可
- MIN_99 : 最小文字数（他と組み合わせる場合は"_MIN_5"などの表現とする。例 : ALPHANUMERIC_MIN_5）
- MAX_99 : 最大文字数（他と組み合わせる場合は"_MAX_20"などの表現とする。例 : ALPHANUMERIC_MIN_5_MAX_10）
- PATTERN : 正規表現でのチェック（正規表現は説明に記述する）
-->

### ボタンエリア
- 削除ボタン
    - 説明 : ※イベント処理参照
- 追加ボタン
    - 説明 : ※イベント処理参照

### 検索条件
- 従業員番号
    - 入力形式 : テキストボックス
    - バリデーション : ALPHANUMERIC_MAX_10
    - 説明 : 前方一致で絞り込みを行う
- 従業員名
    - 入力形式 : テキストボックス
    - バリデーション : MAX_30
    - 説明 : 部分一致で絞り込みを行う
- 部署
    - 入力形式 : 検索ダイアログ
    - 説明 : 
        - 検索ボタンでダイアログを表示する
        - ダイアログで選択された部署の名称をラベル表示する
        - 検索は部署のIDで絞り込みを行う
- 役職
    - 入力形式 : チェックボックス
    - 説明 : 
        - 役職情報の一覧を表示する
        - 値:ID、ラベル:名称
- 検索ボタン
    - 説明 : ※イベント処理参照

### 検索結果一覧
- 表示項目
    - 従業員番号
    - 氏名
    - 氏名カナ
    - 所属部署
    - 役職
    - 雇用形態
    - 在職状態
- ソート機能
    - デフォルト: 従業員番号昇順
    - ソート可能項目: 全項目

<!--
例）編集画面（※画面にコンポーネントを呼び出しを含む場合）
```
### 従業員集約編集フォーム（アグリゲート編集フォームコンポーネント）

- コンポーネントへの授受データ
    - 従業員ID（employeeId）
        - 説明 : クエリストリングから取得する

#### メインアクション
- 一時保存ボタン
    - 説明 : ※イベント処理参照
- 保存ボタン
    - 説明 : ※イベント処理参照
- キャンセルボタン
    - 説明 : ※イベント処理参照
#### セクション型入力フォーム
- 基本情報セクション
    - 従業員番号
        - 入力形式 : テキストボックス
        - 必須
        - バリデーション : ALPHANUMERIC_MAX_10
        - 説明 : 登録時は自動採番、更新時は編集不可
    - 氏名
        - 入力形式 : テキストボックス
        - 必須
        - バリデーション : MAX_50
    - 生年月日
        - 入力形式 : 日付選択
        - 必須
        - 説明 : "yyyy/MM/dd"形式で表示
- 資格情報セクション
    - 追加ボタン
        - 説明 : ※イベント処理参照
    - 一覧表示項目
        - 資格名
        - 取得日
            - 説明 : "yyyy/MM/dd"形式で表示
        - 有効期限
            - 説明 : "yyyy/MM/dd"形式で表示
        - 編集アイコン
            - 説明 : ※イベント処理参照
        - 削除アイコン
            - 説明 : ※イベント処理参照
    - 明細編集ダイアログ
        - 保存ボタン
            - 説明 : ※イベント処理参照
        - キャンセルボタン
            - 説明 : ※イベント処理参照
        - 資格名
            - 入力形式 : テキストボックス
            - 必須
            - バリデーション : MAX_50
        - 取得日
            - 入力形式 : 日付選択
            - 必須
            - 説明 : "yyyy/MM/dd"形式で表示
        - 有効期限
            - 入力形式 : 日付選択
            - 説明 : "yyyy/MM/dd"形式で表示
```

例）複数集約表示画面
```
### 基本情報セクション

#### 属性情報サブセクション（アグリゲート詳細表示コンポーネント）
- 表示項目
    - 会社名（CompanyName）
        - 説明 : 法人顧客の会社名を表示
    - URL（Url）
        - 説明 : 顧客の会社ホームページのURLを表示
    - 電話番号（PhoneNumber）
        - 説明 : 顧客の会社電話番号を表示
    - 住所（Address）
        - 説明 : 顧客の会社住所を表示（郵便番号、都道府県、市区町村、番地、建物名）
    - 代表者名（RepresentativeName）
        - 説明 : 代表者の氏名（漢字）を表示
    - 従業員数（NumberOfEmployee）
        - 説明 : 法人に所属する従業員数を表示、"#,###,##0"形式
    - 設立年月日（FoundedDate）
        - 説明 : 法人の設立年月日を表示、"yyyy/MM/dd"形式
    - 資本金（千円）（Capital）
        - 説明 : 法人の資本金を表示、"#,###,##0"形式、単位：千円
    - ローン格付（LoanRating）
        - 説明 : ローン格付内容を表示
    - 債務者区分（BorrowerClassification）
        - 説明 : 債務者の区分を表示
    - 格付更新日（LoanRatingUpdatedAt）
        - 説明 : ローン格付を更新した日次を表示、"yyyy/MM/dd"形式
    - 更新日時（UpdatedAt）
        - 説明 : 最後に情報が更新された日時を表示、"yyyy/MM/dd HH:mm"形式

#### オウンドメディアサブセクション（アグリゲート一覧表示コンポーネント）
- 表示項目
    - 一覧表示
        - オウンドメディア種別（MediaType）
            - 説明 : 発信媒体の種別を表示（Instagram、X、LinkedIn、Facebook、公式LINE、YouTube、その他）
        - URL（Url）
            - 説明 : オウンドメディアのURLを表示
        - その他オウンドメディア（OtherMediaType）
            - 説明 : 種別がその他の場合のオウンドメディアの種類を表示
        - 備考（Note）
            - 説明 : オウンドメディアにかかわる補足情報を表示
        - 更新日時（UpdatedAt）
            - 説明 : 最後に情報が更新された日時を表示、"yyyy/MM/dd HH:mm"形式

### 事業概要セクション

#### 事業概要サブセクション（アグリゲート詳細表示コンポーネント）
- 表示項目
    - 業種コード（IndustryCode）
        - 説明 : 業種を一意に識別するコードを表示
    - 業種情報
        - 大分類名称（MajorCategoryName）
            - 説明 : 業種大分類名称を表示
        - 中分類名称（MediumCategoryName）
            - 説明 : 業種中分類名称を表示
        - 小分類名称（SmallCategoryName）
            - 説明 : 業種小分類名称を表示
        - 細分類名称（DetailCategoryName）
            - 説明 : 業種細分類名称を表示

#### 業種サブセクション（アグリゲート詳細表示コンポーネント）
- 表示項目
    - 業種情報詳細
        - 説明 : 日本標準産業分類に基づく業種情報の詳細表示

### コンタクトセクション

#### 連絡先サブセクション（アグリゲート一覧表示コンポーネント）
- 表示項目
    - 連絡先情報（ContactInfo）
        - 住所（Address）
            - 郵便番号（PostalCode）
                - 説明 : 郵便番号を表示
            - 都道府県（Prefecture）
                - 説明 : 都道府県を表示
            - 市区町村（City）
                - 説明 : 市区町村を表示
            - 番地（StreetAddress）
                - 説明 : 番地を表示
            - 建物名（BuildingName）
                - 説明 : 建物名を表示
        - 電話番号（PhoneNumber）
            - 説明 : 電話番号を表示（固定電話、携帯電話）
        - メールアドレス（EmailAddress）
            - 説明 : メールアドレスを表示（会社、個人）

```
-->

## イベント処理定義
<!-- UIパターン定義の定義イベント概要に従って処理内容を定義します 
- 定義されているイベントの概要を表形式にて記述します。
    - コンポーネントを呼び出す場合は、コンポーネント単位で記述します。
- 一覧表の下にイベント処理の詳細を記述します。
-->

### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 検索条件を表示するのに必要なデータを取得する | ライフサイクルフック onMounted |
| 検索ボタン押下 | 入力された条件にてデータの検索を行う | function search |
| 削除ボタン押下 | 選択されたデータの削除を行う | function remove |
| 追加ボタン押下 | 登録画面に遷移する | function add |
| 編集ボタン押下 | 一覧にて選択されたデータの編集画面に遷移する | function edit |

### 初期表示（onMounted）
1. 部署情報を取得し、プルダウンリストに設定する。
1. 役職情報を取得し、チェックボックスを表示する。

### 検索ボタン押下（search）
1. 入力された条件にてデータの検索を行う。
1. 検索結果を一覧に表示する。

### 削除ボタン押下（remove）
1. 一覧で選択されているデータの有無を確認する。
1. 選択されているデータが存在する場合、削除確認ダイアログを表示する。
1. 削除確認ダイアログで「OK」が選択された場合、選択されたデータを削除する。

### 追加ボタン押下（add）
1. 新規登録画面に遷移する。

### 編集ボタン押下（edit）
1. 対象行のIDをパラメータとして、編集画面に遷移する。

<!--
記述例）編集画面（※画面にコンポーネントを呼び出しを含む場合）
```
### 従業員集約編集フォーム（アグリゲート編集フォームコンポーネント）

#### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 既存データと関連情報を取得し、フォームの初期化とデータ変更監視を開始する | ライフサイクルフック onMounted |
| 一時保存ボタン押下| 編集された情報の一時保存を行う | function saveDraft |
| 保存ボタン押下| 編集された情報の保存を行う | function save |

#### 初期表示（onMounted）
1. 画面から渡された従業員IDを取得する。
1. 従業員情報を取得し、フォームに設定する。
1. 資格情報を取得し、資格情報セクションに設定する。

### 一時保存ボタン押下（saveDraft）
1. 編集された情報を一時保存する。
1. 一時保存後、確認メッセージを表示する。

### 保存ボタン押下（save）
1. 確認ダイアログを表示し、ユーザに保存の確認を行う。
1. 入力されたデータを検証し、エラーがあればエラーメッセージを表示する。
1. 入力内容に問題がなければ、データを保存する。
1. 保存後、成功メッセージを表示する。
```

例）
```

### 基本情報セクション

#### 属性情報サブセクション

##### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 顧客識別IDに基づいて法人顧客の基本属性情報を取得し表示する | ライフサイクルフック onMounted |

##### 初期表示（onMounted）
1. 顧客識別IDを受け取る。
2. 法人顧客の基本属性情報を取得する。
3. 取得したデータを画面に表示する。

#### オウンドメディアサブセクション

##### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 顧客識別IDに基づいてオウンドメディア情報を取得し一覧表示する | ライフサイクルフック onMounted |

##### 初期表示（onMounted）
1. 顧客識別IDを受け取る。
2. オウンドメディア情報の一覧を取得する。
3. 取得したデータを一覧形式で表示する。

### 事業概要セクション

#### 事業概要サブセクション（BusinessOverviewSection）

##### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 顧客識別IDに基づいて事業概要情報を取得し表示する | ライフサイクルフック onMounted |

##### 初期表示（onMounted）
1. 顧客識別IDを受け取る。
2. 法人顧客の事業概要情報を取得する。
3. 業種マスタ情報を取得し、業種コードから業種名を解決する。
4. 取得したデータを画面に表示する。
    - 業種コード
    - 業種情報（大分類、中分類、小分類、細分類名称）

#### 業種サブセクション（IndustrySection）

##### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 顧客識別IDに基づいて業種詳細情報を取得し表示する | ライフサイクルフック onMounted |

##### 初期表示（onMounted）
1. 顧客識別IDを受け取る。
2. 法人顧客の業種情報を取得する。
3. 日本標準産業分類に基づく詳細業種情報を表示する。

### コンタクトセクション

#### 連絡先サブセクション（ContactAddressSection）

##### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 顧客識別IDに基づいて連絡先情報を取得し表示する | ライフサイクルフック onMounted |

##### 初期表示（onMounted）
1. 顧客識別IDを受け取る。
2. 法人顧客の連絡先情報を取得する。
3. 取得したデータを画面に表示する。
    - 住所（郵便番号、都道府県、市区町村、番地、建物名）
    - 電話番号（固定電話、携帯電話の種別も表示）
    - メールアドレス（会社、個人の種別も表示）
```
-->