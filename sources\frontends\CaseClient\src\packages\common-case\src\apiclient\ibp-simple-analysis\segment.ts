﻿import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type { ApiResult, Pagination } from '@hox/base/src/apiclient/shared/types'

export const segmentSchema = z.object({
  id: z.string(),
  segmentName: z.string().max(15),
  detail: z.string().max(200),
  order: z
    .number()
    .int('整数で入力してください')
    .max(2147483647, '0～2147483647の範囲で入力してください')
    .min(0, '0～2147483647の範囲で入力してください'),
  isUserNonEditable: z.boolean().nullish(),
  isInProcessUnlink: z.boolean().nullish(),
  version: z.string(),
})

export type Segment = z.infer<typeof segmentSchema>

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================
export type FindSegmentCriteria = {
  // 画面での検索条件なし
} & Pagination

export type FindSegmentResultItem = {
  id: string
  segmentName: string
  detail: string
  order: number
  isUserNonEditable: boolean
  isInProcessUnlink: boolean
  version: string
}
// 検索結果の型にページングの情報を追加します。
export type FindSegmentResult = ApiResult<FindSegmentResultItem>

/**
 * データを指定して検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindSegments(query: Ref<FindSegmentCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindSegmentResult>(
    useFetch(
      () => $endpoints.default.get('ibp-simple-analysis/v1.0/segment'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * セグメントを取得する
 */
export function useGetSegment(id: string) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Segment>(
    useFetch(
      () => $endpoints.default.get(`ibp-simple-analysis/v1.0/segment/${id}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 登録関連
 */
export const segmentSchemaForPost = segmentSchema.omit({
  id: true,
  version: true,
})
export type SegmentForPost = z.infer<typeof segmentSchemaForPost>

/**
 * セグメントを新規登録する
 */
export function usePostSegment(body: Ref<SegmentForPost>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Segment>(
    useFetch(
      () => $endpoints.default.get('ibp-simple-analysis/v1.0/segment'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 更新関連
 */
export const segmentSchemaForPut = segmentSchema
export type SegmentForPut = z.infer<typeof segmentSchemaForPut>

/**
 * セグメントを更新する
 */
export function usePutSegment(body: Ref<SegmentForPut>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Segment>(
    useFetch(
      () => $endpoints.default.get('ibp-simple-analysis/v1.0/segment'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 削除関連
 */
export const segmentSchemaForDelete = segmentSchema.pick({
  id: true,
  segmentName: true, // memo:削除時のメッセージ表示用
  version: true,
})
export type SegmentForDelete = z.infer<typeof segmentSchemaForDelete>
/**
 * セグメントを削除する
 */
export function useDeleteSegment(body: Ref<SegmentForDelete>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () => $endpoints.default.get('ibp-simple-analysis/v1.0/segment'),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

/**
 * 一括紐づけ解除関連
 */
export const segmentForUpdateIsProcessUnlinkSchema = segmentSchema.pick({
  id: true,
  version: true,
})

export type SegmentForUpdateIsProcessUnlink = z.infer<
  typeof segmentForUpdateIsProcessUnlinkSchema
>
/**
 * セグメントを一括紐づけ解除する
 */
export function useUpdateSegmentIsProcessUnlink(
  body: Ref<SegmentForUpdateIsProcessUnlink>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Segment>(
    useFetch(
      () =>
        $endpoints.default.get(
          'ibp-simple-analysis/v1.0/segment/isProcessUnlink',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}
