### Shared.EntityFrameworkCore

## 概要

Shared.EntityFrameworkCoreライブラリは、Entity Framework Coreを使用したデータアクセス層の拡張機能を提供するライブラリです。リポジトリパターン、仕様パターン、ValueConverter、データベースセットアップなどの機能を提供し、Entity Framework Coreを使用したアプリケーションの開発効率と保守性向上を支援します。

## 主要機能

### リポジトリパターン
- **汎用リポジトリ**: 型安全なDBアクセス、クエリの共通化
- **リポジトリ関数**: 汎用リポジトリ関数、DB操作の効率化
- **拡張メソッド**: IQueryableに対するクエリ操作拡張

### 仕様パターン
- **仕様評価**: 仕様パターンの評価・実装基盤
- **クエリ部品**: 柔軟なクエリ構築用部品
- **クエリ最適化**: クエリ部品の評価、DBクエリ最適化

### データベース管理
- **自動値設定**: SaveChanges時の標準値自動適用
- **モデル拡張**: Entity Frameworkのモデル構築拡張
- **設定管理**: DB接続、運用設定の一元管理

### データ変換
- **ValueConverter**: 日付・カスタム型等の型変換
- **コマンドビルダ**: DBコマンド生成・実行支援
- **プロバイダ対応**: SQL Server、SQLite等の対応

### セットアップ・テスト
- **データベースセットアップ**: マイグレーション、テスト用初期化
- **例外処理**: DB関連の標準例外クラス
- **テスト支援**: 開発環境の初期化処理

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| EntityFrameworkRepository / EntityFrameworkRepositoryT | リポジトリ | Entity Frameworkを用いた汎用リポジトリ実装 | DBアクセス共通化・再利用性向上 |
| ApplyStandardValuesInterceptor | インターセプタ | SaveChanges時にエンティティへ標準値を自動適用するインターセプタ | データ整合性・自動補完 |
| IQueryableExtensions | 拡張メソッド | IQueryableに対するクエリ操作拡張メソッド群 | クエリ操作効率化・API標準化 |
| ModelBuilderExtensions | モデル拡張 | Entity Frameworkのモデル構築を拡張するメソッド群 | DBスキーマ定義・拡張性向上 |
| DatabaseSettings | 設定 | DB接続や運用に必要な設定値を管理 | DB構成・運用管理 |
| ValueConverter各種 | 型変換 | 日付・カスタム型等のValueConverter実装群 | DB型変換・データ整合性 |
| NotMappedToTableException / ResourceDataNotFoundException | 例外 | DB関連の標準例外クラス | エラー処理統一・例外管理 |
| EFCoreSpecificationEvaluator / AbstractSpecificationEvaluator | 仕様パターン | 仕様パターンの評価・実装基盤 | 柔軟なクエリ構築・ドメイン駆動設計 |
| DeleteCommandBuilder / InsertCommandBuilder / MultiValuesInsertCommandBuilder / SqliteDeleteCommandBuilder / SqliteInsertCommandBuilder / SqlServerInsertCommandBuilder / SqlServerDeleteCommandBuilder | コマンドビルダ | DBコマンド生成・実行を支援するビルダ群 | DB操作効率化・SQL生成 |
| DatabaseSetup | 初期化 | DBセットアップやテスト用初期化処理 | テスト・開発環境の初期化 |
