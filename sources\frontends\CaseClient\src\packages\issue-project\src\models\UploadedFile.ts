export interface UploadedFile {
  fileName: string
  updatedBy: string
  updatedDateTime: string
  version?: string | number
  fileSize?: number
  fileType?: string
  filePath?: string
}

export interface UploadFile extends File {
  previewUrl?: string
  progress?: number
  status?: 'pending' | 'uploading' | 'success' | 'error'
  errorMessage?: string
}

export interface FileToDelete {
  deleteTargetFile: UploadedFile
  deleteItemName: string
  deleteItemVersion: string | number
}

export interface FileUploadResult {
  uploadFiles: File[]
  filesToRemove: FileToDelete[]
}
