import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import { type Ref } from 'vue'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

const getCustomerReactionSchema = z.object({
  id: z.string(),
  content: z.string(),
  registeredDateTime: z.string(),
  register: z.string(),
  hypotheticalDiscussionOfIssuesId: z.string(),
})

const hypotheticalDiscussionOfIssuesCommentReactionSchema = z.object({
  id: z.string(),
  commentId: z.string(),
  reactionType: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  updatedDateTime: z.string(),
})

const hypotheticalDiscussionOfIssuesCommentFileSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  commentId: z.string(),
  updatedDateTime: z.string(),
  updaterId: z.string().nullable(),
  updaterName: z.string().nullable(),
})

const hypotheticalDiscussionOfIssuesCommentSchema = z.object({
  id: z.string(),
  registeredDateTime: z.string(),
  registrant: z.string(),
  registrantId: z.string(),
  description: z.string(),
  containCustomerReaction: z.boolean(),
  purpose: z.number(),
  person: z.string().nullable(),
  isPersonOfPower: z.boolean().nullable(),
  reactions: z.array(hypotheticalDiscussionOfIssuesCommentReactionSchema),
  files: z.array(hypotheticalDiscussionOfIssuesCommentFileSchema),
  threadId: z.string(),
  mentionTargetUserIds: z.array(z.string()).nullable(),
  mentionTargetTeamIds: z.array(z.string()).nullable(),
})

const hypotheticalDiscussionOfIssuesThreadReactionSchema = z.object({
  id: z.string(),
  threadId: z.string(),
  reactionType: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  updatedDateTime: z.string(),
})

const hypotheticalDiscussionOfIssuesThreadFileSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  threadId: z.string(),
  updatedDateTime: z.string(),
  updaterId: z.string().nullable(),
  updaterName: z.string().nullable(),
})

const hypotheticalDiscussionOfIssuesThreadSchema = z.object({
  id: z.string(),
  registeredDateTime: z.string(),
  registrant: z.string(),
  registrantId: z.string(),
  title: z.string(),
  description: z.string(),
  hypotheticalDiscussionOfIssuesId: z.string(),
  comments: z.array(hypotheticalDiscussionOfIssuesCommentSchema),
  containCustomerReaction: z.boolean(),
  purpose: z.number(),
  person: z.string().nullable(),
  isPersonOfPower: z.boolean().nullable(),
  correspondenceDate: z.string().nullable(),
  reactions: z.array(hypotheticalDiscussionOfIssuesThreadReactionSchema),
  files: z.array(hypotheticalDiscussionOfIssuesThreadFileSchema),
  mentionTargetsHtml: z.string().nullable(),
  mentionTargetUserIds: z.array(z.string()).nullable(),
  mentionTargetTeamIds: z.array(z.string()).nullable(),
})

const hypotheticalDiscussionOfIssuesSchemaForGet = z.object({
  id: z.string(),
  staffId: z.string(),
  staffName: z.string(),
  currentSituation: z.string().nullable(),
  ideal: z.string().nullable(),
  issue: z.string().nullable(),
  threads: z.array(hypotheticalDiscussionOfIssuesThreadSchema).nullable(),
  customerReaction: getCustomerReactionSchema.nullable(),
  expiredAt: z.string().nullable(),
  registeredDateTime: z.string(),
  updatedDateTime: z.string().nullable(),
  completedDateTime: z.string().nullable(),
  updaterId: z.string().nullable(),
  updaterName: z.string().nullable(),
  registrantId: z.string().nullable(),
  registrantName: z.string().nullable(),
  title: z.string().nullable(),
  status: z.number().nullable(),
  order: z.number().nullable(),
  businessUnderstandingId: z.string(),
  version: z.string(),
})

type HypotheticalDiscussionOfIssuesForGet = z.infer<typeof hypotheticalDiscussionOfIssuesSchemaForGet>

/**
 * 課題の仮説協議データをIDで取得する
 * @param id 課題の仮説協議ID
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetHypotheticalDiscussionOfIssues(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<HypotheticalDiscussionOfIssuesForGet>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/hypotheticalDiscussionOfIssues/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}