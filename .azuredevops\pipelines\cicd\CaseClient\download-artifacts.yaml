jobs:
- job: CaseClientClientAppDownloadArtifacts
  pool:
    vmImage: $(vmImageName)
  steps:
    # Download Artifact by envrionment
    - task: DownloadPipelineArtifact@2
      displayName: Download Current Artifact
      inputs:
        source: current
        artifact: CaseClient
        path: $(Pipeline.Workspace)/CaseClient
      condition: ne('${{ parameters.env }}', 'production')

    - task: DownloadPipelineArtifact@2
      displayName: Download Specific Artifact
      inputs:
        source: specific
        project: ${{ parameters.devopsProjectName }}
        pipeline: ${{ parameters.pipelineId }}
        tags: $(Build.SourceVersion)
        artifact: CaseClient
        path: $(Pipeline.Workspace)/CaseClient
        allowFailedBuilds: false
      condition: eq('${{ parameters.env }}', 'production')

    - publish: '$(Pipeline.Workspace)/CaseClient'
      displayName: 'Publish Artifact'
      artifact: CaseClient
