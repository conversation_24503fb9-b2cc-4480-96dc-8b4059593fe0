<template>
  <section>
    <h1>@ibp/base Components 使用パターン</h1>
    
    <!-- AppIconBtn（アイコンボタン）コンポーネント -->
    <section>
      <h2>AppIconBtn（アイコンボタン）コンポーネント</h2>

      <div class="component-group">
        <app-icon-btn
          icon="mdi-home"
          help-message="ホーム"
          @click="handleIconClick('home')"
        />
        <app-icon-btn
          icon="mdi-account"
          help-message="ユーザー情報"
          @click="handleIconClick('user')"
        />
      </div>
    </section>

    <!-- BasicCard（基本カード）コンポーネント -->
    <section>
      <h2>BasicCard（基本カード）コンポーネント</h2>
      
      <h3>基本的な使用方法</h3>
      <div class="component-group">
        <basic-card title="シンプルなカード">
          <p>これは基本的なカードコンポーネントです。</p>
          <p>タイトルとコンテンツを表示できます。</p>
        </basic-card>
      </div>

      <h3>プロパティとスロットのパターン</h3>
      <div class="component-group">
        <h4>タイトルプロパティ使用</h4>
        <basic-card title="プロパティで設定したタイトル">
          <p>titleプロパティを使用してタイトルを設定した例です。</p>
          <v-list>
            <v-list-item>
              <v-list-item-title>リスト項目1</v-list-item-title>
            </v-list-item>
            <v-list-item>
              <v-list-item-title>リスト項目2</v-list-item-title>
            </v-list-item>
            <v-list-item>
              <v-list-item-title>リスト項目3</v-list-item-title>
            </v-list-item>
          </v-list>
        </basic-card>

        <h4>タイトルスロット使用</h4>
        <basic-card>
          <template #title>
            <span style="color: #1976d2; font-weight: bold;">カスタムタイトル</span>
          </template>
          <p>titleスロットを使用してカスタムタイトルを設定した例です。</p>
          <p>スロットを使用することで、HTMLタグやスタイルを含むタイトルを設定できます。</p>
        </basic-card>

        <h4>アイコンスロット使用</h4>
        <basic-card title="アイコン付きカード">
          <template #icon>
            <v-icon color="success" size="large">mdi-check-circle</v-icon>
          </template>
          <p>iconスロットを使用してタイトルの横にアイコンを表示した例です。</p>
          <v-alert type="success" variant="tonal">
            処理が正常に完了しました。
          </v-alert>
        </basic-card>

        <h4>ボタンスロット使用</h4>
        <basic-card title="ボタン付きカード">
          <template #button>
            <v-btn color="primary" @click="handleCardAction('primary')">
              メインアクション
            </v-btn>
            <v-btn color="secondary" variant="outlined" @click="handleCardAction('secondary')">
              サブアクション
            </v-btn>
          </template>
          <p>buttonスロットを使用してカードの右上にボタンを配置した例です。</p>
          <p>複数のボタンを配置することも可能です。</p>
        </basic-card>

        <h4>すべてのスロットを使用</h4>
        <basic-card>
          <template #title>
            <span style="color: #9c27b0;">完全装備カード</span>
          </template>
          <template #icon>
            <v-icon color="purple" size="large">mdi-star</v-icon>
          </template>
          <template #button>
            <v-btn color="purple" size="small" @click="handleCardAction('featured')">
              特集
            </v-btn>
          </template>
          <p>title、icon、buttonのすべてのスロットを使用した例です。</p>
          <v-row>
            <v-col cols="6">
              <v-card variant="outlined">
                <v-card-text>
                  ネストされたカード1
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="6">
              <v-card variant="outlined">
                <v-card-text>
                  ネストされたカード2
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </basic-card>
      </div>

      <h3>ローディング状態</h3>
      <div class="component-group">
        <h4>タイトルローディング</h4>
        <basic-card :is-loading-title="isLoadingTitle">
          <template #button>
            <v-btn @click="toggleTitleLoading">
              {{ isLoadingTitle ? 'ローディング停止' : 'ローディング開始' }}
            </v-btn>
          </template>
          <p>isLoadingTitleプロパティをtrueにすると、タイトル部分にスケルトンローダーが表示されます。</p>
          <p>データの読み込み中などに使用します。</p>
        </basic-card>
      </div>
    </section>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// ミドルウェア定義サンプル
definePageMeta({
  middleware: [
    // アクセス可否チェック
    'check-accessible', 
    // アクセスログ出力
    'add-access-log',
    // 次画面パスチェック
    'check-next-path'
  ],
})

// BasicCard 関連
const isLoadingTitle = ref(false)

// イベントハンドラー
const handleIconClick = (action: string) => {
  console.log('AppIconBtn clicked:', action)
}

const handleCardAction = (action: string) => {
  console.log('Card action:', action)
}

const toggleTitleLoading = () => {
  isLoadingTitle.value = !isLoadingTitle.value
}
</script>
