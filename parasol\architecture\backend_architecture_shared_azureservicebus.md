### Shared.AzureServiceBus

## 概要

Shared.AzureServiceBusライブラリは、Azure Service Busとの連携機能を提供するライブラリです。メッセージの送受信、キューの管理、接続の最適化などの機能を提供し、Azure Service Busを使用したメッセージングアプリケーションの開発効率と保守性向上を支援します。

## 主要機能

### 接続管理
- **接続設定**: Service Busへの接続、運用に必要な設定値管理
- **接続文字列**: 接続先、認証情報の一元管理
- **設定管理**: 各種設定値の一元化

### クライアント最適化
- **接続キャッシュ**: 接続や認証情報のキャッシュ
- **効率的通信**: 効率的なService Bus通信の実現
- **リソース管理**: リソース効率化、パフォーマンス最適化

### テスト・開発支援
- **スタブ実装**: テストや開発用途でのService Busクライアントスタブ
- **単体テスト**: 単体テストでの動作検証
- **開発環境**: 開発環境での動作確認

### 依存性注入
- **DI拡張**: Service Bus関連サービスのDI登録
- **サービス構成**: 依存性注入の簡素化
- **設定管理**: サービス構成の一元化

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| ServiceBusSettings | 設定 | Azure Service Busへの接続や運用に必要な設定値（接続文字列等）を管理。 | 接続先・認証情報の一元管理 |
| CachedServiceBusClient | クライアント | 接続や認証情報をキャッシュし、効率的なServiceBus通信を実現するクライアント。 | パフォーマンス最適化・リソース効率化 |
| ServiceBusClientStub | スタブ | テストや開発用途で利用するServiceBusクライアントのスタブ実装。 | 単体テスト・開発環境での動作検証 |
| IServiceCollectionExtensions | DI拡張 | ServiceBus関連サービスのDI登録を簡単に行う拡張メソッド群。 | サービス構成・依存性注入の簡素化 |
