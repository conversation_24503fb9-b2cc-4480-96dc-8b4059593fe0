<template>
  <div>
    <v-dialog v-model="dialog" @click:outside="cancel" @keydown.esc="cancel">
      <app-simple-dialog-tmpl
        :loading="loading"
        dialog-title="CSVアップロード"
        @action="action"
        @cancel="cancel"
      >
        <template #default>
          <v-row>
            <AppFileInput
              v-model="targetData.attachedFile"
              label="ファイルを選択してください"
              required-mark
              is-chips
              show-size
              counter
              clearable
              accept=".csv"
              :error-messages="errorMessages?.attachedFile"
            />
          </v-row>
        </template>
      </app-simple-dialog-tmpl>
    </v-dialog>
    <v-dialog v-model="uploadedDialog" max-width="560">
      <v-card>
        <v-card-title> 結果は案件データ取込履歴から確認ください </v-card-title>
        <v-card-text>
          処理に時間がかかる場合がございます。都度、「最新情報に更新」してご確認ください。
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <app-main-btn @click="emitCsvUploaded()">OK</app-main-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { useUploadByCsv } from '@ibp/common-case/src/apiclient/customerProposal/case'
import { z } from 'zod'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'

// =====================================================================================================================
// プロパティの定義
// =====================================================================================================================

interface Props {
  caseCategory?: string
}

const props = withDefaults(defineProps<Props>(), {
  caseCategory: '',
})

// =====================================================================================================================
// イベントの定義
// =====================================================================================================================

interface Emits {
  csvUploaded: []
}

const emit = defineEmits<Emits>()

// =====================================================================================================================
// リアクティブデータの定義
// =====================================================================================================================

const dialog = ref<boolean>(false) // ダイアログの表示非表示
const uploadedDialog = ref<boolean>(false)
const targetData = ref<{
  attachedFile: File[] | null
  caseCategory: string
}>({
  attachedFile: null,
  caseCategory: '',
})
const formData = ref<FormData>(new FormData())

// Promise の resolve/reject 関数
const promiseResolve = ref<((value: any) => void) | null>(null)
const promiseReject = ref<((reason?: any) => void) | null>(null)

// ローディング状態の管理
const inProgress = ref(false)
const { hasTrue: loading } = useFlagCondition(inProgress)

// Toast の設定
const { error: errorToast } = useAppToasts()

// =====================================================================================================================
// バリデーションの定義
// =====================================================================================================================

// バリデーションスキーマの定義
const csvUploadSchema = z.object({
  attachedFile: z
    .any()
    .refine((file) => file !== null && file !== undefined, {
      message: 'ファイルが選択されていません',
    }),
  caseCategory: z
    .string()
    .min(1, { message: '案件カテゴリを選択してください' }),
})

// useValidationの正しい使用方法
const { errorMessages, validate } = useValidation(
  csvUploadSchema,
  targetData,
)

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

const { executeWithResult: uploadByCsv } = useUploadByCsv(formData)

// =====================================================================================================================
// 外部に公開するメソッドの定義
// =====================================================================================================================

// ダイアログを表示します。ダイアログを表示するときに初期値を渡す引数を定義します。
function open() {
  // 各種状態を初期化します。
  targetData.value = {
    attachedFile: null,
    caseCategory: props.caseCategory,
  }
  errorMessages.value = {}

  // ダイアログを表示します。
  dialog.value = true

  // 呼び出し元に Promise を返します。
  return new Promise((resolve, reject) => {
    promiseResolve.value = resolve
    promiseReject.value = reject
  })
}

// =====================================================================================================================
// 内部メソッドの定義
// =====================================================================================================================

// アクションボタンがクリックされたときの処理をします。
async function action() {
  // バリデーションを実行します。バリデーションが不要な場合は削除してください。
  const { success } = await validate()
  if (!success) return

  inProgress.value = true

  try {
    formData.value = new FormData()
    if (targetData.value.attachedFile) {
      formData.value.append('file', targetData.value.attachedFile[0])
    }
    formData.value!.append('caseCategory', targetData.value.caseCategory)

    // apiClient を呼び出して CSV アップロードを実行します。
    const result = await uploadByCsv().catch((e) => {
      // 失敗だった場合の処理を行います。
      if (!e.hasProblemDetails) {
        errorToast('アップロードに失敗しました。')
      }
      if (e.data.type === '/validation-error') {
        Object.keys(e.data.errors).forEach((k) =>
          errorToast(e.data.errors[k]),
        )
      } else if (e.data.status === 500) {
        errorToast(
          'アップロードに失敗しました。',
        )
      } else {
        errorToast('アップロードに失敗しました。')
      }
      return false
    })
    if (!result) {
      return
    }

    // 処理結果を resolve にしてデータを返します。
    promiseResolve.value?.({
      isOk: true,
      data: Object.assign({}, targetData.value || {}),
    })

    // ダイアログを閉じます。
    dialog.value = false
    uploadedDialog.value = true
  } catch (error: any) {
    // APIがエラーだった場合は、メッセージを表示します。
    handleApiError(error)
  } finally {
    inProgress.value = false
  }
}

// 選択がされずに閉じられる/キャンセルボタンがクリックされたときの処理をします。
function cancel() {
  promiseResolve.value?.({ isOk: false })
  // ダイアログを閉じます。
  dialog.value = false
}

// APIエラーハンドリング
function handleApiError(error: any) {
  const data = error.response?.data || {}
  if (data.type === '/validation-error') {
    Object.keys(data.errors).forEach((k) =>
      errorToast(data.errors[k]),
    )
  } else if (error.response?.status === 500) {
    errorToast(error.response.data.detail)
  } else {
    errorToast('予期しないエラーが発生しました')
  }
}

// CSV アップロード完了イベントの発行
function emitCsvUploaded() {
  uploadedDialog.value = false
  emit('csvUploaded')
}

// =====================================================================================================================
// 外部に公開するメソッドとプロパティ
// =====================================================================================================================

// コンポーネントのrefとして利用するために公開
defineExpose({
  open,
})
</script>

<style scoped>
/* 必要に応じてスタイルを追加 */
</style>
