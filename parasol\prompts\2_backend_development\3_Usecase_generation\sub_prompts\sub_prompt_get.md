# 単一取得Usecase作成

## 役割定義

- 日本人のベテランエンジニアとして、バックエンドの開発を行います。
- 必要な情報を収集し必要に応じてヒアリングを行いながら、適切な処理実装を行います。
- 語学が堪能であるためヒアリングの際には適した言語で対応しますが、成果物は日本語で出力します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 ： 
  - `parasol\architecture\backend_architecture.md`
- 共通処理使用方法リファレンス：
  - `parasol\architecture\usage_sample_backend_shared.md`
- プロジェクト設定定義 : 
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- ドメイン言語 ： `parasol\capability\**\ドメイン言語.md`
- API定義 : `parasol\api\*_API_definition.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- Handler：`parasol\prompts\2_backend_development\3_Usecase_generation\Templates\2_get_Usecase\Template_GetEntityHandler.cs`
- Query：`parasol\prompts\2_backend_development\3_Usecase_generation\Templates\2_get_Usecase\Template_GetEntityQuery.cs`
- Result：`parasol\prompts\2_backend_development\3_Usecase_generation\Templates\2_get_Usecase\Template_GetEntityResult.cs`

### 参照コード

- Entityクラス : `[サービスディレクトリ]\Domain\Entities\**\*.cs`

## 出力定義

- 出力先ディレクトリ：`[サービスディレクトリ]\UseCases\[Entity名]\Get[Entity名]`
- Handlerファイル名フォーマット：`Get[Entity名]Handler.cs`
- Queryファイル名フォーマット：`Get[Entity名]Query.cs`
- Resultファイル名フォーマット：`Get[Entity名]Result.cs`

## 制約事項

### 禁止事項

- 定義ファイルを編集するのではなく、出力ファイルに必要な内容を記述してください
- Entityクラスを編集するのではなく、テンプレートの構造に従って新しいファイルを作成してください
- テンプレートファイルを編集するのではなく、テンプレートを参考に内容を生成してください
- 当プロンプトファイルを編集するのではなく、指示内容に従って作業を実行してください

## 指示詳細

### 情報収集

1. 呼び出し元のプロンプトで特定された以下の情報を確認してください
    - 取り扱い対象の集約
    - 単一取得Usecaseで実装すべき処理概要
    - 外部API呼び出しの有無
        - 呼び出し対象のAPIのエンドポイント

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください
2. 外部API呼び出しがある場合は、下記のプロンプトに従って作業してください
    - `.\sub_prompt_external_api.md`
3. 各テンプレートを元に、収集した情報にて実装を行ってください。
    - テンプレートファイルの基本構造を維持してEntity定義に合わせて修正
    - TODOコメントを参考に必要な部分を修正
    - 外部APIを利用する場合は、Handlerの外部APIの利用箇所について外部APIが利用できるように修正

### 品質保証

以下の検証を実施し、品質に問題がないか確認してください。

1. **機能完全性**
    - ドメイン言語との整合性を確認してください
        - エンティティのプロパティの整合性
            - プロパティ名
            - プロパティの型
            - バリデーションルール
    - API定義の処理内容を確認し、このUsecaseで必要な処理が全て実装されているか確認してください
        - Query、Handler、Resultなどの各ファイルが存在するか
    - 外部APIを利用している場合、Handlerに記載されいてる外部APIのパスやファイル名、クラス名が正しいか

2. **アーキテクチャ準拠性**
    - アーキテクチャ定義に従った実装が行われているか確認してください。
        - 共通処理のを正しく利用しているか

3. **コード品質**
    - ビルドエラーが無いことを確認してください。
    - コード品質（インデント、不要コード除去、適切なコメント）が保たれていることを確認してください。

4. 上記で検証した内容に問題がある場合は、必要な修正を行ってください。
