using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Nut.Results;
using Nut.Results.FluentAssertions;
using Shared.Domain;
using Shared.Messaging;
using Shared.ObjectStorage;
using Shared.Results.Errors;
using Shared.Spec;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Domain;
// using SampleService.Domain.Enums;
// using SampleService.Infrastructure.Persistence;
// using SampleService.Infrastructure.Storage;
// using SampleService.Services.Domain;
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.UpdateEntity;
// using SharedKernel.ExternalApi.MessageContract.Notification;
// using Entities = SampleService.Domain.Entities;

// TODO: 長いジェネリック型の短縮形エイリアス（可読性向上のため）
using EntityRepository = Shared.Domain.IRepository<SampleService.Domain.Entities.Entity, string>;
using RelatedEntityRepository = Shared.Domain.IRepository<SampleService.Domain.Entities.RelatedEntity, string>;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.UpdateEntity;

/// <summary>
/// UpdateEntityHandlerの単体テストクラス
/// </summary>
/// <remarks>
/// 統合テスト（SQLiteインメモリDB使用）を中心に、
/// Update処理のテストを包括的に実装し、コードカバレッジ100%を目指します。
/// 正常系、異常系、楽観的ロック、ファイル操作、認可、外部サービス連携を網羅的にテストします。
/// UpdateUseCaseは複雑な更新ロジックと競合制御を持つため、詳細なエラーハンドリングと状態検証を行います。
/// </remarks>
public class UpdateEntityHandlerTest : IAsyncLifetime
{
    #region Test Constants
    // TODO: テストファイル関連の定数
    private const int TestFileSize = 25;
    private const string TestFileContent1 = "updated file content 1";
    private const string TestFileContent2 = "updated file content 2";
    private const string TestFileName1 = "testfile1";
    private const string TestFileName2 = "testfile2";
    private const string TestFileDisplayName1 = "更新ファイル1.txt";
    private const string TestFileDisplayName2 = "更新ファイル2.png";
    #endregion

    private readonly ApplicationDbContext _dbContext;
    private readonly TestCurrentUserService _currentUserService;
    private readonly TestCurrentDateTimeService _currentDateTimeService;
    
    // TODO: 実際の外部サービス依存関係に合わせて以下のモックを調整してください
    private readonly Mock<IMessageSender<NotificationQuery, string>> _notificationSenderMock;
    private readonly Mock<IMessageSender<ExternalServiceQuery, ExternalServiceResult>> _externalServiceSenderMock;
    private readonly Mock<IMessageSender<UpdateRelatedEntityMessage>> _updateRelatedEntitySenderMock;
    private readonly Mock<IStorageClientProvider> _storageClientProviderMock;
    private readonly Mock<IObjectStorageClient> _objectStorageClientMock;
    private readonly IFileProcessingService _fileProcessingService;
    private readonly Mock<IImageProcessingService> _imageProcessingServiceMock;
    
    // TODO: 実際のストレージ設定に合わせて変更してください
    private readonly string _containerName = "entity-storage";
    private readonly string _folderName = "entity";

    public UpdateEntityHandlerTest()
    {
        _currentUserService = new TestCurrentUserService();
        _currentDateTimeService = new TestCurrentDateTimeService();

        _dbContext = TestDbContextBuilder.Build(_currentUserService, _currentDateTimeService);

        // TODO: 実際の外部サービスに合わせてモックを初期化してください
        _notificationSenderMock = new Mock<IMessageSender<NotificationQuery, string>>();
        _notificationSenderMock.Setup(x => x.SendAsync(It.IsAny<NotificationQuery>()))
            .ReturnsAsync(Result.Ok("notification-sent"));

        _externalServiceSenderMock = new Mock<IMessageSender<ExternalServiceQuery, ExternalServiceResult>>();
        _externalServiceSenderMock.Setup(x => x.SendAsync(It.IsAny<ExternalServiceQuery>()))
            .ReturnsAsync(Result.Ok(new ExternalServiceResult()));

        _updateRelatedEntitySenderMock = new Mock<IMessageSender<UpdateRelatedEntityMessage>>();
        _updateRelatedEntitySenderMock.Setup(x => x.SendAsync(It.IsAny<UpdateRelatedEntityMessage>()))
            .ReturnsAsync(Result.Ok());

        // ストレージ関連のモック初期化
        _storageClientProviderMock = new Mock<IStorageClientProvider>();
        _objectStorageClientMock = new Mock<IObjectStorageClient>();
        _objectStorageClientMock.Setup(m => m.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok);
        _objectStorageClientMock.Setup(m => m.DeleteAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok);
        _storageClientProviderMock.Setup(m => m.CreateAsync(_containerName))
            .ReturnsAsync(_objectStorageClientMock.Object);

        // ファイル処理サービス初期化
        _fileProcessingService = new FileProcessingService(new UnitOfWork(_dbContext), _storageClientProviderMock.Object, _currentDateTimeService);

        // 画像処理サービスのモック初期化
        _imageProcessingServiceMock = new Mock<IImageProcessingService>();
        _imageProcessingServiceMock.Setup(x => x.ProcessImageAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Ok("processed-image-url"));
    }

    public Task InitializeAsync()
        => TestData.CreateAsync(_dbContext);

    public Task DisposeAsync() => Task.CompletedTask;

    #region テスト対象とテストデータの準備

    /// <summary>
    /// テスト対象のハンドラーを作成します
    /// </summary>
    /// <returns>UpdateEntityHandler インスタンス</returns>
    private UpdateEntityHandler CreateHandler()
    {
        // TODO: プロジェクトのパターンに合わせてハンドラーの生成方法を調整してください
        var unitOfWork = new UnitOfWork(_dbContext);
        return new UpdateEntityHandler(
            unitOfWork,
            _currentDateTimeService,
            _currentUserService,
            _notificationSenderMock.Object,
            _externalServiceSenderMock.Object,
            _updateRelatedEntitySenderMock.Object,
            _fileProcessingService,
            _imageProcessingServiceMock.Object
        );
    }

    /// <summary>
    /// 正常なコマンドを作成します（既存データのVersionを取得して使用）
    /// </summary>
    /// <param name="entityId">更新対象のエンティティID</param>
    /// <returns>UpdateEntityCommand インスタンス</returns>
    private async Task<UpdateEntityCommand> CreateValidCommandAsync(string entityId)
    {
        // TODO: 実際のエンティティに合わせて調整してください
        var currentEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        if (currentEntity == null)
            throw new InvalidOperationException($"Entity with ID {entityId} not found for test setup");

        return new UpdateEntityCommand(
            Id: entityId,
            Name: "更新されたエンティティ",
            Description: "更新された説明",
            Status: EntityStatus.Active,
            Priority: 5,
            RelatedEntityId: "related-entity-1",
            Tags: new List<string> { "updated-tag1", "updated-tag2" },
            Version: currentEntity.Version,
            UploadFiles: null,
            FilesToRemove: null
        );
    }

    /// <summary>
    /// テスト用の既存エンティティIDを取得します
    /// </summary>
    /// <returns>テスト用エンティティID</returns>
    private static string GetTestEntityId()
    {
        // TODO: TestDataクラスで作成される実際のエンティティIDに合わせて変更してください
        return "entity-1";
    }

    #endregion

    #region 異常系テスト（null チェック）

    [Fact]
    public void Ctor_引数にnullを渡すと例外が発生する()
    {
        var unitOfWork = new UnitOfWork(_dbContext);

        // TODO: 実際のコンストラクタ引数に合わせて全パターンのnullチェックを実装してください
        Assert.Throws<ArgumentNullException>("unitOfWork", 
            () => new UpdateEntityHandler(
                null!, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _updateRelatedEntitySenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("currentDateTimeService", 
            () => new UpdateEntityHandler(
                unitOfWork, 
                null!, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _updateRelatedEntitySenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("currentUserService", 
            () => new UpdateEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                null!, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _updateRelatedEntitySenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("notificationSender", 
            () => new UpdateEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                null!, 
                _externalServiceSenderMock.Object, 
                _updateRelatedEntitySenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("externalServiceSender", 
            () => new UpdateEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                null!, 
                _updateRelatedEntitySenderMock.Object, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("updateRelatedEntitySender", 
            () => new UpdateEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                null!, 
                _fileProcessingService, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("fileProcessingService", 
            () => new UpdateEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _updateRelatedEntitySenderMock.Object, 
                null!, 
                _imageProcessingServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("imageProcessingService", 
            () => new UpdateEntityHandler(
                unitOfWork, 
                _currentDateTimeService, 
                _currentUserService, 
                _notificationSenderMock.Object, 
                _externalServiceSenderMock.Object, 
                _updateRelatedEntitySenderMock.Object, 
                _fileProcessingService, 
                null!));
    }

    [Fact]
    public async Task Handle_Requestがnullの場合は例外が発生する()
    {
        var handler = CreateHandler();

        var act = () => handler.Handle(null!, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    #endregion

    #region データ不存在・楽観的ロックテスト（Update特有）

    [Fact]
    public async Task Handle_指定されたデータが存在しない場合はDataNotFoundErrorが返る()
    {
        // Arrange
        var command = new UpdateEntityCommand(
            Id: "NONEXISTENT_ID",
            Name: "更新名",
            Description: "更新説明",
            Status: EntityStatus.Active,
            Priority: 1,
            RelatedEntityId: null,
            Tags: null,
            Version: "dummy-version",
            UploadFiles: null,
            FilesToRemove: null
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    [Fact]
    public async Task Handle_Versionが違う場合は楽観的ロックエラーが返る()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var currentEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        var command = new UpdateEntityCommand(
            Id: entityId,
            Name: "更新名",
            Description: "更新説明", 
            Status: EntityStatus.Active,
            Priority: 1,
            RelatedEntityId: null,
            Tags: null,
            Version: currentEntity.Version + "_INVALID", // 異なるVersion
            UploadFiles: null,
            FilesToRemove: null
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
        // TODO: 実際の楽観的ロックエラー型に合わせて調整してください
        // result.Should().BeError().And.BeOfType<OptimisticLockError>();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("INVALID_VERSION_123")]
    public async Task Handle_無効なVersionで楽観的ロックエラーが返る(string invalidVersion)
    {
        // Arrange
        var entityId = GetTestEntityId();
        var command = new UpdateEntityCommand(
            Id: entityId,
            Name: "更新名",
            Description: "更新説明",
            Status: EntityStatus.Active,
            Priority: 1,
            RelatedEntityId: null,
            Tags: null,
            Version: invalidVersion,
            UploadFiles: null,
            FilesToRemove: null
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    #endregion

    #region 正常系テスト

    [Fact]
    public async Task Handle_指定された内容で更新される()
    {
        // Arrange
        var expectedDateTime = new DateTimeOffset(2024, 1, 15, 10, 30, 0, TimeSpan.Zero);
        var expectedUserName = "UpdateTestUser";

        _currentDateTimeService.UpdateNowDateTimeOffset(expectedDateTime);
        _currentUserService.UpdateUserName(expectedUserName);

        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        var handler = CreateHandler();

        // 更新前の状態を記録
        var beforeUpdate = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // データベースから更新後のエンティティを取得して詳細検証
        var updatedEntity = await _dbContext.Entities
            .Include(e => e.RelatedEntity)
            .Include(e => e.Tags)
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();
        
        updatedEntity.Should().NotBeNull();
        
        // TODO: 実際のエンティティプロパティに合わせて検証項目を調整してください
        // 更新されたプロパティの検証
        updatedEntity.Name.Should().Be(command.Name);
        updatedEntity.Description.Should().Be(command.Description);
        updatedEntity.Status.Should().Be(command.Status);
        updatedEntity.Priority.Should().Be(command.Priority);
        updatedEntity.RelatedEntityId.Should().Be(command.RelatedEntityId);
        
        // メタ情報の検証（作成日時は変更されない）
        updatedEntity.CreatedAt.Should().Be(beforeUpdate.CreatedAt);
        updatedEntity.CreatedBy.Should().Be(beforeUpdate.CreatedBy);
        // 更新日時・更新者は変更される
        updatedEntity.UpdatedAt.Should().Be(expectedDateTime);
        updatedEntity.UpdatedBy.Should().Be(expectedUserName);
        // バージョンは更新される
        updatedEntity.Version.Should().NotBe(beforeUpdate.Version);

        // TODO: 関連エンティティや派生処理がある場合は追加で検証してください
        // 例: 関連エンティティの更新、ログテーブル、キューテーブルなど
        // var queueEntity = _dbContext.ProcessingQueues.Where(x => x.EntityId == entityId).FirstOrDefault();
        // queueEntity.Should().NotBeNull();
        // queueEntity.ProcessDateTime.Should().Be(expectedDateTime);

        // 外部サービス呼び出しの検証
        _imageProcessingServiceMock.Verify(m => m.ProcessImageAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);
        _updateRelatedEntitySenderMock.Verify(m => m.SendAsync(It.IsAny<UpdateRelatedEntityMessage>()), Times.Once);
    }

    [Fact]
    public async Task Handle_部分更新が正しく動作する()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var currentEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        // 一部のフィールドのみ更新するコマンド
        var command = new UpdateEntityCommand(
            Id: entityId,
            Name: "部分更新された名前", // これだけ更新
            Description: currentEntity.Description, // 元の値を保持
            Status: currentEntity.Status, // 元の値を保持
            Priority: currentEntity.Priority, // 元の値を保持
            RelatedEntityId: currentEntity.RelatedEntityId, // 元の値を保持
            Tags: currentEntity.Tags?.Select(t => t.Name).ToList(), // 元の値を保持
            Version: currentEntity.Version,
            UploadFiles: null,
            FilesToRemove: null
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        var updatedEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        // 更新されたフィールドの確認
        updatedEntity.Name.Should().Be("部分更新された名前");
        // 更新されていないフィールドの確認（元の値と同じ）
        updatedEntity.Description.Should().Be(currentEntity.Description);
        updatedEntity.Status.Should().Be(currentEntity.Status);
        updatedEntity.Priority.Should().Be(currentEntity.Priority);
    }

    [Theory]
    [InlineData(EntityStatus.Active)]
    [InlineData(EntityStatus.Inactive)]
    [InlineData(EntityStatus.Draft)]
    public async Task Handle_異なるステータスで正しく更新される(EntityStatus status)
    {
        // Arrange
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        command = command with { Status = status };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        var updatedEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();
        
        updatedEntity.Should().NotBeNull();
        updatedEntity.Status.Should().Be(status);
    }

    #endregion

    #region ファイル操作テスト（Update特有）

    [Fact]
    public async Task Handle_ファイル追加で正しく更新される()
    {
        // Arrange
        var formFiles = CreateTestFiles();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        command = command with { UploadFiles = formFiles };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // ファイルストレージの呼び出し検証
        _objectStorageClientMock.Verify(m => m.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()), Times.Exactly(formFiles.Count));
        
        // TODO: 実際のファイルパスパターンに合わせて調整してください
        foreach (var file in formFiles)
        {
            var expectedPath = $"{_folderName}/{entityId}/{file.FileName}";
            _objectStorageClientMock.Verify(m => m.PostAsync(expectedPath, It.IsAny<Stream>()), Times.Once);
        }

        // データベースのファイル情報検証
        var updatedEntity = await _dbContext.Entities
            .Include(e => e.Files)
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        // 元のファイル数 + 追加ファイル数を確認
        updatedEntity.Files.Should().Contain(f => formFiles.Any(uf => uf.FileName == f.FileName));
    }

    [Fact]
    public async Task Handle_ファイル削除で正しく更新される()
    {
        // Arrange
        var entityId = GetTestEntityId();
        
        // 削除対象のファイル名を取得（事前にテストデータに存在するファイル名）
        var existingFiles = await _dbContext.Files
            .Where(f => f.EntityId == entityId)
            .Select(f => f.FileName)
            .ToListAsync();
        
        var filesToRemove = existingFiles.Take(1).ToList(); // 1つのファイルを削除
        
        var command = await CreateValidCommandAsync(entityId);
        command = command with { FilesToRemove = filesToRemove };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // ストレージからの削除確認
        _objectStorageClientMock.Verify(m => m.DeleteAsync(It.IsAny<string>()), Times.Exactly(filesToRemove.Count));
        
        foreach (var fileName in filesToRemove)
        {
            var expectedPath = $"{_folderName}/{entityId}/{fileName}";
            _objectStorageClientMock.Verify(m => m.DeleteAsync(expectedPath), Times.Once);
        }

        // データベースからファイル情報が削除されていることを確認
        var updatedEntity = await _dbContext.Entities
            .Include(e => e.Files)
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        foreach (var fileName in filesToRemove)
        {
            updatedEntity.Files.Should().NotContain(f => f.FileName == fileName);
        }
    }

    [Fact]
    public async Task Handle_ファイル追加と削除を同時に実行して正しく更新される()
    {
        // Arrange
        var formFiles = CreateTestFiles();
        var entityId = GetTestEntityId();
        
        // 既存ファイルから削除対象を選択
        var existingFiles = await _dbContext.Files
            .Where(f => f.EntityId == entityId)
            .Select(f => f.FileName)
            .ToListAsync();
        var filesToRemove = existingFiles.Take(1).ToList();
        
        var command = await CreateValidCommandAsync(entityId);
        command = command with 
        { 
            UploadFiles = formFiles, 
            FilesToRemove = filesToRemove 
        };
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // ファイル追加の確認
        _objectStorageClientMock.Verify(m => m.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()), Times.Exactly(formFiles.Count));
        // ファイル削除の確認
        _objectStorageClientMock.Verify(m => m.DeleteAsync(It.IsAny<string>()), Times.Exactly(filesToRemove.Count));

        var updatedEntity = await _dbContext.Entities
            .Include(e => e.Files)
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        // 追加されたファイルが存在することを確認
        foreach (var file in formFiles)
        {
            updatedEntity.Files.Should().Contain(f => f.FileName == file.FileName);
        }
        
        // 削除されたファイルが存在しないことを確認
        foreach (var fileName in filesToRemove)
        {
            updatedEntity.Files.Should().NotContain(f => f.FileName == fileName);
        }
    }

    #endregion

    #region エラーハンドリングテスト

    [Fact]
    public async Task Handle_メインエンティティのリポジトリエラーはそのまま返る()
    {
        // Arrange
        var unitOfWork = new UnitOfWork(_dbContext);
        var unitOfWorkMock = new Mock<IUnitOfWork>();
        var repositoryMock = new Mock<EntityRepository>();
        
        unitOfWorkMock.Setup(x => x.GetRepository<Entity, string>())
            .Returns(repositoryMock.Object);

        repositoryMock.Setup(x => x.SingleAsync(It.IsAny<ISpecification<Entity>>()))
            .ReturnsAsync(Result.Error<Entity>(new Error("Repository Error")));

        var handler = new UpdateEntityHandler(
            unitOfWorkMock.Object,
            _currentDateTimeService,
            _currentUserService,
            _notificationSenderMock.Object,
            _externalServiceSenderMock.Object,
            _updateRelatedEntitySenderMock.Object,
            _fileProcessingService,
            _imageProcessingServiceMock.Object
        );
        
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Repository Error");
    }

    [Fact]
    public async Task Handle_関連エンティティのリポジトリエラーはそのまま返る()
    {
        // Arrange
        var unitOfWork = new UnitOfWork(_dbContext);
        var unitOfWorkMock = new Mock<IUnitOfWork>();
        var mainRepositoryMock = new Mock<EntityRepository>();
        var relatedRepositoryMock = new Mock<RelatedEntityRepository>();
        
        unitOfWorkMock.Setup(x => x.GetRepository<Entity, string>())
            .Returns(mainRepositoryMock.Object);
        unitOfWorkMock.Setup(x => x.GetRepository<RelatedEntity, string>())
            .Returns(relatedRepositoryMock.Object);

        // メインエンティティは正常取得
        mainRepositoryMock.Setup(x => x.SingleAsync(It.IsAny<ISpecification<Entity>>()))
            .ReturnsAsync(Result.Ok(TestData.Entity.CreateValid()));

        // 関連エンティティでエラー
        relatedRepositoryMock.Setup(x => x.SingleAsync(It.IsAny<ISpecification<RelatedEntity>>()))
            .ReturnsAsync(Result.Error<RelatedEntity>(new Error("Related Entity Error")));

        var handler = new UpdateEntityHandler(
            unitOfWorkMock.Object,
            _currentDateTimeService,
            _currentUserService,
            _notificationSenderMock.Object,
            _externalServiceSenderMock.Object,
            _updateRelatedEntitySenderMock.Object,
            _fileProcessingService,
            _imageProcessingServiceMock.Object
        );
        
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        command = command with { RelatedEntityId = "related-entity-1" };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Related Entity Error");
    }

    [Fact]
    public async Task Handle_外部サービスからのエラーはそのまま返る()
    {
        // Arrange
        _externalServiceSenderMock.Setup(x => x.SendAsync(It.IsAny<ExternalServiceQuery>()))
            .ReturnsAsync(Result.Error<ExternalServiceResult>(new Error("External Service Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("External Service Error");
    }

    [Fact]
    public async Task Handle_通知サービスからのエラーはそのまま返る()
    {
        // Arrange
        _notificationSenderMock.Setup(x => x.SendAsync(It.IsAny<NotificationQuery>()))
            .ReturnsAsync(Result.Error<string>(new Error("Notification Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Notification Error");
    }

    [Fact]
    public async Task Handle_画像処理サービスからのエラーはそのまま返る()
    {
        // Arrange
        _imageProcessingServiceMock.Setup(x => x.ProcessImageAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Error<string>(new Error("Image Processing Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Image Processing Error");
    }

    [Fact]
    public async Task Handle_ストレージサービスからのエラーはそのまま返る()
    {
        // Arrange
        var formFiles = CreateTestFiles();
        _objectStorageClientMock.Setup(m => m.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Error(new Error("Storage Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        command = command with { UploadFiles = formFiles };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Storage Error");
    }

    #endregion

    #region 通知・メンションテスト（Update特有の複雑な通知ロジック）

    [Fact]
    public async Task Handle_更新通知が正しく送信される()
    {
        // Arrange
        var capturedNotifications = new List<NotificationQuery>();
        _notificationSenderMock.Setup(x => x.SendAsync(It.IsAny<NotificationQuery>()))
            .Callback<NotificationQuery>(notification => capturedNotifications.Add(notification))
            .ReturnsAsync(Result.Ok("notification-sent"));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        command = command with 
        { 
            // TODO: 実際のメンション機能に合わせて調整してください
            MentionTargetUserIds = new List<string> { "mention-user-1", "mention-user-2" }
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // 通知送信の確認
        _notificationSenderMock.Verify(x => x.SendAsync(It.IsAny<NotificationQuery>()), Times.AtLeast(1));

        // TODO: 実際の通知ロジックに合わせて詳細検証を実装してください
        // var mentionNotification = capturedNotifications.FirstOrDefault(n => n.Type == NotificationType.Mention);
        // mentionNotification.Should().NotBeNull();
        // mentionNotification.TargetUserIds.Should().Contain("mention-user-1");
        // mentionNotification.TargetUserIds.Should().Contain("mention-user-2");
    }

    #endregion

    #region 境界値・エッジケーステスト

    [Fact]
    public async Task Handle_最小限の更新内容で正しく処理される()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var currentEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        // 必須フィールドのみの最小限更新
        var command = new UpdateEntityCommand(
            Id: entityId,
            Name: "最小限更新",
            Description: null,
            Status: EntityStatus.Draft,
            Priority: 0,
            RelatedEntityId: null,
            Tags: null,
            Version: currentEntity.Version,
            UploadFiles: null,
            FilesToRemove: null
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        var updatedEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        updatedEntity.Should().NotBeNull();
        updatedEntity.Name.Should().Be("最小限更新");
        updatedEntity.Description.Should().BeNull();
    }

    [Fact]
    public async Task Handle_最大文字数で更新される()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var currentEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        // TODO: 実際の文字数制限に合わせて調整してください
        var maxName = new string('あ', 100); // 最大文字数
        var maxDescription = new string('い', 1000);
        
        var command = new UpdateEntityCommand(
            Id: entityId,
            Name: maxName,
            Description: maxDescription,
            Status: EntityStatus.Active,
            Priority: 100,
            RelatedEntityId: null,
            Tags: Enumerable.Range(1, 10).Select(i => $"tag{i}").ToList(), // 最大個数
            Version: currentEntity.Version,
            UploadFiles: null,
            FilesToRemove: null
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        var updatedEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        updatedEntity.Should().NotBeNull();
        updatedEntity.Name.Should().Be(maxName);
        updatedEntity.Description.Should().Be(maxDescription);
    }

    #endregion

    #region ヘルパーメソッド

    /// <summary>
    /// テスト用ファイルを作成します
    /// </summary>
    /// <returns>テスト用ファイルのリスト</returns>
    private static List<IFormFile> CreateTestFiles()
    {
        var formFiles = new List<IFormFile>();
        
        // TODO: 実際のファイルテストパターンに合わせて調整してください
        var file1 = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes(TestFileContent1)), 0, TestFileSize, TestFileName1, TestFileDisplayName1);
        var file2 = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes(TestFileContent2)), 0, TestFileSize, TestFileName2, TestFileDisplayName2);
        
        formFiles.Add(file1);
        formFiles.Add(file2);
        
        return formFiles;
    }

    #endregion
}
