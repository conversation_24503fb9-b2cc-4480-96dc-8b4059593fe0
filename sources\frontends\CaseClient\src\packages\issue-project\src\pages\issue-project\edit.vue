<template>
  <div>
    <app-edit-page-tmpl
      :sections="sections"
      :can-remove="!isAdd"
      :loading="isLoading"
      :on-save="save"
      :on-remove="remove"
    >
      <template #main-actions>
        <app-sub-btn
          v-if="targetData.id"
          :loading="isLoading"
          small
          style="margin-top: 10px; width: 100%"
          @click="toIssueProjectDiscussionPage"
        >
          協議
        </app-sub-btn>
        <app-sub-btn
          v-if="targetData.id"
          :loading="isLoading"
          small
          style="margin-top: 10px; width: 100%"
          @click="toBusinessUnderstandingPage"
        >
          事業性理解詳細
        </app-sub-btn>
      </template>

      <template #section-basic-info>
        <app-edit-item-group-tmpl>
          <template #title>課題情報</template>
          <v-container fluid class="pt-1">
            <v-row>
              <v-col v-if="isAdd" cols="12"> 課題No： 未登録 </v-col>
              <v-col v-else cols="12"> 課題No： {{ targetData.issueInformation?.issueNumber }} </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12" md="6">
                <app-autocomplete
                  v-model="targetData.issueInformation.issueItem"
                  :items="options.issueItemList"
                  item-value="value"
                  item-title="text"
                  label="課題項目"
                  clearable
                  required-mark
                  :error-messages="errorMessages?.['issueInformation.issueItem']"
                  @blur="validateItem('issueInformation.issueItem')"
                  @update:model-value="validateItem('issueInformation.issueItem')"
                />
              </v-col>
              <v-col cols="12" md="6">
                <app-autocomplete
                  v-model="targetData.issueInformation.customerIssuePerception"
                  :items="options.customerIssuePerceptionList"
                  item-value="value"
                  item-title="text"
                  label="顧客の課題感"
                  clearable
                  required-mark
                  :error-messages="errorMessages?.['issueInformation.customerIssuePerception']"
                  @blur="validateItem('issueInformation.customerIssuePerception')"
                  @update:model-value="validateItem('issueInformation.customerIssuePerception')"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <app-text-field
                  v-model="targetData.issueInformation.overview"
                  label="概要"
                  counter="50"
                  required-mark
                  :error-messages="errorMessages?.['issueInformation.overview']"
                  @blur="validateItem('issueInformation.overview')"
                  @update:model-value="validateItem('issueInformation.overview')"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <app-textarea
                  v-model="targetData.issueInformation.currentStatus"
                  label="現状"
                  counter="500"
                  auto-grow
                  required-mark
                  variant="outlined"
                  :error-messages="errorMessages?.['issueInformation.currentStatus']"
                  @blur="validateItem('issueInformation.currentStatus')"
                  @update:model-value="validateItem('issueInformation.currentStatus')"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <app-textarea
                  v-model="targetData.issueInformation.ideal"
                  label="あるべき姿"
                  counter="500"
                  auto-grow
                  required-mark
                  variant="outlined"
                  :error-messages="errorMessages?.['issueInformation.ideal']"
                  @blur="validateItem('issueInformation.ideal')"
                  @update:model-value="validateItem('issueInformation.ideal')"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <app-textarea
                  v-model="targetData.issueInformation.issue"
                  label="課題"
                  counter="500"
                  auto-grow
                  required-mark
                  variant="outlined"
                  :error-messages="errorMessages?.['issueInformation.issue']"
                  @blur="validateItem('issueInformation.issue')"
                  @update:model-value="validateItem('issueInformation.issue')"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12" md="4">
                <app-autocomplete
                  v-model="targetData.issueInformation.industryDetail"
                  :items="options.industryDetailList"
                  item-value="value"
                  item-title="text"
                  label="業種詳細"
                  clearable
                  required-mark
                  :error-messages="errorMessages?.['issueInformation.industryDetail']"
                  :menu-props="{ maxHeight: 700, offsetX: true }"
                  @blur="validateItem('issueInformation.industryDetail')"
                  @update:model-value="validateItem('issueInformation.industryDetail')"
                />
              </v-col>
              <v-col cols="12" md="8">
                <app-text-field
                  v-model="targetData.issueInformation.keyPerson"
                  label="キーパーソン"
                  counter="50"
                  required-mark
                  :error-messages="errorMessages?.['issueInformation.keyPerson']"
                  @blur="validateItem('issueInformation.keyPerson')"
                  @update:model-value="validateItem('issueInformation.keyPerson')"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12" md="6">
                <a :href="industryDetailLink" target="_blank">
                  業種詳細検索サイトへのリンク
                </a>
              </v-col>
            </v-row>
          </v-container>
        </app-edit-item-group-tmpl>
      </template>

      <template #section-deadline-management>
        <app-edit-item-group-tmpl>
          <template #title>期日管理</template>
          <v-container fluid class="pt-1">
            <v-row>
              <v-col cols="12" md="4"> 案件ステータス：{{ issueStatusText }} </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="4">
                <app-autocomplete
                  v-model="targetData.deadlineManagement.nextActionforCustomer"
                  :items="options.nextActionforCustomerList"
                  item-value="value"
                  item-title="text"
                  label="顧客あて次のアクション"
                  clearable
                  :error-messages="errorMessages?.['deadlineManagement.nextActionforCustomer']"
                  @blur="validateItem('deadlineManagement.nextActionforCustomer')"
                  @update:model-value="validateItem('deadlineManagement.nextActionforCustomer')"
                />
              </v-col>
              <v-col cols="12" md="8">
                <app-text-field
                  v-model="targetData.deadlineManagement.other"
                  label="その他"
                  counter="50"
                  :error-messages="errorMessages?.['deadlineManagement.other']"
                  @blur="validateItem('deadlineManagement.other')"
                  @update:model-value="validateItem('deadlineManagement.other')"
                />
              </v-col>
            </v-row>
          </v-container>
        </app-edit-item-group-tmpl>
      </template>

      <template #section-project-info>
        <app-edit-item-group-tmpl>
          <template #title>案件情報</template>
          <v-container fluid class="pt-1">
            <v-row>
              <v-col cols="12" md="6">
                <app-autocomplete
                  v-model="targetData.projectInformation.issueProjectTeam"
                  :items="options.issueProjectTeamList"
                  item-value="value"
                  item-title="text"
                  label="担当チーム"
                  clearable
                  no-data-text="該当なし"
                  :disabled="isLoading"
                  :error-messages="errorMessages?.['projectInformation.issueProjectTeam']"
                  @blur="changeStatus('projectInformation.issueProjectTeam')"
                  @update:model-value="changeStatus('projectInformation.issueProjectTeam')"
                />
              </v-col>
              <v-col cols="12" md="6">
                <app-autocomplete
                  v-model="targetData.projectInformation.staffId"
                  :items="options.staffNameList"
                  item-value="value"
                  item-title="text"
                  label="担当者"
                  clearable
                  no-data-text="該当なし"
                  :disabled="isLoading"
                  :error-messages="errorMessages?.['projectInformation.staffId']"
                  @blur="changeStatus('projectInformation.staffId')"
                  @update:model-value="changeStatus('projectInformation.staffId')"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <app-popup-date-picker
                  v-model="targetData.projectInformation.proposalDate"
                  label="提案日"
                  display-text-format="yyyy/MM/dd"
                  clearable
                  :disabled="inputField.groupB.disabled"
                  :error-messages="errorMessages?.['projectInformation.proposalDate']"
                  @update:model-value="changeStatus('projectInformation.proposalDate')"
                  @blur="changeStatus('projectInformation.proposalDate')"
                />
              </v-col>
              <v-col cols="12" md="6">
                <app-number-field
                  v-model="targetData.projectInformation.proposalFee"
                  label="提案金額"
                  counter="11"
                  suffix="円"
                  clearable
                  :disabled="inputField.groupB.disabled"
                  :error-messages="errorMessages?.['projectInformation.proposalFee']"
                  @blur="changeStatus('projectInformation.proposalFee')"
                  @update:model-value="changeStatus('projectInformation.proposalFee')"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <app-popup-date-picker
                  v-model="targetData.projectInformation.consultingContractDate"
                  label="コンサル契約日"
                  display-text-format="yyyy/MM/dd"
                  clearable
                  :disabled="inputField.groupC.disabled"
                  :error-messages="errorMessages?.['projectInformation.consultingContractDate']"
                  @update:model-value="changeStatus('projectInformation.consultingContractDate')"
                  @blur="changeStatus('projectInformation.consultingContractDate')"
                />
              </v-col>
              <v-col cols="12" md="6">
                <app-number-field
                  v-model="targetData.projectInformation.contractFee"
                  label="契約金額"
                  counter="11"
                  suffix="円"
                  clearable
                  :disabled="inputField.groupC.disabled"
                  :error-messages="errorMessages?.['projectInformation.contractFee']"
                  @blur="changeStatus('projectInformation.contractFee')"
                  @update:model-value="changeStatus('projectInformation.contractFee')"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12" md="6">
                <app-popup-date-picker
                  v-model="targetData.projectInformation.consultingStartDate"
                  label="コンサル開始日"
                  display-text-format="yyyy/MM/dd"
                  clearable
                  :disabled="inputField.groupC.disabled"
                  :error-messages="errorMessages?.['projectInformation.consultingStartDate']"
                  @update:model-value="changeStatus('projectInformation.consultingStartDate')"
                  @blur="changeStatus('projectInformation.consultingStartDate')"
                />
              </v-col>
              <v-col cols="12" md="6">
                <app-popup-date-picker
                  v-model="targetData.projectInformation.dueDate"
                  label="完了期限"
                  display-text-format="yyyy/MM/dd"
                  clearable
                  :disabled="inputField.groupC.disabled"
                  :error-messages="errorMessages?.['projectInformation.dueDate']"
                  @update:model-value="changeStatus('projectInformation.dueDate')"
                  @blur="changeStatus('projectInformation.dueDate')"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12" md="6">
                <app-popup-date-picker
                  v-model="targetData.projectInformation.issueResolvedDate"
                  label="課題解決日"
                  display-text-format="yyyy/MM/dd"
                  clearable
                  :disabled="inputField.groupD.disabled"
                  :error-messages="errorMessages?.['projectInformation.issueResolvedDate']"
                  @update:model-value="changeStatus('projectInformation.issueResolvedDate')"
                  @blur="changeStatus('projectInformation.issueResolvedDate')"
                />
              </v-col>
              <v-col cols="12" md="6">
                <app-popup-date-picker
                  v-model="targetData.projectInformation.issueCanceledDate"
                  label="課題中止日"
                  display-text-format="yyyy/MM/dd"
                  clearable
                  :disabled="inputField.groupE.disabled"
                  :error-messages="errorMessages?.['projectInformation.issueCanceledDate']"
                  @update:model-value="changeStatus('projectInformation.issueCanceledDate')"
                  @blur="changeStatus('projectInformation.issueCanceledDate')"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12" md="6">
                <app-text-field
                  v-model="targetData.projectInformation.cooperationWith"
                  label="連携先"
                  counter="50"
                  :error-messages="errorMessages?.['projectInformation.cooperationWith']"
                  @update:model-value="changeStatus('projectInformation.cooperationWith')"
                  @blur="changeStatus('projectInformation.cooperationWith')"
                />
              </v-col>
            </v-row>
          </v-container>
        </app-edit-item-group-tmpl>
      </template>

      <template #section-file>
        <app-edit-item-group-tmpl>
          <template #title>ファイル</template>
          <v-container fluid class="pt-1">
            <v-row>
              <v-col cols="12">
                <v-card class="pa-5 mb-3 mx-2">
                  <file-uploader
                    v-if="!filesLoading"
                    ref="fileUploaderRef"
                    :loading="filesLoading"
                    :files-to-upload="filesToUpload"
                    :uploaded-files="uploadedFiles"
                    :temp-files-to-delete="filesToRemove"
                    :use-save-btn="true"
                    :is-add="isAdd"
                    @download-file="downloadFile"
                    @update-uploaded-files="updateUploadedFiles"
                  />
                  <v-skeleton-loader v-else type="table" />
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </app-edit-item-group-tmpl>
      </template>

      <template #section-link>
        <app-edit-item-group-tmpl>
          <template #title>リンク</template>
          <v-container fluid class="pt-1">
            <v-row>
              <v-col cols="12">
                <v-card class="pa-5 mb-3 mx-2">
                  <issue-project-link
                    v-if="!isLoading"
                    :issue-project-links-data="targetData.issueProjectLinks"
                    :issue-project-id="targetData?.id ? targetData.id : ''"
                    :is-add="isAdd"
                    @update-issue-project-links-data="updateIssueProjectLinksData"
                  />
                  <v-skeleton-loader v-else type="list-item-three-line" />
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </app-edit-item-group-tmpl>
      </template>

      <template #section-issue-project-task>
        <app-edit-item-group-tmpl>
          <template #title>タスク</template>
          <v-container fluid class="pt-1">
            <v-row v-if="isAdd">
              <v-col cols="12">
                <v-card>
                  <v-card-text class="text-center">
                    課題案件を登録してください
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <v-row v-else>
              <v-col cols="12">
                <app-search-page-tmpl
                  ref="taskDataTable"
                  v-model:page-index="taskPagination.pageIndex"
                  v-model:sort="taskPagination.sort"
                  :page-size="taskPagination.pageSize"
                  :list-item-key="'id'"
                  :list-headers="headers"
                  :list-items="taskData?.items"
                  :list-items-total="taskData?.total"
                  :loading="isLoading"
                  :can-add="true"
                  :can-edit="false"
                  :can-remove="false"
                  :can-pagination="true"
                  :show-pagination-count="true"
                  :on-search="searchTasks"
                  :on-add="toIssueProjectTaskEditPage"
                  :on-edit="toIssueProjectTaskEditPage"
                >
                  <template #criteria>
                    <v-row>
                      <v-col cols="12" md="3">
                        <app-autocomplete
                          v-model="taskSearchCondition.staffIds"
                          :items="userOptions"
                          item-value="value"
                          item-title="text"
                          label="タスク担当者"
                          clearable
                          multiple
                          chips
                          no-data-text="該当なし"
                          :disabled="isLoading"
                        />
                      </v-col>
                      <v-col cols="12" md="3">
                        <app-autocomplete
                          v-model="taskSearchCondition.issueProjectTaskStatus"
                          :items="options.issueProjectTaskStatusList"
                          item-value="value"
                          item-title="text"
                          label="タスクステータス"
                          clearable
                          :disabled="isLoading"
                        />
                      </v-col>
                      <v-col cols="12" md="3">
                        <app-popup-date-picker
                          v-model="taskSearchCondition.expiredAtFrom"
                          label="期日From"
                          display-text-format="yyyy/MM/dd"
                          clearable
                          :disabled="isLoading"
                        />
                      </v-col>
                      <v-col cols="12" md="3">
                        <app-popup-date-picker
                          v-model="taskSearchCondition.expiredAtTo"
                          label="期日To"
                          display-text-format="yyyy/MM/dd"
                          clearable
                          :disabled="isLoading"
                        />
                      </v-col>
                    </v-row>
                  </template>
                </app-search-page-tmpl>
              </v-col>
            </v-row>
          </v-container>
        </app-edit-item-group-tmpl>
      </template>
    </app-edit-page-tmpl>
    
    <app-confirm-dialog ref="confirmDialog" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, reactive, type Ref, onBeforeMount, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useGetCustomerIdentificationId } from '@ibp/base/src/composables/shared/useGetCustomerIdentificationId'
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import { defaultInstance } from '@hox/base/src/utils/shared/zodDefaultInstance'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import { confirmBeforeUnload } from '@hox/base/src/utils/shared/confirmBeforeUnload'

import { z } from 'zod'
import {
  type IssueProject,
  type IssueProjectForCreate,
  type IssueProjectForUpdate,
  type IssueProjectOption,
  useGetIssueProject,
  usePostIssueProject,
  usePutIssueProject,
  useDeleteIssueProject,
  useGetIssueProjectOptions,
  issueProjectSchema,
  issueProjectSchemaForCreate,
  issueProjectSchemaForUpdate
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProject'
import {
  type DownloadIssueProjectFile,
  useFindIssueProjectFile,
  useDownloadIssueProjectFile,
  useUploadIssueProjectFile
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectFile'
import {
  type FindIssueProjectTaskCriteria,
  useFindIssueProjectTask
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectTask'
import { useFindUser } from '@ibp/issue-project/src/apiclient/myCareer/user'
import { useGetAllTeams } from '@ibp/issue-project/src/apiclient/customerProposal/team'
import { validateFileSize } from '@ibp/issue-project/src/utils/fileUploadValidator'
import { useGetHypotheticalDiscussionOfIssues } from '@ibp/issue-project/src/apiclient/businessUnderstanding/hypotheticalDiscussionOfIssues'
import { useGetToDo } from '@ibp/issue-project/src/apiclient/businessUnderstanding/toDo'
import type { SortItem } from '@hox/base/src/components/shared/types'
import type { SectionConfig } from '@hox/base/src/components/shared/templates/AppEditPageTmpl.vue'
import type { SearchOptions } from '@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue'
import type { AppConfirmDialogType } from '@hox/base/src/components/shared/molecules/AppConfirmDialog.vue'
import { formatDateYMD } from '@ibp/issue-project/src/utils/shared/date'
import FileUploader from '@ibp/issue-project/src/components/shared/organisms/FileUploader.vue'
import IssueProjectLink from '@ibp/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLink.vue'

definePageMeta({
  layout: 'customer'
})

useHead({
  title: '課題案件編集'
})

interface UserOption {
  value: string
  text: string
  teamMembers: string[]
  isMyTeam: number
  teamId?: string
}

const route = useRoute()
const router = useRouter()
const { success: successToast, error: errorToast } = useAppToasts()
const { $auth } = useNuxtApp()

const inProgress = ref(false)
const { hasTrue: isLoading, addFlag } = useFlagCondition(inProgress)

const { customerIdentificationId } = useGetCustomerIdentificationId(route)

const paramId = computed(() => route.query?.id?.toString())
const isAdd = computed(() => !paramId.value)

const businessUnderstandingId = computed(() => route.query?.businessUnderstandingId?.toString())
const taskId = computed(() => route.query?.taskId?.toString())
const communicationPlanCategory = computed(() => route.query?.communicationPlanCategory?.toString())

const extendedSchemaForCreate = issueProjectSchemaForCreate.extend({})
const extendedSchemaForUpdate = issueProjectSchemaForUpdate.extend({})

function createDefaultViewData(): IssueProject {
  const result = reactive(defaultInstance<typeof issueProjectSchema>(issueProjectSchema))
  return result
}

const targetData = createDefaultViewData()

function createSendData(): IssueProjectForCreate | IssueProjectForUpdate | null {
  try {
    const tempData = { ...targetData }
    const sendData: Partial<IssueProjectForCreate | IssueProjectForUpdate> = {}

    if (isAdd.value) {
      (sendData as IssueProjectForCreate).customerIdentificationId = customerIdentificationId.value
    } else {
      (sendData as IssueProjectForUpdate).id = tempData.id,
      (sendData as IssueProjectForUpdate).version = tempData.version
    }

    sendData.issueInformation = {
      issueItem: tempData.issueInformation?.issueItem ?? null,
      customerIssuePerception: tempData.issueInformation?.customerIssuePerception ?? null,
      overview: tempData.issueInformation?.overview ?? '',
      currentStatus: tempData.issueInformation?.currentStatus ?? '',
      ideal: tempData.issueInformation?.ideal ?? '',
      issue: tempData.issueInformation?.issue ?? '',
      industryDetail: tempData.issueInformation?.industryDetail ?? null,
      keyPerson: tempData.issueInformation?.keyPerson ?? null
    }

    sendData.projectInformation = {
      issueProjectTeam: tempData.projectInformation?.issueProjectTeam ?? null,
      staffId: tempData.projectInformation?.staffId ?? null,
      staffName: tempData.projectInformation?.staffName ?? null,
      proposalDate: tempData.projectInformation?.proposalDate ?? null,
      proposalFee: tempData.projectInformation?.proposalFee ?? null,
      consultingContractDate: tempData.projectInformation?.consultingContractDate ?? null,
      contractFee: tempData.projectInformation?.contractFee ?? null,
      consultingStartDate: tempData.projectInformation?.consultingStartDate ?? null,
      dueDate: tempData.projectInformation?.dueDate ?? null,
      issueResolvedDate: tempData.projectInformation?.issueResolvedDate ?? null,
      issueCanceledDate: tempData.projectInformation?.issueCanceledDate ?? null,
      cooperationWith: tempData.projectInformation?.cooperationWith ?? null
    }

    sendData.deadlineManagement = {
      status: tempData.deadlineManagement?.status ?? null,
      nextActionforCustomer: tempData.deadlineManagement?.nextActionforCustomer ?? null,
      other: tempData.deadlineManagement?.other ?? null
    }

    if (sendData.projectInformation.proposalFee !== null && sendData.projectInformation.proposalFee !== undefined) {
      const proposalFeeStr = String(sendData.projectInformation.proposalFee).trim()
      sendData.projectInformation.proposalFee = proposalFeeStr === '' ? null : sendData.projectInformation.proposalFee
    }

    if (sendData.projectInformation.contractFee !== null && sendData.projectInformation.contractFee !== undefined) {
      const contractFeeStr = String(sendData.projectInformation.contractFee).trim()
      sendData.projectInformation.contractFee = contractFeeStr === '' ? null : sendData.projectInformation.contractFee
    }

    if (sendData.projectInformation?.staffId) {
      const staffMember = options.value.staffNameList.find(
        item => item.value === sendData.projectInformation?.staffId
      )
      if (sendData.projectInformation) {
        sendData.projectInformation.staffName = staffMember ? staffMember.text : null
      }
    }

    if (tempData.issueProjectLinks && Array.isArray(tempData.issueProjectLinks) && tempData.issueProjectLinks.length > 0) {
      const formedLinks = tempData.issueProjectLinks.map(p => ({
        Id: p.id,
        Title: p.title,
        Url: p.url,
        Updater: p.updater,
        UpdaterId: p.updaterId,
        UpdatedDateTime: new Date(),
        Version: p.version
      }))
      sendData.issueProjectLinks = formedLinks.map(link => JSON.stringify(link))
    } else {
      sendData.issueProjectLinks = []
    }

    return sendData as IssueProjectForCreate | IssueProjectForUpdate
  } catch (error) {
    errorToast('リクエストデータの作成に失敗しました')
    return null
  }
}

const targetDataRef = computed({
  get() {
    return Object.assign({}, createSendData())
  },
  set(val) {
    Object.assign(targetData, val)
  }
})

const { executeWithResult: getData, inProgress: getInProgress } = useGetIssueProject(paramId as Ref<string>)
const { executeWithResult: postData, inProgress: postInProgress } = usePostIssueProject(targetDataRef as Ref<IssueProjectForCreate>)
const { executeWithResult: putData, inProgress: putInProgress } = usePutIssueProject(targetDataRef as Ref<IssueProjectForUpdate>)
const { executeWithResult: getIssueProjectOptions, inProgress: getOptionsInProgress } = useGetIssueProjectOptions()

const deleteParams = ref({ id: '', version: '' })
const { executeWithResult: deleteIssueProject, inProgress: deleteInProgress } = useDeleteIssueProject(deleteParams)

const userQuery = ref({
  userId: undefined,
  name: undefined,
  branchNumbers: undefined
})
const { executeWithResult: findUser, inProgress: findUserInProgress } = useFindUser(userQuery)
const { executeWithResult: getAllTeams, inProgress: getAllTeamsInProgress } = useGetAllTeams()

const fileQuery = computed(() => ({
  issueProjectId: targetData?.id || '',
  pageIndex: 1,
  pageSize: 100,
  sort: []
}))
const { executeWithResult: findIssueProjectFile } = useFindIssueProjectFile(fileQuery)

const taskPagination = reactive({
  pageIndex: 1,
  pageSize: 10,
  sort: [] as SortItem[]
})

const taskSearchCondition = reactive<{
  staffIds: string[] | undefined
  issueProjectTaskStatus: number | undefined
  expiredAtFrom: Date | undefined
  expiredAtTo: Date | undefined
  issueProjectId?: string
}>({
  staffIds: undefined,
  issueProjectTaskStatus: undefined,
  expiredAtFrom: undefined,
  expiredAtTo: undefined
})

const taskCriteria = ref<FindIssueProjectTaskCriteria | null>(null)

const taskQuery = computed(() => {
  if (!taskCriteria.value) return null
  
  const sort = taskPagination.sort.reduce((prev, curr) => {
    prev.push(`${curr.key}${curr.order === 'desc' ? ' desc' : ''}`)
    return prev
  }, [] as string[])

  return {
    ...taskCriteria.value,
    pageIndex: taskPagination.pageIndex,
    pageSize: taskPagination.pageSize,
    sort
  }
})

const { data: taskData, executeWithResult: findTasks, inProgress: tasksLoading } = useFindIssueProjectTask(taskQuery as Ref<FindIssueProjectTaskCriteria>)

addFlag(getInProgress)
addFlag(postInProgress)
addFlag(putInProgress)
addFlag(deleteInProgress)
addFlag(getOptionsInProgress)
addFlag(findUserInProgress)
addFlag(getAllTeamsInProgress)
addFlag(tasksLoading)

const {
  errorMessages: errorMessagesForCreate,
  validate: validateForCreate,
  validateItem: validateItemForCreate,
} = useValidation(extendedSchemaForCreate, targetDataRef)

const {
  errorMessages: errorMessagesForUpdate,
  validate: validateForUpdate,
  validateItem: validateItemForUpdate,
} = useValidation(extendedSchemaForUpdate, targetDataRef)

const errorMessages: any = computed({
  get() {
    return isAdd.value ? errorMessagesForCreate.value : errorMessagesForUpdate.value
  },
  set(value) {
    if (isAdd.value) {
      errorMessagesForCreate.value = value
    } else {
      errorMessagesForUpdate.value = value
    }
  },
})

const validate = () => {
  return isAdd.value ? validateForCreate() : validateForUpdate()
}

const validateItem = (key: string) => {
  return isAdd.value ? validateItemForCreate(key) : validateItemForUpdate(key)
}

const { init, hasChanged, restart } = useWatchDataChanges()
const watcher = { init, hasChanged, restart }
confirmBeforeUnload(hasChanged, '')

const issueStatus = {
  beforeProposal: 1,
  underProposal: 2,
  accepted: 3,
  underConsulting: 4,
  finished: 5,
  cancelBeforeProposal: 6,
  cancelAfterProposal: 7
}

const inputField = ref({
  groupB: { disabled: true },
  groupC: { disabled: true },
  groupD: { disabled: false },
  groupE: { disabled: false }
})

const industryDetailLink = 'https://www.e-stat.go.jp/classifications/terms/10?revision=03&isf1=1&isf2=1&isf3=0&ksf=1&sbs1=1&sbs2=0&sbs3=0'

const sections: SectionConfig[] = [
  { key: 'basic-info', title: '課題情報' },
  { key: 'deadline-management', title: '期日管理' },
  { key: 'project-info', title: '案件情報' },
  { key: 'file', title: 'ファイル' },
  { key: 'link', title: 'リンク' },
  { key: 'issue-project-task', title: 'タスク' }
]

const options = ref<{
  issueItemList: IssueProjectOption[]
  customerIssuePerceptionList: IssueProjectOption[]
  industryDetailList: IssueProjectOption[]
  nextActionforCustomerList: IssueProjectOption[]
  issueProjectTeamList: IssueProjectOption[]
  staffNameList: IssueProjectOption[]
  issueStatusList: IssueProjectOption[]
  issueProjectTaskStatusList: IssueProjectOption[]
}>({
  issueItemList: [],
  customerIssuePerceptionList: [],
  industryDetailList: [],
  nextActionforCustomerList: [],
  issueProjectTeamList: [],
  staffNameList: [],
  issueStatusList: [],
  issueProjectTaskStatusList: []
})

const issueStatusText = computed(() => {
  if (!targetData.deadlineManagement?.status) return '-'
  const index = targetData.deadlineManagement.status - 1
  return `【${options.value.issueStatusList[index]?.text || ''}】`
})

const preIsIssueResolveOrCancelDateRegistered = ref(false)
const isIssueResolveOrCancelDateRegistered = computed(() => {
  return !!(targetData.projectInformation?.issueResolvedDate || 
           targetData.projectInformation?.issueCanceledDate)
})

const filesLoading = ref(true)
const filesToUpload = ref<File[]>([])
const uploadedFiles = ref<any[]>([])
const filesToRemove = ref<any[]>([])
const fileUploaderRef = ref()

const headers = [
  { title: '依頼日', key: 'requestedAt', sortable: true },
  { title: '課題概要', key: 'issueInformationOverview' },
  { title: '期日', key: 'expiredAt', sortable: true, value: (item: any) => formatDateYMD(item.expiredAt) },
  { title: 'ステータス', key: 'issueProjectTaskStatus', sortable: true },
  { title: 'タスク担当者', key: 'staffName', sortable: true },
  { title: 'タスク概要', key: 'overview' },
]

const userOptions = ref<UserOption[]>([])
const bpoTeamId = '01HG84PND8SKX843WCA2PSNDSN'
const bpoTeam = ref<any>(null)
const communicationPlanData = ref(null)
const confirmDialog = ref<AppConfirmDialogType>()

watch(taskData, (newData) => {
  if (newData) {
    const processedItems = createListItems(newData.items || [])
  }
}, { immediate: true })

function calculateStatus(): void {
  let status = 0
  
  if (
    !targetData.projectInformation?.issueProjectTeam &&
    !targetData.projectInformation?.issueResolvedDate &&
    !targetData.projectInformation?.issueCanceledDate
  ) {
    if (targetData.deadlineManagement) {
      targetData.deadlineManagement.status = status
    }
    return
  }

  if (targetData.projectInformation?.issueProjectTeam) {
    status = issueStatus.beforeProposal
  }

  if (
    targetData.projectInformation?.issueResolvedDate ||
    targetData.projectInformation?.issueCanceledDate
  ) {
    status = issueStatus.cancelBeforeProposal
  }

  if (
    targetData.projectInformation?.proposalDate &&
    targetData.projectInformation?.proposalFee !== null &&
    targetData.projectInformation?.proposalFee !== undefined
  ) {
    status = issueStatus.underProposal

    if (
      targetData.projectInformation?.issueResolvedDate ||
      targetData.projectInformation?.issueCanceledDate
    ) {
      status = issueStatus.cancelAfterProposal
    }
  }

  if (
    targetData.projectInformation?.consultingContractDate &&
    targetData.projectInformation?.contractFee !== null &&
    targetData.projectInformation?.contractFee !== undefined &&
    targetData.projectInformation?.consultingStartDate &&
    targetData.projectInformation?.dueDate
  ) {
    status = issueStatus.accepted
    if (targetData.projectInformation.consultingContractDate <= new Date()) {
      status = issueStatus.underConsulting
    }
  }

  if (status === issueStatus.accepted || status === issueStatus.underConsulting) {
    if (targetData.projectInformation?.issueResolvedDate) {
      status = issueStatus.finished
    } else if (targetData.projectInformation?.issueCanceledDate) {
      status = issueStatus.cancelAfterProposal
    }
  }

  if (targetData.deadlineManagement) {
    targetData.deadlineManagement.status = status
  }
}

function inputFieldGroupAChanged(): void {
  if (targetData.projectInformation?.issueProjectTeam && targetData.projectInformation?.staffName) {
    inputField.value.groupB.disabled = false
  } else {
    if (targetData.projectInformation) {
      targetData.projectInformation.proposalDate = null
      targetData.projectInformation.proposalFee = null
    }
    inputField.value.groupB.disabled = true

    if (targetData.projectInformation) {
      targetData.projectInformation.consultingContractDate = null
      targetData.projectInformation.contractFee = null
      targetData.projectInformation.consultingStartDate = null
      targetData.projectInformation.dueDate = null
    }
    inputField.value.groupC.disabled = true

    delete errorMessages.value['projectInformation.proposalDate']
    delete errorMessages.value['projectInformation.proposalFee']
    delete errorMessages.value['projectInformation.consultingContractDate']
    delete errorMessages.value['projectInformation.contractFee']
    delete errorMessages.value['projectInformation.consultingStartDate']
    delete errorMessages.value['projectInformation.dueDate']
  }
}

function inputFieldGroupBChanged(): void {
  if (
    targetData.projectInformation?.proposalDate !== null &&
    targetData.projectInformation?.proposalDate !== undefined &&
    targetData.projectInformation?.proposalFee !== null &&
    targetData.projectInformation?.proposalFee !== undefined
  ) {
    inputField.value.groupC.disabled = false
  } else {
    if (targetData.projectInformation) {
      targetData.projectInformation.consultingContractDate = null
      targetData.projectInformation.contractFee = null
      targetData.projectInformation.consultingStartDate = null
      targetData.projectInformation.dueDate = null
    }
    inputField.value.groupC.disabled = true

    delete errorMessages.value['projectInformation.consultingContractDate']
    delete errorMessages.value['projectInformation.contractFee']
    delete errorMessages.value['projectInformation.consultingStartDate']
    delete errorMessages.value['projectInformation.dueDate']
  }
}

function changeStatus(item?: string): void {
  if (item) {
    validateItem(item)
  }
  
  if (item === 'projectInformation.staffId') {
    setStaffName()
  }
  
  inputFieldGroupAChanged()
  inputFieldGroupBChanged()
  calculateStatus()
}

function setStaffName(): void {
  const id = targetData.projectInformation?.staffId
  if (!id) {
    if (targetData.projectInformation) {
      targetData.projectInformation.staffName = null
    }
  } else {
    const staffList = options.value.staffNameList.find(
      (item: any) => item.value === id
    )
    if (targetData.projectInformation) {
      targetData.projectInformation.staffName = staffList ? staffList.text : null
    }
  }
}

async function save(): Promise<void> {
  const { success } = await validate()
  if (!success) {
    return
  }

  const confirmResult = await confirmDialog.value?.open('save')
  if (!confirmResult) return

  const { uploadFiles, filesToRemove: removeFiles } = 
    await fileUploaderRef.value?.getUploadFilesWithConfirmationOverwrite() || { uploadFiles: [], filesToRemove: [] }

  const fileSizeValidateResult = validateFileSize(uploadFiles || [])
  if (!fileSizeValidateResult.isValid) {
    errorToast(fileSizeValidateResult.message)
    return
  }

  errorMessages.value = {}
  inProgress.value = true
  
  try {
    const result = await (isAdd.value ? postData : putData)().catch((e) => {
      if (!e.hasProblemDetails) {
        throw e
      }
      if (e.data.type === '/validation-error') {
        if (e.data.errors.toast) {
          errorToast(e.data.errors.toast.join(''))
        } else {
          errorMessages.value = e.data.errors
        }
      } else if (e.data.type === '/conflict') {
        errorToast('すでに別のユーザーがデータを登録・変更しているため保存できませんでした。')
      } else {
        throw e
      }
      return false
    })

    if (typeof result === 'boolean') {
      return
    }

    const { data } = result
    successToast('データを保存しました。', { retain: true })

    const uploadErrorMessage = await uploadFile(uploadFiles || [], removeFiles || [])
    if (uploadErrorMessage) {
      errorToast(uploadErrorMessage)
    }

    const interactionCategory =
      !preIsIssueResolveOrCancelDateRegistered.value &&
      isIssueResolveOrCancelDateRegistered.value
        ? 'IssueProject_Complete'
        : isAdd.value
        ? 'IssueProject_Add'
        : 'IssueProject_Update'
    
    await saveInteractionLog(data, customerIdentificationId.value, interactionCategory)

    if (isAdd.value) {
      watcher.init(targetData)
      await router.push({
        query: {
          id: data.id,
        },
      })
    }

    Object.assign(targetData, data)
    preIsIssueResolveOrCancelDateRegistered.value = isIssueResolveOrCancelDateRegistered.value

  } finally {
    watcher.init(targetData)
    inProgress.value = false
    filesToRemove.value = []
  }
}

async function remove(): Promise<void> {
  const confirmResult = await confirmDialog.value?.open('remove')
  if (!confirmResult) return

  inProgress.value = true
  try {
    deleteParams.value = {
      id: targetData.id,
      version: targetData.version
    }
    
    const result = await deleteIssueProject().catch((e) => {
      if (!e.hasProblemDetails) {
        throw e
      }
      if (e.data.type === '/validation-error') {
        if (e.data.errors.toast) {
          errorToast(e.data.errors.toast.join(''))
        } else {
          errorMessages.value = e.data.errors
        }
      } else if (e.data.type === '/conflict') {
        errorToast('すでに別のユーザーがデータを登録・変更しているため削除できませんでした。')
      } else {
        throw e
      }
      return false
    })

    if (typeof result === 'boolean') {
      return
    }
    
    successToast(`${targetData.id} を削除しました。`)
    toIssueProjectSearchPage()
  } finally {
    inProgress.value = false
  }
}

async function uploadFile(uploadFiles: File[], filesToRemoveParam: any[]): Promise<string | null> {
  try {
    if (
      (!uploadFiles || uploadFiles.length === 0) &&
      (!filesToRemoveParam || filesToRemoveParam.length === 0)
    ) {
      return null
    }

    filesLoading.value = true
    filesToUpload.value = uploadFiles

    // Use FormData like the old version
    const formData = new FormData()
    formData.append('issueProjectId', targetData.id)
    formData.append('updaterId', $auth.getUser().userId)
    formData.append('updater', $auth.getUser().displayName)
    formData.append('updaterName', $auth.getUser().displayName)

    for (const file of filesToUpload.value) {
      formData.append('uploadFiles', file)
      formData.append(
        'versions',
        uploadedFiles.value.find((f) => f.fileName === file.name)?.version ?? ''
      )
    }

    for (const fileToRemove of filesToRemoveParam) {
      formData.append('filesToRemove', fileToRemove.deleteItemName)
    }

    let message: string | null = null

    const { executeWithResult: uploadIssueProjectFile } = useUploadIssueProjectFile(ref(formData))

    const result = await uploadIssueProjectFile().catch((err) => {
      if (!err.response) {
        message = 'ファイルの更新に失敗しました。添付ファイルを開いていないか確認をお願いします。開いていない場合は通信エラーの可能性がありますので、時間をおいて再度実行してください。'
        return false
      }

      const data = err.response?.data || {}
      if (data.type === '/validation-error') {
        errorMessages.value = data.errors
      } else if (data.type === '/conflict') {
        message = 'ファイルの更新失敗。すでに別のユーザーがデータを登録・変更しているため保存できませんでした。'
      } else {
        throw err
      }
      return false
    })

    if (!result) {
      return message
    }

    filesToUpload.value = []
    filesToRemove.value = []

    await getFileList()
    return null
  } catch (error) {
    return 'ファイルのアップロードに失敗しました。'
  } finally {
    filesLoading.value = false
  }
}

async function saveInteractionLog(
  data: any, 
  customerIdentificationId: string, 
  category: string
): Promise<boolean> {
  try {
    const user = $auth.getUser()
    const sendInteractionLogData = {
      customerIdentificationId,
      interactionCategory: category,
      sourceId: data?.id,
      description1: options.value.issueItemList.find(
        (x: any) => x.value === data.issueInformation.issueItem
      )?.text || '',
      creatorId: user.userId,
      creatorName: user.displayName,
      createdDateTime: new Date(),
    }

    return true
  } catch (error) {
    errorToast('データの保存には成功しましたが、行動記録の登録に失敗しました。')
    return false
  }
}

async function loadIssueProjectData(): Promise<void> {
  if (!paramId.value) return

  try {
    const result = await getData()
    if (!result) {
      errorToast('データの取得に失敗しました')
      return
    }
    
    if (result.error) {
      errorToast('データの取得でエラーが発生しました')
      return
    }

    if (result.data) {
      Object.assign(targetData, result.data)
    }
  } catch (error) {
    errorToast('データの読み込み中にエラーが発生しました')
  }
}

async function loadOptions(): Promise<void> {
  try {
    const result = await getIssueProjectOptions()
    
    if (!result) {
      errorToast('オプションデータの取得に失敗しました')
      return
    }

    if (result.error) {
      errorToast('オプションデータの取得でエラーが発生しました')
      return
    }

    if (result.data) {
      options.value = { ...result.data, staffNameList: [] }
      await loadStaffNameList()
    }
  } catch (error) {
    errorToast('オプションデータの読み込み中にエラーが発生しました')
  }
}

async function loadStaffNameList(): Promise<void> {
  try {
    const result = await findUser()
    
    if (!result) {
      errorToast('担当者データの取得に失敗しました')
      return
    }

    if (result.error) {
      errorToast('担当者データの取得でエラーが発生しました')
      return
    }

    if (result.data && Array.isArray(result.data)) {
      options.value.staffNameList = result.data.map((user: any) => ({
        value: user.userId,
        text: user.nameKanji
      }))

      for (const staff of [...options.value.staffNameList]) {
        if (staff.value === $auth.getUser().userId) {
          options.value.staffNameList = options.value.staffNameList.filter(
            (x: any) => x.value !== $auth.getUser().userId
          )
          options.value.staffNameList.unshift(staff)
        }
      }
    }
  } catch (error) {
    errorToast('担当者データの読み込み中にエラーが発生しました')
  }
}

async function setUserOptions(): Promise<void> {
  try {
    const teamResult = await getAllTeams()
    let teamOptions: UserOption[] = []

    if (teamResult && !teamResult.error && teamResult.data) {
      teamOptions = teamResult.data.map((team: any, index: number) => ({
        value: `team_${index}`,
        text: team.teamName,
        teamId: team.id,
        teamMembers: team.teamMembers?.map((member: any) => member.staffId) || [],
        isMyTeam: team.teamMembers?.filter(
          (member: any) => member.staffId === $auth.getUser().userId
        ).length || 0,
      }))

      bpoTeam.value = teamResult.data.find((x: any) => x.id === bpoTeamId)

      for (const teamOption of teamOptions) {
        if (bpoTeam.value && teamOption.teamId === bpoTeam.value.id) {
          // BPOチームの場合、チームIDをteamMembersに追加
          if (bpoTeam.value.id && !teamOption.teamMembers.includes(bpoTeam.value.id)) {
            teamOption.teamMembers.push(bpoTeam.value.id)
          }
        }
      }
    }

    const myTeamMemberIds = Array.from(
      new Set(
        teamOptions
          .filter((team) => team.isMyTeam > 0)
          .map((team) => team.teamMembers)
          .flat()
      )
    )

    const userResult = await findUser()
    let userOptionsData: UserOption[] = []

    if (userResult && !userResult.error && userResult.data) {
      userOptionsData = userResult.data.map(
        (user: any, index: number) => ({
          value: `user_${index}`,
          text: user.nameKanji,
          teamMembers: [user.userId],
          isMyTeam: myTeamMemberIds.filter((member) => member === user.userId)
            .length,
        })
      )
    }

    userOptions.value = teamOptions.concat(userOptionsData)

    userOptions.value.sort((a, b) => {
      return b.isMyTeam - a.isMyTeam
    })

    for (const userOption of [...userOptions.value]) {
      if (
        userOption.value.includes('user') &&
        userOption.teamMembers[0] === $auth.getUser().userId
      ) {
        userOptions.value = userOptions.value.filter((x: any) => x.value !== userOption.value)
        userOptions.value.unshift(userOption)
        break
      }
    }
  } catch (error) {
    errorToast('ユーザーオプションの設定中にエラーが発生しました')
  }
}

async function getHypotheticalDiscussionOfIssues(hdoiId: string) {
  try {
    const { executeWithResult: getHypotheticalDiscussionOfIssuesData } = useGetHypotheticalDiscussionOfIssues(ref(hdoiId))
    const result = await getHypotheticalDiscussionOfIssuesData()
    
    if (!result || result.error) {
      return {
        title: '',
        currentSituation: '',
        ideal: '',
        issue: '',
      }
    }

    return {
      title: result.data?.title ?? '',
      currentSituation: result.data?.currentSituation ?? '',
      ideal: result.data?.ideal ?? '',
      issue: result.data?.issue ?? '',
    }
  } catch (error) {
    return {
      title: '',
      currentSituation: '',
      ideal: '',
      issue: '',
    }
  }
}

async function getCommunicationPlanData(category: string, id: string) {
  try {
    if (category === '課題の仮説協議') {
      return await getHypotheticalDiscussionOfIssues(id)
    } else if (category === 'ToDo') {
      const { executeWithResult: getToDo } = useGetToDo(ref(id))
      const result = await getToDo()
      return result?.data || undefined
    }
    return undefined
  } catch (error) {
    return undefined
  }
}

const communicationPlanLink = computed(() => {
  if (!communicationPlanData.value) return null
  
  return {
    title: `コミュニケーションプラン_${communicationPlanData.value?.title}`,
    url: `${window.location.origin}/business-understanding/communication-plan?`
      .concat(`businessUnderstandingId=${businessUnderstandingId.value}`)
      .concat(`&communicationPlanCategory=${communicationPlanCategory.value}`)
      .concat(`&taskId=${taskId.value}`),
    updaterId: $auth.getUser().userId,
    updater: $auth.getUser().displayName,
  }
})

function toIssueProjectSearchPage(): void {
  const defaultStatuses = {
    caseStatuses: []
  }
  
  router.push({
    path: '/customer-proposal/case',
    query: {
      tab: 'issue-project',
      customerIdentificationId: customerIdentificationId.value,
      from: 'customer-identifying',
      issueProjectIsNothing: 'true',
      caseStatuses: defaultStatuses.caseStatuses
    }
  })
}

async function searchTasks(searchOptions: SearchOptions = {}): Promise<boolean> {
  try {
    if ((searchOptions.isSortChangeRequest || searchOptions.isPaginationRequest) && !taskCriteria.value) {
      return true
    }

    if (!searchOptions.isSortChangeRequest && !searchOptions.isPaginationRequest) {
      taskPagination.pageIndex = 1
      const condition = Object.assign({}, taskSearchCondition)
      condition.issueProjectId = targetData.id

      const selectedIds = condition.staffIds
      if (userOptions.value?.length > 0 && selectedIds && Array.isArray(selectedIds)) {
        const staffIds = selectedIds.map((id: string) => {
          const user = userOptions.value.filter(
            (option: any) => option.value === id
          )
          return user.length === 0 ? undefined : user[0].teamMembers
        }).filter((id): id is string[] => id !== undefined)

        condition.staffIds = Array.from(new Set(staffIds.flat()))
      }

      taskCriteria.value = {
        issueProjectId: targetData.id,
        staffIds: condition.staffIds || undefined,
        issueProjectTaskStatus: condition.issueProjectTaskStatus ? [condition.issueProjectTaskStatus] : undefined,
        customerStaffIds: undefined,
        issueProjectStaffIds: undefined,
        registrantIds: undefined,
        expiredAtFrom: condition.expiredAtFrom ? condition.expiredAtFrom.toISOString() : undefined,
        expiredAtTo: condition.expiredAtTo ? condition.expiredAtTo.toISOString() : undefined,
        customerName: undefined,
        pageIndex: taskPagination.pageIndex,
        pageSize: taskPagination.pageSize,
        sort: taskPagination.sort.map(item => `${item.key}${item.order === 'desc' ? ' desc' : ''}`)
      }
    }

    const result = await findTasks()
    
    if (!result) {
      errorToast('タスク検索に失敗しました')
      return false
    }
    
    if (result.error) {
      errorToast('タスク検索でエラーが発生しました')
      return false
    }

    return true
  } catch (error) {
    errorToast('タスク検索に失敗しました')
    return false
  }
}

function createListItems(items: any[]): any[] {
  if (items.length === 0) return items
  
  for (const item of items) {
    if (item.issueProjectTaskStatus !== null) {
      const status = options.value.issueProjectTaskStatusList.find(
        (x: any) => x.value === item.issueProjectTaskStatus
      )
      item.issueProjectTaskStatus = status?.text || '-'
    } else {
      item.issueProjectTaskStatus = '-'
    }
    
    if (item.requestedAt !== null) {
      item.requestedAt = formatDateYMD(item.requestedAt)
    }
    
    if (item.staffId === bpoTeamId && bpoTeam.value) {
      item.staffName = bpoTeam.value.teamName
    }
    
    if (item.expiredAt !== null) {
      item.expiredAtFormatted = formatDateYMD(item.expiredAt)
    }
  }
  
  return items
}

async function getFileList(): Promise<void> {
  try {
    filesLoading.value = true
    
    const result = await findIssueProjectFile()
    if (!result) return
    if (result.error) return

    const usersResult = await findUser()
    let users: any[] = []

    if (usersResult && !usersResult.error && usersResult.data) {
      users = usersResult.data
    }

    if (result.data && Array.isArray(result.data.items)) {
      uploadedFiles.value = result.data.items.map((item) => ({
        ...item,
        updatedBy: users.find((user) => user.userId === item.updaterId)?.nameKanji || item.updater || '-'
      }))
    } else {
      uploadedFiles.value = []
    }
  } finally {
    filesLoading.value = false
  }
}

async function downloadFile(item: any): Promise<void> {
  try {
    const downloadQuery = ref<DownloadIssueProjectFile>({
      issueProjectId: targetData.id,
      fileName: item.fileName
    })

    const { executeWithResult: downloadFile } = useDownloadIssueProjectFile(downloadQuery)
    const result = await downloadFile()

    if (!result) return
    if (result.error) return

    if (result.data) {
      const blob = new Blob([result.data])
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = item.fileName
      link.click()
      URL.revokeObjectURL(link.href)
    }
  } catch (error) {
    errorToast('ファイルのダウンロードに失敗しました')
  }
}

function updateUploadedFiles(fileToRemove: any): void {
  filesToRemove.value.push(fileToRemove)
  const tempFilesToDeleteIds = filesToRemove.value.map((item) => item.deleteTargetFile.id)
  uploadedFiles.value = uploadedFiles.value.filter(
    (item) => !tempFilesToDeleteIds.includes(item.id)
  )
}

function updateIssueProjectLinksData(data: any): void {
  targetData.issueProjectLinks = data
}

function toIssueProjectDiscussionPage(): void {
  let from = route.query.from as string
  
  if (from === 'customerIdentifying-issueProjectTab') {
    from = 'customerIdentifying-issueProjectTab-issueProjectEdit'
  }
  if (from === 'top-issueProjectTab') {
    from = 'top-issueProjectTab-issueProjectEdit'
  }
  if (from === 'customerIdentifying-caseTab') {
    from = 'customerIdentifying-issueProjectDiscussionTab'
  }

  router.push({
    path: `/customer-proposal/case/issue-project/issue-project-discussion?issueProjectId=${targetData.id}&customerIdentificationId=${customerIdentificationId.value}&from=${from}`
  })
}

function toBusinessUnderstandingPage(): void {
  router.push({
    path: '/business-understanding',
    query: {
      customerIdentificationId: customerIdentificationId.value
    }
  })
}

function toIssueProjectTaskEditPage(e?: any): void {
  let from = route.query.from as string
  if (from === 'customerIdentifying-issueProjectTab') {
    from = 'customerIdentifying-issueProjectTab-issueProjectEdit'
  }
  if (from === 'top-issueProjectTab') {
    from = 'top-issueProjectTab-issueProjectEdit'
  }

  if (e?.target?.id) {
    const url = `/customer-proposal/case/issue-project/issue-project-task/edit?id=${e.target.id}&customerIdentificationId=${customerIdentificationId.value}&issueProjectId=${targetData.id}&from=${from}`
    
    if (e.pressedCtrlKey) {
      window.open(url, e.target.id)
    } else {
      router.push(url)
    }
  } else {
    const url = `/customer-proposal/case/issue-project/issue-project-task/edit?customerIdentificationId=${customerIdentificationId.value}&issueProjectId=${targetData.id}&from=${from}`
    router.push(url)
  }
}

async function fetchInitialData(): Promise<void> {
  try {
    inProgress.value = true

    await loadOptions()
    await setUserOptions()

    if (!isAdd.value) {
      await loadIssueProjectData()
      await getFileList()
      
      // Add initial task search for edit mode
      if (targetData.id) {
        await searchTasks()
      }
      
      calculateStatus()
      preIsIssueResolveOrCancelDateRegistered.value = isIssueResolveOrCancelDateRegistered.value
    } else {
      filesLoading.value = false
    }
  } catch (error) {
    errorToast('データの読み込み中にエラーが発生しました')
  } finally {
    inProgress.value = false
  }
}

watch(() => targetData.projectInformation?.issueResolvedDate, (newVal) => {
  inputField.value.groupE.disabled = !!newVal
})

watch(() => targetData.projectInformation?.issueCanceledDate, (newVal) => {
  inputField.value.groupD.disabled = !!newVal
})

await fetchInitialData()
</script>
