### SharedKernel

## 概要

SharedKernelライブラリは、ドメイン共通基盤を提供するライブラリです。ドメイン駆動設計（DDD）の実装に必要な基本的な機能、バリデーション、ID管理などの機能を提供し、ドメイン層の実装効率と品質向上を支援します。

## 主要機能

### ドメイン基盤
- **ID管理**: ドメインID操作の拡張機能
- **値オブジェクト**: ドメイン固有の値型の管理
- **エンティティ**: ドメインエンティティの基本機能

### バリデーション
- **ビジネス値検証**: ビジネスルールに基づく値検証
- **検証ルール**: ドメイン固有の検証ルール拡張
- **柔軟な検証**: カスタム検証ルールの実装

### 拡張性
- **拡張メソッド**: 既存クラスの機能拡張
- **カスタマイズ**: ドメイン固有の要件への対応
- **再利用性**: 共通ドメイン機能の再利用

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| IdentityExtensions | 拡張メソッド | ID操作の拡張 | ドメインID管理 |
| BusinessValueValidationRuleExtensions | バリデーション | ビジネス値検証ルール拡張 | 入力値検証 |
