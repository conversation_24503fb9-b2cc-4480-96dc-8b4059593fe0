variables:
- group: common
- group: develop
- group: e2e

jobs:
  - job: E2E
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: file-creator@6
      displayName: 'Create env file'
      condition: succeeded()
      inputs:
        fileoverwrite: true
        filepath: $(System.DefaultWorkingDirectory)/tests/e2e/env.js
        filecontent: |
          module.exports = {
            baseUrl: '$(e2e_baseUrl)',
            apiBaseUrl: '$(e2e_apiBaseUrl)',
            userId: '$(e2e_userId)',
            password: '$(e2e_password)',
            loginStateFile: '.loginstate.json'
          }

    - task: NodeTool@0
      displayName: 'Install Node.js'
      condition: succeeded()
      inputs:
        versionSpec: '12.x'

    - script: docker build . -t tmp/e2e
      displayName: 'Build docker image'
      workingDirectory: $(System.DefaultWorkingDirectory)/tests/e2e
      condition: succeeded()

    - script: docker run --rm -v `pwd`:/e2e --ipc=host tmp/e2e /bin/bash -c "npm install && npx playwright install && npx playwright test"
      displayName: 'Npm install and run e2e test in docker'
      workingDirectory: $(System.DefaultWorkingDirectory)/tests/e2e
      condition: succeeded()

    - task: PublishTestResults@2
      displayName: 'Publish test results'
      condition: always()
      inputs:
        testResultsFormat: 'JUnit' # Options: JUnit, NUnit, VSTest, xUnit, cTest
        testResultsFiles: '$(System.DefaultWorkingDirectory)/tests/e2e/test-results/results.xml'

    - task: PublishPipelineArtifact@1
      displayName: 'Publish result artifacts'
      condition: always()
      inputs:
        targetPath: $(System.DefaultWorkingDirectory)/tests/e2e/test-results
        artifactName: e2e_results
