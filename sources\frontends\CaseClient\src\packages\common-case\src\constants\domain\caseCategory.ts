/**
 * 案件カテゴリ
 */
export const CASE_CATEGORY = {
  Undefined: '未定義',
  NewLoan: '融資案件（新規）',
  OtherLoan: '融資案件（その他）',
  UnorganizedLoan: '融資案件（未整理）',
  NewLease: 'リース案件（新規）',
  ExternallyCompletedLease: 'リース案件（他社満了）',
  OtherLease: 'リース案件（その他） ',
  GeneralTransaction: '総合取引案件',
  CorporateLifeInsurance: '法人保険案件（生保）',
  CorporateNonlifeInsurance: '法人保険案件（損保）',
  NewInvestment: '投資案件（新規）',
  IssueProject: '課題案件',
  BusinessMatchingBuy: 'ビジネスマッチング(買)',
  BusinessMatchingSell: 'ビジネスマッチング(売)',
} as const

export type CaseCategoryType = keyof typeof CASE_CATEGORY

export const caseCategoryKeys = Object.keys(CASE_CATEGORY) as CaseCategoryType[]
