import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

// 主として扱うデータのスキーマを定義します。
export const issueprojectdiscussioncommentreactionSchema = z.object({
  id: z.string(),
  commentId: z.string(),
  staffId: z.string(),
  staffName: z.string(),
  reactionType: z.number(),
  updateDateTime: z.date(),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type IssueProjectDiscussionCommentReaction = z.infer<
  typeof issueprojectdiscussioncommentreactionSchema
>

// =====================================================================================================================
// APIクライアントの定義(idで取得)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProjectDiscussionCommentReaction(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionCommentReaction>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/issueprojectdiscussioncommentreaction/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(新規追加)
// =====================================================================================================================

// 作成用のスキーマを作成します。
export const IssueProjectDiscussionCommentReactionForCreateSchema = z.object({
  commentId: z.string(),
  staffId: z.string(),
  staffName: z.string(),
  reactionType: z.number(),
  updatedDateTime: z.date(),
})

export type IssueProjectDiscussionCommentReactionForCreate = z.infer<
  typeof IssueProjectDiscussionCommentReactionForCreateSchema
>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostIssueProjectDiscussionCommentReaction(
  body: Ref<IssueProjectDiscussionCommentReactionForCreate>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionCommentReaction>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncommentreaction',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(更新)
// =====================================================================================================================

// 更新用のデータのスキーマを定義します。
export const issueprojectdiscussioncommentreactionSchemaForSave =
  issueprojectdiscussioncommentreactionSchema.pick({
    commentId: true,
    staffId: true,
    staffName: true,
  })
// 更新用の型を作成します。
export type IssueProjectDiscussionCommentReactionForSave = z.infer<
  typeof issueprojectdiscussioncommentreactionSchemaForSave
>

// 作成用のスキーマを作成します。
export const issueprojectdiscussioncommentreactionSchemaForSaveSchema =
  z.object({
    id: z.string(),
    reactionType: z.number(),
    updatedDateTime: z.date(),
    version: z.string(),
  })

export type updateCommentreactionSchemaForSave = z.infer<
  typeof issueprojectdiscussioncommentreactionSchemaForSaveSchema
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutIssueProjectDiscussionCommentReaction(
  body: Ref<updateCommentreactionSchemaForSave>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionCommentReaction>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncommentreaction',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */

export type IssueProjectDiscussionCommentReactionForDelete = {
  id: string
  version: string
}

export function useDeleteIssueProjectDiscussionCommentReaction(
  body: Ref<IssueProjectDiscussionCommentReactionForDelete>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncommentreaction',
        ),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}
