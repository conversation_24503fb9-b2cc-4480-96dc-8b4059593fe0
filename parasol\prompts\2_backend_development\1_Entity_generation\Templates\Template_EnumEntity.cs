using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace BusinessCustomerUnderstandingService.Domain.Enums;

public enum MediumToLongTermVisionType
{
  // TODO: Undefined については、ドメイン言語に指定がない場合は実装しないでください。

  /// <summary>
  /// 未定義
  /// </summary>
  [Display(Name = "")]
  Undefined = -1,

  /// <summary>
  /// 中長期ビジョン0
  /// </summary>
  [Display(Name = "0 : 経営理念、中長期ビジョンが無い、調べても見つからない")]
  MediumToLongTermVisionZero = 0,

  /// <summary>
  /// 中長期ビジョン1
  /// </summary>
  [Display(Name = "1 : 経営理念がある")]
  MediumToLongTermVisionOne = 1,

  /// <summary>
  /// 中長期ビジョン2
  /// </summary>
  [Display(Name = "2 : 経営理念、中長期ビジョンがある(HPや会社内に掲示されている)")]
  MediumToLongTermVisionTwo = 2,
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum SubjectsOfCertificationAndDeclarationType
{
    /// <summary>
    /// 未定義
    /// </summary>
    Undefind,

    /// <summary>
    /// SDGs宣言
    /// </summary>
    [Display(Name = "SDGs宣言")]
    SDGsDeclaration,

    /// <summary>
    /// SBT
    /// </summary>
    [Display(Name = "SBT")]
    SBT,

    /// <summary>
    /// 中小企業版SBT
    /// </summary>
    [Display(Name = "中小企業版SBT")]
    SBTForSME,

    /// <summary>
    /// エコアクション21
    /// </summary>
    [Display(Name = "エコアクション21")]
    EcoAction21,

    /// <summary>
    /// ISO14001
    /// </summary>
    [Display(Name = "ISO14001")]
    ISO14001,

    /// <summary>
    /// 格付機関による格付・セカンドオピニオン取得（直近３年以内
    /// </summary>
    [Display(Name = "格付機関による格付・セカンドオピニオン取得（直近３年以内）")]
    CertifiedByAnExternalOrganization,

    /// <summary>
    /// その他
    /// </summary>
    [Display(Name = "その他")]
    Other,
}