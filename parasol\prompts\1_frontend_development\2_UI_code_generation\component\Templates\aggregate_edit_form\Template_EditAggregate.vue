<!--
===========================================
アグリゲート編集コンポーネント テンプレート
===========================================

このテンプレートは、アグリゲートルートエンティティの編集画面コンポーネントの
基本構造を提供します。

【使用方法】
1. TODOコメントに従って、エンティティ固有の情報に置き換える
2. 不要なセクションは削除し、必要なセクションを追加する
3. 各セクションのコンポーネント名を実際のエンティティに合わせて変更する
4. パスやcomposable名をエンティティに応じて変更する
-->

<script setup lang="ts">
// 共通のcomposablesをインポート
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import { confirmBeforeUnload } from '@hox/base/src/utils/shared/confirmBeforeUnload'
import type { SectionConfig } from '@hox/base/src/components/shared/templates/AppEditPageTmpl.vue'
import { layoutOption } from '@ibp/base/src/constants/layout'

// TODO: エンティティ固有のcomposablesをインポート
import { useGetSample } from './useGetSample'
import { useCreatedSampleState } from './useCreatedSampleState'
import { useUpdateSample } from './useUpdateSample'
import { useApproveConditionRegistration } from '../common/composables/useApproveConditionRegistration'
import { useResetConditionRegistrationApply } from '../common/composables/useResetConditionRegistrationApply'
import { useResetConditionRegistrationApprove } from '../common/composables/useResetConditionRegistrationApprove'

// TODO: エンティティに応じてセクション構成を変更してください
const sections: SectionConfig[] = [
  {
    key: 'basic',
    title: '基本情報',
  },
  // TODO: 以下は例です。実際のエンティティに合わせて変更してください
  {
    key: 'detail',
    title: '詳細情報',
  },
  {
    key: 'settings',
    title: '設定',
  },
]

// 共通のprops定義
const props = defineProps<{
  caseId: string
}>()

const caseId = toRef(props, 'caseId')

const inProgress = ref(true)

// TODO: エンティティ固有のcomposableを使用
const {
  fetchSample, // TODO: fetchYourEntity に変更
  inProgress: inProgressGet,
} = useGetSample(caseId)

// TODO: エンティティ固有のcomposableを使用
const {
  SampleModel, // TODO: yourEntityModel に変更
  errorMessages,
  setSampleModel, // TODO: setYourEntityModel に変更
  inProgress: inProgressUpdate,
  validateItem,
} = useUpdateSample()

// TODO: エンティティ固有のcomposableを使用
const { inProgress: inProgressApprove, approveConditionRegistration } =
  useApproveConditionRegistration(
    SampleModel, // TODO: yourEntityModel に変更
    'Sample', // TODO: エンティティ名に変更
  )

// TODO: エンティティ固有のcomposableを使用
const { inProgress: inProgressResetApply, resetConditionRegistrationApply } =
  useResetConditionRegistrationApply(
    SampleModel, // TODO: yourEntityModel に変更
  )

// TODO: エンティティ固有のcomposableを使用
const {
  inProgress: inProgressResetApprove,
  resetConditionRegistrationApprove,
} = useResetConditionRegistrationApprove(
  SampleModel, // TODO: yourEntityModel に変更
  'Sample', // TODO: エンティティ名に変更
)

// TODO: エンティティ固有のcomposableを使用
const { getCreatedModel } =
  useCreatedSampleState() // TODO: useCreatedYourEntityState() に変更

// ローディング状態の管理（通常変更不要）
const { hasTrue: loading } = useFlagCondition(
  inProgress,
  inProgressGet,
  inProgressUpdate,
  inProgressApprove,
  inProgressResetApply,
  inProgressResetApprove,
)

// データ変更監視（通常変更不要）
const { hasChanged, init: initWatchDataChanges } = useWatchDataChanges()

// ページ離脱前の確認（通常変更不要）
confirmBeforeUnload(hasChanged, '')

// TODO: エンティティ固有のモデル取得関数を実装
async function fetchSampleModel() {
  const createdModel = getCreatedModel()
  if (createdModel) {
    return createdModel
  }
  return await fetchSample() // TODO: fetchYourEntity() に変更
}

// TODO: 必要に応じてアクション関数を定義してください
// 例: 一時保存、申請、承認、リセットなど、エンティティに応じたアクション
/*
async function onDraft() {
  // 一時保存処理
  const { success } = await draftYourEntity()
  if (!success) return
  initWatchDataChanges(yourEntityModel)
}

async function onApply() {
  // 申請処理
  const { success } = await updateYourEntity()
  if (!success) return
  initWatchDataChanges(yourEntityModel)
}

async function onApprove() {
  // 承認処理
  await approveConditionRegistration()
  initWatchDataChanges(yourEntityModel)
}

async function onResetApply() {
  // 申請リセット処理
  await resetConditionRegistrationApply()
  initWatchDataChanges(yourEntityModel)
}

async function onResetApprove() {
  // 承認リセット処理
  await resetConditionRegistrationApprove()
  initWatchDataChanges(yourEntityModel)
}
*/

// コンポーネントマウント時の初期化処理
onMounted(async () => {
  try {
    // TODO: 関数名をエンティティに応じて変更、不要な場合は削除
    const [Sample] = await Promise.all([
      fetchSampleModel(), // TODO: fetchYourEntityModel() に変更
    ])
    // TODO: 関数名をエンティティに応じて変更
    setSampleModel(
      Sample, // TODO: setYourEntityModel に変更
    )
    // TODO: モデル名をエンティティに応じて変更
    initWatchDataChanges(SampleModel) // TODO: yourEntityModel に変更
  } finally {
    inProgress.value = false
  }
})

</script>
<template>
  <!-- 編集ページテンプレート（通常変更不要） -->
  <app-edit-page-tmpl
    :sections
    :loading
    :can-save="false"
    :can-remove="false"
    :header-top-offset="layoutOption.CUSTOMER_HEADER_TOP_OFFSET"
  >
    <!-- メインアクション（操作ボタンエリア） -->
    <template #main-actions>
      <!-- TODO: エンティティに応じてボタンコンポーネントを調整・実装してください -->
      <!-- 
      アクションボタンの例:
      【承認フローがある場合】
      <condition-registration-actions
        :item="yourEntityModel"
        condition-registration-type="YourEntityType"
        :loading
        @draft="onDraft"
        @apply="onApply"
        @reset-apply="onResetApply"
        @approve="onApprove"
        @reset-approve="onResetApprove"
      />
      
      【シンプルな編集の場合】
      <app-main-btn density="compact" :loading @click="onSave">保存</app-main-btn>
      <app-sub-btn density="compact" @click="onCancel">キャンセル</app-sub-btn>
      
      【削除可能な場合】
      <app-main-btn density="compact" :loading @click="onSave">保存</app-main-btn>
      <app-dangerous-btn density="compact" @click="onDelete">削除</app-dangerous-btn>
      -->
    </template>
    
    <!-- 基本情報セクション -->
    <template #section-basic>
      <!-- TODO: コンポーネント名をエンティティに応じて変更 -->
      <!-- 例: your-entity-basic-info -->
      <acceptance-and-guarantee-within-credit-limit-basic-info
        :item="SampleModel"
        :loading
      />
    </template>
    
    <!-- 詳細情報セクション -->
    <template #section-detail>
      <!-- TODO: コンポーネント名をエンティティに応じて変更 -->
      <!-- 例: your-entity-detail-form -->
      <acceptance-and-guarantee-within-credit-limit-form
        v-model="SampleModel"
        :error-messages
        :loading
        @validate="validateItem"
      />
    </template>
    
    <!-- 設定セクション -->
    <template #section-settings>
      <!-- TODO: コンポーネント名をエンティティに応じて変更 -->
      <!-- 例: your-entity-settings-form -->
      <acceptance-and-guarantee-within-credit-limit-interest-form
        v-model="SampleModel"
        :error-messages
        :loading
        @validate="validateItem"
      />
    </template>
  </app-edit-page-tmpl>
</template>

<!--
===========================================
テンプレート使用時の変更点チェックリスト
===========================================

【必須変更事項】
□ エンティティ固有のcomposablesをインポート（useGetSample → useGetYourEntity等）
□ sectionsの構成を実際のエンティティに応じて完全に書き換え
□ composableの返り値プロパティ名をエンティティに応じて変更（xxxModel、fetchXxx等）
□ onMounted内の関数呼び出しをエンティティに応じて変更
□ templateセクションを実際のエンティティ用に完全に書き換え
□ main-actionsテンプレート内にアクションボタンを実装

【オプション変更事項】
□ 必要なアクション関数を追加（エンティティ固有の操作）
□ 各Formコンポーネントのpropsの調整（:schema-shapeなど特殊なpropsがある場合）
□ 承認フローが不要な場合は関連するcomposablesとアクション関数を削除
-->
