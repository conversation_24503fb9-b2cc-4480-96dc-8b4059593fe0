// ESLint config generated by Nuxt
/// <reference path="./eslint-typegen.d.ts" />

import { composer } from '../node_modules/eslint-flat-config-utils/dist/index.mjs';
import typegen from '../node_modules/eslint-typegen/dist/index.mjs';
import { createConfigForNuxt, defineFlatConfigs, resolveOptions } from '../node_modules/@nuxt/eslint-config/dist/flat.mjs';

export { defineFlatConfigs }

export const configs = composer()

export const options = resolveOptions({
  "features": {
    "standalone": true
  },
  "dirs": {
    "pages": [
      "src/pages",
      "src/packages/common-case/src/pages",
      "node_modules/@ibp/base/src/pages",
      "node_modules/@hox/base/src/pages",
      "src/packages/issue-project/src/pages",
      "src/packages/case-work-flow-optimization/src/pages",
      "src/packages/lease/src/pages"
    ],
    "composables": [
      "src/composables",
      "src/utils",
      "src/packages/common-case/src/composables",
      "src/packages/common-case/src/utils",
      "node_modules/@ibp/base/src/composables",
      "node_modules/@ibp/base/src/utils",
      "node_modules/@hox/base/src/composables",
      "node_modules/@hox/base/src/utils",
      "src/packages/issue-project/src/composables",
      "src/packages/issue-project/src/utils",
      "src/packages/case-work-flow-optimization/src/composables",
      "src/packages/case-work-flow-optimization/src/utils",
      "src/packages/lease/src/composables",
      "src/packages/lease/src/utils"
    ],
    "components": [
      "src/packages/common-case/src/components",
      "src/packages/issue-project/src/components",
      "src/packages/case-work-flow-optimization/src/components",
      "src/packages/lease/src/components",
      "src/packages/common-case/src/components",
      "src/packages/common-case/src/@hox/base/src/components",
      "src/packages/common-case/src/@ibp/base/src/components",
      "node_modules/@ibp/base/src/@hox/base/src/components",
      "node_modules/@ibp/base/src/components",
      "node_modules/@hox/base/src/components",
      "src/packages/issue-project/src/components",
      "src/packages/issue-project/src/@hox/base/src/components",
      "src/packages/issue-project/src/@ibp/base/src/components",
      "src/packages/case-work-flow-optimization/src/components",
      "src/packages/case-work-flow-optimization/src/@hox/base/src/components",
      "src/packages/case-work-flow-optimization/src/@ibp/base/src/components",
      "src/packages/lease/src/components",
      "src/packages/lease/src/@hox/base/src/components",
      "src/packages/lease/src/@ibp/base/src/components"
    ],
    "componentsPrefixed": [],
    "layouts": [
      "src/layouts",
      "src/packages/common-case/src/layouts",
      "node_modules/@ibp/base/src/layouts",
      "node_modules/@hox/base/src/layouts",
      "src/packages/issue-project/src/layouts",
      "src/packages/case-work-flow-optimization/src/layouts",
      "src/packages/lease/src/layouts"
    ],
    "plugins": [
      "src/plugins",
      "src/packages/common-case/src/plugins",
      "node_modules/@ibp/base/src/plugins",
      "node_modules/@hox/base/src/plugins",
      "src/packages/issue-project/src/plugins",
      "src/packages/case-work-flow-optimization/src/plugins",
      "src/packages/lease/src/plugins"
    ],
    "middleware": [
      "src/middleware",
      "src/packages/common-case/src/middleware",
      "node_modules/@ibp/base/src/middleware",
      "node_modules/@hox/base/src/middleware",
      "src/packages/issue-project/src/middleware",
      "src/packages/case-work-flow-optimization/src/middleware",
      "src/packages/lease/src/middleware"
    ],
    "modules": [
      "src/modules",
      "src/packages/common-case/src/modules",
      "node_modules/@ibp/base/src/modules",
      "node_modules/@hox/base/src/modules",
      "src/packages/issue-project/src/modules",
      "src/packages/case-work-flow-optimization/src/modules",
      "src/packages/lease/src/modules"
    ],
    "servers": [],
    "root": [
      "D:/aspire/ibp-case-app-fork/sources/frontends/CaseClient"
    ],
    "src": [
      "src",
      "src/packages/common-case/src",
      "node_modules/@ibp/base/src",
      "node_modules/@hox/base/src",
      "src/packages/issue-project/src",
      "src/packages/case-work-flow-optimization/src",
      "src/packages/lease/src"
    ]
  }
})

configs.append(
// Nuxt Configs
createConfigForNuxt(options),

// Set globals from imports registry
{"name":"nuxt/import-globals","languageOptions":{"globals":{"useScriptTriggerConsent":"readonly","useScriptEventPage":"readonly","useScriptTriggerElement":"readonly","useScript":"readonly","useScriptGoogleAnalytics":"readonly","useScriptPlausibleAnalytics":"readonly","useScriptClarity":"readonly","useScriptCloudflareWebAnalytics":"readonly","useScriptFathomAnalytics":"readonly","useScriptMatomoAnalytics":"readonly","useScriptGoogleTagManager":"readonly","useScriptGoogleAdsense":"readonly","useScriptSegment":"readonly","useScriptMetaPixel":"readonly","useScriptXPixel":"readonly","useScriptIntercom":"readonly","useScriptHotjar":"readonly","useScriptStripe":"readonly","useScriptLemonSqueezy":"readonly","useScriptVimeoPlayer":"readonly","useScriptYouTubePlayer":"readonly","useScriptGoogleMaps":"readonly","useScriptNpm":"readonly","useScriptCrisp":"readonly","isVue2":"readonly","isVue3":"readonly","defineNuxtLink":"readonly","useNuxtApp":"readonly","tryUseNuxtApp":"readonly","defineNuxtPlugin":"readonly","definePayloadPlugin":"readonly","useRuntimeConfig":"readonly","defineAppConfig":"readonly","requestIdleCallback":"readonly","cancelIdleCallback":"readonly","setInterval":"readonly","useAppConfig":"readonly","updateAppConfig":"readonly","defineNuxtComponent":"readonly","useAsyncData":"readonly","useLazyAsyncData":"readonly","useNuxtData":"readonly","refreshNuxtData":"readonly","clearNuxtData":"readonly","useHydration":"readonly","callOnce":"readonly","useState":"readonly","clearNuxtState":"readonly","clearError":"readonly","createError":"readonly","isNuxtError":"readonly","showError":"readonly","useError":"readonly","useFetch":"readonly","useLazyFetch":"readonly","useCookie":"readonly","refreshCookie":"readonly","onPrehydrate":"readonly","prerenderRoutes":"readonly","useRequestHeader":"readonly","useRequestHeaders":"readonly","useResponseHeader":"readonly","useRequestEvent":"readonly","useRequestFetch":"readonly","setResponseStatus":"readonly","onNuxtReady":"readonly","preloadComponents":"readonly","prefetchComponents":"readonly","preloadRouteComponents":"readonly","abortNavigation":"readonly","addRouteMiddleware":"readonly","defineNuxtRouteMiddleware":"readonly","setPageLayout":"readonly","navigateTo":"readonly","useRoute":"readonly","useRouter":"readonly","isPrerendered":"readonly","loadPayload":"readonly","preloadPayload":"readonly","definePayloadReducer":"readonly","definePayloadReviver":"readonly","useLoadingIndicator":"readonly","getAppManifest":"readonly","getRouteRules":"readonly","reloadNuxtApp":"readonly","useRequestURL":"readonly","usePreviewMode":"readonly","useRouteAnnouncer":"readonly","useRuntimeHook":"readonly","onBeforeRouteLeave":"readonly","onBeforeRouteUpdate":"readonly","withCtx":"readonly","withDirectives":"readonly","withKeys":"readonly","withMemo":"readonly","withModifiers":"readonly","withScopeId":"readonly","onActivated":"readonly","onBeforeMount":"readonly","onBeforeUnmount":"readonly","onBeforeUpdate":"readonly","onDeactivated":"readonly","onErrorCaptured":"readonly","onMounted":"readonly","onRenderTracked":"readonly","onRenderTriggered":"readonly","onServerPrefetch":"readonly","onUnmounted":"readonly","onUpdated":"readonly","computed":"readonly","customRef":"readonly","isProxy":"readonly","isReactive":"readonly","isReadonly":"readonly","isRef":"readonly","markRaw":"readonly","proxyRefs":"readonly","reactive":"readonly","readonly":"readonly","ref":"readonly","shallowReactive":"readonly","shallowReadonly":"readonly","shallowRef":"readonly","toRaw":"readonly","toRef":"readonly","toRefs":"readonly","triggerRef":"readonly","unref":"readonly","watch":"readonly","watchEffect":"readonly","watchPostEffect":"readonly","watchSyncEffect":"readonly","isShallow":"readonly","effect":"readonly","effectScope":"readonly","getCurrentScope":"readonly","onScopeDispose":"readonly","defineComponent":"readonly","defineAsyncComponent":"readonly","resolveComponent":"readonly","getCurrentInstance":"readonly","h":"readonly","inject":"readonly","hasInjectionContext":"readonly","nextTick":"readonly","provide":"readonly","mergeModels":"readonly","toValue":"readonly","useModel":"readonly","useAttrs":"readonly","useCssModule":"readonly","useCssVars":"readonly","useSlots":"readonly","useTransitionState":"readonly","useId":"readonly","useTemplateRef":"readonly","useShadowRoot":"readonly","Component":"readonly","ComponentPublicInstance":"readonly","ComputedRef":"readonly","DirectiveBinding":"readonly","ExtractDefaultPropTypes":"readonly","ExtractPropTypes":"readonly","ExtractPublicPropTypes":"readonly","InjectionKey":"readonly","PropType":"readonly","Ref":"readonly","MaybeRef":"readonly","MaybeRefOrGetter":"readonly","VNode":"readonly","WritableComputedRef":"readonly","injectHead":"readonly","useHead":"readonly","useSeoMeta":"readonly","useHeadSafe":"readonly","useServerHead":"readonly","useServerSeoMeta":"readonly","useServerHeadSafe":"readonly","toDateRef":"readonly","toStringArray":"readonly","useDateFormatters":"readonly","getStaffOptions":"readonly","getStaffAndTeamOptions":"readonly","getBranchOptions":"readonly","getIndustryOptions":"readonly","validateFileSize":"readonly","isValidJson":"readonly","parseQuery":"readonly","quillDeltaToHtml":"readonly","htmlToQuillDelta":"readonly","htmlToPlainText":"readonly","getMentionTargetIds":"readonly","getMentionTargets":"readonly","setTOCToCookie":"readonly","removeTOCFromCookie":"readonly","useNextPath":"readonly","useTeam":"readonly","useUser":"readonly","createErrors":"readonly","redirectTo":"readonly","useNuxtDevTools":"readonly","definePageMeta":"readonly","useLink":"readonly"}}}
)

export function withNuxt(...customs) {
  return configs
    .clone()
    .append(...customs)
    .onResolved(configs => typegen(configs, { dtsPath: new URL("./eslint-typegen.d.ts", import.meta.url), augmentFlatConfigUtils: true }))
}

export default withNuxt