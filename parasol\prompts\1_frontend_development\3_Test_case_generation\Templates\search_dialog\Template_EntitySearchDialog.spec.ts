import { describe, test, expect, vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { mountSuspended } from '@nuxt/test-utils/runtime'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import {
  patchOFetchOnMsw,
  getRequestQueryFromMswHandler,
  getVdataTableRowsAsObjectList,
  setTestUser,
  clearTestUser,
} from '../../../testSupport'
// TODO: テスト対象コンポーネントのパスを実際のエンティティに合わせて変更してください
// 例: import YourEntitySearchDialog from '@/components/your-entity/organisms/YourEntitySearchDialog.vue'
import BranchSearchDialog from '@/components/branch/organisms/BranchSearchDialog.vue'

// =====================================================================================================================
// テスト用処理設定
// =====================================================================================================================
// APIのモックサーバー定義
const server = setupServer()
// TODO: テスト対象コンポーネントの型定義を実際のエンティティに合わせて変更してください
// 例: BranchSearchDialog → YourEntitySearchDialog
const TestComponent = defineComponent({
  components: { BranchSearchDialog },
  setup() {
    const dialog = ref()
    return {
      dialog,
    }
  },
  render() {
    return h('div', [
      h(BranchSearchDialog, { ref: 'dialog' }),
    ])
  },
})

// TODO: ダイアログコンポーネント名を実際のエンティティに合わせて変更してください
// 例: 'your-entity-search-dialog'
const dialogComponentName = 'branch-search-dialog'

// =====================================================================================================================
// 前処理/後処理
// =====================================================================================================================
// テスト開始前処理
beforeAll(() => {
  // ユーザー情報を設定
  setTestUser('テストユーザー', 'test_user', ['admin'])
})
// テスト終了後処理
afterAll(() => {
  // APIモックサーバーを閉じる
  server.close()
  // ユーザー情報をクリア
  clearTestUser()
})
// テストケース開始前処理
beforeEach(() => {
  // 処理なし
})
// テストケース終了時後処理
afterEach(() => {
  // APIのモックハンドラをリセット
  server.resetHandlers()
  // モック化したモジュールを元の実装に戻す
  vi.restoreAllMocks()
})

// =====================================================================================================================
// APIデータ定義
// =====================================================================================================================
// TODO: テストデータを実際のエンティティの構造に合わせて変更してください
const dataList = [
  { id: '1', name: '部署1' },
  { id: '2', name: '部署2' },
  { id: '3', name: '部署3' },
]
const apiResult = {
  items: dataList,
  total: dataList.length * 2,
}

// =====================================================================================================================
// テスト実装
// =====================================================================================================================
describe('EntitySearchDialog Component Test', () => {
  test('初期表示処理：初期表示時には一覧データなしで表示される', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開く
    vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // 表示されていることを
    expect(dialogVm.dialog).toBe(true)
    // データが設定されていないことを確認
    expect(dialogVm.data).toEqual({})
  })

  test('検索処理：検索条件ありの場合、正しいクエリパラメータにてAPIが呼び出されデータが取得できる', async () => {
    // TODO: APIのモックエンドポイントを実際のエンティティのAPIエンドポイントに変更してください
    // 例: '/v1.0/your-entities'
    // APIのモックEndpointを登録
    const getHandler = vi.fn(() => HttpResponse.json(apiResult))
    server.use(http.get('/v1.0/branch', getHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開く
    vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // TODO: 検索条件を実際のエンティティの項目に合わせて変更してください
    // 例: dialogVm.searchCondition.name = 'test'
    // 検索条件を設定
    dialogVm.searchCondition.branchName = '部署'

    // 検索処理を実行
    await dialogVm.search({})

    // APIが呼び出されたことを確認
    expect(getHandler).toHaveBeenCalledTimes(1)
    // TODO: APIのリクエストパラメータを実際のエンティティの項目に合わせて変更してください
    // 例: expect(query).toEqual({ name: 'test', pageIndex: '1', pageSize: '10' })
    // APIのリクエストパラメータを検証
    const query = await getRequestQueryFromMswHandler(getHandler)
    expect(query).toEqual({ branchName: '部署', pageIndex: '1', pageSize: '10' })
    // 検索結果を確認
    expect(dialogVm.data.total).toBe(apiResult.total)
    expect(dialogVm.data.items.length).toBe(apiResult.items.length)
    // TODO: 検索結果のデータ構造を実際のエンティティに合わせて変更してください
    // 例: expect(dialogVm.data.items[0]).toEqual({ id: '1', name: 'test' })
    expect(dialogVm.data.items[0]).toEqual({ id: '1', name: '部署1' })
    // 表示されているデータを確認
    const datas = getVdataTableRowsAsObjectList(dialogWrapper)
    expect(datas).toEqual(apiResult.items.map(i => {
      return {
        // TODO: プロパティを実際のエンティティの項目に合わせて変更してください
        // 例: Id: i.id, Name: i.name
        Id: i.id,
        部署名: i.name,
      }
    }))
  })

  test('選択処理：一覧から選択を行うと選択された情報が返却される', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開き、結果返却を待つ
    const openPromis = vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // 一覧のデータを設定
    dialogVm.data = apiResult

    // データを選択
    await dialogWrapper.vm.select({ item: dialogVm.data.items[0] })

    // ダイアログの返却値を確認
    openPromis.then((result: any) => {
      expect(result).toEqual({
        isOk: true,
        // TODO: 返却データの構造を実際のエンティティに合わせて変更してください
        // 例: data: { id: '1', name: 'test' }
        data: { id: '1', name: '部署1' },
      })
    })
  })
})
