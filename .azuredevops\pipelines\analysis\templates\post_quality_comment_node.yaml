parameters:
  analysisResultDirectory: ''
  testProjectRootDirectory: ''
  scriptDirectory: ''
  threadId: $(ThreadId)
  title: ''

steps:
- task: PowerShell@2
  displayName: 'post pr quality comment for node'
  inputs:
    targetType: 'filePath'
    filePath: '${{ parameters.scriptDirectory }}/post_comment_node.ps1'
    pwsh: true
    arguments: >
      -accessToken $(System.AccessToken)
      -analysisResultDirectory ${{ parameters.analysisResultDirectory }}
      -testProjectRootDirectory ${{ parameters.testProjectRootDirectory }}  
      -sourceBranch $(System.PullRequest.SourceBranch)
      -threadId ${{ parameters.threadId }}
      -title ${{ parameters.title }}