import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type { CaseDiscussionPurposeType } from '@ibp/issue-project/src/constants/domain/caseDiscussionPurpose'
import type { ReactionType } from '@ibp/issue-project/src/constants/domain/reactionType'
import { caseDiscussionPurposeKeys } from '@ibp/issue-project/src/constants/domain/caseDiscussionPurpose'

// =====================================================================================================================
// APIクライアントの定義(idで取得)
// =====================================================================================================================

export type IssueProjectDiscussionCommentFile = {
  id: string
  fileName: string
  commentId: string
  updatedDateTime: Date
  updaterId: string
  updaterName: string
}

export type IssueProjectDiscussionCommentReaction = {
  id: string
  commentId: string
  reactionType: ReactionType
  staffId: string
  staffName: string
  updatedDateTime: Date
  version: string
}

// Resultの型定義
export type GetIssueProjectDiscussionCommentResult = {
  id: string
  threadId: string
  registeredDateTime: Date
  registrant: string
  registrantId: string
  purpose: CaseDiscussionPurposeType
  person: string | null
  isPersonOfPower: boolean | null
  description: string
  reactions: IssueProjectDiscussionCommentReaction[]
  files: IssueProjectDiscussionCommentFile[]
  version: string
}

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProjectDiscussionComment(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetIssueProjectDiscussionCommentResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/issueprojectdiscussioncomment/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(新規追加)
// =====================================================================================================================

// IFormFileのスキーマ
export const IFormFileSchema = z.object({
  contentType: z.string(),
  length: z.number().int(),
  name: z.string(),
  fileName: z.string(),
})

// AddIssueProjectDiscussionCommentCommandのスキーマ
export const addIssueProjectDiscussionCommentCommandSchema = z.object({
  threadId: z.string(),
  registeredDateTime: z.date(),
  registrant: z.string(),
  registrantId: z.string(),
  purpose: z.enum(
    caseDiscussionPurposeKeys.map(String) as [string, ...string[]],
  ),
  person: z.string().nullish(),
  isPersonOfPower: z.boolean().nullish(),
  description: z.string(),
  uploadFiles: z.array(IFormFileSchema).nullish(),
  customerIdentificationId: z.string().uuid(), // GuidはUUIDとして扱う
  customerName: z.string(),
  mentionTargetUserIds: z.array(z.string()).nullish(),
  mentionTargetTeamIds: z.array(z.string()).nullish(),
  mentionTargetTeamMemberUserIds: z.array(z.string()).nullish(),
})

// 作成用のデータのスキーマを定義します。
export const issueprojectdiscussioncommentSchemaForCreate =
  addIssueProjectDiscussionCommentCommandSchema
// 作成用の型を作成します。
export type IssueProjectDiscussionCommentForCreate = z.infer<
  typeof issueprojectdiscussioncommentSchemaForCreate
>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostIssueProjectDiscussionComment(body: Ref<FormData>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetIssueProjectDiscussionCommentResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncomment',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(更新)
// =====================================================================================================================

// UpdateIssueProjectDiscussionCommentCommandのスキーマ
export const updateIssueProjectDiscussionCommentCommandSchema = z.object({
  id: z.string(),
  registrantId: z.string(),
  purpose: z.enum(
    caseDiscussionPurposeKeys.map(String) as [string, ...string[]],
  ),
  person: z.string().nullish(),
  isPersonOfPower: z.boolean().nullish(),
  description: z.string(),
  customerIdentificationId: z.string().uuid(), // GuidはUUIDとして扱います
  customerName: z.string(),
  mentionTargetUserIds: z.array(z.string()).nullish(),
  mentionTargetTeamIds: z.array(z.string()).nullish(),
  mentionTargetTeamMemberUserIds: z.array(z.string()).nullish(),
  version: z.string(),
  uploadFiles: z.array(IFormFileSchema),
  filesToRemove: z.array(z.string()),
})

// 更新用のデータのスキーマを定義します。
export const issueprojectdiscussioncommentSchemaForSave =
  updateIssueProjectDiscussionCommentCommandSchema
// 更新用の型を作成します。
export type IssueProjectDiscussionCommentForSave = z.infer<
  typeof issueprojectdiscussioncommentSchemaForSave
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutIssueProjectDiscussionComment(body: Ref<FormData>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetIssueProjectDiscussionCommentResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncomment',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(削除)
// =====================================================================================================================

export const deleteIssueProjectDiscussionCommentCommand = z.object({
  id: z.string(),
  registrantId: z.string(),
  version: z.string(),
})

export type IssueProjectDiscussionCommentForDelete = z.infer<
  typeof deleteIssueProjectDiscussionCommentCommand
>

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteIssueProjectDiscussionComment(
  body: Ref<IssueProjectDiscussionCommentForDelete>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncomment',
        ),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(ダウンロード)
// =====================================================================================================================

// ダウンロードの型を定義します。
export type downloadIssueProjectDiscussionCommentCriteria = {
  commentId: string
  fileName: string
}

// Resultのスキーマを定義します。
export type ResultFileSchema = {
  data: Blob
}

/**
 * ダウンロードする
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDownloadIssueProjectDiscussionComment(
  query: Ref<downloadIssueProjectDiscussionCommentCriteria>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<ResultFileSchema>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussioncomment/download',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}
