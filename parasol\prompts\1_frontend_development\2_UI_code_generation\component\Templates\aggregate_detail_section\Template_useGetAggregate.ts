/**
 * ===========================================
 * 詳細情報表示パターン Composable テンプレート
 * ===========================================
 * 
 * このテンプレートは、単一エンティティの詳細情報取得パターンのComposableを提供します。
 * 
 * 【適用対象】
 * - 単一エンティティの詳細情報取得
 * - 計算値や変換値の提供
 * - 読み取り専用データの管理
 * - 存在判定とデフォルト値の処理
 * 
 * 【参考実装】
 * - useGetListingInformationByCustomerIdentificationId: 上場情報取得
 * - useGetBusinessOverviewByCustomerIdentificationId: 事業概要取得
 * - useGetYourEntityByCustomerIdentificationId: 取引経緯取得
 * 
 * 【使用方法】
 * 1. TODOコメントに従って、エンティティ固有の情報に置き換える
 * 2. API呼び出し処理を実装する
 * 3. 必要に応じて計算値や変換処理を追加する
 */

import { defaultInstance } from '@hox/base/src/utils/shared/zodDefaultInstance'
// TODO: 取り扱うエンティティのスキーマをインポート
import { yourEntitySchema } from '../../constants/domain/entities/yourEntity'
// TODO: API Client をインポート
import { 
  // TODO: 実装されている関数を確認し、適切なAPI関数名に置き換える
  useGetYourEntityByCustomerIdentificationId as useGetYourEntityByCustomerIdentificationIdAPI,
} from '../../apiclient/yourEntity'

/**
 * エンティティ詳細情報取得 Composable
 * @param customerIdentificationId 顧客識別ID (TODO: 識別子名をエンティティに応じて変更)
 * @returns エンティティ詳細情報関連の状態と操作関数
 */
export const useGetYourEntityByCustomerIdentificationId(
  customerIdentificationId: MaybeRefOrGetter<string>,
) => {
  /** 取得データ */
  const fetchedYourEntity = ref(
    defaultInstance<
      typeof yourEntitySchema
    >(yourEntitySchema),
  )

  /** データ取得用ID */
  const paramId = computed(() => toValue(customerIdentificationId))
  const { data, inProgress, executeWithResult } =
    useGetYourEntityByCustomerIdentificationIdAPI(paramId)

  /**
   * エンティティ詳細情報を取得する
   * @returns Promise<void>
   */
  const getYourEntity = async (): Promise<void> => {
    await executeWithResult()
    fetchedYourEntity.value = { ...data.value }
  }
  /** 既にデータが存在するか */
  const hasYourEntity = computed(
    () => !!fetchedYourEntity.value.id,
  )

  watch(
    () => paramId.value,
    async () => await getYourEntity(),
  )

  onMounted(async () => {
    await getYourEntity()
  })

  // ========================================
  // 戻り値
  // ========================================

  return {
    fetchedYourEntity,
    hasYourEntity,
    inProgressFind: inProgress,
    getYourEntity,
  }
}

/**
 * ===========================================
 * 使用例
 * ===========================================
 * 
 * ```typescript
 * const props = defineProps<{
 *   customerIdentificationId: string
 * }>()
 * // コンポーネント内での使用
 * const customerIdentificationId = computed(() => props.customerIdentificationId)
 * 
 * const {
 *   fetchedYourEntity,
 *   hasYourEntity,
 *   inProgressFind,
 *   getYourEntity,
 * } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)
 * 
 * // 初期データ取得
 * onMounted(async () => {
 *   await getYourEntity()
 * })
 * 
 * ```
 */
