<#
.SYNOPSIS
    .ソリューションファイルをもとに、新しいソリューションファイルを作成します。
.PARAMETER source
    元となるソリューションファイルの名を指定します。
.PARAMETER output
    出力するソリューションファイル名を指定します。
.PARAMETER groups
    含めるプロジェクトのグループ名を指定します。指定しない場合は全てのプロジェクトが対象になります。大文字小文字は区別されません。
.PARAMETER projects
    含めるプロジェクト名を指定します。指定しない場合は全てのプロジェクトが対象になります。大文字小文字は区別されません。
.PARAMETER excludes
    除外するプロジェクト名を指定します。指定しない場合は除外しません。大文字小文字は区別されません。
.PARAMETER withTest
    テストプロジェクトを含めるかどうかを指定します。指定しない場合はテストプロジェクトは含まれません。
#>

param(
    [Alias("s")]
    [string]$sln,
    [Alias("o")]
    [string]$output,
    [Alias("p")]
    [string[]]$projects,
    [<PERSON>as("g")]
    [string[]]$groups,
    [<PERSON><PERSON>("e")]
    [string[]]$excludes,
    [<PERSON><PERSON>("wt")]
    [switch]$withTest = $false
)

# スクリプトのディレクトリ
$scriptPath = $PSScriptRoot

#####
# 対象のプロジェクトを取得
#####

# $source が指定されている場合は、絶対パスに変換します。指定されていない場合は、 $scriptPath にある最初に見つかった sln ファイルを利用します。
if ($sln) {
    $slnFile = (Convert-Path $sln)
}
else {
    $slnFile = Get-ChildItem -Path $scriptPath -Filter "*.sln" | Select-Object -First 1 | ForEach-Object { $_.FullName }
}
if (-not $slnFile) {
    Write-Error "Solution file not found: $slnFile"
    exit 1
}

Write-Host "Solution file: $slnFile"
$slnDir = Split-Path -Path $slnFile

# ファイルの内容を読み取り、.csproj ファイルを持つプロジェクト行をフィルタリング
$projectInfo = Get-Content $slnFile | Where-Object { $_ -match 'Project\("\{[A-F0-9\-]*\}"\) = "(.+)"( *, *)"(.+\.csproj)"( *, *)"\{[A-F0-9\-]*\}"' } | ForEach-Object { [PSCustomObject]@{ 'Name' = $matches[1]; 'File' = $matches[3] } }

# withTest が false の場合は、テストプロジェクトを除外します。
if (-not $withTest) {
    $projectInfo = $projectInfo | Where-Object { $_.Name -notmatch "\.Tests$" }
}

# プロジェクト名をカンマ区切りで表示します。
Write-Host "Projects: $($projectInfo.Name -join ', ')"

$targetProjects = @()

# groups および projects が指定されている場合は、指定されたプロジェクトを追加します。そうでない場合は、全てのプロジェクトを追加します。
if ($groups) {
    Write-Host "Group: $groups"
    foreach ($group in $groups) {
        $targetProjects += $projectInfo | Where-Object { $_.File -match "^$group" }
    }
}
if ($projects) {
    foreach ($project in $projects) {
        $targetProjects += $projectInfo | Where-Object { $_.Name -eq $project }
    }
}
if (-not $groups -and -not $projects) {
    $targetProjects = $projectInfo
}

# 同じ名前のプロジェクトを除外します。
$targetProjects = $targetProjects | Select-Object -Unique -Property Name, File

# excludes が指定されている場合は、指定されたプロジェクトを除外します
if ($excludes) {
    foreach ($exclude in $excludes) {
        $targetProjects = $targetProjects | Where-Object { $_.Name -ne $exclude }
    }
}

# プロジェクト名をカンマ区切りで表示します。
Write-Host "Targets : $($targetProjects.Name -join ', ')"

# name が指定されている場合は、絶対パスに変換します。指定されていない場合は、 $scriptPath にある最初に見つかった sln ファイルを利用します。
$newSlnName = "new"
$newSlnDir = $slnDir

if ($output) {
    # $output が指定されている場合は、絶対パスに変換します。
    $newSlnFile = [System.IO.Path]::GetFullPath($output, $slnDir)
    # $newSlnFile = (Convert-Path $output)
    $newSlnName = (Split-Path -Path $newSlnFile -Leaf).Replace(".sln", "")
    $newSlnDir = Split-Path -Path $newSlnFile
}
else {
    $newSlnFile = Join-Path -Path $slnDir -ChildPath "new.sln"
}

# 新しいソリューションファイルを作成します。
& dotnet new sln --name:"$newSlnName" --output:"$newSlnDir" --force

# プロジェクトを追加します。
foreach ($project in $targetProjects) {
    & dotnet sln "$newSlnFile" add "$slnDir\$($project.File)"
}

Write-Host "Create new solution file: $newSlnFile"
