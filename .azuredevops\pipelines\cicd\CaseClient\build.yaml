parameters:
  rootDirectory: $(System.DefaultWorkingDirectory)/sources/frontends/CaseClient

jobs:
  - job: CaseClientAppBuild
    pool:
      vmImage: $(vmImageName)
    steps:
    - checkout: self
      displayName: 'checkout'
      fetchDepth: -1
      clean: true
      submodules: true

    # - task: UseDotNet@2
    #   displayName: 'Install .Net 8 SDK'
    #   inputs:
    #     packageType: 'sdk'
    #     version: '8.x'
    #     includePreviewVersions: true

    # - task: DotNetCoreCLI@2
    #   displayName: 'dotnet tool restore'
    #   inputs:
    #     command: 'custom'
    #     custom: 'tool'
    #     arguments: 'restore'

    - task: NodeTool@0
      displayName: 'Install Node.js'
      condition: succeeded()
      inputs:
        versionSpec: '20.x'

    - task: npmAuthenticate@0
      inputs:
        workingFile: '${{ parameters.rootDirectory }}/.npmrc'
      displayName: 'authenticate to Azure Artifacts feed'
      
    - script: npm ci
      displayName: 'Install node modules'
      condition: succeeded()
      workingDirectory: ${{ parameters.rootDirectory }}/

    - script: npm run build
      displayName: 'Generate static web contents'
      condition: succeeded()
      workingDirectory: ${{ parameters.rootDirectory }}/

    - script: npm run lint
      displayName: 'Run Code Analysis'
      condition: succeeded()
      workingDirectory: ${{ parameters.rootDirectory }}/

    - script: npm run test
      displayName: 'execute vitest'
      condition: succeeded()
      workingDirectory: ${{ parameters.rootDirectory }}/

    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: ${{ parameters.rootDirectory }}/.output
        includeRootFolder: true
        archiveType: zip
        archiveFile: $(Build.ArtifactStagingDirectory)/CaseClient.zip
        replaceExistingArchive: true

    - publish: $(Build.ArtifactStagingDirectory)/CaseClient.zip
      artifact: CaseClient

## production のビルド時にアーティファクトが取得できるようにビルドタグを追加します。
  - job: CaseClientAttacheBuildTag
    dependsOn:
      - CaseClientAppBuild
    condition: succeeded('CaseClientAppBuild')
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: Bash@3
      displayName: Add build commit id as build tag
      inputs:
        targetType: inline
        script: |
          BASE64PATSTRING=`echo -n :$(Azure.DevOps.PAT)|base64`
          curl -isSL \
            -H "Authorization: Basic ${BASE64PATSTRING}" \
            -H "Content-length: 0" \
            -X PUT \
            "$(System.CollectionUri)/$(System.TeamProject)/_apis/build/builds/$(Build.BuildId)/tags/$(Build.SourceVersion)?api-version=5.1"
          echo ""
          echo "Result:$?"