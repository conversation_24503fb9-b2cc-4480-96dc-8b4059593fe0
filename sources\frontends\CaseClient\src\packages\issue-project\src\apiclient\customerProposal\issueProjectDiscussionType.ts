import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type { PurposeTypeOfTemplate } from '@ibp/issue-project/src/constants/domain/purposeTypeOfTemplate'
import type { DiscussionPurposeType } from '@ibp/issue-project/src/constants/domain/discussionPurpose'

// =====================================================================================================================
// APIクライアントの定義(idで取得)
// =====================================================================================================================

// GetIssueProjectDiscussionTypeResultの型定義
export type GetIssueProjectDiscussionTypeResult = {
  value: string
  text: string
  isApprovalDiscussion: boolean
  isInactive: boolean
  purposeTypeOfTemplate: PurposeTypeOfTemplate
  version: string
}

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProjectDiscussionType(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetIssueProjectDiscussionTypeResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/issueprojectdiscussiontype/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(更新)
// =====================================================================================================================

// UpdateIssueProjectDiscussionTypeCommandのスキーマ
export const updateIssueProjectDiscussionTypeCommandSchema = z.object({
  id: z.string(),
  isApprovalDiscussion: z.boolean(),
  isInactive: z.boolean(),
  purposeTypeOfTemplate: z.string(),
  version: z.string(),
})

// 型定義
export type UpdateIssueProjectDiscussionTypeCommand = z.infer<
  typeof updateIssueProjectDiscussionTypeCommandSchema
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutIssueProjectDiscussionType(
  body: Ref<UpdateIssueProjectDiscussionTypeCommand>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetIssueProjectDiscussionTypeResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussiontype',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(batchUpdateOrder)
// =====================================================================================================================

export const issueProjectDiscussionTypeOrderDataSchema = z.object({
  id: z.string(),
  version: z.string(),
  order: z.number().nullish(),
})

export const issueProjectDiscussionTypeOrderDataListSchema = z.array(
  issueProjectDiscussionTypeOrderDataSchema,
)

// 保存用のデータのスキーマを定義します。
export const issueProjectDiscussionTypeOrderSchemaForSave = z.object({
  specifiedOrders: issueProjectDiscussionTypeOrderDataListSchema,
})
// 保存用の型を作成します。
export type BatchUpdateOrderIssueProjectDiscussionTypeCommand = z.infer<
  typeof issueProjectDiscussionTypeOrderSchemaForSave
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useBatchUpdateOrderIssueProjectDiscussionType(
  body: Ref<BatchUpdateOrderIssueProjectDiscussionTypeCommand>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussiontype/update-order',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(getDiscussionPurposeTypes)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetDiscussionPurposeTypes() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Array<DiscussionPurposeType>>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussiontype/discussion-purpose-type',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(getOptions)
// =====================================================================================================================

// 個々のIssueProjectDiscussionTypeの型定義
export type IssueProjectDiscussionTypeItem = {
  value: string
  text: string
  isApprovalDiscussion: boolean
  isInactive: boolean
  order: number | null
  purposeTypeOfTemplate: PurposeTypeOfTemplate
  version: string
}

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProjectDiscussionOptions() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionTypeItem[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussiontype/get-all',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(getOptionsIncludeInactive)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetOptionsIncludeInactive() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionTypeItem[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussiontype/get-all-include-inactive',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}
