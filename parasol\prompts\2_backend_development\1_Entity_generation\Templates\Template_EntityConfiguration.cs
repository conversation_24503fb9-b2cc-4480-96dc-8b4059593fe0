using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EmployeeService_tmpl.Domain.Entities;

namespace EmployeeService_tmpl.Infrastructure.Persistence.Configurations;

public class EmployeeConfiguration : IEntityTypeConfiguration<Employee>
{
    public void Configure(EntityTypeBuilder<Employee> builder)
    {
        builder.Property(u => u.Id)
            .HasColumnName("employee_id")
            .HasMaxLength(7);
        // ApplyRecommendModelSettings で 26 で設定されていますが、 00000000 のフォーマットなので 7 で上書きします。

        builder.Property(u => u.NameKanji)
            .IsRequired()
            .HasMaxLength(30);

        builder.Property(u => u.NameKana)
            .IsRequired()
            .HasMaxLength(30);

        builder.Property(u => u.Address)
            .IsRequired()
            .HasMaxLength(100);

        // 列挙型のDBへのマッピング例
        builder.Property(u => u.Status)
            .HasConversion(v => v.ToString(), v => Enum.Parse<EmployeeStatus>(v!));
    }
}