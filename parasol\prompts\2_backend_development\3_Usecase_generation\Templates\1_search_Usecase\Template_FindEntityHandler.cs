using MediatR;
using Nut.Results;
using SampleService.Domain;
using Shared.Application;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.FindEntity;

public class FindEntityHandler(IRepositoryFactory repositoryFactory) : IRequestHandler<FindEntityQuery, Result<PaginatedResult<FindEntityResult>>>
{
    public Task<Result<PaginatedResult<FindEntityResult>>> <PERSON>le(FindEntityQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request);

        // TODO: 利用するリポジトリに置き換えてください。
        var repository = repositoryFactory.GetRepository<Domain.Entities.Entity>();

        // 検索を行います。
        var spec = new FindEntitySpecification(request);
        var result = repository.FindWithPaginationAsync(
            spec,
            request.ExtractSafePaginationOption(sort:
            [
                // TODO: デフォルトのソート条件を設定します。
                new Sort { Target = nameof(Domain.Entities.Entity.Id) },
            ]));

        // 検索結果をマップして返します。
        return result.Map(pr =>
            pr.Map(v => new FindEntityResult(
                // TODO: 必要な結果の値を設定してください。
                v.Id, v.Name, v.PeriodFrom, v.PeriodTo, v.Amount, v.IsAssumption, v.Version)));
    }
}
