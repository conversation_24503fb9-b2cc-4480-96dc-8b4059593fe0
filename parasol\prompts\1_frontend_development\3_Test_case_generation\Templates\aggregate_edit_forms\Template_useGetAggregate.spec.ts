/**
 * ===========================================
 * アグリゲート取得コンポーザブル テストテンプレート
 * ===========================================
 * 
 * このテンプレートは、アグリゲート取得用composableのユニットテストです。
 * 
 * 【テスト観点】
 * - 取得API呼び出しの正常系・異常系テスト
 * - パラメータに応じた取得処理のテスト
 * - 状態管理のテスト
 * - エラーハンドリングのテスト
 * 
 * 【使用方法】
 * 1. TODOコメントに従って、エンティティ固有の情報に置き換える
 * 2. 型名とインポートパスを実際のエンティティに変更する
 * 3. 関数名と変数名をエンティティに応じて変更する
 * 4. テストデータを実際のエンティティ構造に合わせて調整する
 */

import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { ref } from 'vue'
import { patchOFetchOnMsw } from '@hox/base/test/testSupport/api'

// TODO: テスト対象composableをインポート - 実際のパスに変更
import { useGetYourAggregate } from '@your-module/src/composables/yourAggregate/useGetYourAggregate'

// TODO: 型定義をインポート - 実際のパスに変更
import type { YourAggregateType } from '@your-module/src/constants/domain/entities/yourAggregate'

// テスト用モックサーバー
const server = setupServer()

function startServer() {
  server.listen({ onUnhandledRequest: 'error' })
  patchOFetchOnMsw()
}

// TODO: 取得API用のモック関数 - エンドポイントを実際のものに変更
function setupGetYourAggregateAPIMock(caseId: string | undefined, shouldSucceed: boolean = true) {
  // TODO: 成功時のレスポンスデータを実際のエンティティ構造に変更
  const successResponse: YourAggregateType = {
    id: 'test-aggregate-id',
    caseId: caseId || 'test-case-id',
    customerIdentificationId: '00000000-1111-2222-3333-444444444444',
    conditionRegistrationStatus: 'Registered',
    version: 1,
    // TODO: エンティティ固有のフィールドを追加
    name: 'Test Aggregate',
    description: 'Test Description',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }

  const notFoundResponse = {
    type: '/not-found',
    title: 'Not Found',
    status: 404,
    detail: 'アグリゲートが見つかりません',
  }

  const serverHandler = vi.fn(() =>
    shouldSucceed
      ? HttpResponse.json(successResponse)
      : HttpResponse.json(notFoundResponse, { status: 404 })
  )

  // TODO: APIエンドポイントを実際のものに変更
  server.use(http.get('/your-module/v1.0/your-aggregates/by-case/:caseId', serverHandler))

  return {
    mockHandler: serverHandler,
    successResponse,
    notFoundResponse,
  }
}

// TODO: テストスイート名をエンティティに応じて変更
describe('useGetYourAggregate', () => {
  beforeEach(() => {
    clearNuxtState()
    startServer()
  })

  afterEach(() => {
    vi.restoreAllMocks()
    server.resetHandlers()
    server.close()
  })

  describe('fetchYourAggregate', () => {
    test('正常系: ケースIDによるアグリゲートの取得が成功する', async () => {
      const caseId = ref('test-case-id')
      const { mockHandler, successResponse } = setupGetYourAggregateAPIMock(caseId.value, true)

      // TODO: composable関数名を実際のものに変更
      const {
        fetchYourAggregate,
        fetchedYourAggregate,
        inProgress,
        hasFetchedYourAggregate,
        fetchedYourAggregateId,
      } = useGetYourAggregate(caseId)

      // 取得処理を実行
      const result = await fetchYourAggregate()

      // アサーション
      expect(mockHandler).toHaveBeenCalledTimes(1)
      expect(result).toEqual(successResponse)
      expect(fetchedYourAggregate.value).toEqual(successResponse)
      expect(hasFetchedYourAggregate.value).toBe(true)
      expect(fetchedYourAggregateId.value).toBe(successResponse.id)
      expect(inProgress.value).toBe(false)
    })

    test('正常系: 異なるケースIDでの取得', async () => {
      const caseId = ref('different-case-id')
      const { mockHandler, successResponse } = setupGetYourAggregateAPIMock(caseId.value, true)

      const {
        fetchYourAggregate,
        fetchedYourAggregate,
      } = useGetYourAggregate(caseId)

      // 取得処理を実行
      const result = await fetchYourAggregate()

      // アサーション
      expect(mockHandler).toHaveBeenCalledTimes(1)
      expect(result.caseId).toBe('different-case-id')
      expect(fetchedYourAggregate.value?.caseId).toBe('different-case-id')
    })

    test('異常系: 存在しないケースIDでの取得', async () => {
      const caseId = ref('non-existent-case-id')
      const { mockHandler } = setupGetYourAggregateAPIMock(caseId.value, false)

      const {
        fetchYourAggregate,
        fetchedYourAggregate,
        hasFetchedYourAggregate,
      } = useGetYourAggregate(caseId)

      // 取得処理を実行してエラーを期待
      await expect(fetchYourAggregate()).rejects.toThrow()

      // アサーション
      expect(mockHandler).toHaveBeenCalledTimes(1)
      expect(fetchedYourAggregate.value).toBeUndefined()
      expect(hasFetchedYourAggregate.value).toBe(false)
    })

    test('異常系: APIエラーが発生する', async () => {
      const caseId = ref('test-case-id')
      
      // サーバーエラーのモック
      const serverHandler = vi.fn(() =>
        HttpResponse.json({ error: 'Internal Server Error' }, { status: 500 })
      )
      server.use(http.get('/your-module/v1.0/your-aggregates/by-case/:caseId', serverHandler))

      const {
        fetchYourAggregate,
        fetchedYourAggregate,
      } = useGetYourAggregate(caseId)

      // 取得処理を実行してエラーを期待
      await expect(fetchYourAggregate()).rejects.toThrow()

      // アサーション
      expect(serverHandler).toHaveBeenCalledTimes(1)
      expect(fetchedYourAggregate.value).toBeUndefined()
    })
  })

  describe('clearFetchedYourAggregate', () => {
    test('正常系: 取得したデータのクリアが成功する', async () => {
      const caseId = ref('test-case-id')
      setupGetYourAggregateAPIMock(caseId.value, true)

      const {
        fetchYourAggregate,
        fetchedYourAggregate,
        clearFetchedYourAggregate,
        hasFetchedYourAggregate,
        fetchedYourAggregateId,
      } = useGetYourAggregate(caseId)

      // まずデータを取得
      await fetchYourAggregate()
      expect(fetchedYourAggregate.value).toBeDefined()
      expect(hasFetchedYourAggregate.value).toBe(true)

      // データをクリア
      clearFetchedYourAggregate()

      // アサーション
      expect(fetchedYourAggregate.value).toBeUndefined()
      expect(hasFetchedYourAggregate.value).toBe(false)
      expect(fetchedYourAggregateId.value).toBeUndefined()
    })
  })

  describe('caseIdの変更に対する動作', () => {
    test('正常系: caseIdが変更された場合の状態管理', async () => {
      const caseId = ref('initial-case-id')
      setupGetYourAggregateAPIMock('initial-case-id', true)
      
      // 新しいケースIDでも正常なレスポンスを設定
      const newCaseHandler = vi.fn(() => HttpResponse.json({
        id: 'new-aggregate-id',
        caseId: 'new-case-id',
        customerIdentificationId: '00000000-1111-2222-3333-444444444444',
        name: 'New Aggregate',
        description: 'New Description',
        version: 1,
        conditionRegistrationStatus: 'Registered',
      }))
      server.use(http.get('/your-module/v1.0/your-aggregates/by-case/new-case-id', newCaseHandler))

      const {
        fetchYourAggregate,
        fetchedYourAggregate,
      } = useGetYourAggregate(caseId)

      // 初回取得
      await fetchYourAggregate()
      expect(fetchedYourAggregate.value?.caseId).toBe('initial-case-id')

      // ケースIDを変更
      caseId.value = 'new-case-id'

      // 再取得
      await fetchYourAggregate()
      expect(fetchedYourAggregate.value?.caseId).toBe('new-case-id')
      expect(newCaseHandler).toHaveBeenCalledTimes(1)
    })
  })

  describe('進行状況管理', () => {
    test('API実行中はinProgressがtrueになる', async () => {
      const caseId = ref('test-case-id')
      setupGetYourAggregateAPIMock(caseId.value, true)

      const {
        fetchYourAggregate,
        inProgress,
      } = useGetYourAggregate(caseId)

      // 非同期で実行開始
      const fetchPromise = fetchYourAggregate()
      
      // API実行開始後すぐに進行状況を確認
      await nextTick()
      // Note: 実際のテストでは、APIの応答遅延をモックして確認する

      // 完了まで待機
      await fetchPromise

      // 完了後は進行状況がfalseになることを確認
      expect(inProgress.value).toBe(false)
    })
  })

  describe('computed値の動作', () => {
    test('hasFetchedYourAggregateの動作確認', async () => {
      const caseId = ref('test-case-id')
      setupGetYourAggregateAPIMock(caseId.value, true)

      const {
        fetchYourAggregate,
        hasFetchedYourAggregate,
        clearFetchedYourAggregate,
      } = useGetYourAggregate(caseId)

      // 初期状態
      expect(hasFetchedYourAggregate.value).toBe(false)

      // データ取得後
      await fetchYourAggregate()
      expect(hasFetchedYourAggregate.value).toBe(true)

      // データクリア後
      clearFetchedYourAggregate()
      expect(hasFetchedYourAggregate.value).toBe(false)
    })

    test('fetchedYourAggregateIdの動作確認', async () => {
      const caseId = ref('test-case-id')
      const { successResponse } = setupGetYourAggregateAPIMock(caseId.value, true)

      const {
        fetchYourAggregate,
        fetchedYourAggregateId,
        clearFetchedYourAggregate,
      } = useGetYourAggregate(caseId)

      // 初期状態
      expect(fetchedYourAggregateId.value).toBeUndefined()

      // データ取得後
      await fetchYourAggregate()
      expect(fetchedYourAggregateId.value).toBe(successResponse.id)

      // データクリア後
      clearFetchedYourAggregate()
      expect(fetchedYourAggregateId.value).toBeUndefined()
    })
  })

  describe('エラーハンドリング', () => {
    test('コンソールログにエラーが出力される', async () => {
      const caseId = ref('test-case-id')
      setupGetYourAggregateAPIMock(caseId.value, false)

      // console.errorをモック
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const { fetchYourAggregate } = useGetYourAggregate(caseId)

      // エラーをキャッチして処理継続
      try {
        await fetchYourAggregate()
      } catch (error) {
        // エラーが発生することを期待
      }

      // コンソールログの確認
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'アグリゲートの取得に失敗しました:',
        expect.any(Error)
      )

      consoleErrorSpy.mockRestore()
    })
  })
})

/*
===========================================
テンプレート使用時の変更点チェックリスト
===========================================

【必須変更事項】
□ TODO: composableのインポートパスを実際のものに変更
□ TODO: 型定義のインポートパスを実際のものに変更
□ TODO: APIエンドポイントを実際のものに変更
□ TODO: テストデータを実際のエンティティ構造に変更
□ TODO: 関数名と変数名をエンティティに応じて変更
□ TODO: テストスイート名を実際のcomposable名に変更

【オプション変更事項】
□ TODO: エンティティ固有のテストケースの追加
□ TODO: 取得条件（パラメータ）のテスト追加
□ TODO: キャッシュ戦略のテスト追加
□ TODO: データの前処理ロジックのテスト追加
□ TODO: パフォーマンステストの追加
*/
