# @ibp/base定義

## 概要

本ドキュメントは、ibp-frontend-layer-baseで定義されているIBP（Investment Banking Platform）フロントエンドアーキテクチャの設計思想、構成要素、実装ガイドラインを説明します。このアーキテクチャは、@hox/baseを基盤としたNuxt Layerとして実装され、IBP固有のビジネス機能とコンポーネントを提供します。

## アーキテクチャ概要

### 基本設計思想

- **Layered Architecture**: Nuxt Layer機能を活用したモジュラー設計
- **Base Extension**: @hox/baseパッケージの機能拡張
- **Business Domain Focus**: IBP業務領域に特化した機能提供
- **Component Reusability**: IBP内での再利用可能なコンポーネント設計
- **Type Safety**: TypeScriptによる型安全性の確保

### 技術スタック

| 技術領域 | 採用技術 |
|---------|----------|
| Base Framework | @hox/base (Nuxt 3.12+) |
| Runtime | Node.js 20.11.0 (Volta管理) |
| UI Library | Vuetify 3.6+ |
| Authentication | Azure MSAL |
| Testing | Vitest + Vue Test Utils |
| Build Tool | Vite |
| Linting | ESLint + Prettier + Stylelint |
| Package Manager | npm |

### 依存関係

```mermaid
graph TD
    A[ibp-frontend-layer-base] --> B[hox/base v2.8.4]
    B --> C[Nuxt 3.12+]
    B --> D[Vuetify 3.6+]
    B --> E[Azure MSAL]
    A --> F[IBP固有コンポーネント]
    A --> G[IBPビジネスロジック]
    A --> H[IBP固有API Client]
```

## ディレクトリ構造

```
src/
├── @types/            # TypeScript型定義
├── apiclient/         # IBP API通信関連
├── assets/            # IBP固有アセット
├── components/        # IBP固有UIコンポーネント
│   ├── Icon/          # アイコンコンポーネント
│   ├── Layout/        # レイアウトコンポーネント
│   ├── Menu/          # メニューコンポーネント
│   ├── Notification/  # 通知コンポーネント
│   └── TimeLine/      # タイムラインコンポーネント
├── composables/       # IBP固有Composables
│   ├── accessLog/     # アクセスログ管理
│   ├── accessManagement/ # アクセス管理
│   ├── accessReason/  # アクセス理由管理
│   ├── customer/      # 顧客管理
│   ├── customerFixedInformation/ # 顧客固定情報
│   ├── layout/        # レイアウト管理
│   ├── notification/  # 通知機能
│   └── shared/        # 共有機能
├── constants/         # 定数定義
│   └── domain/        # ドメイン定数
├── layouts/           # レイアウトコンポーネント
├── middleware/        # IBP固有ミドルウェア
├── pages/             # ページコンポーネント
├── plugins/           # IBP固有プラグイン
├── public/            # 公開ファイル
└── utils/             # IBP固有ユーティリティ
    └── shared/        # 共有ユーティリティ
```

## コンポーネント定義

components配下に定義されているコンポーネントを表形式で列挙し、概要を説明します。

### Icon Components

| コンポーネント名 | 概要 | 主な機能 |
|-----------------|------|----------|
| BellIcon | 通知ベルアイコン | 通知機能に使用するSVGアイコン |

### Layout Components

| コンポーネント名 | 概要 | 主な機能 |
|-----------------|------|----------|
| BaseLayout | IBPベースレイアウト | ヘッダー・メニュー・パンくずナビゲーション・通知表示 |
| BasicCard | 基本カード | 統一されたカードレイアウト |
| CustomerHeader | 顧客ヘッダー | 顧客情報表示ヘッダー |
| CustomerStaffMenu | 顧客担当者メニュー | 担当者選択・表示機能 |
| EditableCustomerStaffMenu | 編集可能担当者メニュー | 担当者編集機能付きメニュー |
| CustomIcon | カスタムアイコン | IBP固有アイコン表示 |
| InformationIcon | 情報アイコン | 情報表示用アイコン |
| AppIconBtn | アイコンボタン | アイコン付きボタンコンポーネント |
| SearchCustomerStaffUserDialog | 担当者検索ダイアログ | 担当者ユーザー検索機能 |

### Menu Components

| コンポーネント名 | 概要 | 主な機能 |
|-----------------|------|----------|
| MenuCards | メニューカード | IBPアプリケーションメニュー表示 |

### Notification Components

| コンポーネント名 | 概要 | 主な機能 |
|-----------------|------|----------|
| NotificationCircle | 通知サークル | 未読通知数表示、通知状態の視覚的表現 |

### TimeLine Components

| コンポーネント名 | 概要 | 主な機能 |
|-----------------|------|----------|
| TimeLine | タイムライン | 業務プロセスタイムライン、顧客対応履歴・案件進捗表示 |

## 共通処理ライブラリ

composables配下に定義されているロジックを表形式で列挙し、概要を説明します。

### 基本Composables

| Composable名 | 分類 | 機能 | 概要 |
|-------------|------|------|------|
| useUser | ユーザー管理 | IBPユーザー情報管理 | ログインユーザー情報の取得・管理、ユーザー一覧取得、担当者・チーム選択オプション生成 |
| useTeam | チーム管理 | チーム・部門管理 | 組織階層とチーム情報の管理 |
| useNextPath | パス管理 | 業務フロー内パス管理 | 次画面遷移のパス制御 |

### アクセス管理関連

| Composable名 | ファイル名 | 機能 | 概要 |
|-------------|----------|------|------|
| useGetAccessManager | useGetAccessManager.ts | アクセス確認責任者管理 | アクセス確認責任者情報の取得・管理 |
| useGetAccessRestrictedTarget | useGetAccessRestrictedTarget.ts | アクセス制限対象管理 | アクセス制限対象情報の取得 |
| useGetAccessRestrictedTargetGroup | useGetAccessRestrictedTargetGroup.ts | アクセス制限グループ管理 | アクセス制限対象グループ情報の取得 |
| useGetCustomerIdentificationIdsFromTodaysAccessReason | useGetCustomerIdentificationIdsFromTodaysAccessReason.ts | 当日アクセス理由管理 | 当日のアクセス理由に基づく顧客識別ID取得 |

### アクセスログ関連

| Composable名 | ファイル名 | 機能 | 概要 |
|-------------|----------|------|------|
| useCreateAccessLog | useCreateAccessLog.ts | アクセスログ作成 | 業務アクセス履歴の記録、監査ログ対応 |

### アクセス理由関連

| Composable名 | ファイル名 | 機能 | 概要 |
|-------------|----------|------|------|
| useCreateAccessReason | useCreateAccessReason.ts | アクセス理由作成 | 業務アクセス理由の記録、コンプライアンス対応 |

### 顧客管理関連

| Composable名 | ファイル名 | 機能 | 概要 |
|-------------|----------|------|------|
| useGetCustomer | useGetCustomer.ts | 顧客情報取得 | 顧客データの取得・更新、顧客検索機能 |

### 顧客固定情報関連

| Composable名 | ファイル名 | 機能 | 概要 |
|-------------|----------|------|------|
| useCreateCustomerStaff | useCreateCustomerStaff.ts | 顧客担当者作成 | 顧客担当者情報の新規作成 |
| useDeleteCustomerStaff | useDeleteCustomerStaff.ts | 顧客担当者削除 | 顧客担当者情報の削除 |
| useGetCustomerStaff | useGetCustomerStaff.ts | 顧客担当者取得 | 顧客担当者情報の取得・管理 |
| useSearchCustomerStaffUser | useSearchCustomerStaffUser.ts | 担当者ユーザー検索 | 担当者ユーザーの検索機能 |
| useUpdateCustomerStaff | useUpdateCustomerStaff.ts | 顧客担当者更新 | 顧客担当者情報の更新 |

### レイアウト関連

| Composable名 | ファイル名 | 機能 | 概要 |
|-------------|----------|------|------|
| useGetCustomerLayout | useGetCustomerLayout.ts | 顧客レイアウト管理 | 顧客関連画面のレイアウト管理 |

### 通知関連

| Composable名 | ファイル名 | 機能 | 概要 |
|-------------|----------|------|------|
| useGetUnreadNotificationCount | useGetUnreadNotificationCount.ts | 未読通知数取得 | 未読通知数の取得・管理 |

### 共有機能

| Composable名 | ファイル名 | 機能 | 概要 |
|-------------|----------|------|------|
| useConfirmDiscardEditedDialog | useConfirmDiscardEditedDialog.ts | 編集破棄確認 | 編集内容破棄時の確認ダイアログ |
| useGetCustomerIdentificationId | useGetCustomerIdentificationId.ts | 顧客識別ID取得 | ルートから顧客識別IDを取得 |
| useOneDayPersistState | useOneDayPersistState.ts | 一日限定状態管理 | 1日限定での状態永続化 |
| useSearchWithCriteriaHistory | useSearchWithCriteriaHistory.ts | 検索条件履歴管理 | 検索条件の履歴管理機能 |

## 定数

constants配下に定義されている定数を表形式で列挙し、概要を説明します。

| ファイル名 | 概要 | 主な定数 |
|-----------|------|----------|
| path.ts | IBPアプリケーション間の連携パス定義 | 各IBPアプリケーションのURL、顧客関連パス |
| layout.ts | レイアウト定数定義 | レイアウト関連の設定値 |

### ドメイン定数

| ファイル名 | 概要 | 主な定数 |
|-----------|------|----------|
| domain/accessRestrictedTarget.ts | アクセス制限対象定数 | アクセス制限対象の分類・設定 |
| domain/customer.ts | 顧客関連定数 | 顧客分類・ステータス等の定数 |

## ユーティリティ関数

utils配下に定義されているユーティリティ関数を表形式で列挙し、概要を説明します。

### 共有ユーティリティ (shared/)

| ファイル名 | 機能 | 概要 | 主な用途 |
|-----------|------|------|----------|
| requestValueConverter.ts | リクエスト値変換 | APIリクエスト時の値変換処理 | データ形式の統一・変換 |

## プラグイン

pluginsディレクトリ配下に定義されているファイルについて表形式で列挙し、概要を説明します。

### IBP固有プラグイン

| 順序 | ファイル名 | 機能 | 概要 |
|------|----------|------|------|
| 50 | initialize.ts | 初期化処理 | IBPアプリケーション初期化処理、ユーザー情報の事前取得、IBP固有の初期設定 |

## ミドルウェア

middlewareディレクトリ配下に定義されているミドルウェアについて表形式で列挙し、概要を説明します。

### グローバルミドルウェア

| ファイル名 | 機能 | 概要 |
|-----------|------|------|
| 10.fetchCustomer.global.ts | 顧客情報取得 | 顧客識別IDに基づく顧客情報・担当者情報の事前取得 |

### 個別ミドルウェア

| ファイル名 | 機能 | 概要 |
|-----------|------|------|
| addAccessLog.ts | アクセスログ出力 | ページアクセス時のログ記録ミドルウェア |
| checkAccessible.ts | アクセス可否チェック | アクセス制限対象への本日分アクセス確認 |
| checkNextPath.ts | 次画面パスチェック | 業務フロー内での次画面遷移制御 |

## API通信ライブラリ

apiclient配下に定義されているAPI通信関連の機能について説明します。IBP固有のAPI通信機能が実装されており、@hox/baseの機能を拡張して以下の領域をカバーしています：

### 主要API分類

| API分類 | 概要 | 主な機能 |
|---------|------|----------|
| user | ユーザー管理API | ユーザー情報の取得・管理 |
| accessLog | アクセスログAPI | アクセス履歴の記録・取得 |
| accessManagement | アクセス管理API | アクセス権限・制限の管理 |
| accessReason | アクセス理由API | アクセス理由の記録・管理 |
| customer | 顧客API | 顧客情報の取得・更新 |
| customerFixedInformation | 顧客固定情報API | 顧客担当者情報の管理 |
| layout | レイアウトAPI | 画面レイアウト設定の取得 |
| notification | 通知API | 通知情報の取得・管理 |

※ 各APIの詳細な型定義とエンドポイント設定は、@hox/baseのAPI通信機能を基盤として実装されています。

