import { z } from 'zod'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import type { Ref } from 'vue'

/**
 * 融資リース案件一覧の検索条件バリデーション
 */
export const useLoanAndLeaseValidation = (searchCondition: Ref<any>) => {
  // バリデーションスキーマ定義
  const { errorMessages, validate, validateItem } = useValidation(
    z.object({
      // CIF番号
      cifNumber: z.string().max(8, 'CIF番号は8桁以内で入力してください').nullish(),
      // 氏名
      customerName: z.string().nullish(),
      // 期日
      fromDate: z.date().nullish(),
      toDate: z.date().nullish(),
      // 金額
      fromAmount: z.number().nullish(),
      toAmount: z.number().nullish(),
      // 金利
      fromInterestRate: z.number().nullish().refine(
        (val) => {
          if (val === null || val === undefined) return true
          // 小数点第5位までかチェック（小数点以下の桁数が5桁以下）
          const decimalPlaces = val.toString().split('.')[1]?.length || 0
          return decimalPlaces <= 5
        },
        {
          message: '金利を小数点第5位までで入力してください',
        },
      ),
      toInterestRate: z.number().nullish().refine(
        (val) => {
          if (val === null || val === undefined) return true
          // 小数点第5位までかチェック（小数点以下の桁数が5桁以下）
          const decimalPlaces = val.toString().split('.')[1]?.length || 0
          return decimalPlaces <= 5
        },
        {
          message: '金利を小数点第5位までで入力してください',
        },
      ),
    }),
    searchCondition,
  )
  /**
   * 全てのバリデーションを実行
   * @returns バリデーション結果 (成功: true, 失敗: false)
   */
  const validateAll = async () => {
    const { success } = await validate()
    return success
  }

  return {
    errorMessages,
    validate,
    validateItem,
    validateAll,
  }
}
