# 画面コード生成

## 役割定義

あなたはフロントエンドアプリケーションを開発するベテランエンジニアです。  
指定された入力情報を元に、フロントエンドのプログラム実装を行います。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義：
    - `parasol\architecture\frontend_architecture_hoxbase.md`
    - `parasol\architecture\frontend_architecture_ibpbase.md`
- コンポーネント使用方法リファレンス：
    - `parasol\architecture\usage_sample_hoxbase.vue`
    - `parasol\architecture\usage_sample_ibpbase.vue`
- UIパターン定義：
    - `parasol\architecture\UI_pattern.md`
- プロジェクト設定定義：
    - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- ドメイン言語定義：`parasol\capability\**\ドメイン言語.md`
- ページ定義ファイル：`parasol\ui\pages\*.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

#### 検索画面用

- `parasol\prompts\1_frontend_development\2_UI_code_generation\page\Templates\Template_index.vue`

#### 登録画面用

- `parasol\prompts\1_frontend_development\2_UI_code_generation\page\Templates\Template_add.vue`

#### 編集画面用

- `parasol\prompts\1_frontend_development\2_UI_code_generation\page\Templates\Template_edit.vue`

#### 複数集約表示画面用

- `parasol\prompts\1_frontend_development\2_UI_code_generation\page\Templates\Template_multi.vue`

### 利用コード

- APIクライアント : 
    - `[APIクライアントディレクトリ]\[操作対象集約/エンティティ名].ts`
    - `[APIクライアントディレクトリ]\shared\*.ts`
- 関数 : 
    - `[関数ディレクトリ]\*.ts`
- 列挙型 : 
    - `[列挙型ディレクトリ]\*.ts`
- zodスキーマ定義 : 
    - `[zodスキーマディレクトリ]\[操作対象集約/エンティティ名].ts`
    - `[zodスキーマディレクトリ]\shared\*.ts`

## 出力定義

出力対象ファイルの出力先のディレクトリや出力ファイル名のフォーマットは以下に従ってください。  
プロジェクトの詳細なディレクトリ構成は`parasol\architecture\project_settings.md`を参照してください。

### ページファイル

- 出力先：
    - 対象画面で扱う集約が単一の場合 : `[画面ディレクトリ]\[操作対象集約名(ケバブケース)]\`
    - 対象画面で扱う集約が複数の場合 : `[画面ディレクトリ]\[画面名(ケバブケース)]\`
        - 画面名についてはページ定義内に記述されているページ名を英訳し、ケバブケースに変換してください。
        - 例）画面名が「ユーザー・グループ管理」の場合 : `[画面ディレクトリ]\user-group-management\`
- ファイル名：
    - 検索画面 : `index.vue`
    - 登録画面 : `add.vue`
    - 編集画面 : `edit.vue`
    - 複数集約表示画面 : `index.vue`

## 制約事項

以下の制約に従ってください。

### 禁止事項
- 定義ファイル、テンプレートファイル、当プロンプトファイルの編集は行わないでください。
- 指示していない内容の編集は行わないでください。
- 作業効率を重視した作業内容の省略は行わず、各作業を正しく丁寧に完了させてください。

### その他ルール
- 各フィールドについては、Vuetifyコンポーネントの直接使用を避け、@hox/baseまたは@ibp/baseで提供されるコンポーネントを使用してください。
- コンポーネントの使用方法は、コンポーネント使用方法リファレンスを参照してください。

## 指示詳細

### 情報収集

1. 指定されたページ定義ファイルを読み込ます。
    - 作成が必要なコンポーネントを特定します。
        - 特定したコンポーネントとそのコンポーネントで扱う集約を列挙してください。
    - 対象画面で定義されている画面項目とバリデーションルールを抽出します。
    - 対象画面で定義されているイベント処理を抽出します。

2. ドメイン言語ファイルを読み込み、必要なエンティティとフィールド情報を取得します。
    - 対象画面で取り扱う集約やエンティティの情報を列挙してください。
    - 対象の情報をドメイン言語から読み込みます。

3. アーキテクチャ定義とコンポーネント使用方法リファレンスを読み込み、利用可能なコンポーネントとその使用方法を把握します。

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成します。

2. 画面から利用するコンポーネントを作成します。
    - 以下の内容をコンポーネントごとに繰り返し作業してください。
        - 以下のプロンプトを参照し、コンポーネントの作成作業を行ってください。
            - コンポーネント生成プロンプト : `..\component\prompt.md`
        - コンポーネントごとに扱う集約が異なるため、作成先のディレクトリが異なる点に注意してください。
        - 時間がかかってもよいので、１つ１つのコンポーネントを正確に作成してください。
        - 対象コンポーネントの作業が完了したら、作業が必要な残りのコンポーネントを確認してください。
    - 作成対象のコンポーネント全ての対応が終わるまで繰り返します。
        - すべてのコンポーネントについて、作業の省略は行わず正しく処理を完了させてください。
        - 未作成のコンポーネントがある場合、それらを作成してください。

3. UI定義ファイルの内容をテンプレートファイルを元に実装します。
    - 表示項目の順序はUI定義ファイルに従ってください。
    - コンポーネント呼び出しがある場合、正しく呼び出しを行ってください。
        - 必要なコンポーネントが足りない場合はコンポーネント生成作業に戻り、すべてのコンポーネントを作成してください。

4. イベント処理の内容を実装します。
    - テンプレートファイルのイベント処理を参考に、必要な処理を実装します。

### 品質検証

以下の検証を実施し、品質に問題がないか確認してください。

1. **機能完全性**
    - ページ定義との整合性を確認してください。
        - 表示項目
        - バリデーションルール
        - イベント処理
    - 必要なコンポーネントが全て作成されているか確認してください。
        - コンポーネントのインポートと使用が正しく行われているか
        - コンポーネントのプロパティやイベントが正しく設定されているか

2. **アーキテクチャ準拠性**
    - アーキテクチャ定義に従った実装が行われているか確認してください。
        - @hox/base、@ibp/baseコンポーネントの正しいインポート
        - `app-`プレフィックスコンポーネントの使用
        - Vuetifyコンポーネント直接使用の回避
        - 使用方法ファイルに従ったプロパティ指定

3. **コード品質**
    - ビルドエラーが無いことを確認してください。
    - コード品質（インデント、不要コード除去、適切なコメント）が保たれていることを確認してください。

4. 上記で検証した内容に問題がある場合は、必要な修正を行ってください。

