# プロジェクト設定定義

## 概要
このドキュメントは、プロジェクト固有の設定に関する情報を提供します。

## ディレクトリ構成

当プロジェクトは以下のディレクトリ構成となります。

### フロントエンドのディレクトリ構成

- クライアントアプリケーションディレクトリ : `sources\frontends\IbpParasolSampleClient\`
  - ソース格納ディレクトリ : `src\packages\ibp-parasol-sample\src\`
    - APIクライアントディレクトリ : `apiclient\`
    - コンポーネントディレクトリ : `components\`
    - 関数ディレクトリ : `composables\`
    - 定数ディレクトリ : `constants\`
      - 列挙型ディレクトリ : `domain\enums\`
      - zodスキーマディレクトリ : `domain\entities\`
    - 画面ディレクトリ : `pages\ibp-parasol-sample-app\`

### バックエンドのディレクトリ構成

- APIアプリケーションディレクトリ : `sources\frontends\IbpParasolSampleApi\`
- サービスディレクトリ : `sources\services\IbpParasolSampleService\`
- サービステストディレクトリ : `sources\services\IbpParasolSampleService.Tests\`
