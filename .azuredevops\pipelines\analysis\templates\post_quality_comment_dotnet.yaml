parameters:
  analysisResultDirectory: ''
  testProjectRootDirectory: ''
  scriptDirectory: ''
  threadId: $(ThreadId)
  title: ''
  
steps:
- task: PowerShell@2
  displayName: 'post pr quality comment for dotnet'
  inputs:
    targetType: 'filePath'
    filePath: '${{ parameters.scriptDirectory }}/post_comment_dotnet.ps1'
    pwsh: true
    arguments: >
      -accessToken $(System.AccessToken)
      -analysisResultDirectory ${{ parameters.analysisResultDirectory }}
      -testProjectRootDirectory ${{ parameters.testProjectRootDirectory }}  
      -sourceBranch $(System.PullRequest.SourceBranch)
      -threadId ${{ parameters.threadId }}
      -title ${{ parameters.title }}