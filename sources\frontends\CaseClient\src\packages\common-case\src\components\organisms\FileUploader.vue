<template>
  <div>
    <VLabel
      text="ファイルアップロード"
      class="label-title mt-10 font-weight-bold"
    />

    <!-- アップロードエリア -->
    <v-form v-model="dataRef.isFormValid">
      <div
        ref="dropZoneRef"
        class="px-1 mt-3"
        :class="{ enter: isOverDropZone, noEnter: !isOverDropZone }"
        @change="change"
      >
        <v-file-input
          v-model="dataRef.inputFiles"
          :disabled="!userId || loading || disabled"
          show-size
          multiple
          label="ドラッグ&ドロップか入力フォームを選択してファイルを登録してください"
        />
      </div>

      <!-- アップロードファイル -->
      <v-col>
        <div
          v-for="file in currentFileData"
          :key="file.name"
          style="display: inline"
        >
          <v-chip
            color="#00a63c"
            label
            size="small"
            class="me-2 mb-3 chip"
            :disabled="loading || disabled"
          >
            {{ file.name }}
            <v-icon
              color="#df6249"
              class="ml-2"
              @click="deleteUploadFile(file.name)"
            >
              mdi-close-circle-outline
            </v-icon>
          </v-chip>
        </div>
      </v-col>

      <!-- ヒントテキスト -->
      <div v-if="useSaveBtn && !disableList">
        ※保存するまでファイルはアップロード・削除されません
      </div>
      <!-- ボタン -->
      <AppMainBtn
        v-if="!useSaveBtn && !disableList"
        class="mt-5"
        :disabled="dataRef.isFormValid || disabled"
        @click="uploadFile"
      >
        <v-icon>mdi-upload</v-icon>アップロード
      </AppMainBtn>
    </v-form>

    <div v-if="disableList" class="mb-4">
      <div v-if="uploadedFiles?.length > 0" class="font-weight-bold">
        アップロードしたファイル
      </div>
      <div
        v-for="file in uploadedFiles"
        :key="file.fileName"
        style="display: inline"
      >
        <v-chip
          v-if="!isFileInChipFilesToDelete(file)"
          color="#00a63c"
          label
          size="small"
          class="me-2 mb-3 chip"
          :disabled="loading"
        >
          {{ file.fileName }}
          <v-icon color="#df6249" class="ml-2" @click="deleteFromChipSet(file)">
            mdi-close-circle-outline
          </v-icon>
        </v-chip>
      </div>
    </div>

    <!-- 上書き確認ダイアログ -->
    <AppConfirmDialog ref="confirmDialog" />
  </div>
</template>

<script setup lang="ts">
import { useDropZone } from '@vueuse/core'
// import { createContainerName, scrollToDefault } from '@/libs/scrollUtil'
// import confirmPageTransfer from '@/mixins/confirmPageTransferMixin'
import type { AppConfirmDialogType } from '@hox/base/src/components/shared/molecules/AppConfirmDialog.vue'
import type { DeleteItem } from '@ibp/common-case/src/models/share/FileUploadType'
import type { issueProjectDiscussionFileModelItem } from '@ibp/issue-project/src/models/issueProjectDiscussion/FileUploadType'
import { useFileManagement } from '@ibp/issue-project/src/composables/fileUploader/useFileManagement'
import { useSharedData } from '@ibp/issue-project/src/composables/fileUploader/useSharedData'

// =====================================================================================================================
// ファイル管理のcomposable
// =====================================================================================================================
const { dataRef, nowUploadFiles } = useSharedData()
const {
  addFiles,
  deleteUploadFile,
  isFileInChipFilesToDelete,
  deleteFromChipSet,
  change,
} = useFileManagement()

const currentFileData = computed(() => {
  return nowUploadFiles.value
})

// =====================================================================================================================
// ドラッグ&ドロップ関連
// =====================================================================================================================
const dropZoneRef = ref<HTMLDivElement>()

function onDrop(files: File[] | null) {
  addFiles(files)
}

const { isOverDropZone } = useDropZone(dropZoneRef, {
  onDrop,
})

// =====================================================================================================================
// 上書き確認ダイアログ
// =====================================================================================================================
const confirmDialog = ref<AppConfirmDialogType>()

// const containerName = createContainerName()

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  disableList: {
    type: Boolean,
    default: false,
  },
  useSaveBtn: {
    type: Boolean,
    default: false,
  },
  isAdd: {
    type: Boolean,
    default: false,
  },
  uploadedFiles: {
    type: Array as PropType<Array<issueProjectDiscussionFileModelItem>>,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const { $auth } = useNuxtApp()
const userInfo = $auth?.getUser()
const userId = userInfo?.userId ?? 'unknown'

// const uploadFiles = defineModel<Array<UploadFileItem>>('uploadFiles')
// const uploadFiles = defineModel<File[]>('uploadFiles')
// const tempFilesToDelete = defineModel<Array<DeleteItem>>('tempFilesToDelete')

const emits = defineEmits([
  'update-uploaded-files',
  'upload-file',
  'download-file',
  'delete-file-done',
  'delete-file',
])

const confirmOverwrite = async (targetFileNames: Array<string>) => {
  // 既存のファイルの中からファイル名を取得
  const currentFileNames = props.uploadedFiles.map((file) => file.fileName)

  // 同じファイル名が存在するか確認
  dataRef.value.sameFileNames = targetFileNames.filter((targetFileName) =>
    currentFileNames.includes(targetFileName),
  )

  const dialogParam = {
    title: '上書き確認',
    message:
      '同じファイル名が存在します。上書きしますか？' +
      dataRef.value.sameFileNames,
    icon: 'mdi-comment-question-outline',
  }

  if (dataRef.value.sameFileNames.length > 0) {
    await confirmDialog.value
      // ?.open(JSON.stringify(dataRef.value.sameFileNames))
      ?.open(dialogParam)
      .then((isOk) => {
        dataRef.value.isOk = isOk
      })
  } else {
    dataRef.value.isOk = true
  }

  return dataRef.value.isOk
}

const uploadFile = async () => {
  // 同じファイルがあれば上書き確認モーダルを表示する
  const targetFileNames = nowUploadFiles.value!.map((file: File) => file.name)

  let isConfirm: boolean
  if (props.disableList) {
    // チップを削除しながら、uploadFilesに存在していたら、confirmOverwriteで無視する
    const filteredUploadFiles = nowUploadFiles
      .value!.filter((file: File) => {
      const isNotInChipFilesToDelete = !dataRef.value.chipFilesToDelete.some(
        (chipFile: DeleteItem) => chipFile.deleteItemName === file.name,
      )
      return isNotInChipFilesToDelete
    })
      .map((file: File) => file.name)

    isConfirm = await confirmOverwrite(filteredUploadFiles)
  } else {
    isConfirm = await confirmOverwrite(targetFileNames)
  }

  // cancelに押すと、新規ファイルしかアップロードしません
  if (!isConfirm) {
    nowUploadFiles.value = nowUploadFiles.value!.filter(
      (file: File) => !dataRef.value.sameFileNames.includes(file.name),
    )
  }

  const filesToRemove = [
    ...dataRef.value.chipFilesToDelete,
    // ...props.tempFilesToDelete,
  ]

  emits('upload-file', nowUploadFiles.value, filesToRemove)
  nowUploadFiles.value = []
  dataRef.value.filesToRemove = []
  dataRef.value.chipFilesToDelete = []
  dataRef.value.isFormValid = true
}

/**
 * FileUploaderが保持する固有のパラメータを初期化する。
 * プロパティでも渡せないパラメータのため呼び出し元でクリアする。
 */
const clearInternalParameter = (): void => {
  nowUploadFiles.value = []
  dataRef.value.chipFilesToDelete = []
}

const exposeFunctions = {
  uploadFile,
  clearInternalParameter,
}

defineExpose(exposeFunctions)
</script>

<style scoped>
.enter {
  border: 3px dotted red;
  height: 150px;
  padding-top: 30px;
}

.noEnter {
  border: 3px dashed gray;
  height: 150px;
  padding-top: 30px;
}
.drop-area {
  border: 3px dashed gray;
  height: 100px;
  padding-top: 15px;
}

.chip {
  border: 1px solid #00a63c;
  font-size: 14px;
  min-height: 32px;
  height: auto;
  white-space: pre-wrap;
  overflow-wrap: anywhere;
}
</style>
