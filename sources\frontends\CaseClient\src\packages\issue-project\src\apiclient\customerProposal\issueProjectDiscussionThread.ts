import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import { purposeTypeOfTemplateKeys } from '@ibp/issue-project/src/constants/domain/purposeTypeOfTemplate'
import { discussionPurposeKeys } from '@ibp/issue-project/src/constants/domain/discussionPurpose'
import { issueProjectTeamKeys } from '@ibp/issue-project/src/constants/domain/issueProjectTeam'
import type {
  Pagination,
  ApiResult,
} from '@hox/base/src/apiclient/shared/types'

// IssueProjectDiscussionThreadFileのスキーマ
const issueProjectDiscussionThreadFileSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  updatedDateTime: z.date(),
  updaterId: z.string().nullish(),
  updaterName: z.string().nullish(),
})

export type issueProjectDiscussionThreadFileType = z.infer<
  typeof issueProjectDiscussionThreadFileSchema
>

export const descriptionsSchema = z.object({
  key: z.string(),
  value: z.any(),
})

// FileModelItemのスキーマ
const FileModelItemSchema = z.object({
  id: z.string().nullish(),
  fileName: z.string(),
  updaterId: z.string(),
  updater: z.string(),
  updatedDateTime: z.instanceof(Date),
  version: z.string(),
})

// DeleteItemのスキーマ
const DeleteItemSchema = z.object({
  deleteTargetFile: FileModelItemSchema,
  deleteItemName: z.string(),
  deleteItemVersion: z.string(),
})

export const targetDataSchema = z.object({
  id: z.string(),
  version: z.string(),
  discussionType: z.string(),
  mentionTargetsHtml: z.string().nullable().optional(),
  templateVersion: z.number(),
  purpose: z.string().nullish(),
  person: z.string().nullable().optional(),
  isPersonOfPower: z.boolean().nullable().optional(),
  purposeTypeOfTemplate: z.string(),
  descriptions: z.array(descriptionsSchema),
  descriptionJson: z.string().optional(),
  mentionTargetUserIds: z.array(z.string()).nullable().optional(),
  mentionTargetTeamMemberUserIds: z.array(z.string()).nullable().optional(),
  mentionTargetTeamIds: z.array(z.string()),
  filesToUpload: z.array(z.instanceof(File)),
  filesToRemove: z.array(DeleteItemSchema),
})

// IssueProjectDiscussionCommentFileのスキーマ
const issueProjectDiscussionCommentFileSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  commentId: z.string(),
  updatedDateTime: z.date(),
  updaterId: z.string(),
  updaterName: z.string(),
})

// IssueProjectDiscussionCommentReactionのスキーマ
const issueProjectDiscussionCommentReactionSchema = z.object({
  id: z.string(),
  commentId: z.string(),
  reactionType: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  updatedDateTime: z.date(),
  version: z.string(),
})

export type issueProjectDiscussionCommentReactionType = z.infer<
  typeof issueProjectDiscussionCommentReactionSchema
>

// IssueProjectDiscussionCommentのスキーマ
export const issueProjectDiscussionCommentSchema = z.object({
  id: z.string(),
  threadId: z.string(),
  registeredDateTime: z.date(),
  registrant: z.string(),
  registrantId: z.string(),
  description: z.string(),
  purpose: z.string(),
  person: z.string().nullish(),
  isPersonOfPower: z.boolean().nullish(),
  reactions: z.array(issueProjectDiscussionCommentReactionSchema),
  files: z.array(issueProjectDiscussionCommentFileSchema),
  mentionTargetUserIds: z.array(z.string()).nullish(),
  mentionTargetTeamIds: z.array(z.string()).nullish(),
  version: z.string(),
})

export type issueProjectDiscussionCommentType = z.infer<
  typeof issueProjectDiscussionCommentSchema
>

// IssueProjectDiscussionThreadReactionのスキーマ
const issueProjectDiscussionThreadReactionSchema = z.object({
  id: z.string(),
  threadId: z.string(),
  reactionType: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  updatedDateTime: z.string().datetime(),
  version: z.string(),
})

export type issueProjectDiscussionThreadReactionType = z.infer<
  typeof issueProjectDiscussionThreadReactionSchema
>
// IssueProjectDiscussionTypeのスキーマ
export const issueProjectDiscussionTypeSchema = z.object({
  id: z.string(),
  discussionTypeName: z.string(),
  isApprovalDiscussion: z.boolean(),
  isInactive: z.boolean().default(false),
  purposeTypeOfTemplate: z.enum(
    purposeTypeOfTemplateKeys.map(String) as [string, ...string[]],
  ),
  order: z.number().int().nullish(),
})

export type IssueProjectDiscussionType = z.infer<
  typeof issueProjectDiscussionTypeSchema
>

// KeyValuePairのスキーマ定義
export const keyValuePairSchema = z.object({
  key: z.string(),
  value: z.string(),
})

// 主として扱うデータのスキーマを定義します。
export const issueprojectdiscussionthreadSchema = z.object({
  id: z.string(),
  issueProjectId: z.string(),
  registeredDateTime: z.string().datetime(),
  registrant: z.string(),
  registrantId: z.string(),
  discussionType: issueProjectDiscussionTypeSchema,
  purposeTypeOfTemplate: z.enum(
    purposeTypeOfTemplateKeys.map(String) as [string, ...string[]],
  ),
  purpose: z.enum(discussionPurposeKeys.map(String) as [string, ...string[]]),
  person: z.string().nullish(),
  isPersonOfPower: z.boolean().nullish(),
  title: z.string(),
  descriptions: z.array(keyValuePairSchema),
  displayDescriptions: z.array(z.string()),
  reactions: z.array(issueProjectDiscussionThreadReactionSchema).nullish(),
  templateVersion: z.number().int(),
  comments: z.array(issueProjectDiscussionCommentSchema),
  files: z.array(issueProjectDiscussionThreadFileSchema),
  mentionTargetsHtml: z.string().nullish(),
  mentionTargetUserIds: z.array(z.string()).nullish(),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type IssueProjectDiscussionThread = z.infer<
  typeof issueprojectdiscussionthreadSchema
>

// =====================================================================================================================
// APIクライアントの定義(検索)
// =====================================================================================================================

// 検索条件の型を定義します。
export type FindIssueProjectDiscussionThreadCriteria = {
  issueProjectId: string
  fromDate?: string
  toDate?: string
  discussionTypeId?: string
}

// 検索結果の型を定義します。主として扱うデータの型をOmitやUnionなどで編集してください。
export type FindIssueProjectDiscussionThreadResultItem =
  IssueProjectDiscussionThread

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindIssueProjectDiscussionThread(
  query: Ref<FindIssueProjectDiscussionThreadCriteria>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindIssueProjectDiscussionThreadResultItem[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthread',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(idで取得する)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProjectDiscussionThread(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionThread>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/issueprojectdiscussionthread/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(新規追加)
// =====================================================================================================================

// IFormFileのスキーマ
export const IFormFileSchema = z.object({
  contentType: z.string(),
  length: z.number().int(),
  name: z.string(),
  fileName: z.string(),
})

// AddIssueProjectDiscussionThreadCommandのスキーマ
export const addIssueProjectDiscussionThreadCommandSchema = z.object({
  issueProjectId: z.string(),
  registeredDateTime: z.date(),
  registrant: z.string(),
  registrantId: z.string(),
  discussionTypeId: z.string(),
  purposeTypeOfTemplate: z.enum(
    purposeTypeOfTemplateKeys.map(String) as [string, ...string[]],
  ),
  discussionTypeName: z.string(),
  descriptionJson: z.string(),
  templateVersion: z.number().int(),
  purpose: z.enum(discussionPurposeKeys.map(String) as [string, ...string[]]),
  person: z.string().nullish(),
  isPersonOfPower: z.boolean().nullish(),
  uploadFiles: z.array(IFormFileSchema).nullish(),
  customerIdentificationId: z.string().uuid(),
  customerName: z.string(),
  mentionTargetsHtml: z.string().nullish(),
  mentionTargetUserIds: z.array(z.string()).nullish(),
  mentionTargetTeamIds: z.array(z.string()).nullish(),
  mentionTargetTeamMemberUserIds: z.array(z.string()).nullish(),
})

// 作成用のデータのスキーマを定義します。
export const issueprojectdiscussionthreadSchemaForCreate =
  addIssueProjectDiscussionThreadCommandSchema
// 作成用の型を作成します。
export type IssueProjectDiscussionThreadForCreate = z.infer<
  typeof issueprojectdiscussionthreadSchemaForCreate
>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostIssueProjectDiscussionThread(body: Ref<FormData>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionThread>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthread',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(更新)
// =====================================================================================================================

// UpdateIssueProjectDiscussionThreadCommandのスキーマ
export const updateIssueProjectDiscussionThreadCommandSchema = z.object({
  id: z.string(),
  registrantId: z.string(),
  discussionTypeName: z.string(),
  purpose: z.enum(discussionPurposeKeys.map(String) as [string, ...string[]]),
  person: z.string().nullish(),
  isPersonOfPower: z.boolean().nullish(),
  descriptionJson: z.string(),
  uploadFiles: z.array(IFormFileSchema).nullish(),
  filesToRemove: z.array(z.string()).nullish(),
  fileIds: z.array(z.string()).nullish(),
  customerIdentificationId: z.string().uuid(),
  customerName: z.string(),
  mentionTargetsHtml: z.string().nullish(),
  mentionTargetUserIds: z.array(z.string()).nullish(),
  mentionTargetTeamIds: z.array(z.string()).nullish(),
  mentionTargetTeamMemberUserIds: z.array(z.string()).nullish(),
  version: z.string(),
})

// 更新用のデータのスキーマを定義します。
export const issueprojectdiscussionthreadSchemaForSave =
  updateIssueProjectDiscussionThreadCommandSchema
// 更新用の型を作成します。
export type IssueProjectDiscussionThreadForSave = z.infer<
  typeof issueprojectdiscussionthreadSchemaForSave
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutIssueProjectDiscussionThread(body: Ref<FormData>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionThread>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthread',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(削除)
// =====================================================================================================================

export const deleteIssueProjectDiscussionThreadCommandSchema = z.object({
  id: z.string(),
  registrantId: z.string(),
  version: z.string(),
})

export type DeleteIssueProjectDiscussionThreadType = z.infer<
  typeof deleteIssueProjectDiscussionThreadCommandSchema
>

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteIssueProjectDiscussionThread(
  body: Ref<DeleteIssueProjectDiscussionThreadType>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthread',
        ),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (ファイルダウンロード)
// =====================================================================================================================

// データのスキーマを定義します。
export const downloadIssueProjectDiscussionThreadCommandSchema = z.object({
  data: z.instanceof(Blob),
})
// データ型を定義します。
export type DownloadIssueProjectDiscussionThreadResult = z.infer<
  typeof downloadIssueProjectDiscussionThreadCommandSchema
>

// 検索条件
export type IssueProjectDiscussionThreadCriteria = {
  issueProjectDiscussionThreadId: string
  fileName: string
}

/**
 * ダウンロードを行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDownloadIssueProjectDiscussionThread(
  query: Ref<IssueProjectDiscussionThreadCriteria>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<DownloadIssueProjectDiscussionThreadResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthread/download',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (検索、cross-customer)
// =====================================================================================================================

// データのスキーマを定義します。
const findIPDTCrossCustomerResultSchema = z.object({
  id: z.string(),
  customerIdentificationId: z.string().uuid(),
  issueProjectId: z.string(),
  customerName: z.string(),
  overview: z.string(),
  registeredDateTime: z.date(),
  registrant: z.string(),
  registrantId: z.string(),
  discussionType: issueProjectDiscussionTypeSchema,
  title: z.string(),
  purpose: z.enum(discussionPurposeKeys.map(String) as [string, ...string[]]),
  person: z.string().nullish(),
  isPersonOfPower: z.boolean().nullish(),
  descriptions: z.array(keyValuePairSchema),
  displayDescriptions: z.array(z.string()),
  reactions: z.array(issueProjectDiscussionCommentReactionSchema),
  templateVersion: z.number().int(),
  comments: z.array(issueProjectDiscussionCommentSchema),
  files: z.array(issueProjectDiscussionCommentFileSchema),
  mentionTargetsHtml: z.string().nullish(),
  mentionTargetUserIds: z.array(z.string()).nullish(),
  customerStaffName: z.string().nullish(),
  issueProjectStaffName: z.string().nullish(),
  issueProjectTeam: z
    .enum(issueProjectTeamKeys.map(String) as [string, ...string[]])
    .nullish(),
  isAccessRestricted: z.boolean(),
  version: z.string(),
})
// データ型を定義します。
export type FindIPDTCrossCustomerQueryResult = z.infer<
  typeof findIPDTCrossCustomerResultSchema
>
// 検索結果の型にページングの情報を追加します。
export type FindIPDTCrossCustomerQueryResultItem =
  ApiResult<FindIPDTCrossCustomerQueryResult>

// 検索条件の型定義
export type FindIPDTCrossCustomerQuery = {
  fromDate: string | undefined
  toDate: string | undefined
  registrantIds?: string[]
  customerStaffIds?: string[]
  issueProjectStaffIds?: string[]
  issueProjectTeams?: number[]
  discussionTypeIds?: string[]
} & Pagination

/**
 * 検索を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFIndIssueProjectDiscussionThreadCrossCustomer(
  query: Ref<FindIPDTCrossCustomerQuery>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindIPDTCrossCustomerQueryResultItem>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthread/cross-customer',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
