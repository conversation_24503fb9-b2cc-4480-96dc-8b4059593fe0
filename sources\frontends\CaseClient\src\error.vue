<template>
  <!-- NuxtErrorBoundary で基本は処理されるが抜けが無いように設定。
    https://nuxt.com/docs/getting-started/error-handling#rendering-an-error-page -->
  <error :error="error" />
</template>
<script setup lang="ts">
const config = useRuntimeConfig()
useHead({
  titleTemplate(title) {
    return title
      ? `${title} - ${config.public.appInfo.applicationName}`
      : config.public.appInfo.applicationName
  },
  title: 'エラー',
})
</script>
<script lang="ts">
export default defineComponent({
  props: {
    error: {
      type: Object,
      default: () => {},
    },
  },
})
</script>
