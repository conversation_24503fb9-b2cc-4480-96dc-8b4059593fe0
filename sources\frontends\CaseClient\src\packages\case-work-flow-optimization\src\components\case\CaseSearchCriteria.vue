<template>
  <div>
    <VRow dense>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.branchNumbers"
          :items="branchMasterOptions || []"
          label="店番"
          data-testid="branchNumbers"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.branchNumbers"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppTextField
          v-model="localSearchCondition.cifNumber"
          label="CIF番号"
          data-testid="cifNumber"
          type="number"
          counter="8"
          clearable
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.cifNumber"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppTextField
          v-model="localSearchCondition.customerName"
          label="氏名（漢字 または カナ）"
          style="white-space: break-spaces"
          data-testid="name"
          hint="部分一致検索ができます。"
          persistent-hint
          clearable
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.customerName"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.industryCodes"
          :items="caseOptions.industry || []"
          label="業種"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.industryCodes"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
    </VRow>
    <VRow dense>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.caseCategories"
          :items="caseOptions.categories || []"
          label="カテゴリ"
          data-testid="category"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.caseCategories"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.caseStatuses"
          :items="caseOptions.caseStatuses || []"
          label="ステータス"
          data-testid="status"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.caseStatuses"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.staffIds"
          :items="caseOptions.staffAndTeams || []"
          label="案件担当者"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading || loadingStaffAndTeam"
          :loading="loading || loadingStaffAndTeam"
          :error-messages="errorMessages?.staffIds"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.customerStaffIds"
          :items="caseOptions.staffAndTeams || []"
          label="顧客担当者"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading || loadingStaffAndTeam"
          :loading="loading || loadingStaffAndTeam"
          :error-messages="errorMessages?.customerStaffIds"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
    </VRow>
    <VRow dense>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.generalTransactionTypeIds"
          :items="caseOptions.generalTransactionTypeOptions || []"
          label="案件項目"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.generalTransactionTypeIds"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppPopupDatePicker
          v-model="localSearchCondition.fromDate"
          label="期日 From"
          display-text-format="yyyy/MM/dd"
          clearable
          dense
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.fromDate"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppPopupDatePicker
          v-model="localSearchCondition.toDate"
          label="期日 To"
          display-text-format="yyyy/MM/dd"
          clearable
          dense
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.toDate"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol>
        <VCheckbox
          v-model="localSearchCondition.isFavorite"
          label="お気に入り"
          :disabled="loading"
          :loading="loading"
          :true-value="true"
          :false-value="false"
          :error-messages="errorMessages?.isFavorite"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <!-- 検索結果件数表示 -->
      <VCol class="d-inline-flex justify-end align-center">
        <SearchResultArea :count="totalCount" />
      </VCol>
    </VRow>
  </div>
</template>

<script setup lang="ts">
import { deepClone, deepEqual } from '~/packages/common-case/src/utils/shared/objectUtils'

/**
 * 案件検索条件コンポーネント
 *
 * 機能:
 * 1. 検索条件の入力フォーム
 * 2. バリデーション処理
 * 3. 検索条件の親コンポーネントへの通知
 */

// === プロパティ定義 ===
const props = defineProps({
  // 検索条件オブジェクト
  searchCondition: {
    type: Object,
    required: true,
  },
  // 店番マスタオプション
  branchMasterOptions: {
    type: Array,
    default: () => [],
  },
  // 案件関連オプション
  caseOptions: {
    type: Object,
    required: true,
  },
  // スタッフ・チーム読み込み中フラグ
  loadingStaffAndTeam: {
    type: Boolean,
    default: false,
  },
  // ローディング状態
  loading: {
    type: Boolean,
    default: false,
  },
  // エラーメッセージ
  errorMessages: {
    type: [Object, null],
    default: null,
  },
  // 検索結果件数
  totalCount: {
    type: Number,
    default: 0,
  },
})

// === イベント定義 ===
const emit = defineEmits([
  'update:search-condition', // 検索条件更新イベント
])

// === ローカル状態 ===
// 検索条件のローカルコピー
const localSearchCondition = ref(deepClone(props.searchCondition))

// 内部更新フラグ（無限ループ防止用）
let isInternalUpdate = false

// === 監視処理 ===
// 検索条件が変更されたら親コンポーネントに通知
watch(localSearchCondition, (newValue) => {
  // 内部更新の場合はイベント発火をスキップ
  if (isInternalUpdate) {
    isInternalUpdate = false
    return
  }
  emit('update:search-condition', newValue)
}, { deep: true })

// 親コンポーネントからの変更を監視して反映
watch(() => props.searchCondition, (newValue) => {
  // 値が実際に変更された場合のみ更新
  if (!deepEqual(localSearchCondition.value, newValue)) {
    // 内部更新フラグを設定してからローカル状態を更新
    isInternalUpdate = true
    localSearchCondition.value = deepClone(newValue)
  }
}, { deep: true })

// === イベントハンドラ ===
/**
 * 検索条件更新処理
 */
function updateSearchCondition() {
  emit('update:search-condition', localSearchCondition.value)
}

</script>
