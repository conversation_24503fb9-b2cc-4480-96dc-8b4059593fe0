/**
 * IssueProjectTeam
 */
export const ISSUE_PROJECT_TEAM = {
  0: 'Undefined',
  1: '経営戦略',
  2: 'ICT',
  3: '人事',
  4: '事業承継M&A',
  5: '海外ビジネス',
  6: 'CCIシンガポール',
  7: 'CCIベトナム',
  8: 'タイCCI',
  9: '深圳CCI',
  10: 'ソリューション',
  11: 'マーケティンググループ',
  12: 'デジタル部',
  13: 'FDAlco',
  14: 'BPOマネジメント',
  15: 'CCIアフリカ',
  99: 'その他',
} as const

export type IssueProjectTeamType = keyof typeof ISSUE_PROJECT_TEAM

export const issueProjectTeamKeys = Object.keys(ISSUE_PROJECT_TEAM).map((key) =>
  Number(key),
) as IssueProjectTeamType[]

export const issueProjectTeams = Object.entries(ISSUE_PROJECT_TEAM).map(
  ([value, title]) => ({
    value: Number(value),
    title,
  }),
)
