import { useNuxtApp } from 'nuxt/app'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

// 主として扱うデータのデータ型を定義します。
export type User = {
  userId: string
  mainCompanyId: string
  mainCompanyName: string
  nameKanji: string
  nameRomaji: string
  mainBranchNumber: string
  mainBranchName: string
  mainPosition: string
  currentJobDescription: string
  isManager: boolean
}

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

export type FindUserCriteria = {
  userId?: string | undefined
  name?: string | undefined
  branchNumbers?: string[] | undefined
}

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindUser(query?: MaybeRefOrGetter<FindUserCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<User[]>(
    useFetch(
      () => $endpoints.default.get('/mycareer-user/v1.0/useranalysis'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data.returnSearchUsersResults
        },
      }),
    ),
  )
}
