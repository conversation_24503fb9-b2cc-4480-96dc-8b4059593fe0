const maxFileSize = 104857600
const maxFileSizeMb = maxFileSize / 1024 / 1024

/**
 * 合計ファイルサイズを検証する
 * @param files ファイルのリスト
 * @returns 検証結果を返す(メッセージと許容サイズも返すが、使用するかは呼び出しもとに任せる)
 */
export const validateFileSize = (files: File[]) => {
  let fileSizeSum = 0
  files.forEach((x) => {
    if (x.size) {
      fileSizeSum += x.size
    }
  })

  if (fileSizeSum > maxFileSize) {
    return {
      isValid: false,
      message: `添付ファイルの合計サイズが${maxFileSizeMb}MBを超えています。添付ファイルを減らして${maxFileSizeMb}MBに収まるようにしてください。`,
      maxFileSizeMb,
    }
  }

  return { isValid: true, message: '', maxFileSizeMb }
}
