import { format, parseISO } from 'date-fns'
import { isString } from '@hox/base/src/utils/shared/is'

export function formatDateYM(date?: Date | string | null) {
  return date ? format(isString(date) ? parseISO(date) : date, 'yyyy/MM') : '-'
}

export function formatDateYMD(date?: Date | string | null) {
  return date ? format(isString(date) ? parseISO(date) : date, 'yyyy/MM/dd') : '-'
}

export function formatDateYMDHm(date?: Date | string | null) {
  return date ? format(isString(date) ? parseISO(date) : date, 'yyyy/MM/dd HH:mm') : '-'
}

export function formatDateYMDHms(date?: Date | string | null) {
  return date ? format(isString(date) ? parseISO(date) : date, 'yyyy/MM/dd HH:mm:ss') : '-'
}