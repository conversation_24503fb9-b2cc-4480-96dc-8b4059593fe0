import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type {
  Pagination,
  ApiResult,
} from '@hox/base/src/apiclient/shared/types'

// =====================================================================================================================
// APIクライアントの定義 (検索)
// =====================================================================================================================

// 検索結果用のスキーマを定義します
export const issueProjectFileSchemaForFind = z.object({
  id: z.string(),
  fileName: z.string(),
  updater: z.string(),
  updaterId: z.string(),
  updatedDateTime: z.string().datetime(),
  issueProjectId: z.string(),
  version: z.string(),
})

// 検索条件の型を定義します
export type IssueProjectFileForFindCriteria = {
  issueProjectId: string
} & Pagination

export type IssueProjectFileForFind = z.infer<typeof issueProjectFileSchemaForFind>
export type IssueProjectFileForFindResult = ApiResult<IssueProjectFileForFind>

/**
 * ファイル一覧を検索する
 * @param query 検索条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindIssueProjectFile(
  query: Ref<IssueProjectFileForFindCriteria>
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectFileForFindResult>(
    useFetch(
      () =>
        $endpoints.default.get('/ibp-customer-proposal/v1.0/issueprojectfile'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      })
    )
  )
}

// =====================================================================================================================
// APIクライアントの定義 (ファイルアップロード)
// =====================================================================================================================

// アップロード用のスキーマを定義します
export const issueProjectFileSchemaForUpload = z.object({
  issueProjectId: z.string(),
  files: z.array(z.instanceof(File)),
})

export type IssueProjectFileForUpload = z.infer<typeof issueProjectFileSchemaForUpload>

// アップロード結果用のスキーマを定義します
export const issueProjectFileSchemaForUploadResult = z.object({
  success: z.boolean(),
  uploadedFiles: z.array(issueProjectFileSchemaForFind).optional(),
  errors: z.array(z.string()).optional(),
})

export type IssueProjectFileForUploadResult = z.infer<typeof issueProjectFileSchemaForUploadResult>

/**
 * ファイルをアップロードする
 * @param data アップロードデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useUploadIssueProjectFile(
  data: Ref<IssueProjectFileForUpload>
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectFileForUploadResult>(
    useFetch(
      () =>
        $endpoints.default.post('/ibp-customer-proposal/v1.0/issueprojectfile/upload'),
      withDefaultFetchOptions({
        method: 'POST',
        body: computed(() => {
          const formData = new FormData()
          formData.append('issueProjectId', data.value.issueProjectId)
          data.value.files.forEach((file) => {
            formData.append('files', file)
          })
          return formData
        }),
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      })
    )
  )
}

// =====================================================================================================================
// APIクライアントの定義 (ファイルダウンロード)
// =====================================================================================================================

// ダウンロード用の型を定義します
export type DownloadIssueProjectFile = {
  issueProjectId: string
  fileName: string
}

/**
 * ダウンロードする
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDownloadIssueProjectFile(
  query: Ref<DownloadIssueProjectFile>
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectfile/download'
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      })
    )
  )
}

// =====================================================================================================================
// APIクライアントの定義 (ファイル削除)
// =====================================================================================================================

// 削除用のスキーマを定義します
export const issueProjectFileSchemaForDelete = z.object({
  id: z.string(),
  version: z.string(),
})

export type IssueProjectFileForDelete = z.infer<typeof issueProjectFileSchemaForDelete>

// 削除結果用のスキーマを定義します
export const issueProjectFileSchemaForDeleteResult = z.object({
  success: z.boolean(),
  message: z.string().optional(),
})

export type IssueProjectFileForDeleteResult = z.infer<typeof issueProjectFileSchemaForDeleteResult>

/**
 * ファイルを削除する
 * @param data 削除データ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteIssueProjectFile(
  data: Ref<IssueProjectFileForDelete>
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectFileForDeleteResult>(
    useFetch(
      () =>
        $endpoints.default.delete(`/ibp-customer-proposal/v1.0/issueprojectfile/${data.value.id}`),
      withDefaultFetchOptions({
        method: 'DELETE',
        body: data,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      })
    )
  )
}