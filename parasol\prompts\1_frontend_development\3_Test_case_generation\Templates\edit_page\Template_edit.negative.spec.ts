import { describe, test, expect, vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { mockNuxtImport, mountSuspended } from '@nuxt/test-utils/runtime'

import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import {
  setupMockUseLayout,
  setupMockUseDisplay,
  getMockUseRouteDefaultImpl,
  createConflictError,
  createValidationError,
  patchOFetchOnMsw,
  setTestUser,
  clearTestUser,
} from '../../testSupport'
// テスト対象コンポーネント
import edit from '@/pages/employee/edit.vue'
import { useAppToasts } from '~/composables/shared/useAppToasts'

// =====================================================================================================================
// 定数定義
// =====================================================================================================================
const BASE_URL = 'http://localhost:3000/employee/edit'
const window = globalThis.window

// =====================================================================================================================
// テスト用処理設定
// =====================================================================================================================
// APIのモックサーバー定義
const server = setupServer()

// onBeforeRouteLeaveのモック化
// confirmBeforeUnload で利用している
mockNuxtImport('onBeforeRouteLeave', () => {
  return () => {}
})

// useRoute のモック化
mockNuxtImport('useRoute', () => {
  return () => {
    return getMockUseRouteDefaultImpl()
  }
})

// navigateToのモック化
const { mockNavigateTo } = vi.hoisted(() => ({
  mockNavigateTo: vi.fn(),
}))
mockNuxtImport('navigateTo', () => mockNavigateTo)

// 共通モック設定処理
function setUpMocks() {
  setupMockUseLayout()
  setupMockUseDisplay()
}

// =====================================================================================================================
// 前処理/後処理
// =====================================================================================================================
// テスト開始前処理
beforeAll(() => {
  // ユーザー情報を設定
  setTestUser('テストユーザー', 'test_user', ['admin'])
})
// テスト終了後処理
afterAll(() => {
  // APIモックサーバーを閉じる
  server.close()
  // ユーザー情報をクリア
  clearTestUser()
})
// テストケース開始前処理
beforeEach(() => {
  // デフォルトURL設定
  window.location.href = BASE_URL
  setUpMocks()
})
// テストケース終了時後処理
afterEach(() => {
  // APIのモックハンドラをリセット
  server.resetHandlers()
  // モック化したモジュールを元の実装に戻す
  vi.restoreAllMocks()
  // トーストが表示されている場合はクリア
  const { forceClear } = useAppToasts()
  forceClear()
})

// =====================================================================================================================
// APIデータ定義
// =====================================================================================================================
const responseSourceData = {
  // TODO: 実際のAPIのレスポンスに合わせて変更してください
  id: '1',
  nameKanji: 'nameKanji_1',
  nameKana: 'nameKana_1',
  birthDate: new Date(2000, 0, 1),
  address: 'address_1',
  branchId: '1',
  branchName: 'branchName_1',
  version: '1001',
}
const qualifications = [
  { id: '1', name: 'qualificationName_1' },
  { id: '2', name: 'qualificationName_2' },
]

// =====================================================================================================================
// テスト実装
// =====================================================================================================================
describe('employee/edit.vue negative test', () => {
  test('登録処理：登録APIにて排他エラーが発生した場合、エラーメッセージがToastにて表示される', async () => {
    // APIのモックEndpointを登録
    // 排他エラーを返すように設定する
    const postHandler = vi.fn(() => HttpResponse.json(createConflictError(), { status: 409 }))
    server.use(http.post('/v1.0/employee', postHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockConfirmDialog.mockReturnValue(true)

    // ブラウザ側でのバリデーションでエラーにする。
    vi.spyOn(vm.validators, 'validate').mockResolvedValue({ success: true })

    await vm.save()

    // 登録APIの呼び出し確認
    expect(postHandler).toBeCalledTimes(1)
    // 排他エラーメッセージの確認
    // 登録完了メッセージが表示されているか
    const { messages } = useAppToasts()
    expect(messages.value[0]).toMatchObject({
      message: 'すでに別のユーザーがデータを登録・変更しているため保存できませんでした。',
      color: 'error',
    })
  })

  test('登録処理：登録APIにてバリデーションエラーが発生した場合、エラーメッセージが各項目に設定される', async () => {
    // APIのモックEndpointを登録
    // TODO: エラーメッセージを設定
    const errors = {
      nameKanji: ['nameKanjiのエラーメッセージ'],
      nameKana: ['nameKanaのエラーメッセージ'],
      address: ['addressのエラーメッセージ'],
      branchId: ['branchIdのエラーメッセージ'],
    }
    const postHandler = vi.fn(() => HttpResponse.json(createValidationError({
      errors,
    }), { status: 400 }))
    server.use(http.post('/v1.0/employee', postHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化して、結果を true に設定する
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockConfirmDialog.mockReturnValue(true)
    // ブラウザ側でのバリデーションは素通りにする
    vi.spyOn(vm.validators, 'validate').mockResolvedValue({ success: true })
    await vm.save()

    // 登録APIの呼び出し確認
    expect(postHandler).toBeCalledTimes(1)
    // エラーメッセージの確認
    expect(vm.errorMessages.value).toEqual(errors)
  })

  test('登録処理：登録APIにて内部エラーが発生した場合、エラーがスローされ処理が中断される', async () => {
    // APIのモックEndpointを登録
    const postHandler = vi.fn(() => new HttpResponse(null, { status: 500 }))
    server.use(http.post('/v1.0/employee', postHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化して、結果を true に設定する
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockConfirmDialog.mockReturnValue(true)
    // ブラウザ側でのバリデーションは素通りにする
    vi.spyOn(vm.validators, 'validate').mockResolvedValue({ success: true })

    let error
    try {
      await vm.save()
    } catch (e) {
      error = e
    }

    // 登録APIの呼び出し確認
    expect(postHandler).toBeCalledTimes(1)
    // エラーが発生したことを確認
    expect(error).toBeInstanceOf(Error)
  })

  // 登録と更新が違うコードで実装している場合は、例外ケースのテストを追加してください。

  test('削除処理：削除APIにて排他エラーが発生した場合、エラーメッセージがToastにて表示される', async () => {
    // URL設定を上書き
    window.location.href = BASE_URL + '?id=1'

    // APIのモックEndpointを登録
    const getHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.get('/v1.0/employee/1', getHandler))
    const qualificationsHandler = vi.fn(() => HttpResponse.json(qualifications))
    server.use(http.get('/v1.0/employee/1/qualifications', qualificationsHandler))
    const deleteHandler = vi.fn(() => HttpResponse.json(createConflictError(), { status: 409 }))
    server.use(http.delete('/v1.0/employee', deleteHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockConfirmDialog.mockReturnValue(true)
    await vm.remove()

    // 削除APIの呼び出し確認
    expect(deleteHandler).toBeCalledTimes(1)
    // 排他エラーメッセージの確認
    const { messages } = useAppToasts()
    expect(messages.value[0]).toMatchObject({
      message: 'すでに別のユーザーがデータを登録・変更しているため削除できませんでした。',
      color: 'error',
    })
  })

  test('削除処理：削除APIにてバリデーションエラーが発生した場合、エラーメッセージが各項目に設定される', async () => {
    // URL設定を上書き
    window.location.href = BASE_URL + '?id=1'

    // APIのモックEndpointを登録
    const getHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.get('/v1.0/employee/1', getHandler))
    const qualificationsHandler = vi.fn(() => HttpResponse.json(qualifications))
    server.use(http.get('/v1.0/employee/1/qualifications', qualificationsHandler))
    const errors = {
      id: ['idのエラーメッセージ'],
      version: ['versionのエラーメッセージ'],
    }
    const deleteHandler = vi.fn(() => HttpResponse.json(createValidationError({
      errors,
    }), { status: 400 }))
    server.use(http.delete('/v1.0/employee', deleteHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')

    // 削除処理を実行（確認ダイアログで「はい」）
    mockConfirmDialog.mockReturnValue(true)
    await vm.remove()

    // 削除APIの呼び出し確認
    expect(deleteHandler).toBeCalledTimes(1)
    // エラーメッセージの確認
    expect(vm.errorMessages.value).toEqual(errors)
  })

  test('削除処理：削除APIにて内部エラーが発生した場合、エラーがスローされ処理が中断される', async () => {
    // URL設定を上書き
    window.location.href = BASE_URL + '?id=1'

    // APIのモックEndpointを登録
    const getHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.get('/v1.0/employee/1', getHandler))
    const qualificationsHandler = vi.fn(() => HttpResponse.json(qualifications))
    server.use(http.get('/v1.0/employee/*/qualifications', qualificationsHandler))
    const deleteHandler = vi.fn(() => new HttpResponse(null, { status: 500 }))
    server.use(http.delete('/v1.0/employee', deleteHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')

    // 削除処理を実行（確認ダイアログで「はい」）
    mockConfirmDialog.mockReturnValue(true)
    let error
    try {
      await vm.remove()
    } catch (e) {
      error = e
    }

    // 削除APIの呼び出し確認
    expect(deleteHandler).toBeCalledTimes(1)
    // エラーが発生したことを確認
    expect(error).toBeInstanceOf(Error)
  })
})
