次の変数グループはパイプラインで参照されるため定義が必要です。

| グループ | 説明 |
|---------|------|
| common | 全てのパイプラインから共通で利用される値を定義します。 |
| develop | 開発環境用のビルドやデプロイのパイプラインで利用される値を定義します。 |
| production | プロダクション用のビルドやデプロイのパイプラインで利用される値を定義します。 |

次の値は common の変数グループに設定が必要な変数です。アプリケーションの要件によっては内容が可変するため、都度調整してください。

| 変数名 | 概要 | 値 | 備考 |
|--|--|--|--|
| azureSubscription | パイプラインのデプロイ先 Azureサブスクリプションを指定します | Azure DevOps に作成された Service Connection名を指定 |  |
| buildConfiguration | .NETアプリケーションのビルド種別を指定します | Release |  |
| resourceGroupName | デプロイ先となるAzure リソースグループ名を指定します | Azure リソースグループ名を指定  |  |
| vmImageName | Azure Pipeline Agent のOSイメージを指定します | ubuntu-18.04 | App Service Environmentの構成により利用しない場合があります |