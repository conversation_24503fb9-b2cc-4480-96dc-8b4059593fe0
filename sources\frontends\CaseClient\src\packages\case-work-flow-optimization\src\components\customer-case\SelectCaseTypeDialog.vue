<template>
  <VDialog v-model="dialog" @click:outside="cancel" @keydown.esc="cancel">
    <AppSimpleDialogTmpl
      :action-button-hide="true"
      dialog-title="追加案件種別選択"
      @cancel="cancel"
    >
      <VExpansionPanels variant="popout" class="my-4">
        <VExpansionPanel v-for="menu in menus" :key="menu.primaryTitle">
          <VExpansionPanelTitle>
            {{ menu.primaryTitle }}
          </VExpansionPanelTitle>
          <VExpansionPanelText>
            <VCardActions>
              <VRow>
                <VCol
                  v-for="card in menu.cards"
                  :key="card.title"
                  cols="12"
                  sm="6"
                  md="6"
                  lg="4"
                >
                  <VCard
                    height="60"
                    min-width="300"
                    class="menu-card"
                    hover
                    ripple
                    :target="isFromCommunicationPlan ? '_blank' : undefined"
                    nuxt
                    @click="edit(card.path)"
                  >
                    <VCardTitle>
                      {{ card.title }}
                    </VCardTitle>
                  </VCard>
                </VCol>
              </VRow>
            </VCardActions>
          </VExpansionPanelText>
        </VExpansionPanel>
      </VExpansionPanels>
    </AppSimpleDialogTmpl>
  </VDialog>
</template>
<script setup lang="ts">
import { useFeatureManager } from '@ibp/common-case/src/composables/shared/useFeatureManager'
export type OpenParameter = {
  from?: string
  customerIdentificationId?: string
  communicationPlanCategory?: string
  businessUnderstandingId?: string
  taskId?: string
}

// 利用側で expose したメソッドを認識しないため、こちらで型定義を行って export する
export interface SelectCaseTypeDialogType {
  // 定義した open メソッドと一致するように型を定義します。
  open(opts?: OpenParameter): Promise<{ isOk: boolean }>
}

// =====================================================================================================================
// 画面全体で利用するデータの定義
// =====================================================================================================================

// 現在useFeatureManagerにgetMAMatchingFeatureFlagが存在しないため、コメントアウト
const { getRecruitMatchingFeatureFlag, getFundingSupportFeatureFlag } = useFeatureManager()
// ダイアログの表示非表示
const dialog = ref(false)
const resolve = ref<((value: { isOk: boolean }) => void) | null>(null)

const dialogData = ref<{
  from?: string
  customerIdentificationId?: string
  communicationPlanCategory?: string
  businessUnderstandingId?: string
  taskId?: string
}>({
  from: '',
  customerIdentificationId: '',
  communicationPlanCategory: '',
  businessUnderstandingId: '',
  taskId: '',
})

const nextPathQueryParams = computed(() => {
  let query = `from=${dialogData.value.from}&customerIdentificationId=${dialogData.value.customerIdentificationId}`
  if (dialogData.value.communicationPlanCategory) {
    query += `&communicationPlanCategory=${dialogData.value.communicationPlanCategory}`
  }
  if (dialogData.value.businessUnderstandingId) {
    query += `&businessUnderstandingId=${dialogData.value.businessUnderstandingId}`
  }
  if (dialogData.value.taskId) {
    query += `&taskId=${dialogData.value.taskId}`
  }

  return query
})

const nextPathQueryParamsForIssueProject = computed(() => {
  let query = `from=${dialogData.value.from}&customerIdentificationId=${dialogData.value.customerIdentificationId}`
  if (dialogData.value.communicationPlanCategory) {
    query += `&communicationPlanCategory=${dialogData.value.communicationPlanCategory}`
  }
  if (dialogData.value.businessUnderstandingId) {
    query += `&businessUnderstandingId=${dialogData.value.businessUnderstandingId}`
  }
  if (
    dialogData.value.taskId &&
    dialogData.value.communicationPlanCategory === '課題の仮説協議'
  ) {
    // 課題の仮説協議のときのみ課題案件画面で初期表示を行うようにする
    query += `&hypotheticalDiscussionOfIssuesId=${dialogData.value.taskId}`
  }
  if (dialogData.value.taskId) {
    query += `&taskId=${dialogData.value.taskId}`
  }
  return query
})

const nextPathQueryParamsForMatching = computed(() => {
  let query = `from=${dialogData.value.from}&customerIdentificationId=${dialogData.value.customerIdentificationId}`
  if (dialogData.value.communicationPlanCategory) {
    query += `&communicationPlanCategory=${dialogData.value.communicationPlanCategory}`
  }
  if (dialogData.value.taskId) {
    query += `&taskId=${dialogData.value.taskId}`
  }
  if (dialogData.value.businessUnderstandingId) {
    query += `&businessUnderstandingId=${dialogData.value.businessUnderstandingId}`
  }

  return query
})

const menus = computed(() => {
  const editPath = `/customer-case/edit?${nextPathQueryParams.value}`
  const menuList = [
    {
      primaryTitle: '課題案件',
      cards: [
        {
          title: '課題案件',
          path: `/issue-project/edit?${nextPathQueryParamsForIssueProject.value}`,
        },
      ],
    },
    {
      primaryTitle: '融資案件',
      cards: [
        {
          title: '融資案件（新規）',
          path: `${editPath}&caseCategory=NewLoan`,
        },
        {
          title: '融資案件（その他）',
          path: `${editPath}&caseCategory=OtherLoan`,
        },
        {
          title: '融資案件（未整理）',
          path: `${editPath}&caseCategory=UnorganizedLoan`,
        },
        ...(getFundingSupportFeatureFlag.value
          ? [
              {
                title: '融資案件（証書貸付）',
                path: `/funding-support/case/deed-loan/edit?${nextPathQueryParams.value}`,
              },
              {
                title: '融資案件（商業手形）',
                path: `/funding-support/case/commercial-bill/edit?${nextPathQueryParams.value}`,
              },
              {
                title: '融資案件（支払承諾）',
                path: `/funding-support/case/acceptance-and-guarantee/edit?${nextPathQueryParams.value}`,
              },
              {
                title: '融資案件（外為取引）',
                path: `/funding-support/case/foreign-exchange-transaction/edit?${nextPathQueryParams.value}`,
              },
              {
                title: '融資案件（極度）',
                path: `/funding-support/case/credit-limit/edit?${nextPathQueryParams.value}`,
              },
              {
                title: '融資案件（条件変更）',
                path: `/funding-support/case/loan-restructuring/edit?${nextPathQueryParams.value}`,
              },
              {
                title: '融資案件（その他稟議）',
                path: `/funding-support/case/other-approval/edit?${nextPathQueryParams.value}`,
              },
            ]
          : []),
      ],
    },
    {
      primaryTitle: 'リース案件',
      cards: [
        {
          title: 'リース案件（新規）',
          path: `${editPath}&caseCategory=NewLease`,
        },
        {
          title: 'リース案件（他社満了）',
          path: `${editPath}&caseCategory=ExternallyCompletedLease`,
        },
      ],
    },
    {
      primaryTitle: '保険案件',
      cards: [
        {
          title: '法人保険案件（生保）',
          path: `${editPath}&caseCategory=CorporateLifeInsurance`,
        },
        {
          title: '法人保険案件（損保）',
          path: `${editPath}&caseCategory=CorporateNonlifeInsurance`,
        },
      ],
    },
    {
      primaryTitle: '投資案件',
      cards: [
        {
          title: '投資案件（新規）',
          path: `${editPath}&caseCategory=NewInvestment`,
        },
      ],
    },
    {
      primaryTitle: 'ビジネスマッチング案件',
      cards: [
        {
          title: '売り案件',
          path: `/matching/business-matching/sell-case/edit?${nextPathQueryParamsForMatching.value}`,
        },
        {
          title: '買い案件',
          path: `/matching/business-matching/buy-case/edit?${nextPathQueryParamsForMatching.value}`,
        },
      ],
    },
    {
      primaryTitle: 'M&Aマッチング案件',
      cards: [
        {
          title: '売り案件',
          path: `/matching/ma-matching/sell-case/edit?${nextPathQueryParamsForMatching.value}`,
        },
        {
          title: '買い案件',
          path: `/matching/ma-matching/buy-case/edit?${nextPathQueryParamsForMatching.value}`,
        },
      ],
    },
  ]
  if (getRecruitMatchingFeatureFlag) {
    menuList.push({
      primaryTitle: '人材紹介案件',
      cards: [
        {
          title: '正社員',
          path: `/matching/recruit-matching/permanent-job-case/edit?${nextPathQueryParamsForMatching.value}`,
        },
        {
          title: '副業',
          path: `/matching/recruit-matching/side-job-case/edit?${nextPathQueryParamsForMatching.value}`,
        },
      ],
    })
  }

  menuList.push({
    primaryTitle: '総合取引案件',
    cards: [
      {
        title: '総合取引案件',
        path: `${editPath}&caseCategory=GeneralTransaction`,
      },
    ],
  })

  return menuList
})

const isFromCommunicationPlan = computed(() => {
  return dialogData.value.from === 'communicationPlan'
})

// =====================================================================================================================
// 画面で使用するメソッドの定義
// =====================================================================================================================
function open(options: OpenParameter = {}): Promise<{ isOk: boolean }> {
  setOpenParameter(options)
  // ダイアログを表示します。
  dialog.value = true

  // 呼び出し元に Promise を返します。
  return new Promise((_resolve) => {
    resolve.value = _resolve
  })
}

function setOpenParameter(options: OpenParameter) {
  dialogData.value.from = options.from
  dialogData.value.customerIdentificationId = options.customerIdentificationId
  dialogData.value.communicationPlanCategory = options.communicationPlanCategory
  dialogData.value.businessUnderstandingId = options.businessUnderstandingId
  dialogData.value.taskId = options.taskId
}

async function edit(path: string) {
  if (path.startsWith('/customer-case/edit') || path.startsWith('/issue-project/edit')) {
    // 人材紹介案件のパスで、かつコミュニケーションプランからの遷移の場合は、外部リンクとして開く
    return navigateTo(path)
  } else {
    const currentUrl = new URL(window.location.href)
    const fullUrl = currentUrl.origin + path
    return navigateTo(fullUrl, {
      external: true,
    })
  }
}

// 選択がされずに閉じられる/キャンセルボタンがクリックされたときの処理をします。
function cancel() {
  if (resolve.value) {
    resolve.value({ isOk: false })
  }
  // ダイアログを閉じます。
  dialog.value = false
}

onBeforeRouteLeave((_to, _from, next) => {
  if (dialog.value) {
    dialog.value = false
  }
  next()
})

// expose を使用してコンポーネントのメソッドを外部に公開
defineExpose({
  open,
})
</script>
