<!-- TODO: API名 取り扱う集約のルートエンティティ名 + 操作API -->
# 3C分析（ThreeCAnalysis）操作API

## 概要
<!-- TODO: APIの概要を説明します -->
- 3C分析集約（ThreeCAnalysisAggregate）を扱うAPIです。

## 所属マイクロサービス

<!-- TODO: 対象APIが所属するマイクロサービスを記述します -->
- 事業性理解マイクロサービス
  - マイクロサービスのパス: `/ibp-customer-understanding`

## ベースURL
<!--
  TODO: 対象APIに接続するためのベースURLを記述します 
    - 必要に応じて複数記述することも可です
    - 特に指定がない場合、"/v{version}/[操作対象集約のルートエンティティ名（PascalCase）]"のみとします
-->

- `/v{version}/ThreeCAnalysis`

## エンドポイント一覧
<!-- TODO: 必要な処理について全て列挙します -->

<!-- TODO: エンドポイントのタイトル（処理の物理名） -->
### 3C分析の取得（Get）

#### エンドポイント
<!--
　TODO: エンドポイントのパス 
    - HTTPメソッドとパスを記述します
    - シンプルなAPIの場合、以下のように定義します
      - 集約のIDによる単一データ取得：`GET [ベースURL]`
      - 複数データ取得：`GET [ベースURL]/find`
      - データ作成：`POST [ベースURL]`
      - データ更新：`PUT [ベースURL]/{id}`
      - データ削除：`DELETE [ベースURL]/{id}`
    - その他のAPIの場合のパスは[ベースURL]の後に任意のパスを記述してください
      - 例）
        - 集約ID以外での単一データ取得：`GET [ベースURL]/sample-code/{sampleCode}`
        - 履歴を一緒に取得する単一データ取得：`GET [ベースURL]/with-history/{id}`
-->
- `GET [ベースURL]/{id}`

#### 概要
<!-- TODO: 対象エンドポイントの処理概要を記述します -->

指定されたIDの3C分析データを取得します。

#### パラメータ
<!-- TODO: 対象エンドポイントで利用するパラメータを記述します -->

| パラメータ名 | 型 | 必須 | 場所 | 説明 |
|-------------|---|-----|------|------|
| version | string | ✓ | path | APIバージョン |
| id | string | ✓ | path | 3C分析ID |

#### HTTPステータス
<!-- TODO: エ対象APIで返すHTTPステータスを列挙します -->

- **200 OK**: 3C分析データの取得成功
- **400 Bad Request**: 無効なリクエスト
- **404 Not Found**: 指定されたIDの3C分析が見つからない
- **500 Internal Server Error**: サーバーエラー

#### レスポンスボディスキーマ
<!-- TODO: 対象エンドポイントで返すレスポンスボディのスキーマを記述します -->
```typescript
{
  id: string;
  customer: string;
  company: string;
  competitor: string;
  updatedDateTime: string;
  updaterId: string;
  updaterName: string;
  businessUnderstandingId: string;
  version: string;
}
```

#### 処理定義
<!-- TODO: 対象エンドポイントで行う処理を記述します -->

1. IDが指定されていない場合、例外をスローする
1. 指定されたIDにて3C分析データを取得し返却する

---

<!-- TODO: エンドポイントのタイトル（処理の物理名） -->
### 3C分析の履歴付き取得（GetWithHistory）

#### エンドポイント
<!--
　TODO: エンドポイントのパス 
    - HTTPメソッドとパスを記述します
    - シンプルなAPIの場合、以下のように定義します
      - 単一データ取得：`GET [ベースURL]`
      - 複数データ取得：`GET [ベースURL]/find`
      - データ作成：`POST [ベースURL]`
      - データ更新：`PUT [ベースURL]/{id}`
      - データ削除：`DELETE [ベースURL]/{id}`
    - その他のAPIの場合のパスは[ベースURL]の後に任意のパスを記述してください
-->
- `GET [ベースURL]/with-history/{id}`

#### 概要
<!-- TODO: 対象エンドポイントの処理概要を記述します -->
指定されたIDの3C分析データを履歴情報と合わせて取得します。

#### パラメータ
<!-- TODO: 対象エンドポイントで利用するパラメータを記述します -->
| パラメータ名 | 型 | 必須 | 場所 | 説明 |
|-------------|---|-----|------|------|
| version | string | ✓ | path | APIバージョン |
| id | string | ✓ | path | 3C分析ID |

#### HTTPステータス
<!-- TODO: エ対象APIで返すHTTPステータスを列挙します -->

- **200 OK**: 3C分析データの取得成功
- **400 Bad Request**: 無効なリクエスト
- **404 Not Found**: 指定されたIDの3C分析が見つからない
- **500 Internal Server Error**: サーバーエラー

#### レスポンスボディスキーマ
<!-- TODO: 対象エンドポイントで返すレスポンスボディのスキーマを記述します -->
```typescript
{
  id: string;
  customer: string;
  company: string;
  competitor: string;
  updatedDateTime: string;
  updaterId: string;
  updaterName: string;
  businessUnderstandingId: string;
  histories: Array<{
    id: string;
    customer: string;
    company: string;
    competitor: string;
    updatedDateTime: string;
    updaterId: string;
    updaterName: string;
    originalId: string;
  }>;
  version: string;
}
```

#### 処理定義
<!-- TODO: 対象エンドポイントで行う処理を記述します -->

1. IDが指定されていない場合、例外をスローする
1. 指定されたIDにて3C分析データを取得する
1. 取得したデータに紐づく履歴情報を取得する
1. 取得した3C分析データに履歴情報を追加して返却する

---

<!-- TODO: エンドポイントのタイトル（処理の物理名） -->
### 事業性理解IDによる3C分析取得（GetByBusinessUnderstandingId）

#### エンドポイント
<!--
　TODO: エンドポイントのパス 
    - HTTPメソッドとパスを記述します
    - シンプルなAPIの場合、以下のように定義します
      - 単一データ取得：`GET [ベースURL]`
      - 複数データ取得：`GET [ベースURL]/find`
      - データ作成：`POST [ベースURL]`
      - データ更新：`PUT [ベースURL]/{id}`
      - データ削除：`DELETE [ベースURL]/{id}`
    - その他のAPIの場合のパスは[ベースURL]の後に任意のパスを記述してください
-->
`GET [ベースURL]/business-understanding/{id}`

#### 概要
<!-- TODO: 対象エンドポイントの処理概要を記述します -->
指定された事業性理解IDに関連する3C分析データを取得します。

#### パラメータ
<!-- TODO: 対象エンドポイントで利用するパラメータを記述します -->
| パラメータ名 | 型 | 必須 | 場所 | 説明 |
|-------------|---|-----|------|------|
| version | string | ✓ | path | APIバージョン |
| id | string | ✓ | path | 事業性理解ID |

#### HTTPステータス
<!-- TODO: エ対象APIで返すHTTPステータスを列挙します -->

- **200 OK**: 3C分析データの取得成功
- **400 Bad Request**: 無効なリクエスト
- **404 Not Found**: 指定された事業性理解IDに関連する3C分析が見つからない
- **500 Internal Server Error**: サーバーエラー

#### レスポンスボディスキーマ
<!-- TODO: 対象エンドポイントで返すレスポンスボディのスキーマを記述します -->
```typescript
{
  id: string;
  customer: string;
  company: string;
  competitor: string;
  updatedDateTime: Date;
  updaterId: string;
  updaterName: string;
  businessUnderstandingId: string;
  version: string;
}
```

#### 処理定義
<!-- TODO: 対象エンドポイントで行う処理を記述します -->

1. リクエストパラメータが存在しない場合、例外をスローする
1. 指定された事業性理解IDにて3C分析データを取得し返却する

---

<!-- TODO: エンドポイントのタイトル（処理の物理名） -->
### 3C分析検索（Find）

#### エンドポイント
<!--
　TODO: エンドポイントのパス 
    - HTTPメソッドとパスを記述します
    - シンプルなAPIの場合、以下のように定義します
      - 単一データ取得：`GET [ベースURL]`
      - 複数データ取得：`GET [ベースURL]/find`
      - データ作成：`POST [ベースURL]`
      - データ更新：`PUT [ベースURL]/{id}`
      - データ削除：`DELETE [ベースURL]/{id}`
    - その他のAPIの場合のパスは[ベースURL]の後に任意のパスを記述してください
-->
- `GET [ベースURL]/find`

#### 概要
<!-- TODO: 対象エンドポイントの処理概要を記述します -->
指定された条件で3C分析データを検索します。

#### パラメータ
<!-- TODO: 対象エンドポイントで利用するパラメータを記述します -->
| パラメータ名 | 型 | 必須 | 場所 | 説明 |
|-------------|---|-----|------|------|
| version | string | ✓ | path | APIバージョン |
| company | string |  | query | 検索条件：企業名（前方一致） |

#### HTTPステータス
<!-- TODO: エ対象APIで返すHTTPステータスを列挙します -->

- **200 OK**: 3C分析データの取得成功
- **400 Bad Request**: 無効なリクエスト
- **500 Internal Server Error**: サーバーエラー

#### レスポンスボディスキーマ
<!-- TODO: 対象エンドポイントで返すレスポンスボディのスキーマを記述します -->
```typescript
{
  items: Array<{
    id: string;
    customer: string;
    company: string;
    competitor: string;
    updatedDateTime: Date;
    updaterId: string;
    updaterName: string;
    businessUnderstandingId: string;
    version: string;
  }>;
  total: number;
}
```

#### 処理定義
<!-- TODO: 対象エンドポイントで行う処理を記述します -->

1. リクエストパラメータが存在しない場合、例外をスローする
1. 指定された条件にて3C分析データを検索し返却する

---

<!-- TODO: エンドポイントのタイトル（処理の物理名） -->
### 3C分析の更新（Put）

#### エンドポイント
<!--
　TODO: エンドポイントのパス 
    - HTTPメソッドとパスを記述します
    - シンプルなAPIの場合、以下のように定義します
      - 単一データ取得：`GET [ベースURL]`
      - 複数データ取得：`GET [ベースURL]/find`
      - データ作成：`POST [ベースURL]`
      - データ更新：`PUT [ベースURL]/{id}`
      - データ削除：`DELETE [ベースURL]/{id}`
    - その他のAPIの場合のパスは[ベースURL]の後に任意のパスを記述してください
-->
- `PUT [ベースURL]`

#### 概要
<!-- TODO: 対象エンドポイントの処理概要を記述します -->
3C分析データを更新し、更新後のデータを返します。

#### パラメータ
<!-- TODO: 対象エンドポイントで利用するパラメータを記述します -->
| パラメータ名 | 型 | 必須 | 場所 | 説明 |
|-------------|---|-----|------|------|
| version | string | ✓ | path | APIバージョン |

#### リクエストボディスキーマ
<!--
　TODO: 対象エンドポイントで受け取るリクエストボディのスキーマを記述します
    - HTTPメソッドがPOST/PUTなどリクエストボディを伴う場合に記述します
-->
```typescript
{
  id: string;
  customer: string;
  company: string;
  competitor: string;
  updatedDateTime: string;
  updaterId: string;
  updaterName: string;
  version: string;
}
```

#### リクエスト内容詳細
<!--
　 TODO: 対象エンドポイントで受け取るリクエストの内容を説明します
    - HTTPメソッドがPOST/PUTなどリクエストボディを伴う場合に記述します
-->

| フィールド名 | 型 | 必須 | 説明 |
|-------------|---|-----|------|
| id | string | ✓ | 3C分析ID |
| customer | string | | 顧客（Customer）分析 |
| company | string | | 企業（Company）分析 |
| competitor | string | | 競合（Competitor）分析 |
| updatedDateTime | string | | 更新日時（ISO 8601形式） |
| updaterId | string | ✓ | 更新者ID |
| updaterName | string | ✓ | 更新者名 |
| version | string | ✓ | バージョン |

#### HTTPステータス
<!-- TODO: エ対象APIで返すHTTPステータスを列挙します -->

- **200 OK**: 3C分析の更新成功
- **400 Bad Request**: 無効なリクエスト
- **404 Not Found**: 指定されたIDの3C分析が見つからない
- **422 Unprocessable Entity**: バリデーションエラー
- **500 Internal Server Error**: サーバーエラー

#### レスポンスボディスキーマ
<!-- TODO: 対象エンドポイントで返すレスポンスボディのスキーマを記述します -->
```typescript
{
  id: string;
  customer: string;
  company: string;
  competitor: string;
  updatedDateTime: Date;
  updaterId: string;
  updaterName: string;
  businessUnderstandingId: string;
  version: string;
}
```

#### 処理定義
<!-- TODO: 対象エンドポイントで行う処理を記述します -->

1. リクエストデータが存在しない場合、例外をスローする
1. 既存データを取得する
  - 取得できなかった場合、例外をスローする
1. 既存データを履歴データとして追加する
1. 既存データをリクエストデータで上書きする
1. 事業性理ファサードの更新日時更新を実施します
    - 事業性理解操作APIの事業性理解更新日時更新を呼び出します
1. 上書きしたデータを保存し、更新後のデータを返却する

---

## エラーレスポンス
<!-- TODO: エラー時のレスポンスの説明を行います -->

全エンドポイント共通で以下のエラーレスポンスを返します。

#### レスポンスボディスキーマ
<!-- TODO: システム全体で共通的なレスポンスのスキーマです -->
```typescript
{
  error: {
    code: string;
    message: string;
    details: string;
  }
}
```

## 注意事項
<!-- TODO: 共通的な注意事項です -->

1. すべてのAPIエンドポイントは認証が必要です（実装詳細は別途参照）
2. APIバージョンは必須パラメータです
3. リクエスト/レスポンスのContent-Typeは`application/json`です
4. 日時形式はISO 8601形式（UTC）を使用します

