import { z } from 'zod'

/**
 * 協議のリアクションで使用するスキーマの定義
 */

// スレッド
export const threadReactionSchema = z.object({
  id: z.string(),
  threadId: z.string(),
  reactionType: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  updatedDateTime: z.date(),
  version: z.string(),
})

export type ThreadReactionType = z.infer<typeof threadReactionSchema>

// コメント
export const commentReactionSchema = z.object({
  id: z.string(),
  commentId: z.string(),
  reactionType: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  updatedDateTime: z.string().datetime(),
  version: z.string(),
})

export type CommentReactionType = z.infer<typeof commentReactionSchema>

export const REACTION_TYPE = {
  0: '未定義',
  1: 'いいね・確認しました',
  2: 'おめでとう・契約お疲れ様',
  3: 'OK',
  4: '驚き',
} as const
