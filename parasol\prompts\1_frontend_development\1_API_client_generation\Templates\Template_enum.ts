// 列挙型の定義
export const ACCOUNT_TYPE = {
  CommercialBill: '商業手形',
  BillLoan: '手形貸付',
  DeedLoanPrincipal: '証貸元金',
  DeedLoanLevelPayment: '証貸元利',
  AgencyLoan: '代理貸付',
  AcceptanceAndGuarantee: '支払承諾',
  Overdraft: '当座貸越',
  GeneralOverdraft: '当貸一般',
  BusinessCardLoan: 'BCL',
  ForeignExchangeTransaction: '外為取引',
  Custom: '指定なし',
} as const

// 列挙型の型定義
export type AccountType = keyof typeof ACCOUNT_TYPE

// 列挙型のキー一覧
export const accountTypes = readonly(Object.keys(ACCOUNT_TYPE) as [AccountType])

// 列挙型をOptions形式で定義
export const accountTypeOptions = Object.freeze(
  accountTypes.map(value => ({ value, title: ACCOUNT_TYPE[value] })),
)

// 引数から列挙型のキーに含まれるものを抽出
export function convertToAccountTypes(values: string[] | string | undefined) {
  if (!values) return []
  return (Array.isArray(values) ? values : [values]).filter(x => x in ACCOUNT_TYPE) as AccountType[]
}

// 列挙型のキーから値を取得する関数
export function toAccountTypeName(value?: AccountType | null) {
  return value ? ACCOUNT_TYPE[value] : undefined
}

/* 値が設定されている場合はこちら
// 列挙型の定義
const EXPERIENCE_TYPE = {
  '-1': '',
  '0': '0 : 社長経験3年未満、経験なし',
  '1': '1 : 社長経験3年以上',
  '2': '2 : 社長経験10年以上',
} as const;

// 列挙型の型定義
export type ExperienceType = keyof typeof EXPERIENCE_TYPE

// 列挙型のキー一覧
export const experienceTypes = readonly(Object.keys(EXPERIENCE_TYPE) as [ExperienceType])

// 列挙型をOptions形式で定義
export const experienceTypeOptions = Object.freeze(
  experienceTypes.map(value => ({ value, title: EXPERIENCE_TYPE[value] })),
)

// 引数から列挙型のキーに含まれるものを抽出
export function convertToExperienceTypes(values: string[] | string | undefined) {
  if (!values) return []
  return (Array.isArray(values) ? values : [values]).filter(x => x in EXPERIENCE_TYPE) as AccountType[]
}

// 列挙型のキーから値を取得する関数
export function toExperienceTypeName(value?: ExperienceType | null) {
  return value ? EXPERIENCE_TYPE[value] : undefined
}
*/

