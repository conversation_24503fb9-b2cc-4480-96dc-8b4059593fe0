import { CASE_CATEGORIES } from '@ibp/common-case/src/constants/domain/case'
import { useDateFormatters } from '@/packages/common-case/src/utils/dateFormatters'

// 日付フォーマット関数を取得
const { formatDateYMD } = useDateFormatters()
/**
 * 案件一覧のテーブルヘッダー設定を取得する
 * @returns ヘッダー設定の配列
 */
export const getIdentifiedCaseTableHeaders = () => [
  {
    title: '登録日',
    key: 'registeredAt',
    format: (val: any) => (val ? formatDateYMD(val) : '-'),
    sortable: true,
  },
  {
    title: 'カテゴリ',
    key: 'caseCategory',
    format: (val: any) => {
      const item = CASE_CATEGORIES.find((x) => x.value === val)
      return item?.title ?? '-'
    },
    sortable: true,
  },
  {
    title: '案件項目',
    key: 'generalTransactionTypeName',
    sortable: true,
  },
  {
    title: '案件名',
    key: 'caseName',
    sortable: true,
    type: 'maskTarget',
  },
  {
    title: 'ステータス',
    key: 'caseStatus',
    format: (val: any) => val || '-',
    sortable: true,
  },
  {
    title: '期日',
    key: 'expiredAt',
    format: (val: any) => (val ? formatDateYMD(val) : '-'),
    sortable: true,
  },
  {
    title: '案件担当者',
    key: 'staffName',
    format: (val: any) => val || '-',
    sortable: true,
  },
  {
    title: '最終更新日',
    key: 'caseUpdatedAt',
    format: (val: any) => (val ? formatDateYMD(val) : '-'),
    sortable: true,
  },
]

/**
 * 案件一覧のテーブルヘッダー設定を取得する（詳細版）
 * @returns ヘッダー設定の配列
 */
export const getCaseTableHeaders = (branchOptions?: { title: string, value: string }[]) => [
  {
    title: '登録日',
    key: 'registeredAt',
    format: (val: any) => (val ? formatDateYMD(val) : '-'),
    sortable: true,
  },
  {
    title: '店番',
    key: 'branchNumber',
    format: (val: any) => {
      if (val && branchOptions) {
        return branchOptions.find((x) => x.value === val)?.title ?? val + ':'
      }
      return val || '-'
    },
    sortable: true,
  },
  {
    title: 'CIF番号',
    key: 'cifNumber',
    format: (val: any) => val || '-',
    sortable: true,
  },
  { title: '氏名', key: 'customerName', sortable: true },
  {
    title: 'カテゴリ',
    key: 'caseCategory',
    format: (val: any) => {
      const item = CASE_CATEGORIES.find((x) => x.value === val)
      return item?.title ?? '-'
    },
    sortable: true,
  },
  {
    title: '案件項目',
    key: 'generalTransactionTypeName',
    sortable: true,
  },
  {
    title: '案件名',
    key: 'caseName',
    sortable: true,
    type: 'maskTarget',
  },
  {
    title: 'ステータス',
    key: 'caseStatus',
    format: (val: any) => val || '-',
    sortable: true,
  },
  {
    title: '期日',
    key: 'expiredAt',
    format: (val: any) => (val ? formatDateYMD(val) : '-'),
    sortable: true,
  },
  {
    title: '案件担当者',
    key: 'staffName',
    format: (val: any) => val || '-',
    sortable: true,
  },
  {
    title: '顧客担当者',
    key: 'customerStaffName',
    format: (val: any) => val || '-',
    sortable: true,
  },
  {
    title: '最終更新日',
    key: 'caseUpdatedAt',
    format: (val: any) => (val ? formatDateYMD(val) : '-'),
    sortable: true,
  },
  {
    title: 'お気に入り',
    key: 'isFavorite',
    sortable: true,
  },
]
