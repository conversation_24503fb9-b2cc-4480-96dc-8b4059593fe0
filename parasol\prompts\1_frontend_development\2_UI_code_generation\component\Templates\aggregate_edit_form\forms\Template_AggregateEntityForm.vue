<!--
===========================================
アグリゲートエンティティFormコンポーネント テンプレート
===========================================

このテンプレートは、アグリゲートルートエンティティの各プロパティに対応する
Formコンポーネントの基本構造を提供します。

【使用方法】
1. TODOコメントに従って、エンティティ固有の情報に置き換える
2. 不要なフィールドは削除し、必要なフィールドを追加する
3. レイアウト（v-row/v-col）を調整する
-->

<script setup lang="ts">
import type { ErrorMessage } from '@hox/base/src/composables/shared/useValidation'
// TODO: エンティティの型をインポート（例: SampleEntity、YourAggregateEntity など）
import type { SampleEntity as AggregateEntity } from '../../constants/domain/entities/SampleEntity'
// TODO: 選択肢の取得などで利用するcomposableをインポート
import { useGetTmpData } from '../../composables/useGetTmpData'

// 共通のprops定義
const props = defineProps<{
  loading: boolean
  disabled?: boolean
  errorMessages?: ErrorMessage
}>()

// 共通のemit定義
const emit = defineEmits<{
  (e: 'validate', key: string): unknown
}>()

// TODO: エンティティの型に合わせてmodel定義を変更（AggregateEntity → YourAggregateEntity）
const model = defineModel<Partial<AggregateEntity>>({ default: () => ({}) })

// TODO: 必要に応じて特定のcomposablesを使用（'TmpData' → 'YourReferenceData'）
const {
  fetchTmpData, 
} = useGetTmpData()
const tmpData = fetchTmpData()

// 共通のcomputed properties
const disabled = computed(() => props.disabled ?? false)
const clearable = computed(() => !disabled.value)

// TODO: エンティティの型に合わせてerrorMessages定義を変更（AggregateEntity → YourAggregateEntity）
const errorMessages = computed(
  () =>
    (props.errorMessages ?? {}) as Record<keyof AggregateEntity, string[]>,
)

// 共通のvalidateItem関数
// TODO: エンティティの型に合わせてkey型を変更（AggregateEntity → YourAggregateEntity）
const validateItem = (key: keyof AggregateEntity) => {
  if (props.disabled) return
  emit('validate', key)
}
</script>
<template>
  <!-- 共通のレイアウト構造 -->
  <app-edit-item-group-tmpl>
    <v-container fluid>
      <!-- 行ごとにフィールドをグループ化 -->
      <v-row>
        <!-- ドロップダウン選択（必須） -->
        <!-- TODO: エンティティの具体的なプロパティ名とラベルに変更 -->
        <v-col>
          <app-autocomplete
            v-model="model.densaiType"
            label="でんさい表示"
            item-title="label"
            item-value="value"
            required-mark
            :items="relatedInformation?.densaiTypes"
            :clearable
            :disabled
            :loading
            :error-messages="errorMessages.densaiType"
            @update:model-value="validateItem('densaiType')"
            @blur="validateItem('densaiType')"
          />
        </v-col>
        <!-- 数値入力（無効化） -->
        <!-- TODO: エンティティの具体的なプロパティ名とラベルに変更 -->
        <v-col>
          <app-number-field
            v-model="model.densaiApplyNumber"
            label="でんさい申込番号"
            disabled
            :loading
            :format-options="{ useGrouping: false }"
            :error-messages="errorMessages.densaiApplyNumber"
            @update:model-value="validateItem('densaiApplyNumber')"
            @blur="validateItem('densaiApplyNumber')"
          />
        </v-col>
        <!-- ドロップダウン選択（無効化、必須） -->
        <!-- TODO: エンティティの具体的なプロパティ名とラベルに変更 -->
        <v-col>
          <app-autocomplete
            v-model="model.approvalCategory"
            label="稟議区分"
            item-title="label"
            item-value="value"
            required-mark
            :items="relatedInformation?.approvalCategories"
            disabled
            :loading
            :error-messages="errorMessages.approvalCategory"
            @update:model-value="validateItem('approvalCategory')"
            @blur="validateItem('approvalCategory')"
          />
        </v-col>
        <!-- 空の列：レイアウト調整用 -->
        <v-col />
      </v-row>
      
      <!-- 2行目 -->
      <v-row>
        <!-- 数値入力（サフィックス付き、必須） -->
        <!-- TODO: エンティティの具体的なプロパティ名、ラベル、サフィックスに変更 -->
        <v-col>
          <app-number-field
            v-model="model.lendingScheduledAmount"
            label="貸出予定金額"
            suffix="円"
            required-mark
            :clearable
            :disabled
            :loading
            :error-messages="errorMessages.lendingScheduledAmount"
            @update:model-value="validateItem('lendingScheduledAmount')"
            @blur="validateItem('lendingScheduledAmount')"
          />
        </v-col>
        <!-- 数値入力（サフィックス付き、必須） -->
        <!-- TODO: エンティティの具体的なプロパティ名、ラベル、サフィックスに変更 -->
        <v-col>
          <app-number-field
            v-model="model.numberOfBills"
            label="手形枚数（債権数）"
            suffix="枚"
            required-mark
            :clearable
            :disabled
            :loading
            :error-messages="errorMessages.numberOfBills"
            @update:model-value="validateItem('numberOfBills')"
            @blur="validateItem('numberOfBills')"
          />
        </v-col>
        <!-- 日付選択（必須） -->
        <!-- TODO: エンティティの具体的なプロパティ名とラベルに変更 -->
        <v-col>
          <app-popup-date-only-picker
            v-model="model.loanDate"
            label="実行予定日"
            display-text-format="yyyy/MM/dd"
            required-mark
            :clearable
            :disabled
            :loading
            :error-messages="errorMessages.loanDate"
            @update:model-value="validateItem('loanDate')"
            @blur="validateItem('loanDate')"
          />
        </v-col>
        <!-- 日付選択（必須） -->
        <!-- TODO: エンティティの具体的なプロパティ名とラベルに変更 -->
        <v-col>
          <app-popup-date-only-picker
            v-model="model.maximumBillDueDate"
            label="最長手形期日"
            display-text-format="yyyy/MM/dd"
            required-mark
            :clearable
            :disabled
            :loading
            :error-messages="errorMessages.maximumBillDueDate"
            @update:model-value="validateItem('maximumBillDueDate')"
            @blur="validateItem('maximumBillDueDate')"
          />
        </v-col>
      </v-row>
      
      <!-- 3行目 -->
      <v-row>
        <!-- ドロップダウン選択（特定のcomposableを使用、無効化、必須） -->
        <!-- TODO: エンティティの具体的なプロパティ名、ラベル、itemsに変更 -->
        <v-col>
          <app-autocomplete
            v-model="model.loanTypeCode"
            label="貸出種類コード"
            item-title="label"
            item-value="value"
            required-mark
            :items="tmpData.value"
            disabled
            :loading
            :error-messages="errorMessages.loanTypeCode"
            @update:model-value="validateItem('loanTypeCode')"
            @blur="validateItem('loanTypeCode')"
          />
        </v-col>
        <!-- ドロップダウン選択（無効化、必須） -->
        <!-- TODO: エンティティの具体的なプロパティ名とラベルに変更 -->
        <v-col>
          <app-autocomplete
            v-model="model.loanPurposeCode"
            label="資金使途コード"
            item-title="label"
            item-value="value"
            required-mark
            :items="relatedInformation?.loanPurposeCodes"
            disabled
            :loading
            :error-messages="errorMessages.loanPurposeCode"
            @update:model-value="validateItem('loanPurposeCode')"
            @blur="validateItem('loanPurposeCode')"
          />
        </v-col>
        <!-- 空の列：レイアウト調整用 -->
        <v-col />
        <v-col />
      </v-row>
      
      <!-- 追加フィールドのサンプル -->
      <!-- TODO: 必要に応じて行を追加・削除 -->
      <!--
      <v-row>
        数値コード入力（ヒント付き）
        <v-col>
          <app-numeric-code-field
            v-model="model.yourCodeField"
            label="あなたのコードフィールド"
            hint="ヒントテキストを入力"
            persistent-hint
            maxlength="7"
            fill
            :clearable
            :disabled
            :loading
            :error-messages="errorMessages.yourCodeField"
            @update:model-value="validateItem('yourCodeField')"
            @blur="validateItem('yourCodeField')"
          />
        </v-col>
        数値入力（パーセント、フォーマット指定）
        <v-col>
          <app-number-field
            v-model="model.yourPercentageField"
            label="あなたのパーセンテージフィールド"
            suffix="%"
            :clearable
            :disabled
            :loading
            :format-options="{ minimumFractionDigits: 5 }"
            :error-messages="errorMessages.yourPercentageField"
            @update:model-value="validateItem('yourPercentageField')"
            @blur="validateItem('yourPercentageField')"
          />
        </v-col>
        テキスト入力の例
        <v-col>
          <app-text-field
            v-model="model.yourTextField"
            label="あなたのテキストフィールド"
            required-mark
            :clearable
            :disabled
            :loading
            :error-messages="errorMessages.yourTextField"
            @update:model-value="validateItem('yourTextField')"
            @blur="validateItem('yourTextField')"
          />
        </v-col>
        空の列：レイアウト調整用
        <v-col />
      </v-row>
      -->
    </v-container>
  </app-edit-item-group-tmpl>
</template>

<!--
===========================================
テンプレート使用時の変更点チェックリスト
===========================================

【必須変更事項】
□ エンティティの型定義をインポート（AggregateEntity → YourAggregateEntity）
□ useRelatedInformationStateの引数をエンティティ名に変更
□ 不要なcomposableのインポートを削除、必要なものを追加
□ model、errorMessages、validateItemの型定義を実際のエンティティに変更
□ 各フィールドのv-model、label、プロパティ名を実際のエンティティに合わせて変更
□ 不要なフィールドを削除、必要なフィールドを追加
□ v-row/v-colのレイアウトを調整

【オプション変更事項】
□ カスタムcomposableの追加
□ フィールドタイプの変更（AppAutocomplete → AppTextField等）
□ バリデーションルールの追加
□ 条件分岐による表示制御の追加
-->
