# 課題案件協議一覧ページ定義

## 基本情報

- ページ名 : 課題案件協議一覧
- UIパターン : 検索画面（search）

## 実現対象ハイレベルユースケース

- 課題案件協議を管理する
  - 協議の検索
  - 協議の一覧表示
  - 協議詳細の確認
  - 協議編集画面への遷移

## 共通処理（ミドルウェア）定義

- [ ] アクセスログ出力
- [ ] アクセス可否チェック
- [ ] 次画面パスチェック

## パラメータ定義

| パラメータ名 | 種別 | 必須 | 型 | 説明 |
|---|---|---|---|---|
| customerIdentificationId | query | | 文字列 | 顧客識別ID（指定時は顧客理解レイアウトで表示） |
| from | query | | 文字列 | 遷移元画面識別子 |
| tab | query | | 文字列 | 表示するタブ（case, issue-project, issue-project-task, issue-project-discussion） |

## UI定義

### タブ表示
- 案件一覧タブ
    - 説明 : 一般的な案件の検索・一覧表示
- 課題案件一覧タブ
    - 説明 : 課題案件の検索・一覧表示
- 課題案件タスク一覧タブ
    - 説明 : 課題案件のタスク一覧表示（顧客識別時のみ表示）
- **課題案件協議一覧タブ（現在表示中）**
    - 説明 : 課題案件の協議一覧表示（顧客識別時のみ表示）

### 検索条件
- 登録日From
    - 入力形式 : 日付選択
    - 必須
    - 説明 : "yyyy/MM/dd"形式
- 登録日To
    - 入力形式 : 日付選択
    - 必須
    - バリデーション : 登録日Fromより後の日付、FromとToの期間は1年以内
    - 説明 : "yyyy/MM/dd"形式
- 登録者
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 顧客担当者
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 案件担当者
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 案件担当チーム
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 協議種別
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 検索ボタン
    - 説明 : ※イベント処理参照

### 検索結果一覧
- 表示項目
    - 顧客名（customerName）
    - 課題概要（overview）
        - 説明 : アクセス制限対象の場合はマスキング表示
    - 登録日（registeredDateTime）
        - 説明 : "yyyy/MM/dd"形式で表示
    - 協議名（title）
        - 説明 : アクセス制限対象の場合はマスキング表示
    - 顧客担当者（customerStaffName）
    - 案件担当者（issueProjectStaffName）
    - 案件担当チーム（issueProjectTeam）
    - 協議内容（展開ボタン）
        - 説明 : 行の展開により詳細協議内容を表示
- 機能
    - 行展開機能
        - 説明 : 協議スレッドの詳細内容を展開表示
    - 協議詳細表示
        - 説明 : IssueProjectDiscussionThreadコンポーネントで詳細表示
    - ファイルダウンロード
        - 説明 : スレッドファイル、コメントファイルのダウンロード
    - 行クリック編集
        - 説明 : 協議詳細画面に遷移
    - ページング
        - 説明 : 20件単位でページング表示
    - ソート機能
        - ソート可能項目 : 顧客名、課題概要、登録日、協議名、顧客担当者、案件担当者、案件担当チーム

## イベント処理定義

### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 検索条件選択肢を取得し表示する | ライフサイクルフック mounted |
| 検索ボタン押下 | 入力された条件で課題案件協議を検索し結果を表示する | function search |
| 日付バリデーション | 登録日From/Toの入力値検証を実行する | function validateDate |
| 行展開 | 協議スレッドの詳細内容を表示する | テンプレート slot expand-item |
| 協議詳細表示 | 協議詳細画面に遷移する | function showDetail |
| スレッドファイルダウンロード | スレッドに添付されたファイルをダウンロードする | function downloadThreadFile |
| コメントファイルダウンロード | コメントに添付されたファイルをダウンロードする | function downloadCommentFile |
| 検索条件クリア | 検索条件と結果をクリアする | function clear |
| 検索条件復元 | URLヒストリから検索条件を復元する | function criteriaResotred |

### 初期表示（mounted）
1. 顧客識別IDが指定されている場合は検索条件に設定する。
2. 担当者・チームオプション一覧を取得する。
3. 協議種別オプション一覧を取得する。
4. 検索条件の選択肢を設定する。

### 検索ボタン押下（search）
1. 日付のバリデーションを実行する。
2. 初期検索フラグをチェックし、falseの場合は検索を実行しない。
3. 顧客識別IDが指定されている場合は検索条件に追加する。
4. 課題案件協議検索APIを呼び出し、結果を取得する。
5. 検索結果を整形し、一覧に表示する。
6. アクセス制限対象の課題概要・協議名はマスキング表示する。
7. 検索履歴をブラウザに保存する。

### 日付バリデーション（validateDate）
1. 登録日From、登録日Toの必須チェックを実行する。
2. 登録日ToがFromより後の日付かチェックする。
3. FromとToの期間が1年以内かチェックする。
4. バリデーションエラーがあればエラーメッセージを表示する。

### 行展開（expand-item slot）
1. 対象行の協議スレッド情報を取得する。
2. IssueProjectDiscussionThreadコンポーネントで詳細内容を表示する。
3. 展開領域でのファイルダウンロードやクリックイベントを処理する。

### 協議詳細表示（showDetail）
1. 対象協議のIDをパラメータとして、協議詳細画面に遷移する。

### スレッドファイルダウンロード（downloadThreadFile）
1. 対象スレッドファイルのダウンロード処理を実行する。

### コメントファイルダウンロード（downloadCommentFile）
1. 対象コメントファイルのダウンロード処理を実行する。

### 検索条件クリア（clear）
1. 検索条件と過去の検索履歴をクリアする。
2. 検索結果一覧をクリアする。

### 検索条件復元（criteriaResotred）
1. URLクエリパラメータから検索条件を復元する。
2. 日時条件（fromDate, toDate）をDateオブジェクトに変換する。
3. 復元完了を通知する。
