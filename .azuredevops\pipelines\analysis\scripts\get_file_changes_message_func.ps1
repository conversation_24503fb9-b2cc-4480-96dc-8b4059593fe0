# ファイル変更数の閾値を設定(error, warning)
$filesChangedThreshold = [int[]](50, 10)

# 追加行数の閾値を設定(error, warning)
$insertionsThreshold = [int[]](500, 100)

# 削除行数の閾値を設定(error, warning)
$deletionsThreshold = [int[]](500, 100)

# ファイル毎の追加行数の閾値を設定(error, warning)
$insertionPerFileThreshold = [int[]](60, 30)

# ファイル毎の削除行数の閾値を設定(error, warning)
$deletionPerFileThreshold = [int[]](60, 30)

# 対象件数を設定
$topN = 10

# アイコンを設定
$errorIcon = "❌"
$warningIcon = "⚠️"
$safeIcon = "✅"

<#
.SYNOPSIS
    指定された値が閾値を上回っているかどうかを判定し、エラーまたは警告のアイコンを返します。
.PARAMETER value
    判定する値。
.PARAMETER thresholds
    エラーおよび警告の閾値を含む配列。
.PARAMETER errorOrWarningStatus
    現在のエラーまたは警告のステータス。
.RETURNS
    アイコンと更新されたエラーまたは警告のステータスを返します。
#>

function Get-ErrorOrWarningIcon-Greater-Than {
    param(
        [int]$value,
        [int[]]$thresholds,
        [int]$errorOrWarningStatus
    )

    if ($value -gt $thresholds[0]) {
        # エラーの場合は、エラーアイコンと、引数で渡された$errorOrWarningStatusの値または2の大きい方を返す
        return $errorIcon, [math]::Max($errorOrWarningStatus, 2)
    }
    elseif ($value -gt $thresholds[1]) {
        # 警告の場合は、警告アイコンと、引数で渡された$errorOrWarningStatusの値または1の大きい方を返す
        return $warningIcon, [math]::Max($errorOrWarningStatus, 1)
    }
    else {
        # エラーでも警告でもない場合は、引数で渡された$errorOrWarningStatusの値を返す
        return "", $errorOrWarningStatus
    }
}

<#
.SYNOPSIS
    ファイル変更の統計情報から変更のサイズを解析し、結果のメッセージを生成します。
.PARAMETER changeStats
    ファイル変更の統計情報を含むオブジェクト。
.RETURNS
    生成された変更のサイズのメッセージを返します。
#>

function Get-File-Changes-Message {
    param(
        [PSCustomObject]$changeStats
    )
    
    $errorOrWarningStatus = [int]0

    # $changeStatsの値を集計
    $filesChanged = [int]$changeStats.Count
    $insertions = $changeStats.Values | Where-Object { $_.ShortStatus -ne "A" } | Measure-Object -Property AddedLines -Sum | Select-Object -ExpandProperty Sum
    $deletions = $changeStats.Values | Where-Object { $_.ShortStatus -ne "D" } | Measure-Object -Property DeletedLines -Sum | Select-Object -ExpandProperty Sum
    
    if ($null -eq $insertions) {
        $insertions = 0
    }
    if ($null -eq $deletions) {
        $deletions = 0
    }

    $addedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "A" } | Measure-Object | Select-Object -ExpandProperty Count
    $deletedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "D" } | Measure-Object | Select-Object -ExpandProperty Count
    $modifiedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "M" } | Measure-Object | Select-Object -ExpandProperty Count
    $renamedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "R" } | Measure-Object | Select-Object -ExpandProperty Count
    $otherFiles = $changeStats.Values | Where-Object { $_.ShortStatus -notin @("A", "D", "M", "R") } | Measure-Object | Select-Object -ExpandProperty Count

    # ファイル変更数、追加行数、削除行数の閾値を超えている場合はアイコンを表示
    $filesChanagedIcon, $errorOrWarningStatus = Get-ErrorOrWarningIcon-Greater-Than $filesChanged $filesChangedThreshold $errorOrWarningStatus
    $insertionsIcon, $errorOrWarningStatus = Get-ErrorOrWarningIcon-Greater-Than $insertions $insertionsThreshold $errorOrWarningStatus
    $deletionsIcon, $errorOrWarningStatus = Get-ErrorOrWarningIcon-Greater-Than $deletions $deletionsThreshold $errorOrWarningStatus
    
    # 追加行数が多いTop N のファイルを取得して、次のような形式で表示
    # [追加行数](?_a=files&path=NewFileName|FileName)
    # 行数が0の場合は除外する
    $descendingAddedFiles = $changeStats.Values | Sort-Object -Property AddedLines -Descending | Where-Object { $_.AddedLines -gt 0 -and $_.ShortStatus -ne "A" }
    $topNAddedFiles = $descendingAddedFiles | Select-Object -First $topN
    $topNAddedFilesMessage = ($topNAddedFiles | ForEach-Object {
            $insertionPerFileIcon, $errorOrWarningStatus = Get-ErrorOrWarningIcon-Greater-Than $_.AddedLines $insertionPerFileThreshold $errorOrWarningStatus
        
            # $insertionPerFileIconが空の場合はスキップ
            if ([string]::IsNullOrEmpty($insertionPerFileIcon)) {
                return
            }

            if ($_.NewFileName) {
                $newFileName = Split-Path $_.NewFileName -Leaf
                "<a href=""?_a=files&path=/$([URI]::EscapeUriString($_.NewFileName))"" title=""$newFileName"">$($_.AddedLines)</a>$insertionPerFileIcon"
            }
            else {
                $fileName = Split-Path $_.FileName -Leaf
                "<a href=""?_a=files&path=/$([URI]::EscapeUriString($_.FileName))"" title=""$fileName"">$($_.AddedLines)</a>$insertionPerFileIcon"
            }
        }) -join ", "

    if ([string]::IsNullOrEmpty($topNAddedFilesMessage)) {
        $topNAddedFilesMessage = "Waring, NG なし"
    }

    # 削除行数
    $descendingDeletedFiles = $changeStats.Values | Sort-Object -Property DeletedLines -Descending | Where-Object { $_.DeletedLines -gt 0 -and $_.ShortStatus -ne "D" }
    $topNDeletedFiles = $descendingDeletedFiles | Select-Object -First $topN
    $topNDeletedFilesMessage = ($topNDeletedFiles | ForEach-Object {
            $deletionPerFileIcon, $errorOrWarningStatus = Get-ErrorOrWarningIcon-Greater-Than $_.DeletedLines $deletionPerFileThreshold $errorOrWarningStatus
        
            # $deletionPerFileIconが空の場合はスキップ
            if ([string]::IsNullOrEmpty($deletionPerFileIcon)) {
                return
            }
        
            if ($_.NewFileName) {
                $newFileName = Split-Path $_.NewFileName -Leaf
                "<a href=""?_a=files&path=/$([URI]::EscapeUriString($_.NewFileName))"" title=""$newFileName"">$($_.DeletedLines)</a>$deletionPerFileIcon"
            }
            else {
                $fileName = Split-Path $_.FileName -Leaf
                "<a href=""?_a=files&path=/$([URI]::EscapeUriString($_.FileName))"" title=""$fileName"">$($_.DeletedLines)</a>$deletionPerFileIcon"
            }
        }) -join ", "

    if ([string]::IsNullOrEmpty($topNDeletedFilesMessage)) {
        $topNDeletedFilesMessage = "Waring, NG なし"
    }

    return @"
### 変更のサイズ $(($safeIcon, $warningIcon, $errorIcon)[$errorOrWarningStatus])
<details>
<summary>詳細</summary>

| 項目 | 数 | 詳細 |
| --- | --- | --- |
| __ファイル数__ | $filesChanged$filesChanagedIcon | __追加__: $addedFiles, __変更__: $modifiedFiles, __削除__: $deletedFiles, __リネーム__: $renamedFiles, __その他__: $otherFiles |
| __追加行数__ | $insertions$insertionsIcon | $topNAddedFilesMessage | 
| __削除行数__ | $deletions$deletionsIcon | $topNDeletedFilesMessage |
</details>

"@
}
