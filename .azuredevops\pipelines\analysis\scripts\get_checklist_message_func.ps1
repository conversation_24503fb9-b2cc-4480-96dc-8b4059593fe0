# 対象ファイルのパターンを設定します。
# プルリクエスト内で変更されたファイルのパスが以下のパターンに一致する場合、対応するファイルタイプ(Key)が取得されます。
# チェックリストの個別メッセージに対応するファイルタイプを追加したい場合は、以下に新しいパターンを追加してください。
$patterns = @{
    "Handler"         = "*\services\*\UseCases\*Handler.cs"
    "QueryService"    = "*\services\*\Infrastructure\QueryServices\*.cs"
    "Specification"   = "*\services\*\UseCases\*Specification.cs"
    "Validator"       = "*\services\*\UseCases\*Validator.cs"
    "Service"         = "*\services\*\Services\*.cs"
    "Controller"      = "*\frontends\*\Controllers\*.cs"
    "ServiceProject"  = "*\services\*.cs"
    "FrontendProject" = "*\frontends\*.cs"
    "UseCase"         = "*\services\*\UseCases\*.cs"
    "ProgramCode"     = "*.cs", "*.ts", "*.vue", "*.js", "*.jsx", "*.tsx"
    "Page"            = "*\pages\*.vue"
    "UIComponent"     = "*\components\*.vue"
    "UIUtil"          = "*\utils\*.ts", "*\utils\*.js"
}

# .NET のテスト対象パスを設定します。
$dotNetTestPath = "*\*.Tests\*"

# Node のテスト対象パスを設定します。
$nodeTestPath = "*\test\*"

# テスト対象ファイルのパターンを設定します。
# プルリクエスト内で変更されたファイルのパスが上記のテスト対象パス配下かつ以下のパターンに一致する場合、対応するファイルタイプ(Key)が取得されます。
# テスト対象パス配下でチェックリストの個別メッセージに対応するファイルタイプを追加したい場合は、以下に新しいパターンを追加してください。
$patternsForTest = @{
    "Test" = "*Test.cs", "*.spec.ts", "*.spec.tsx"
}

# 全体メッセージを設定します。
# チェックリストの先頭に表示される全体メッセージを追加したい場合は、以下に新しいメッセージを追加してください。
$allMessages =  @(
    "PRに複数の作業が含まれていないか。含まれている場合、理由は明確か"
)

# チェックリストの個別メッセージと対応するファイルタイプを設定します。
# ファイルタイプのリスト(Value)に一致するファイルタイプが含まれている場合、対応する個別メッセージ(Key)が取得されます。
# チェックリストに表示される個別メッセージを追加したい場合は、以下に新しいメッセージ(Key)とファイルタイプのリスト(Value)を追加してください。
# ファイルタイプのリスト(Value)の項目には、$patternsまたは$patternsForTestに含まれるファイルタイプを指定してください。
$messages = @{
    "利用していないデータをIncludeで取得していないか"                       = @("Specification", "QueryService", "Handler")
    "1度に一括で取得できるデータを1行ずつ取得していないか"                       = @("Handler")
    "Asyncを利用しているか"                                     = @("Handler", "Service", "QueryService")
    "SQLで可能な演算をデータ取得後にメモリで行っていないか"                      = @("QueryService")
    "サービス内でASP.NETやFunctionなどのプラットフォームに依存した機能を利用していないか" = @("ServiceProject")
    "ユースケースレイヤよりも外でドメインレイヤの実装を利用していないか"                 = @("FrontendProject")
    "ユースケースの中で他のユースケースの機能を利用していないか"                     = @("UseCase", "QueryService", "Specification")
    "ユースケース以外からユースケースの機能を利用していないか"                      = @("Service", "QueryService")
    "ユースケース内の各コンポーネントの責務は分離されているか"                      = @("UseCase")
    "メソッドは処理ごとに分割されているか"                                = @("ProgramCode")
    "すべてのアクションに対するテストケースが実装されているか"                      = @("Test", "Handler", "Validator", "QueryService", "Service", "Page", "UIComponent", "UIUtil")
    "データパターンに対するテストが実装されているか"                           = @("Test", "Handler", "Validator", "QueryService", "Service", "Page", "UIComponent", "UIUtil")
    "テストのカバレッジは十分か"                                     = @("Test", "Handler", "Validator", "QueryService", "Service", "Page", "UIComponent", "UIUtil")
    "モックを不適切に利用してテストが意味のないものになっていないか"                   = @("Test", "Handler", "Validator", "QueryService", "Service", "Page", "UIComponent", "UIUtil")
}

<#
.SYNOPSIS
    指定されたファイルパスに対して、対応するファイルタイプを取得します。
.PARAMETER targetPath
    チェックするファイルのパス。
.PARAMETER targetDirectory
    ルートディレクトリのパス。
.RETURNS
    ファイルタイプの配列を返します。
#>

function Get-FileTypes {
    param(
        [string]$targetPath,
        [string]$targetDirectory
    )

    $fileTypes = @()
    $isTestPath = $false

    # テスト対象パスかどうかを判定
    $isTestPath = $targetPath -like $dotNetTestPath -or $targetPath -like $nodeTestPath

    # 適切なパターンセットを選択
    $patternsToUse = $isTestPath ? $patternsForTest : $patterns

    # 各パターンに対してファイルパスが一致するかどうかを確認
    foreach ($key in $patternsToUse.Keys) {
        $patternsForKey = $patternsToUse[$key]

        # パターンが文字列の場合は配列に変換
        if ($patternsForKey -is [string]) {
            $patternsForKey = @($patternsForKey)
        }
        foreach ($pattern in $patternsForKey) {
            if ($targetPath -like "$targetDirectory$pattern") {
                # ファイルタイプが重複しないように追加
                if ($fileTypes -notcontains $key) {
                    $fileTypes += $key
                }
            }
        }
    }

    return $fileTypes
}

<#
.SYNOPSIS
    指定されたファイルタイプに対応するメッセージを取得します。
.PARAMETER fileType
    チェックするファイルタイプ。
.RETURNS
    メッセージの配列を返します。
#>

function Get-MessagesForFileTypes {
    param(
        [string]$fileType
    )

    $messagesForFileTypes = @()

    foreach ($message in $messages.Keys) {
        # ファイルタイプがメッセージに該当する場合は追加
        if ($messages[$message] -contains $fileType) {
            $messagesForFileTypes += $message
        }
    }

    return $messagesForFileTypes
}

<#
.SYNOPSIS
    変更されたファイルに基づいてチェックリストメッセージを生成します。
.PARAMETER changeStats
    変更されたファイルの統計情報を含むオブジェクト。
.PARAMETER rootDirectory
    ルートディレクトリのパス。
.RETURNS
    チェックリストメッセージの文字列。
#>

function Get-CheckList-Message {
    param(
        [PSCustomObject]$changeStats,
        [string]$rootDirectory
    )

    # 変更されたファイルの一覧を取得
    $modifiedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "A" -or $_.ShortStatus -eq "M" } | Select-Object -ExpandProperty FileName
    $renamedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "R" } | Select-Object -ExpandProperty NewFileName
    $targetFiles = $modifiedFiles + $renamedFiles

    $totalFileTypes = @()

    $targetDirectory = Convert-Path $rootDirectory

    foreach ($targetFile in $targetFiles) {
        $targetPath = Convert-Path $targetFile

        # ファイルタイプを取得
        $targetFileTypes = Get-FileTypes -targetPath $targetPath -targetDirectory $targetDirectory

        foreach ($targetFileType in $targetFileTypes) {
            # ファイルタイプが重複しないように追加
            if ($totalFileTypes -notcontains $targetFileType) {
                $totalFileTypes += $targetFileType 
            }
        }
    }

    $resultMessage = @()

    # $totalFileTypesの件数が0の場合
    if ($totalFileTypes.Count -eq 0) {
        $resultMessage = @"
チェック項目なし
"@
    }
    else {
        $resultMessage= @()

        # 全体メッセージを追加
        foreach ($allMessage in $allMessages) {
            $resultMessage += "- $allMessage`r`n"
        }

        foreach ($fileType in $totalFileTypes) {
            # ファイルタイプに対応する個別メッセージを取得
            $messagesForFileTypes = Get-MessagesForFileTypes -fileType $fileType

            foreach ($message in $messagesForFileTypes) {
                # 個別メッセージが重複しないように追加
                if ($resultMessage -notcontains "- $message`r`n") {
                    $resultMessage += "- $message`r`n"
                }
            }
        }
    }

    return @"
$resultMessage
"@
}
