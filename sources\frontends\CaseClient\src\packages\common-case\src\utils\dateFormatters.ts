import { format, parseJSON } from 'date-fns'

/**
 * 日付フォーマット関数を提供するコンポーザブル
 * date-fnsを使用して様々な形式で日付をフォーマットします
 */
export const useDateFormatters = () => {
  /**
   * 日付を年月(yyyy/MM)形式でフォーマットします
   * @param date 日付文字列
   * @returns フォーマットされた日付文字列、または'-'(日付がnullの場合)
   */
  const formatDateYM = (date: string | null | undefined) => {
    return date ? format(parseJSON(date), 'yyyy/MM') : '-'
  }

  /**
   * 日付を年月日(yyyy/MM/dd)形式でフォーマットします
   * @param date 日付文字列
   * @returns フォーマットされた日付文字列、または'-'(日付がnullの場合)
   */
  const formatDateYMD = (date: string | null | undefined) => {
    return date ? format(parseJSON(date), 'yyyy/MM/dd') : '-'
  }

  /**
   * 日付を短い年月日(yy/MM/dd)形式でフォーマットします
   * @param date 日付文字列
   * @returns フォーマットされた日付文字列、または'-'(日付がnullの場合)
   */
  const formatDateShortYMD = (date: string | null | undefined) => {
    return date ? format(parseJSON(date), 'yy/MM/dd') : '-'
  }

  /**
   * 日付を年月日時分(yyyy/MM/dd HH:mm)形式でフォーマットします
   * @param date 日付文字列
   * @returns フォーマットされた日付文字列、または'-'(日付がnullの場合)
   */
  const formatDateYMDHm = (date: string | null | undefined) => {
    return date ? format(parseJSON(date), 'yyyy/MM/dd HH:mm') : '-'
  }

  /**
   * 日付を年月日時分秒(yyyy/MM/dd HH:mm:ss)形式でフォーマットします
   * @param date 日付文字列
   * @returns フォーマットされた日付文字列、または'-'(日付がnullの場合)
   */
  const formatDateYMDHms = (date: string | null | undefined) => {
    return date ? format(parseJSON(date), 'yyyy/MM/dd HH:mm:ss') : '-'
  }

  return {
    formatDateYM,
    formatDateYMD,
    formatDateShortYMD,
    formatDateYMDHm,
    formatDateYMDHms,
  }
}
