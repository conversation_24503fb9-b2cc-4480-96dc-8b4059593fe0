using MediatR;
using Nut.Results;
using SampleService.Domain;
using SampleService.Externals.Sample;
using Shared.Domain;
using SampleService.Domain.Entities;
using Shared.Messaging;
using Shared.Results.Errors;
using Shared.Spec;

// TODO: 外部APIの利用箇所（必要に応じて実装）
using SampleService.Externals.Sample.SampleExternalServicePath;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.AddEntity;

public class AddEntityHandler(IUnitOfWork unitOfWork,
    IIdGenerator<Domain.Entities.Entity, string> idGenerator,
    IIdGenerator<Domain.Entities.EntityMember, string> projectMemberIdGenerator,
    IMessageSender<UpdateEntityLastModifiedDateTime> entityUpdateMessageSender) : IRequestHandler<AddEntityCommand, Result<string>>
{
    public async Task<Result<string>> Handle(AddEntityCommand request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request);
        var memberRepository = unitOfWork.GetRepository<Domain.Entities.Member>();

        var findMemberResult = await memberRepository.FindAsync(new FindMembersSpecification(request.Members.Select(i => i.MemberId)))
            .FlatMap(members =>
            {
                if (members.Count != request.Members.Count)
                {
                    return Result.Error<List<Domain.Entities.Member>>(new DataNotFoundException("Member not found."));
                }
                return Result.Ok(members);
            });
        if (findMemberResult.IsError) return findMemberResult.PreserveErrorAs<string>();

        // 更新する値を作成します。
        // TODO: 利用するエンティティに置き換えてください。
        var newData = new Domain.Entities.Entity()
        {
            // キーの値をUlidで生成します。
            Id = await idGenerator.GetAsync(),
            // TODO: 引数で渡された値でプロパティを設定します。
            Name = request.Name,
            PeriodFrom = request.PeriodFrom,
            PeriodTo = request.PeriodTo,
            Amount = request.Amount,
            IsAssumption = request.IsAssumption
        };
        var currentMembers = findMemberResult.Get();
        foreach (var di in request.Members)
        {
            newData.Members.Add(new EntityMember()
            {
                Id = await projectMemberIdGenerator.GetAsync(),
                Member = currentMembers.First(cm => cm.Id == di.MemberId),
                UnitPricePerHour = di.UnitPricePerHour
            });
        }

        // TODO: 利用するリポジトリに置き換えてください。
        var repository = unitOfWork.GetRepository<Domain.Entities.Entity>();


        var result = await repository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果として追加したデータのキーを返します。
            .ConfigureAwait(false);

        // TODO: 外部APIの利用箇所（必要に応じて実装）
        // 関連エンティティ最終更新日時の更新
        var updateEntityLastModifiedResult = await UpdateEntityLastModifiedDateTime(newData.Id, request.UpdaterId, request.UpdaterName);
        if (updateEntityLastModifiedResult.IsError) return updateEntityLastModifiedResult.PassOnError<string>();

        await unitOfWork.SaveEntitiesAsync(); // データを保存します

        return result;
  }

  /// <summary>
  /// 外部APIの利用箇所（必要に応じて実装）
  /// エンティティ最終更新日時を更新します。
  /// </summary>
  /// <param name="entityId">エンティティID</param>
  /// <param name="updaterId">更新者ID</param>
  /// <param name="updaterName">更新者名</param>
  /// <returns>更新結果</returns>
  private async Task<Result> UpdateEntityLastModifiedDateTime(string entityId, string updaterId, string updaterName)
  {
    var updateResult = await entityUpdateMessageSender.SendAsync(
        new UpdateEntityLastModifiedDateTime()
        {
          EntityId = entityId,
          UpdaterId = updaterId,
          UpdaterName = updaterName,
        }
    );

    return updateResult;
  }
  public class FindMembersSpecification : BaseSpecification<Domain.Entities.Member>
  {
    public FindMembersSpecification(IEnumerable<string> memberIds)
    {
      Query
      .Where(e => memberIds.Contains(e.Id));
    }
  }
}
