<template>
  <div v-for="(reactionType, index) in createDisplayTypes" :key="reactionType">
    <!-- typeがcustomer-identifiedの場合 -->
    <v-btn
      v-if="type === 'customer-identified'"
      variant="text"
      size="small"
      :icon="getReactionIcon(reactionType)?.icon"
      @mouseover="showReactionMembers(reactionType, index)"
      @mouseleave="hideReactionMembers()"
      @click="saveReaction(reactionType)"
    >
      <div
        :class="{
          iconborder: checkMyReaction(reactionType),
          nonselfstyle: !checkMyReaction(reactionType),
        }"
      >
        <v-icon
          v-if="reactionType !== 4"
          :color="getReactionIcon(reactionType)?.color"
        />
        <font-awesome-icon
          v-if="reactionType === 4"
          :icon="getReactionIcon(reactionType)?.icon"
          :color="getReactionIcon(reactionType)?.color"
          class="font-awesome-icon"
        />
        {{ reactionUsersCount(props.reactions, reactionType) }}
      </div>
    </v-btn>

    <!-- typeがcross-customerの場合 -->
    <div
      v-if="type === 'cross-customer'"
      :class="{ iconborder: checkMyReaction(reactionType) }"
      class="ml-2"
    >
      <v-icon
        v-if="reactionType !== 4"
        size="small"
        :color="getReactionIcon(reactionType)?.color"
        :icon="getReactionIcon(reactionType)?.icon"
      />
      <font-awesome-icon
        v-if="reactionType === 4"
        :icon="getReactionIcon(reactionType)?.icon"
        :color="getReactionIcon(reactionType)?.color"
        class="font-awesome-icon"
      />

      <span class="reaction-count">
        {{ reactionUsersCount(reactions, reactionType) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type {
  ThreadReactionType,
  CommentReactionType,
} from '@ibp/issue-project/src/models/reactions/reactionType'

// =====================================================================================================================
// 型定義
// =====================================================================================================================
export type REACTION_TYPE = {
  undefined: number
  good: number
  congratulations: number
  ok: number
  surprise: number
}

// =====================================================================================================================
// Data
// =====================================================================================================================
const dataRef = ref<{
  // displayReactionTypes: Array<number>
  reactionTypes: REACTION_TYPE
}>({
  // displayReactionTypes: [],
  reactionTypes: {
    undefined: 0,
    good: 1,
    congratulations: 2,
    ok: 3,
    surprise: 4,
  },
})

const props = defineProps<{
  reactions: ThreadReactionType[] | CommentReactionType[] | undefined | null
  type: string
}>()

const emit = defineEmits([
  'show-reaction-members',
  'delete-reaction',
  'update-reaction',
  'add-reaction',
])

// =====================================================================================================================
// ユーザー関連
// =====================================================================================================================
const { $auth } = useNuxtApp()
const userInfo = $auth?.getUser()
const loginUserId = userInfo?.userId ?? 'unknown'

// =====================================================================================================================
// リアクション関連
// =====================================================================================================================
const createDisplayTypes = computed(() => {
  // 表示対象のreactionTypeのみを抜き出す
  const reactionTypes = props.reactions?.map((x) => x.reactionType)
  // 重複しているものを削除
  const distinct = new Set(reactionTypes)
  const arrayDistinct = [...distinct]
  // 昇順に並べ替え
  return arrayDistinct.sort((a, b) => a - b)
})

const getReactionIcon = (type: number) => {
  const reactionTypeValues = dataRef.value.reactionTypes
  if (!reactionTypeValues) {
    return undefined
  }

  const reactionIcons = {
    [reactionTypeValues.good]: {
      color: '#7CB342',
      icon: 'mdi-thumb-up',
    },
    [reactionTypeValues.congratulations]: {
      color: '#D81B60',
      icon: 'mdi-hand-clap',
    },
    [reactionTypeValues.ok]: {
      color: '#FB8C00',
      icon: 'mdi-hand-okay',
    },
    [reactionTypeValues.surprise]: {
      color: '#039BE5',
      icon: 'far fa-regular fa-face-surprise 2xl',
    },
  }

  return reactionIcons[type] || undefined
}

/**
 * 対象のリアクションを付けたユーザー数
 */
const reactionUsersCount = (
  reactions: ThreadReactionType[] | CommentReactionType[] | undefined | null,
  reactionType: number,
) => {
  const selectReactions = reactions?.filter(
    (x) => x.reactionType === reactionType,
  )
  return selectReactions?.length
}

/*
 ** ユーザーが付けたリアクションの場合にはアンダーバーを表示
 */
const checkMyReaction = (reactionType: number) => {
  const reaction = props.reactions?.find(
    (x) => x.reactionType === reactionType && x.staffId === loginUserId,
  )
  const returnValue = !!reaction
  return returnValue
}

/**
 * リアクションを付けたユーザー名を表示
 */
const showReactionMembers = (reactionType: number, index: number) => {
  emit('show-reaction-members', reactionType, index)
}

const hideReactionMembers = () => {
  emit('show-reaction-members', 0)
}

/**
 * 保存(リアクションを付ける)
 */
const saveReaction = (newReactionType: number) => {
  const reactionUsers = props.reactions?.map((x) => x.staffId)
  const selectReactionUsers = props.reactions
    ?.filter((x) => x.reactionType === newReactionType)
    .map((x) => x.staffId)

  // 投稿のリアクションに自分のIDを含んでいれば更新か削除
  if (reactionUsers?.includes(loginUserId)) {
    if (selectReactionUsers?.includes(loginUserId)) {
      // 登録済みのリアクションを選択すると削除
      deleteReaction()
    } else {
      updateReaction(newReactionType)
    }
  } else {
    addReaction(newReactionType)
  }
  emit('show-reaction-members', 0)
}

const deleteReaction = () => {
  const targetDeleteReaction = props.reactions?.find(
    (x) => x.staffId === loginUserId,
  )

  if (targetDeleteReaction) {
    const sendData = {
      id: targetDeleteReaction.id,
      version: targetDeleteReaction.version,
    }
    emit('delete-reaction', sendData)
  }
}

const updateReaction = (newReactionType: number) => {
  const sendData = props.reactions?.find((x) => x.staffId === loginUserId)

  if (sendData) {
    emit('update-reaction', sendData, newReactionType)
  }
}

const addReaction = (newReactionType: number) => {
  emit('add-reaction', newReactionType)
}
</script>

<style scoped>
.iconborder {
  border-bottom: 2px solid gray;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nonselfstyle {
  display: flex;
  justify-content: center;
  align-items: center;
}

.font-awesome-icon {
  height: 18px;
}

.reaction-count {
  margin-left: 1px;
  font-size: 12px;
}
</style>
