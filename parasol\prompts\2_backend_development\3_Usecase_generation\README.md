# UI定義ファイル作成

## 手順

1. [prompt.md](./prompt.md)ファイルを Edit with CopilotのAgent Modeにドラッグ&ドロップし入力ファイルとして指定する。
1. プロンプトとして以下を参考に内容を入力し、Copilotを実行する。
    ```
    指定したprompt.mdに従って作業してください。
    - 対象エンティティ：〇〇
    - 対象Usecase：登録・検索
    ```
    - 対象エンティティ・・・Usecaseの扱うエンティティ（アグリゲート）を指定します
    - 対象ユースケース・・・作成したいUsecaseを指定します。指定がない場合は全てのUsecaseを作成します。指定可能なUsecaseは以下です。
        - 検索
        - 取得
        - 更新
        - 登録
        - 削除
1. 出力されたファイルの内容を確認する。
    - 確認して問題がない場合、出力ファイルを受け入れる
    - 確認して問題がある場合、修正対応として以下のいずれかを実施する
        - 出力ファイルを全て受け入れ拒否し、頭からやり直す
        - 出力ファイルを受け入れ、問題の個所を自身の手で修正する
        - Copilotに対し修正したい内容をプロンプトで指示し、AIに修正させる
