# Shared

## 概要

app-architecture-template-backend-sharedは、バックエンドアプリケーションで共通利用される各種機能・ユーティリティをモジュール化したリポジトリです。各アプリケーション間で重複しがちな基盤機能を集約し、再利用性・保守性の向上を目的としています。

## アーキテクチャ概要

### 基本設計思想
- 各種共通機能をドメインごとに分離し、疎結合な構成とする
- .NETの標準的な設計・拡張性を重視
- Azure等のクラウドサービス連携や、アプリ基盤の拡張性を考慮
- DDD（ドメイン駆動設計）の手法を採用し、テンプレートとの整合性を重視

### アプリケーションアーキテクチャ

#### レイヤー構造
本アーキテクチャは以下の2つの主要レイヤーで構成されます：

**Frontendレイヤー**
- アプリケーションの入り口（UI、Controller、Pipeline）
- 外部からのリクエストを受け付け、ServiceレイヤーのUseCaseに渡す
- 認証・認可、例外ハンドリング、ログ出力などの共通処理をPipelineで実行

**Serviceレイヤー**
- アプリケーションのユースケースを実現する処理（UseCase、Pipeline、QueryService/Repository、MessageClient）
- アプリケーションの形態（Web、API、バッチ）に依存しない純粋な業務処理
- 再利用性とテスト容易性を重視した設計

#### UseCase中心の設計
- **UseCase**を軸としたアーキテクチャ構造
- **CQRSパターン**を採用（Command: 更新・追加・削除、Query: 参照）
- **MediatR**を使用した実装
- **Pipeline**による共通処理（認可、バリデーション、ログ出力）

#### コンポーネント構成
| コンポーネント | 説明 | 要否 |
|------------|------|------|
| Request | UseCase実行のパラメーター | 必須 |
| Result | 実行結果 | オプション |
| Handler | 実際の処理を実行 | 必須 |
| Validator | 入力値検証 | オプション |
| Authorizer | 実行可否判定 | オプション |
| Specification | データ取得条件定義 | オプション |

### 技術スタック

| 技術領域 | 採用技術 |
|---------|----------|
| 言語 | C# (.NET 8.0, .NET Standard 2.0) |
| DI/ミドルウェア | Microsoft.Extensions, ASP.NET Core |
| DB/ORM | Microsoft.EntityFrameworkCore |
| ロギング | NLog |
| 分散トレーシング | OpenTelemetry |
| メッセージング | Azure Service Bus, MediatR |
| ストレージ | Azure Blob Storage |
| バリデーション | FluentValidation |
| 認証 | Azure AD |
| CI/CD | Azure DevOps, commitlint, husky |

### API設計
- **ASP.NET Core**をベースとしたRESTful API
- **Controller**はHTTP関連の処理のみに集中
- **UseCase**への依頼と結果の変換に特化
- 最小限のWeb API実装はUseCaseの呼び出しのみ

### データアクセス
- **Entity Framework Core**を採用
- **Repositoryパターン**によるデータアクセス抽象化
- **Specificationパターン**による柔軟なクエリ構築
- **マイグレーション**によるデータベーススキーマ管理

### 認証・セキュリティ
- **Azure AD**による認証
- **Microsoft Identity Web**を使用
- 同一テナント内のアプリケーション間でシームレス連携
- ユーザー情報の一元管理

### ログ出力
- **NLog**によるログプロバイダー
- **JSON形式**での標準出力
- **Azure Monitor Log Analytics**対応
- **リクエスト単位のトレースID**によるログ紐付け
- 標準出力箇所：
  - リクエスト処理前後
  - ユースケース実行前後
  - データベースアクセス時
  - 例外発生時

## ライブラリディレクトリ構造

```
app-architecture-template-backend-shared/
├── Shared/                  # 汎用的な共通ロジック
├── Shared.AspNetCore/       # ASP.NET Core向け拡張
├── Shared.AzureBlob/        # Azure Blob Storage連携
├── Shared.AzureServiceBus/  # Azure Service Bus連携
├── Shared.Domain.CodingSupport/ # コーディング支援・アナライザ
├── Shared.EntityFrameworkCore/  # EntityFrameworkCore拡張
├── Shared.NLog/             # NLog拡張
├── Shared.OpenTelemetry/    # OpenTelemetry拡張
├── SharedKernel/            # ドメイン共通基盤
```

## ライブラリ間の依存関係

### 依存関係図

```
Shared (基底ライブラリ)
├── Shared.AspNetCore
├── Shared.EntityFrameworkCore
├── Shared.AzureBlob
├── Shared.AzureServiceBus
└── Shared.OpenTelemetry

Shared.NLog (独立)
SharedKernel (独立)
Shared.Domain.CodingSupport (独立)
```

### 依存関係の詳細

| ライブラリ | 依存先 | 依存の種類 | 説明 |
|------------|--------|------------|------|
| Shared | - | 基底ライブラリ | 共通ロジック、メッセージング、バリデーション等の基盤機能を提供 |
| Shared.AspNetCore | Shared | プロジェクト参照 | ASP.NET Core向けの拡張機能を提供。Sharedの共通機能を利用 |
| Shared.EntityFrameworkCore | Shared | プロジェクト参照 | EntityFrameworkCore向けの拡張機能を提供。Sharedの共通機能を利用 |
| Shared.AzureBlob | Shared | プロジェクト参照 | Azure Blob Storage連携機能を提供。Sharedの共通機能を利用 |
| Shared.AzureServiceBus | Shared | プロジェクト参照 | Azure Service Bus連携機能を提供。Sharedの共通機能を利用 |
| Shared.OpenTelemetry | Shared | プロジェクト参照 | OpenTelemetry向けの拡張機能を提供。Sharedの共通機能を利用 |
| Shared.NLog | - | 独立 | NLog向けの拡張機能を提供。外部依存なし |
| SharedKernel | - | 独立 | ドメイン共通基盤を提供。外部依存なし |
| Shared.Domain.CodingSupport | - | 独立 | コーディング支援・アナライザを提供。外部依存なし |

### 設計上の特徴

- **Sharedライブラリ**が中心的な役割を果たし、共通機能を集約
- **Azure連携ライブラリ**はSharedに依存し、共通機能を活用
- **フレームワーク拡張ライブラリ**（AspNetCore、EntityFrameworkCore）もSharedに依存
- **独立ライブラリ**（NLog、Kernel、CodingSupport）は特定の機能に特化し、依存関係を持たない

## 実装内容詳細

各ライブラリの実装内容詳細は以下のドキュメントを参照してください。

- [Shared](./backend_architecture_shared.md)
- [Shared.AspNetCore](./backend_architecture_shared_aspnetcore.md)
- [Shared.AzureBlob](./backend_architecture_shared_azureblob.md)
- [Shared.AzureServiceBus](./backend_architecture_shared_azureservicebus.md)
- [Shared.Domain.CodingSupport](./backend_architecture_shared_domain_codingsupport.md)
- [Shared.EntityFrameworkCore](./backend_architecture_shared_entityframeworkcore.md)
- [Shared.NLog](./backend_architecture_shared_nlog.md)
- [Shared.OpenTelemetry](./backend_architecture_shared_opentelemetry.md)
- [SharedKernel](./backend_architecture_sharedkernel.md)
