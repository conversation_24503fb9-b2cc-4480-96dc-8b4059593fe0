import { useDateFormatters } from '@/packages/common-case/src/utils/dateFormatters'
import {
  LEASE_LOAN_CASE_SHORT_CATEGORIES,
  CASE_STATUSES,
  SUBJECT_TYPES,
} from '@/packages/common-case/src/constants/domain/case'

/**
 * 融資リース案件一覧のテーブルヘッダー設定を取得する
 * @returns ヘッダー設定の配列
 */
export const getLoanAndLeaseTableHeaders = () => {
  // 日付フォーマット関数を取得
  const { formatDateShortYMD } = useDateFormatters()

  return [
    {
      title: '登録日',
      key: 'registeredAt',
      format: (val: any) => (val ? formatDateShortYMD(val) : '-'),
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '店番',
      key: 'branchNumber',
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '氏名',
      key: 'customerName',
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '格付',
      key: 'loanRating',
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '取引方針',
      key: 'transactionPolicy',
      sortable: true,
      class: 'th-padding',
    },
    {
      title: 'カテゴリ',
      key: 'caseCategory',
      format: (val: any) => {
        const item = LEASE_LOAN_CASE_SHORT_CATEGORIES.find(({ value }: any) => value === val)
        return item?.title ?? '-'
      },
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '案件名',
      key: 'caseName',
      sortable: true,
      type: 'maskTarget',
      class: 'th-padding',
    },
    {
      title: 'ステータス',
      key: 'caseStatus',
      format: (val: any) => {
        const item = CASE_STATUSES.find(({ value }: any) => value === val)
        return item?.title ?? '-'
      },
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '科目',
      key: 'subjectType',
      format: (val: any) => {
        const item = SUBJECT_TYPES.find(({ value }: any) => value === val)
        return item?.title ?? '-'
      },
      sortable: true,
      type: 'maskTarget',
      class: 'th-padding',
    },
    {
      title: '金額',
      key: 'amount',
      format: (val: any) => (val ? val.toLocaleString() : '-'),
      sortable: true,
      type: 'maskTarget',
      class: 'th-padding',
    },
    {
      title: '期間',
      key: 'period',
      format: (val: any) =>
        val ? Math.floor(val / 12) + '年' + (val % 12) + 'ヶ月' : '-',
      sortable: true,
      type: 'maskTarget',
      class: 'th-padding',
    },
    {
      title: '金利',
      key: 'interestRate',
      format: (val: any) => val || '-',
      sortable: true,
      type: 'maskTarget',
      class: 'th-padding',
    },
    {
      title: '期日',
      key: 'expiredAt',
      format: (val: any) => (val ? formatDateShortYMD(val) : '-'),
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '案件担当者',
      key: 'staffName',
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '顧客担当者',
      key: 'customerStaffName',
      format: (val: any) => val || '-',
      sortable: true,
      class: 'th-padding',
    },
    {
      title: '最終更新日',
      key: 'caseUpdatedAt',
      format: (val: any) => (val ? formatDateShortYMD(val) : '-'),
      sortable: true,
      class: 'th-padding',
    },
  ]
}
