<!--
===========================================
エンティティ一覧セクション テンプレート
===========================================

このテンプレートは、データテーブル表示パターンのセクションコンポーネントを提供します。

【適用対象】
- 複数のエンティティを一覧表示するセクション
- テーブル形式での情報表示
- リンクやアイコンを含む表示
- 必要に応じてCRUD操作（作成・更新・削除）

【参考実装】
- ShareholderSection.vue: 株主・出資者一覧
- DirectorInformationSection.vue: 役員情報一覧  
- OwnedMediaSection.vue: オウンドメディア一覧

【使用方法】
1. TODOコメントに従って、エンティティ固有の情報に置き換える
2. ヘッダー定義を実際のフィールドに変更する
3. カスタムスロットを実装する
4. 必要に応じてCRUD操作ボタンコンポーネントを実装する（コメントアウト部分を解除）
-->

<script setup lang="ts">
// TODO: 外部リンクが必要な場合、パス定数をインポート
// import { PATHS } from '@ibp/base/src/constants/path'
import type { DataTableHeader } from '@hox/base/src/components/shared/types'
// TODO: エンティティ固有のcomposablesをインポート
import { useGetYourEntityByCustomerIdentificationId } from './useGetYourEntityByCustomerIdentificationId'
// TODO: エンティティの型をインポート（例: Shareholder → YourEntity）
import type { YourEntity } from '~/constants/domain/yourEntity'

// 共通のprops定義
const props = defineProps<{
  title: string
  customerIdentificationId: string  // TODO: 識別子名をエンティティに応じて変更
}>()

const paramId = computed(() => props.customerIdentificationId)

// TODO: コンポーネント固有のcomposableを使用
const {
  fetchedYourEntities,      // 一覧データ
  inProgressFind,           // ローディング状態
  getYourEntityList,          // データ取得関数
} = useGetYourEntitiesByCustomerIdentificationId(paramId)

// TODO: 更新日時管理が必要な場合（オプション）
// const { fetchedUpdatedDateTimeSummary, fetchUpdatedDateTimeSummary } =
//   useUpdatedDateTimeSummary(customerIdentificationId)

// TODO: ヘッダー定義を実際のエンティティフィールドに変更
const headers: DataTableHeader<YourEntity>[] = [
  {
    key: 'name',           // TODO: 実際のフィールド名に変更
    title: '名前',         // TODO: 実際の表示名に変更
  },
  {
    key: 'status',         // TODO: 実際のフィールド名に変更
    title: 'ステータス',    // TODO: 実際の表示名に変更
    // TODO: ステータス変換が必要な場合
    // value: (item) => yourEntityStatuses.find(({ value }) => value === item.status)?.title ?? '-',
  },
  {
    key: 'amount',         // TODO: 実際のフィールド名に変更
    title: '金額',         // TODO: 実際の表示名に変更
  },
  // TODO: 必要に応じてフィールドを追加・削除
  //{ key: '##edit', title: '', sortable: false, align: 'end' },
]

// 再取得処理
const refetch = async () => {
  await getYourEntityList()
}

</script>

<template>
  <!-- 
  TODO: セクション枠組みコンポーネントを選択してください
  汎用的なセクション枠組みの場合：app-edit-item-group-tmpl
  プロジェクト固有の場合：your-project-edit-item-group-tmpl
  -->
  <app-edit-item-group-tmpl>
    <template #title>{{ title }}</template>
    
    <app-simple-data-table
      :items="fetchedYourEntities"
      :headers="headers"
      :loading="inProgressFind"
      :items-per-page="-1"
    >
      <!-- TODO: 名前フィールドのカスタム表示（リンクが必要な場合） -->
      <template #[`item.name`]="{ item }">
        <!-- 関連エンティティへのリンクがある場合 -->
        <!--
        <nuxt-link
          v-if="item.relatedEntityId"
          :to="PATHS({ entityId: item.relatedEntityId }).ENTITY_DETAIL.url"
          target="_blank"
        >
          {{ item.name }}
        </nuxt-link>
        <span v-else>
          {{ item.name }}
        </span>
        -->
        
        <!-- 外部URLへのリンクがある場合 -->
        <!--
        <nuxt-link
          v-if="item.url"
          :to="item.url"
          target="_blank"
        >
          {{ item.name }}
          <v-icon class="ml-1" size="small" icon="mdi-launch" />
        </nuxt-link>
        <span v-else>
          {{ item.name }}
        </span>
        -->
        
        <!-- 通常の表示 -->
        {{ item.name }}
      </template>
      
      <!-- TODO: ステータスフィールドの表示（アイコン表示等） -->
      <template #[`item.status`]="{ item }">
        <!-- チェックマークアイコンの場合 -->
        <!--
        <v-icon v-if="item.status" icon="mdi-account-check" />
        <span v-else> - </span>
        -->
        
        <!-- 通常のテキスト表示 -->
        {{ item.status }}
      </template>
      
      <!-- TODO: 金額フィールドの表示（フォーマット適用） -->
      <template #[`item.amount`]="{ value, item }">
        <!-- パーセンテージ表示の場合 -->
        <!--
        <span :class="[getSpecialDisplayValue(value), 'formatted-value']">
          {{ formatDisplayValue(item) }}
        </span>
        -->
        
        <!-- 通常の金額表示 -->
        {{ value ? value.toLocaleString() : '-' }}
      </template>
      
      <!-- TODO: 追加フィールドがある場合 -->
      <!--
      <template #[`item.additionalField`]="{ item }">
        <your-custom-component :data="item.additionalField" />
      </template>
      -->
      
      <!-- 作成ボタン -->
      <template #[`header.##edit`]>
        <!-- TODO: 作成ボタンコンポーネントの実装例 -->
        <!--
        <create-your-entity-button
          :in-progress-find="inProgressFind"
          @refetch="refetch"
        />
        -->
      </template>
      
      <!-- 操作ボタン（更新・削除） -->
      <template #[`item.##edit`]="{ item }">
        <!-- TODO: 操作ボタンコンポーネントの実装例 -->
        <!--
        <update-your-entity-icon
          :selected-your-entity="item"
          :in-progress-find="inProgressFind"
          @refetch="refetch"
        />
        <delete-your-entity-icon
          :selected-your-entity="item"
          :in-progress-find="inProgressFind"
          @refetch="refetch"
        />
        -->
      </template>
      
      <!-- テーブル下部のページネーション等を非表示 -->
      <template #bottom />
    </app-simple-data-table>
    
    <!-- TODO: 警告やサマリー情報の表示 -->
    <!--
    <div v-if="hasWarningCondition" class="warning-label mt-2">
      <v-icon>mdi-alert</v-icon>
      合計値が制限を超えています ({{ totalAmount.toFixed(2) }})
    </div>
    
    <div v-if="fetchedYourEntities.length === 0 && !inProgressFind" class="no-data-message mt-2">
      <v-icon>mdi-information</v-icon>
      データが登録されていません
    </div>
    -->
    
  </app-edit-item-group-tmpl>
</template>

<style scoped>
.warning-label {
  color: #ec6c1f;
}

.no-data-message {
  color: var(--v-theme-on-surface-variant);
  text-align: center;
  padding: 1rem;
}

.formatted-value {
  white-space: nowrap;
}

.updated-date-label {
  font-size: 0.875rem;
  color: var(--v-theme-on-surface-variant);
}
</style>

<!--
===========================================
テンプレート使用時の変更点チェックリスト
===========================================

【必須変更事項】
□ エンティティの型定義をインポート（YourEntity → ActualEntity）
□ Composable名を実際のエンティティに変更（useGetYourEntitiesBy... → useGetActualEntitiesBy...）
□ props の識別子名を変更（customerIdentificationId → 実際の識別子）
□ headers定義のフィールド名とタイトルを実際のエンティティに変更
□ 各templateスロットのitem.フィールド名を実際のフィールドに変更
□ CRUD操作ボタンコンポーネントの実装（コメントアウト部分を解除して実装）

【カスタム表示の実装】
□ リンク表示が必要なフィールドのコメントアウト解除と実装
□ アイコン表示が必要なフィールドの実装
□ フォーマット表示が必要なフィールドの実装
□ 計算値やサマリー表示の実装

【オプション実装】
□ 更新日時管理が必要な場合は関連コメントアウトを解除
□ プロジェクト固有のセクション枠組みコンポーネントに変更
□ 警告表示やサマリー情報の表示を実装
□ 空データ時のメッセージ表示を実装
□ 定数ファイルのインポートと使用

【Composable実装】
□ 対応するComposableファイル（useGetYourEntitiesByCustomerIdentificationId.ts）の実装
□ API呼び出し処理の実装
□ エラーハンドリングの実装

【ボタンコンポーネント実装（必要に応じて）】
□ CreateYourEntityButton.vue の実装
□ UpdateYourEntityIcon.vue の実装  
□ DeleteYourEntityIcon.vue の実装
-->
