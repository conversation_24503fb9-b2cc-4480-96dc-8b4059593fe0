import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type { ReactionType } from '@ibp/issue-project/src/constants/domain/reactionType'

// =====================================================================================================================
// APIクライアントの定義(idで取得)
// =====================================================================================================================

// GetIssueProjectDiscussionThreadReactionResultの型定義
export type GetIssueProjectDiscussionThreadReactionResult = {
  id: string
  threadId: string
  staffId: string
  staffName: string
  reactionType: ReactionType
  updatedDateTime: string
  version: string
}

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProjectDiscussionThreadReaction(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetIssueProjectDiscussionThreadReactionResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/issueprojectdiscussionthreadreaction/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(新規追加)
// =====================================================================================================================

// 作成用のデータのスキーマを定義します。
const issueprojectdiscussionthreadreactionSchemaForCreate = z.object({
  threadId: z.string(),
  staffId: z.string(),
  staffName: z.string(),
  reactionType: z.number(),
  updatedDateTime: z.date(),
})

// 作成用の型を作成します。
export type IssueProjectDiscussionThreadReactionForCreate = z.infer<
  typeof issueprojectdiscussionthreadreactionSchemaForCreate
>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostIssueProjectDiscussionThreadReaction(
  body: Ref<IssueProjectDiscussionThreadReactionForCreate>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetIssueProjectDiscussionThreadReactionResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthreadreaction',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(更新)
// =====================================================================================================================

// 保存用のデータのスキーマを定義します。
const issueprojectdiscussionthreadreactionSchemaForSave = z.object({
  id: z.string(),
  reactionType: z.number(),
  updatedDateTime: z.date(),
  version: z.string(),
})

// 保存用の型を作成します。
export type IssueProjectDiscussionThreadReactionForSave = z.infer<
  typeof issueprojectdiscussionthreadreactionSchemaForSave
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutIssueProjectDiscussionThreadReaction(
  body: Ref<IssueProjectDiscussionThreadReactionForSave>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetIssueProjectDiscussionThreadReactionResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthreadreaction',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(削除)
// =====================================================================================================================

export type IssueProjectDiscussionThreadReactionForDelete = {
  id: string
  version: string
}

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteIssueProjectDiscussionThreadReaction(
  body: Ref<IssueProjectDiscussionThreadReactionForDelete>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthreadreaction',
        ),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(getOptions)
// =====================================================================================================================

// DTOの型(Result)
export type GetOptionsResult = {
  reactionTypeList: Array<ReactionType>
}

/**
 * データを取得する
 */
export function useGetOptions() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetOptionsResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionthreadreaction/get-options',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
