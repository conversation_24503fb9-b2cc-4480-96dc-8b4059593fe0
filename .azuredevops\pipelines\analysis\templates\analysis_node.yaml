parameters:
  rootDirectory: ''
  analysisResultDirectory: ''
  artifactName: ''
  reportTypes: 'Cobertura'

steps:
- checkout: self
  displayName: 'checkout'
  fetchDepth: -1
  clean: true
  submodules: true

- task: Cache@2
  displayName: cache .net sdk
  condition: succeeded()
  inputs:
    key: 'version1 | dotnet | "$(Agent.OS)" | $(System.DefaultWorkingDirectory)/global.json'
    path: $(Agent.ToolsDirectory)/dotnet

- task: UseDotNet@2
  displayName: 'install .net sdk'
  inputs:
    packageType: 'sdk'
    version: '8.x'
    includePreviewVersions: false
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: DotNetCoreCLI@2
  displayName: 'install dotnet tools'
  inputs:
    command: custom
    custom: tool
    arguments: 'restore'
    workingDirectory: '$(System.DefaultWorkingDirectory)'

- task: NodeTool@0
  displayName: 'install Node.js'
  condition: succeeded()
  inputs:
    versionSpec: '20.x'

- task: npmAuthenticate@0
  inputs:
    workingFile: '${{ parameters.rootDirectory }}/.npmrc'
  displayName: 'authenticate to Azure Artifacts feed'

# workspaceと相性が悪いのかキャッシュを使うgenerateでエラーになってしまう
# - task: Cache@2
#   displayName: cache node_modules
#   inputs:
#     key: 'version1 | node_modules | "$(Agent.OS)" | ${{ parameters.rootDirectory }}/package.json'
#     path: ${{ parameters.rootDirectory }}/node_modules
#     cacheHitVar: CACHE_RESTORED

- script: npm ci
  displayName: 'install node modules'
  # condition: and(succeeded(), ne(variables.CACHE_RESTORED, 'true'))
  workingDirectory: ${{ parameters.rootDirectory }}/

- script: npm run build
  displayName: 'generate static web contents'
  condition: succeeded()
  workingDirectory: ${{ parameters.rootDirectory }}/

- script: npm run lint
  displayName: 'run code analysis'
  condition: succeeded()
  workingDirectory: ${{ parameters.rootDirectory }}/

- script: npm run test
  displayName: 'execute vitest'
  condition: succeeded()
  workingDirectory: ${{ parameters.rootDirectory }}/

- task: PublishTestResults@2
  displayName: 'publish vitest results'
  inputs:
    testResultsFormat: 'JUnit'
    testResultsFiles: '${{ parameters.analysisResultDirectory }}/test/test-results.xml'
    publishRunAttachments: false

- task: DotNetCoreCLI@2
  displayName: 'dotnet tool run reportgenerator'
  inputs:
    command: 'custom'
    custom: 'tool'
    arguments: 'run reportgenerator -reports:${{ parameters.analysisResultDirectory }}/coverages/cobertura-coverage.xml -targetdir:${{ parameters.analysisResultDirectory }}/coverages/report -reporttypes:${{ parameters.reportTypes }}'

- task: PublishCodeCoverageResults@2
  displayName: 'publish code coverage report'
  inputs:
    summaryFileLocation: '$(System.DefaultWorkingDirectory)/**/coverages/report/Cobertura.xml'

# 集計元のXMLファイルをパブリッシュする
- publish: '${{ parameters.analysisResultDirectory }}'
  displayName: 'upload node static analysis results to artifact'
  artifact: ${{ parameters.artifactName }}