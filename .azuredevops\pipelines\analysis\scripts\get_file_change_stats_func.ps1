<#
.SYNOPSIS
    指定されたブランチとHEADの間のファイル変更統計を取得します。
.PARAMETER targetBranch
    比較対象のターゲットブランチの名前。
.RETURNS
    変更されたファイルの統計情報を返します。
#>

function Get-File-Changes-Stats {
    param(
        [string]$targetBranch
    )
    Write-Host "Target Branch Name: $targetBranch"
    $gitNumStatResult = git diff "$targetBranch..HEAD" --numstat
    $gitNameStatusResult = git diff "$targetBranch..HEAD" --name-status

    $gitNumStatLines = $gitNumStatResult -split "`n"
    $gitNameStatusLines = $gitNameStatusResult -split "`n"

    $changeStats = @{}

    foreach ($line in $gitNumStatLines) {
        $parts = $line -split "`t"

        if ($parts.Count -eq 3) {
            # ファイル名に変更前と変更後のパスが含まれているため、変更前のパスを取得。以下のような形式になっているため => の前の部分を取得
            # .azuredevops/pipelines/cicd/Api/{README.md => README_NEW.md}
            $newFileName = $parts[2] -replace "(.*){(.*) => .*}(.*)", '$1$2$3'

            # 拡張子がcs, js, ts, vue, css, scss, html, cshtml, mjs, mts, jsx, tsx のファイルのみを抽出
            if ($newFileName -match "\.(cs|js|ts|vue|css|scss|html|cshtml|mjs|mts|jsx|tsx)$") {
                $changeStat = New-Object PSObject -Property @{
                    AddedLines   = [int]$parts[0]
                    DeletedLines = [int]$parts[1]
                    FileName     = $newFileName
                }
                # $changeStat | Add-Member -MemberType NoteProperty -Name "AddedLines" -Value $parts[0]
                # $changeStat | Add-Member -MemberType NoteProperty -Name "DeletedLines" -Value $parts[1]
                # $changeStat | Add-Member -MemberType NoteProperty -Name "FileName" -Value $newFileName
                $changeStats[$newFileName] = $changeStat
            }
        }
    }

    # status
    # A: addition of a file
    # C: copy of a file into a new one
    # D: deletion of a file
    # M: modification of the contents or mode of a file
    # R: renaming of a file
    # T: change in the type of the file (regular file, symbolic link or submodule)
    # U: file is unmerged (you must complete the merge before it can be committed)
    # X: "unknown" change type (most probably a bug, please report it)

    foreach ($line in $gitNameStatusLines) {
        $parts = $line -split "`t"

        if ($parts.Count -ge 2) {
            $fileName = $parts[1]
            $status = $parts[0]

            if ($changeStats.ContainsKey($fileName)) {
                $changeStat = $changeStats[$fileName]
                $changeStat | Add-Member -MemberType NoteProperty -Name "Status" -Value $status
                # ステータスでファイルの変更などでR100などのステータスがあるため、先頭1文字だけ取得
                $shortStatus = $status.Substring(0, 1)
                $changeStat | Add-Member -MemberType NoteProperty -Name "ShortStatus" -Value $shortStatus
                if ($parts.Count -eq 3 && $shortStatus -eq "R") {
                    $changeStat | Add-Member -MemberType NoteProperty -Name "NewFileName" -Value $parts[2]
                }
            }
        }
    }

    return $changeStats
}
