import { ref, reactive } from 'vue'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useBranchMaster } from '@/packages/common-case/src/composables/customerIdentifying/useBranchMaster'
import { getStaffAndTeamOptions } from '@/packages/common-case/src/utils/ddlOptions'
import {
  DEFAULT_SELECTED_CASE_STATUSES_WITH_ISSUE_PROJECT,
  SEARCH_CASE_STATUSES as CASE_STATUSES,
  CASE_CATEGORIES,
  FUNDING_SUPPORT_CASE_CATEGORIES,
} from '@ibp/common-case/src/constants/domain/case'
import { useMaster } from '@ibp/common-case/src/composables/master/useMasters'
import { useFeatureManager } from '@ibp/common-case/src/composables/shared/useFeatureManager'

// 検索条件の型
type SelectOption = {
  title: string
  value: string
}
/**
 * 案件オプションデータ取得用のコンポーザブル
 * マスターデータやスタッフ情報など、検索条件で利用する選択肢の取得を行う
 */
export const useCaseOptions = () => {
  // 処理完了などをトースト表示するために利用
  const { error: errorToast } = useAppToasts()

  // マスタデータ取得用コンポーザブル
  const { branchMasterOptions, fetchBranches } = useBranchMaster()
  const { masterGetters, masterActions } = useMaster()
  const { fetchFeatureFlag, getFundingSupportFeatureFlag, getRecruitMatchingFeatureFlag } = useFeatureManager()

  // スタッフと担当者のローディング状態
  const loadingStaffAndTeam = ref(false)

  // 選択肢
  const options = reactive({
    staffs: [] as Array<SelectOption>,
    staffAndTeams: [] as Array<SelectOption>,
    industry: [] as Array<SelectOption>,
    generalTransactionTypeOptions: [] as Array<SelectOption>,
    categories: [] as Array<SelectOption>,
    caseStatuses: [] as Array<SelectOption>,
  })

  /**
   * すべての選択肢データを取得する
   * @param hasCriteriaHistory 検索条件履歴の有無
   * @param searchCondition 検索条件オブジェクト（初期値設定用）
   * @returns 処理成功フラグ
   */
  async function loadAllOptions(hasCriteriaHistory: boolean, searchCondition: any) {
    loadingStaffAndTeam.value = true
    try {
      const result = await setOptions()
      if (searchCondition && !hasCriteriaHistory) {
        searchCondition.caseStatuses = DEFAULT_SELECTED_CASE_STATUSES_WITH_ISSUE_PROJECT || []
      }
      return result
    } finally {
      loadingStaffAndTeam.value = false
    }
  }
  /**
   * フィーチャーフラグに基づいてカテゴリーをフィルタリングする
   * @returns フィルタリング済みのカテゴリー配列
   */
  function getFilteredCategories() {
    let categories = CASE_CATEGORIES

    // 資金調達支援機能のフィーチャーフラグが無効の場合、該当カテゴリーを除外
    if (!getFundingSupportFeatureFlag.value) {
      const fundingSupportValues = FUNDING_SUPPORT_CASE_CATEGORIES.map(
        (item) => item.value,
      )
      categories = categories.filter(
        (category) => !fundingSupportValues.includes(category.value),
      )
    }

    // TODO: Recruit 1stリリース後に削除
    if (!getRecruitMatchingFeatureFlag.value) {
      categories = categories.filter(
        (x) => x.value !== 'RecruitMatchingPermanentJob',
      )
      categories = categories.filter(
        (x) => x.value !== 'RecruitMatchingSideJob',
      )
    }

    return categories
  }

  /**
   * マスタデータとスタッフ・チーム情報を取得する
   * @returns 処理成功フラグ
   */
  async function setOptions() {
    try {
      await masterActions.fetchGeneralTransactionTypes()

      const [
        getStaffAndTeamOptionsResult,
        getIndustryOptionsResult,
      ] = await Promise.all([
        getStaffAndTeamOptions(),
        getIndustryOptions(), // 業種の選択肢を取得
        fetchBranches(), // 店番情報を取得
      ])

      if (getStaffAndTeamOptionsResult.length === 0) {
        errorToast('検索条件の選択肢が取得できませんでした。リロードしてください。')
        return false
      }

      options.staffAndTeams = getStaffAndTeamOptionsResult
      options.industry = getIndustryOptionsResult
      options.generalTransactionTypeOptions = masterGetters.generalTransactionTypeOptions()
      options.caseStatuses = CASE_STATUSES

      // フィーチャーフラグに基づいてカテゴリーをフィルタリング
      await fetchFeatureFlag()
      options.categories = getFilteredCategories()

      if (
        !getIndustryOptionsResult ||
        !options.generalTransactionTypeOptions
      ) {
        errorToast('検索条件の選択肢が取得できませんでした。リロードしてください。')
        return false
      }

      return true
    } catch (error) {
      errorToast('オプションデータの取得に失敗しました。')
      return false
    }
  }

  return {
    // マスタデータ
    branchMasterOptions,

    // スタッフと担当者
    options,
    loadingStaffAndTeam,

    // メソッド
    loadAllOptions,
    setOptions,
    getFilteredCategories,
  }
}
