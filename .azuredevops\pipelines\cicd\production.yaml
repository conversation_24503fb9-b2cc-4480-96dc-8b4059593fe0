trigger:
  tags:
    include:
      - v*.*.*

variables:
- group: common
- group: production

stages:
- stage: DownloadArtifacts
  jobs:
  - template: CaseClient/download-artifacts.yaml
    parameters:
      env: production
      pipelineId: $(System.DefinitionId) # TODO: Buildのアーティファクトを取得するパイプラインのIDを指定して下さい。
      devopsProjectName: $(System.TeamProject) # TODO: BUildのアーティファクトを取得するAzure Pipelineがあるプロジェクト名/GUIDを指定してください。
  
  - template: Api/download-artifacts.yaml
    parameters:
      env: production
      pipelineId: $(System.DefinitionId) # TODO: Buildのアーティファクトを取得するパイプラインのIDを指定して下さい。
      devopsProjectName: $(System.TeamProject) # TODO: BUildのアーティファクトを取得するAzure Pipelineがあるプロジェクト名/GUIDを指定してください。
#  jobs:
## TODO: WebUIの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/download-artifacts.yaml
#    parameters:
#      env: production
#      pipelineId: 0 # Buildのアーティファクトを取得するパイプラインのIDを指定して下さい。通常は develop でアーティファクトをビルドする pipelineId にします。
#      devopsProjectName: $(System.TeamProject) # BUildのアーティファクトを取得するAzure Pipelineがあるプロジェクト名/GUIDを指定してください。
## TODO: Apiの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/download-artifacts.yaml
#    parameters:
#      env: production
#      pipelineId: 0 # Buildのアーティファクトを取得するパイプラインのIDを指定して下さい。通常は develop でアーティファクトをビルドする pipelineId にします。
#      devopsProjectName: $(System.TeamProject) # BUildのアーティファクトを取得するAzure Pipelineがあるプロジェクト名/GUIDを指定してください。


- stage: DeployToSlot
  jobs:
  - template: CaseClient/deploy.yaml
    parameters:
      env: production
  
  - template: Api/deploy.yaml
    parameters:
      env: production
#  jobs:
## TODO: WebUIの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/deploy.yaml
#    parameters:
#      env: production
## TODO: Apiの定義を展開した場合は次のように参照を追加してください。
#  - template: {project name}/deploy.yaml
#    parameters:
#      env: production

## TODO: Batchの定義を展開した場合はDeployステージで参照を追加してください。
# - stage: Deploy
#   dependsOn:
#   - DeployToSlot
#   jobs:
#   - template: SampleBatch_tmpl/deploy.yaml
#     parameters:
#       env: production