### Shared.OpenTelemetry

## 概要

Shared.OpenTelemetryライブラリは、OpenTelemetryを使用した分散トレーシングの拡張機能を提供するライブラリです。アプリケーション間のトレーシング、メトリクス収集、ログ相関などの機能を提供し、OpenTelemetryを使用した分散システムの監視・運用効率化を支援します。

## 主要機能

### 分散トレーシング
- **トレーシング設定**: OpenTelemetryの設定拡張
- **相関関係**: アプリケーション間の処理相関の追跡
- **パフォーマンス**: 処理時間、ボトルネックの特定

### 監視・運用
- **メトリクス収集**: アプリケーションの性能指標収集
- **ログ相関**: トレースIDによるログの紐付け
- **障害調査**: 問題発生時の原因特定支援

### 拡張性
- **設定拡張**: WebApplicationBuilderの拡張メソッド
- **カスタマイズ**: トレーシング設定の柔軟なカスタマイズ
- **統合**: 既存システムとの統合支援

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| WebApplicationBuilderExtensions | 拡張メソッド | OpenTelemetryの設定拡張 | 分散トレーシング導入 |
