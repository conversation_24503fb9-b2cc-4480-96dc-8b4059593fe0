import { ref, onMounted, onUnmounted } from 'vue'
import { OriginOfTransitions } from '@/packages/case-work-flow-optimization/src/constants/common/query'
import { CASE_CATEGORIES, FUNDING_SUPPORT_CASE_CATEGORIES } from '@ibp/common-case/src/constants/domain/case'

/**
 * 課題案件の編集・画面遷移を管理するコンポーザブル
 * 編集画面への遷移や別タブ表示などの処理を担当
 */
export const useCaseNavigation = () => {
  // Ctrlキーの状態を追跡
  const isControlPressed = ref(false)

  // キーイベントリスナー
  onMounted(() => {
    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('keyup', handleKeyUp)
  })

  onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown)
    window.removeEventListener('keyup', handleKeyUp)
  })

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Control') {
      isControlPressed.value = true
    }
  }

  const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key === 'Control') {
      isControlPressed.value = false
    }
  }

  /**
   * 検索結果のcaseCategoryをEnum文字列に変換する
   */
  function getCaseCategory(dispCaseCategory: string) {
    const category = CASE_CATEGORIES.find((x) => x.title === dispCaseCategory)
    return category ? category.value : null
  }

  /**
   * 案件の編集画面のURLを取得する
   * @param item 選択された案件データ
   * @returns 遷移先URL情報
   */
  function getEditUrl(item: any) {
    const currentUrl = new URL(window.location.href)
    let isExternal = false
    /*
      この時点で案件カテゴリがわからないため、一旦案件編集ページに遷移した後に案件カテゴリを取得、
      必要であれば課題案件の編集ページに遷移する
    */
    let path = `/customer-case/edit?id=${item.id}&customerIdentificationId=${item.customerIdentificationId}&from=${OriginOfTransitions.TOP__CASE}`

    const category = getCaseCategory(item.caseCategory)
    if (category === 'BusinessMatchingBuy') {
      path = `/matching/business-matching/buy-case/edit?id=${item.id}&customerIdentificationId=${item.customerIdentificationId}&from=${OriginOfTransitions.TOP__CASE}`
      isExternal = true
    }
    if (category === 'BusinessMatchingSell') {
      path = `/matching/business-matching/sell-case/edit?id=${item.id}&customerIdentificationId=${item.customerIdentificationId}&from=${OriginOfTransitions.TOP__CASE}`
      isExternal = true
    }
    if (category === 'MAndAMatchingBuy') {
      path = `/matching/ma-matching/buy-case/edit?id=${item.id}&customerIdentificationId=${item.customerIdentificationId}&from=${OriginOfTransitions.TOP__CASE}`
      isExternal = true
    }
    if (category === 'MAndAMatchingSell') {
      path = `/matching/ma-matching/sell-case/edit?id=${item.id}&customerIdentificationId=${item.customerIdentificationId}&from=${OriginOfTransitions.TOP__CASE}`
      isExternal = true
    }
    if (category === 'RecruitMatchingPermanentJob') {
      path = `/matching/recruit-matching/permanent-job-case/edit?id=${item.id}&customerIdentificationId=${item.customerIdentificationId}`
      isExternal = true
    }

    const fundingSupportValues = FUNDING_SUPPORT_CASE_CATEGORIES.map(
      (item) => item.value,
    )
    if (category && fundingSupportValues.includes(category)) {
      path = `/funding-support/case/${encodeURIComponent(category)}`
      isExternal = true
    }

    return {
      path: isExternal ? currentUrl.origin + path : path,
      isExternal: isExternal,
    }
  }

  /**
   * 編集処理 - 案件一覧のアイテムをクリックした時の処理
   * @param event イベントオブジェクト
   * @param onBeforeNavigate 遷移前に実行するコールバック関数
   */
  function navigateToEdit(event: any, onBeforeNavigate?: () => void) {
    // eventの基本的な存在チェック
    if (!event || (!event.row && !event.selectedItem && !event.target)) return

    // itemを安全に取得
    const item = event.row?.item || event.selectedItem || event.target

    // itemの妥当性をチェック（必要な最小限のプロパティを確認）
    if (!item || !item.id || !item.customerIdentificationId || !item.caseCategory) {
      // 必要なプロパティが不足している場合は処理を中断
      return
    }

    const url = getEditUrl(item)
    const isNewTabRequested = isControlPressed.value || event.isPressedCtrlKey

    // Ctrlキーを押しながらクリックした場合は別タブで開く
    if (isNewTabRequested) {
      const finalUrl = url.isExternal
        ? url.path
        : `${window.location.origin}/case${url.path}`

      return navigateTo(finalUrl, {
        open: { target: '_blank' },
        external: url.isExternal,
      })
    } else {
      // 遷移前に実行する処理があれば実行
      if (onBeforeNavigate) {
        onBeforeNavigate()
      }

      // 遷移処理
      return navigateTo(url.path, {
        external: url.isExternal,
      })
    }
  }

  return {
    navigateToEdit,
    getEditUrl,
    isControlPressed,
    getCaseCategory,
  }
}
