using MediatR;
using Nut.Results;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.UpdateEntity;

[WithDefaultBehaviors]
public record UpdateEntityCommand(
    /* TODO: プロパティを追加します。 */
    string Id,
    string Name,
    DateTimeOffset PeriodFrom,
    DateTimeOffset PeriodTo,
    int Amount,
    bool IsAssumption,
    string Version,
    List<UpdateEntityCommandMember> Members,
    string UpdaterId,     // TODO: 更新者IDを設定します
    string UpdaterName    // TODO: 更新者名を設定します
) : IRequest<Result<string>>;

public record UpdateEntityCommandMember(
    string? Id,
    string MemberId,
    int UnitPricePerHour,
    bool Updated);
