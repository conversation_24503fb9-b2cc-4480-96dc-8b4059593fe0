<template>
  <component
    :is="tableComponent"
    :headers="headers"
    :items="[]"
    :no-data-text="message"
    hide-default-footer
  >
    <template #no-data>
      <div class="custom-no-data-message">
        {{ message }}
      </div>
    </template>
  </component>
</template>

<script setup lang="ts">
/**
 * カスタムno-dataメッセージ表示コンポーネント
 *
 * 機能:
 * - テーブルでデータが存在しない場合のカスタムメッセージ表示
 * - 複数のテーブルコンポーネントで再利用可能
 */

import { VDataTable } from 'vuetify/components'

// === プロパティ定義 ===
defineProps({
  // 表示するメッセージ
  message: {
    type: String,
    required: true,
  },
  // テーブルヘッダー定義
  headers: {
    type: Array as () => any[],
    required: true,
  },
})

// === 内部設定 ===
// テーブルコンポーネントの選択（no-dataメッセージ表示用はVDataTable）
const tableComponent = computed(() => VDataTable)
</script>

<style scoped>
/* カスタムno-dataメッセージ */
.custom-no-data-message {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding: 40px 20px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
