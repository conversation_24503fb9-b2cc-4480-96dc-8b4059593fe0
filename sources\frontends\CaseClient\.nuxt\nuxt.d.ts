// Generated by nuxi
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/test-utils" />
/// <reference types="@nuxt/test-utils" />
/// <reference path="types/builder-env.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/app-defaults.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference path="types/base-plugins.d.ts" />
/// <reference types="vitest/import-meta" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/nitro.d.ts" />
/// <reference path="./eslint-typegen.d.ts" />

export {}
