/**
 * ===========================================
 * アグリゲート更新コンポーザブル テンプレート
 * ===========================================
 * 
 * このテンプレートは、既存のアグリゲートの更新機能を提供するコンポーザブル関数です。
 * 
 * 【主な機能】
 * - アグリゲートモデルの状態管理
 * - バリデーション機能
 * - 更新・一時保存API呼び出し
 * - エラーハンドリングとトースト表示
 * 
 * 【使用方法】
 * 1. TODOコメントに従って、エンティティ固有の情報に置き換える
 * 2. 型名とインポートパスを実際のエンティティに変更する
 * 3. 関数名と変数名をエンティティに応じて変更する
 * 4. 不要なフィールドは削除し、必要なフィールドを追加する
 */

import { reactiveComputed } from '@vueuse/core'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'

// TODO: エンティティの型とスキーマ関数をインポート
import {
  toYourAggregateSchemaForUpdate, // TODO: 実際のスキーマ関数名に変更
  type YourAggregateType, // TODO: 実際のエンティティ型名に変更
} from '@your-module/src/constants/domain/entities/yourAggregate' // TODO: 実際のパスに変更

// TODO: エンティティ固有のAPI関数をインポート
import { useUpdateYourAggregateAPI } from '@your-module/src/apiclient/yourAggregate' // TODO: 実際のAPI関数名とパスに変更

import { useGetCustomer } from '@ibp/base/src/composables/customer/useGetCustomer'
import { useBankVisionBusinessError } from '../../composables/conditionRegistration/useBankVisionBusinessError'

// TODO: 関数名をエンティティに応じて変更（例: useUpdateCommercialBill、useUpdateLoanApplication等）
export function useUpdateYourAggregate() {
  const { loggedInUser } = useUser()

  // 一時保存フラグの管理
  const isDraft = ref(false)
  
  // TODO: アグリゲートモデルの状態管理 - 型名を実際のエンティティ型に変更
  const yourAggregateModel = ref<YourAggregateType>({} as YourAggregateType)

  // TODO: 顧客情報の取得 - 必要に応じてカスタマイズまたは削除
  const { customer } = useGetCustomer(
    computed(() => yourAggregateModel.value.customerIdentificationId),
  )

  // TODO: 更新用のデータ構築 - エンティティに必要なフィールドを追加/変更
  const yourAggregateForUpdate = computed(() => ({
    ...yourAggregateModel.value,
    isDraft: isDraft.value,
    // TODO: 以下のフィールドはエンティティに応じて調整してください
    branchNumber: customer.value.branchNumber,
    cifNumber: customer.value.cifNumber,
    conditionRegisterId: loggedInUser.value.userId,
    conditionRegisterName: loggedInUser.value.nameKanji,
  }))

  // TODO: バリデーションスキーマの設定 - スキーマ関数名を実際のものに変更
  const yourAggregateSchema = reactiveComputed(
    () => toYourAggregateSchemaForUpdate(
      isDraft.value,
      yourAggregateForUpdate.value,
    ),
  ) as ReturnType<typeof toYourAggregateSchemaForUpdate>

  // TODO: API呼び出し用コンポーザブル - API関数名を実際のものに変更
  const { inProgress, executeWithResult } = useUpdateYourAggregateAPI(yourAggregateForUpdate)

  // トースト表示用
  const { primary: primaryToast, error: errorToast } = useAppToasts()
  
  // バリデーション機能
  const { validate, validateItem, errorMessages } = useValidation(
    yourAggregateSchema,
    yourAggregateForUpdate,
  )

  // TODO: ビジネスエラーの抽出 - 必要に応じてカスタマイズ
  const { extractBankVisionBusinessError } = useBankVisionBusinessError()

  // TODO: API実行のメイン処理 - 関数名とエラーメッセージを調整
  const executeYourAggregateApi = async (draft: boolean) => {
    isDraft.value = draft
    const opName = draft ? '一時保存' : '更新' // TODO: 適切な操作名に変更

    try {
      // バリデーション実行
      const { success } = await validate()
      if (!success) {
        errorToast('入力項目に不備があります。', {
          retain: true,
        })
        return { success: false, data: undefined }
      }

      // API実行
      const { data } = await executeWithResult()
      setYourAggregateModel(data)
      primaryToast(`${opName}しました。`)
      return { success: true }
    } catch (e: any) {
      if (!e.hasProblemDetails) {
        throw e
      }

      // TODO: エラーハンドリング - ビジネス要件に応じてカスタマイズ
      if (e.data.type === '/validation-error') {
        errorMessages.value = e.data.errors
        errorToast('入力項目に不備があります。', {
          retain: true,
        })
      } else if (e.data.type === '/conflict') {
        errorToast(
          `すでに別のユーザーがデータを登録・変更しているため${opName}できませんでした。`,
        )
      } else {
        const bvError = extractBankVisionBusinessError(e.data.detail)
        if (bvError) {
          errorToast(bvError, {
            retain: true,
          })
        } else {
          errorToast(`${opName}に失敗しました。${e.data.detail}`)
        }
      }
      return { success: false }
    } finally {
      isDraft.value = false
    }
  }

  // TODO: 公開関数 - 関数名をエンティティに応じて変更
  const updateYourAggregate = () => executeYourAggregateApi(false)
  const draftYourAggregate = () => executeYourAggregateApi(true)

  // TODO: モデル設定関数 - 関数名と型をエンティティに応じて変更
  function setYourAggregateModel(yourAggregate: YourAggregateType) {
    yourAggregateModel.value = yourAggregate
  }

  // TODO: 公開インターフェース - プロパティ名をエンティティに応じて変更
  return {
    yourAggregateModel,
    inProgress: readonly(inProgress),
    updateYourAggregate,
    draftYourAggregate,
    setYourAggregateModel,
    validateItem,
    yourAggregateSchema,
    errorMessages,
  }
}

/*
===========================================
テンプレート使用時の変更点チェックリスト
===========================================

【必須変更事項】
□ TODO: エンティティの型定義をインポート（YourAggregateType → 実際の型名）
□ TODO: API関数をインポート（useUpdateYourAggregateAPI → 実際のAPI関数名）
□ TODO: 関数名を変更（useUpdateYourAggregate → 実際の関数名）
□ TODO: スキーマ関数名を変更（toYourAggregateSchemaForUpdate → 実際のスキーマ関数名）
□ TODO: 変数名とプロパティ名をエンティティに応じて変更
□ TODO: 更新用データのフィールドをエンティティの要件に合わせて調整
□ TODO: エラーメッセージと操作名をエンティティに応じて変更

【オプション変更事項】
□ TODO: useGetCustomerが不要なエンティティの場合は削除
□ TODO: 特別なバリデーションロジックの追加
□ TODO: ビジネス固有のエラーハンドリングの追加
□ TODO: エンティティ固有のフィールドやロジックの追加
*/
