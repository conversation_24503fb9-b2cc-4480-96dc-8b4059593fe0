import { useCookies } from '@vueuse/integrations/useCookies'

export const setTOCToCookie = () => {
  const { set } = useCookies()
  const token = window.localStorage.getItem('auth._token.aad')

  // 有効期限を1日後に設定
  const expiresDate = new Date()
  expiresDate.setDate(expiresDate.getDate() + 1) // 現在の日付から1日後

  if (token) {
    set('toc', `${token.replace('Bearer ', '')}`, {
      expires: expiresDate,
      path: '/',
    })
  }
}

export const removeTOCFromCookie = () => {
  const { remove } = useCookies()

  remove('toc')
}
