import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import { purposeTypeOfTemplateKeys } from '@ibp/issue-project/src/constants/domain/purposeTypeOfTemplate'

// IssueProjectDiscussionTypeのスキーマ
export const issueProjectDiscussionTypeSchema = z.object({
  id: z.string(),
  discussionTypeName: z.string(),
  isApprovalDiscussion: z.boolean(),
  isInactive: z.boolean(),
  purposeTypeOfTemplate: z.enum(
    purposeTypeOfTemplateKeys.map(String) as [string, ...string[]],
  ),
  order: z.number().int().nullish(),
})

// KeyValuePairのスキーマ定義
export const keyValuePairSchema = z.object({
  key: z.string(),
  value: z.number().int(),
})

// 主として扱うデータのスキーマ
export const issueProjectDiscussionFormTemplateSchema = z.object({
  id: z.string(),
  discussionType: issueProjectDiscussionTypeSchema,
  templateVersion: z.number().int(),
  key: z.string(),
  inputType: z.number().int(),
  optionDefaultValue: z.number().int().nullish(),
  inputDefaultText: z.string().nullish(),
  option: z.array(keyValuePairSchema).nullish(),
  suffix: z.string().nullish(),
  enabled: z.boolean(),
  isRequired: z.boolean(),
  order: z.number(),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type IssueProjectDiscussionFormTemplate = z.infer<
  typeof issueProjectDiscussionFormTemplateSchema
>
export type OptionKeyValuePair = z.infer<typeof keyValuePairSchema>

// IssueProjectDiscussionFormTemplateDataのスキーマ
export const templateDataSchema =
  issueProjectDiscussionFormTemplateSchema.omit({
    id: true,
    version: true,
    discussionType: true,
    templateVersion: true,
  })

export const templateDataListSchema = z.array(
  templateDataSchema,
)

// =====================================================================================================================
// APIクライアントの定義 (検索)
// =====================================================================================================================

export type FindIssueProjectDiscussionFormTemplateCriteria = {
  discussionTypeId: string
  templateVersion?: number | null
}

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindIssueProjectDiscussionFormTemplate(
  query: Ref<FindIssueProjectDiscussionFormTemplateCriteria>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectDiscussionFormTemplate[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionformtemplate',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (追加)
// =====================================================================================================================

// 作成用のデータのスキーマを定義します。
export const issueProjectDiscussionFormTemplateSchemaForCreate = z.object({
  discussionTypeName: z.string().max(50),
  isApprovalDiscussion: z.boolean(),
  isInactive: z.boolean(),
  purposeTypeOfTemplate: z.string(),
  templateDataList: templateDataListSchema.nullish(),
})

// 作成用の型を作成します。
export type IssueProjectDiscussionFormTemplateForCreate = z.infer<
  typeof issueProjectDiscussionFormTemplateSchemaForCreate
>

export type TemplateData = z.infer<
  typeof templateDataSchema
>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostIssueProjectDiscussionFormTemplate(
  body: Ref<IssueProjectDiscussionFormTemplateForCreate>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Array<IssueProjectDiscussionFormTemplate>>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionformtemplate',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (更新)
// =====================================================================================================================

// 保存用のデータのスキーマを定義します。
export const issueProjectDiscussionFormTemplateSchemaForSave = z.object({
  discussionTypeId: z.string(),
  currentTemplateVersion: z.number(),
  templateDataList: templateDataListSchema.nullish(),
})
// 保存用の型を作成します。
export type IssueProjectDiscussionFormTemplateForSave = z.infer<
  typeof issueProjectDiscussionFormTemplateSchemaForSave
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutIssueProjectDiscussionFormTemplate(
  body: Ref<IssueProjectDiscussionFormTemplateForSave>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Array<IssueProjectDiscussionFormTemplate>>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionformtemplate',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (getOptions)
// =====================================================================================================================
export type InputType = {
  text: string
  value: string
}

// DTOの型
export type GetOptionsResult = {
  inputTypeList: Array<InputType>
}

/**
 * データを取得する
 */
export function useGetOptions() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetOptionsResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojectdiscussionformtemplate/get-options',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
