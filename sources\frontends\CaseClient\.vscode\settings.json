{"prettier.enable": false, "eslint.format.enable": true, "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "editor.formatOnSave": true}