# Entity実装

## 役割定義

バックエンド開発者として、ドメインモデルの定義に基づいてエンティティクラス、コンフィグレーションファイル、マイグレーションファイルを作成します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 ： 
  - `parasol\architecture\backend_architecture.md`
- 共通処理使用方法リファレンス：
  - `parasol\architecture\usage_sample_backend_shared.md`
- プロジェクト設定定義 : 
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- ドメイン言語：`parasol\capability\**\ドメイン言語.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- エンティティクラステンプレート：`parasol\prompts\2_backend_development\1_Entity_generation\Templates\Template_Entity.cs`
- 列挙型エンティティクラステンプレート：`parasol\prompts\2_backend_development\1_Entity_generation\Templates\Template_EnumEntity.cs`
- エンティティコンフィグレーションテンプレート：`parasol\prompts\2_backend_development\1_Entity_generation\Templates\Template_EntityConfiguration.cs`

## 出力定義

- エンティティクラス
  - 出力先ディレクトリ：`[サービスディレクトリ]\Domain\Entities`
  - ファイル名フォーマット：`[扱うエンティティの物理名].cs`
- 列挙型エンティティクラス
  - 出力先ディレクトリ：`[サービスディレクトリ]\Domain\Enums`
  - ファイル名フォーマット：`[扱う列挙型の物理名].cs`
- エンティティコンフィグレーション
  - 出力先ディレクトリ：`[サービスディレクトリ]\Infrastructure\Persistence\Configurations`
  - ファイル名フォーマット：`[扱うエンティティの物理名]Configuration.cs`

## 制約事項

### 禁止事項

- 定義ファイルを編集する代わりに、出力ファイルに必要な内容を記述してください
- テンプレートファイルを編集する代わりに、テンプレートの構造に従って新しいファイルを作成してください
- 当プロンプトファイルを編集する代わりに、指示内容に従って作業を実行してください

## 指示詳細

### 情報収集

1. ドメイン言語を読み込み、定義されているエンティティとプロパティを把握してください
1. ドメイン言語の中から、どのエンティティを対象とするのかヒアリングしてください。
    - ヒアリングで指定されたエンティティと、それに関するエンティティ及び列挙型についてのみ、後続の作業を行います。
1. テンプレートファイルを読み込み、エンティティクラスとコンフィグレーションの構造を確認してください
1. ApplicationDbContext.csを読み込み、現在の構造を確認してください

### 生成作業

1. 出力先ディレクトリの既存ファイルを確認し、作成済みエンティティを特定してください
1. ドメイン言語から未作成のエンティティを抽出し、必要な情報を決定してください
   - 物理名、プロパティの物理名、キー項目、プロパティの型と項目長
1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください
1. 未作成の各エンティティについてファイルを作成してください
   - エンティティクラスファイル（テンプレートの構造に従って実装）
       - エンティティファイルは、エンティティ1つに対して1ファイル作成してください。1つのファイルに複数のエンティティを含めないようにしてください。
   - コンフィグレーションファイル（項目長定義を反映）
   - 列挙型エンティティクラス（テンプレートの構造に従って実装）
       - 列挙型エンティティファイルは、列挙型1つに対して1ファイル作成してください。1つのファイルに複数の列挙型を含めないようにしてください。

1. ApplicationDbContext.csにDbSetプロパティと必要なusing文を追加してください
1. Entity Framework Coreのマイグレーションファイルを作成してください
   - slnファイルの場所とプロジェクト名を確認
   - マイグレーションコマンドを実行

### 品質保証

1. 以下の内容を検証してください
   - 作成したエンティティクラスの命名規則、プロパティ定義、キー項目
   - ApplicationDbContext.csのDbSet追加とusing文
   - コンフィグレーションクラスのテーブル名、カラム名、項目長設定
   - マイグレーションファイルの生成とテーブル定義の正確性
   - 不備が見つかった場合は修正してください
