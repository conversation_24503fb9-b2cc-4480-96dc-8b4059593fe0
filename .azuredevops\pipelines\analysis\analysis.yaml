trigger: none

variables:
  SourcesDirectory: $(System.DefaultWorkingDirectory)/sources
  SolutionFullPath: $(System.DefaultWorkingDirectory)/sources/CustomerUnderstandingApp.sln
  MetricsSolutionFullPath: $(System.DefaultWorkingDirectory)/sources/$(Build.BuildId).sln
  ScriptDirectory: $(System.DefaultWorkingDirectory)/.azuredevops/pipelines/analysis/scripts
  DotNetAnalysisResultDirectory: $(System.DefaultWorkingDirectory)/sources/testresult
  DotNetTestProjectRootDirectory: $(System.DefaultWorkingDirectory)/sources/services
  DotNetArtifactName: dotnet_testresult

  ## TODO: Frontendのパスを調整してください。
  NodeRootDirectory: $(System.DefaultWorkingDirectory)/sources/frontends/CaseClient
  NodeAnalysisResultDirectory: $(System.DefaultWorkingDirectory)/sources/frontends/CaseClient/testresult
  NodeTestProjectRootDirectory: $(System.DefaultWorkingDirectory)/sources/frontends/CaseClient
  NodeArtifactName: node_testresult
  
  ## TODO: 複数のFrontendがある場合は次のように参照を追加してください。
  # NodeRootDirectory2: $(System.DefaultWorkingDirectory)/sources/frontends/CaseClient_2
  # NodeAnalysisResultDirectory2: $(System.DefaultWorkingDirectory)/sources/frontends/CaseClient_2/testresult
  # NodeTestProjectRootDirectory2: $(System.DefaultWorkingDirectory)/sources/frontends/CaseClient_2 
  # NodeArtifactName2: node_testresult_2

  TitleAll: '全体'
  TitleDotNet: 'dotnet'
  TitleNode: 'CaseClient'
  ## TODO: 複数のFrontendがある場合は次のように参照を追加してください。
  # TitleNode2: 'CaseClient_2'

stages:
- stage: Analysis

  jobs:
  - job: StaticAnalysisDotNet
    displayName: 'DotNet Static Analysis'
    pool:
      # metrics を取得するためにVSが必要となるため、windowsのイメージを利用する
      vmImage: windows-latest
    steps:
    - template: templates/analysis_dotnet.yaml
      parameters:
        sourcesDirectory: $(SourcesDirectory)
        solutionFullPath: $(SolutionFullPath)
        metricsSolutionFullPath: $(MetricsSolutionFullPath)
        scriptDirectory: $(ScriptDirectory)
        analysisResultDirectory: $(DotNetAnalysisResultDirectory)
        artifactName: $(DotNetArtifactName)

  ## TODO: Frontendが無い場合は削除してください。
  - job: StaticAnalysisNode
    displayName: 'Node Static Analysis'
    pool:
      vmImage: windows-latest
    steps:
    - template: templates/analysis_node.yaml
      parameters:
        rootDirectory: $(NodeRootDirectory)
        analysisResultDirectory: $(NodeAnalysisResultDirectory)
        artifactName: $(NodeArtifactName)

  ## TODO: 複数のFrontendがある場合は次のように参照を追加してください。
  # - job: StaticAnalysisNode2
  #   displayName: 'Node Static Analysis 2'
  #   pool:
  #     vmImage: windows-latest
  #   steps:
  #   - template: templates/analysis_node.yaml
  #     parameters:
  #       rootDirectory: $(NodeRootDirectory2)
  #       analysisResultDirectory: $(NodeAnalysisResultDirectory2)
  #       artifactName: $(NodeArtifactName2)

  - job: PostStaticAnalysisComments
    displayName: 'Post Static Analysis Comments to Pull Request'
    pool:
      vmImage: windows-latest
    dependsOn:
    - StaticAnalysisDotNet
    ## TODO: Frontendの有無によって設定を追加・削除してください。
    - StaticAnalysisNode
    # - StaticAnalysisNode2
    # プルリクエストの場合のみJobを実行する
    condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
    steps:
    - checkout: self
      displayName: 'checkout'
      fetchDepth: -1
      clean: true

    - download: current
      artifact: $(DotNetArtifactName)
      displayName: 'download dotnet static analysis results from artifact'

    ## TODO: Frontendの有無によって設定を追加・削除してください。
    - download: current
      artifact: $(NodeArtifactName)
      displayName: 'download node static analysis results from artifact'

    # - download: current
    #   artifact: $(NodeArtifactName2)
    #   displayName: 'download node static analysis results from artifact'

    - template: templates/post_quality_comment_all.yaml
      parameters:
        scriptDirectory: $(ScriptDirectory)
        title: $(TitleAll)

    - template: templates/post_quality_comment_dotnet.yaml
      parameters:
        analysisResultDirectory: $(Pipeline.Workspace)/$(DotNetArtifactName)
        testProjectRootDirectory: $(DotNetTestProjectRootDirectory)
        scriptDirectory: $(ScriptDirectory)
        threadId: $(ThreadId)
        title: $(TitleDotNet)

    ## TODO: Frontendの有無によって設定を追加・削除してください。
    - template: templates/post_quality_comment_node.yaml
      parameters:
        analysisResultDirectory: $(Pipeline.Workspace)/$(NodeArtifactName)
        testProjectRootDirectory: $(NodeTestProjectRootDirectory)
        scriptDirectory: $(ScriptDirectory)
        threadId: $(ThreadId)
        title: $(TitleNode)

    # - template: templates/post_quality_comment_node.yaml
    #   parameters:
    #     analysisResultDirectory: $(Pipeline.Workspace)/$(NodeArtifactName2)
    #     testProjectRootDirectory: $(NodeTestProjectRootDirectory2)
    #     scriptDirectory: $(ScriptDirectory)
    #     threadId: $(ThreadId)
    #     title: $(TitleNode2)

    - template: templates/post_quality_comment_checklist.yaml
      parameters:
        rootDirectory: $(SourcesDirectory)
        scriptDirectory: $(ScriptDirectory)
        threadId: $(ThreadId)