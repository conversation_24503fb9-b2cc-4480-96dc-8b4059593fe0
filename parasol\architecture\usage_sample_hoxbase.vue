<template>
  <section>
    <h1>@hox/base Atom Components 使用パターン</h1>
    
    <!-- ボタン系コンポーネント -->
    <section>
      <h2>ボタン系コンポーネント</h2>
      
      <h3>AppBtn（基本ボタン）</h3>
      <div class="component-group">
        <app-btn :disabled="buttonDisabled">基本ボタン</app-btn>
        <app-btn :disabled="buttonDisabled" variant="outlined">アウトラインボタン</app-btn>
        <app-btn :disabled="buttonDisabled" variant="text">テキストボタン</app-btn>
        <app-btn :disabled="buttonDisabled" variant="tonal">トーナルボタン</app-btn>
        <app-btn :disabled="buttonDisabled" variant="plain">プレーンボタン</app-btn>
        <h4>typeプロパティ（自動ラベル・アイコン）</h4>
        <app-btn type="search" :disabled="buttonDisabled" />
        <app-btn type="add" :disabled="buttonDisabled" />
        <app-btn type="update" :disabled="buttonDisabled" />
        <app-btn type="remove" :disabled="buttonDisabled" />
        <app-btn type="save" :disabled="buttonDisabled" />
        <app-btn type="cancel" :disabled="buttonDisabled" />
        <h4>サイズ・色指定</h4>
        <app-btn size="x-small" :disabled="buttonDisabled">極小</app-btn>
        <app-btn size="small" :disabled="buttonDisabled">小</app-btn>
        <app-btn size="default" :disabled="buttonDisabled">標準</app-btn>
        <app-btn size="large" :disabled="buttonDisabled">大</app-btn>
        <app-btn size="x-large" :disabled="buttonDisabled">極大</app-btn>
        <app-btn color="primary" :disabled="buttonDisabled">プライマリ</app-btn>
        <app-btn color="secondary" :disabled="buttonDisabled">セカンダリ</app-btn>
        <app-btn color="success" :disabled="buttonDisabled">成功</app-btn>
        <app-btn color="warning" :disabled="buttonDisabled">警告</app-btn>
        <app-btn color="error" :disabled="buttonDisabled">エラー</app-btn>
      </div>
      
      <h3>AppMainBtn（メインボタン）</h3>
      <div class="component-group">
        <app-main-btn :disabled="buttonDisabled">メインボタン</app-main-btn>
        <app-main-btn :disabled="buttonDisabled" type="save">保存</app-main-btn>
        <app-main-btn :disabled="buttonDisabled" type="add">追加</app-main-btn>
        <app-main-btn :disabled="buttonDisabled" type="update">更新</app-main-btn>
        <h4>アイコン付き</h4>
        <app-main-btn :disabled="buttonDisabled" prepend-icon="mdi-account">ユーザー</app-main-btn>
        <app-main-btn :disabled="buttonDisabled" append-icon="mdi-arrow-right">次へ</app-main-btn>
      </div>
      
      <h3>AppSubBtn（サブボタン）</h3>
      <div class="component-group">
        <app-sub-btn :disabled="buttonDisabled">サブボタン</app-sub-btn>
        <app-sub-btn :disabled="buttonDisabled" type="cancel">キャンセル</app-sub-btn>
        <app-sub-btn :disabled="buttonDisabled" type="search">検索</app-sub-btn>
        <h4>形状・スタイル</h4>
        <app-sub-btn :disabled="buttonDisabled" rounded="lg">角丸大</app-sub-btn>
        <app-sub-btn :disabled="buttonDisabled" rounded="pill">ピル型</app-sub-btn>
        <app-sub-btn :disabled="buttonDisabled" density="compact">コンパクト</app-sub-btn>
        <app-sub-btn :disabled="buttonDisabled" density="comfortable">快適</app-sub-btn>
      </div>
      
      <h3>AppDangerousBtn（危険操作ボタン）</h3>
      <div class="component-group">
        <app-dangerous-btn :disabled="buttonDisabled">危険操作</app-dangerous-btn>
        <app-dangerous-btn :disabled="buttonDisabled" type="remove">削除</app-dangerous-btn>
        <h4>状態表示</h4>
        <app-dangerous-btn :loading="buttonLoading" type="remove">削除中...</app-dangerous-btn>
        <app-dangerous-btn :disabled="buttonDisabled" block>ブロックボタン</app-dangerous-btn>
      </div>
      
      <v-checkbox v-model="buttonDisabled" label="全ボタンを無効化" />
      <v-checkbox v-model="buttonLoading" label="ローディング状態" />
    </section>

    <!-- 入力系コンポーネント -->
    <section>
      <h2>入力系コンポーネント</h2>
      
      <h3>AppTextField（テキスト入力）</h3>
      <div class="component-group">
        <app-text-field
          v-model="textValue"
          label="基本テキストフィールド"
          :required-mark="requiredMark"
          :text-align="textAlign"
        />
        <app-text-field
          v-model="emailValue"
          label="メールアドレス"
          type="email"
          :required-mark="true"
          placeholder="<EMAIL>"
        />
        <app-text-field
          v-model="passwordValue"
          label="パスワード"
          type="password"
          :required-mark="true"
        />
        <h4>テキスト配置パターン</h4>
        <app-text-field
          v-model="textAlignStart"
          label="左寄せ"
          text-align="start"
        />
        <app-text-field
          v-model="textAlignCenter"
          label="中央寄せ"
          text-align="center"
        />
        <app-text-field
          v-model="textAlignEnd"
          label="右寄せ"
          text-align="end"
        />
        <h4>空値処理パターン（noInputTo）</h4>
        <app-text-field
          v-model="textUndefined"
          label="undefined（デフォルト）"
          no-input-to="undefined"
          hint="空の場合: undefined"
        />
        <app-text-field
          v-model="textNull"
          label="null"
          no-input-to="null"
          hint="空の場合: null"
        />
        <app-text-field
          v-model="textEmpty"
          label="empty"
          no-input-to="empty"
          hint="空の場合: ''"
        />
        <h4>バリデーション・装飾</h4>
        <app-text-field
          v-model="textWithRules"
          label="バリデーション付き"
          :rules="textRules"
          :required-mark="true"
        />
        <app-text-field
          v-model="textWithHint"
          label="ヒント付き"
          hint="この欄にヒントが表示されます"
          persistent-hint
        />
        <app-text-field
          v-model="textWithPrefix"
          label="プレフィックス付き"
          prefix="¥"
        />
        <app-text-field
          v-model="textWithSuffix"
          label="サフィックス付き"
          suffix="円"
        />
        <app-text-field
          v-model="textCounter"
          label="文字数カウンター"
          counter="10"
          :rules="[v => !v || v.length <= 10 || '10文字以内で入力してください']"
        />
        <app-text-field
          v-model="textClearable"
          label="クリアボタン付き"
          clearable
        />
        <app-text-field
          v-model="textReadonly"
          label="読み取り専用"
          readonly
        />
        <app-text-field
          v-model="textDisabled"
          label="無効化"
          disabled
        />
      </div>
      
      <h3>AppNumberField（数値入力）</h3>
      <div class="component-group">
        <app-number-field
          v-model="numberValue"
          label="数値フィールド"
          :required-mark="requiredMark"
          :text-align="textAlign"
        />
        <app-number-field
          v-model="priceValue"
          label="価格"
          :min="0"
          :max="1000000"
          step="100"
          suffix="円"
        />
        <h4>空値処理パターン（noInputTo）</h4>
        <app-number-field
          v-model="numberUndefined"
          label="undefined（デフォルト）"
          no-input-to="undefined"
          hint="空の場合: undefined"
        />
        <app-number-field
          v-model="numberNull"
          label="null"
          no-input-to="null"
          hint="空の場合: null"
        />
        <h4>制約・装飾</h4>
        <app-number-field
          v-model="numberWithMin"
          label="最小値制限"
          :min="10"
          hint="最小値: 10"
        />
        <app-number-field
          v-model="numberWithMax"
          label="最大値制限"
          :max="100"
          hint="最大値: 100"
        />
        <app-number-field
          v-model="numberWithStep"
          label="ステップ制限"
          :step="5"
          hint="5刻みで入力"
        />
        <app-number-field
          v-model="numberWithRules"
          label="バリデーション付き"
          :rules="numberRules"
          :required-mark="true"
        />
        <app-number-field
          v-model="numberReadonly"
          label="読み取り専用"
          readonly
        />
        <app-number-field
          v-model="numberDisabled"
          label="無効化"
          disabled
        />
      </div>
      
      <h3>AppTextarea（テキストエリア）</h3>
      <div class="component-group">
        <app-textarea
          v-model="textareaValue"
          label="テキストエリア"
          :required-mark="requiredMark"
          :text-align="textAlign"
          :line-height="lineHeight"
          rows="3"
        />
        <app-textarea
          v-model="commentValue"
          label="コメント"
          placeholder="コメントを入力してください"
          :line-height="lineHeight"
          rows="5"
        />
      </div>
    </section>

    <!-- セレクト系コンポーネント -->
    <section>
      <h2>セレクト系コンポーネント</h2>
      
      <h3>AppSimpleSelect（シンプルセレクト）</h3>
      <div class="component-group">
        <app-simple-select
          v-model="selectValue"
          label="基本セレクト"
          item-title="label"
          item-value="value"
          :required-mark="requiredMark"
          :items="simpleItems"
          clearable
        />
        <app-simple-select
          v-model="multiSelectValue"
          label="複数選択セレクト"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          multiple
          chips
        />
        <h4>空値処理パターン（単一選択）</h4>
        <app-simple-select
          v-model="selectUndefined"
          label="undefined（デフォルト）"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          no-input-to="undefined"
          hint="空の場合: undefined"
        />
        <app-simple-select
          v-model="selectNull"
          label="null"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          no-input-to="null"
          hint="空の場合: null"
        />
        <h4>空値処理パターン（複数選択）</h4>
        <app-simple-select
          v-model="multiSelectUndefined"
          label="undefined（デフォルト）"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          multiple
          no-multiple-input-to="undefined"
          hint="空の場合: undefined"
        />
        <app-simple-select
          v-model="multiSelectNull"
          label="null"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          multiple
          no-multiple-input-to="null"
          hint="空の場合: null"
        />
        <app-simple-select
          v-model="multiSelectEmpty"
          label="empty"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          multiple
          no-multiple-input-to="empty"
          hint="空の場合: []"
        />
        <h4>装飾・機能</h4>
        <app-simple-select
          v-model="selectWithChips"
          label="チップ表示"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          multiple
          chips
          closable-chips
        />
        <app-simple-select
          v-model="selectMenuProps"
          label="メニュープロパティ"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          :menu-props="{ maxHeight: 200 }"
        />
        <app-simple-select
          v-model="selectReadonly"
          label="読み取り専用"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          readonly
        />
        <app-simple-select
          v-model="selectDisabled"
          label="無効化"
          item-title="label"
          item-value="value"
          :items="simpleItems"
          disabled
        />
      </div>
      
      <h3>AppSimpleOptGroupSelect（オプショングループセレクト）</h3>
      <div class="component-group">
        <app-simple-opt-group-select
          v-model="optGroupValue"
          label="グループセレクト"
          item-title="label"
          item-value="value"
          :required-mark="requiredMark"
          :items="groupedItems"
        />
      </div>
      
      <h3>AppAutocomplete（オートコンプリート）</h3>
      <div class="component-group">
        <app-autocomplete
          v-model="autocompleteValue"
          label="オートコンプリート"
          :required-mark="requiredMark"
          :text-align="textAlign"
          :items="autocompleteItems"
          clearable
        />
        <app-autocomplete
          v-model="multiAutocompleteValue"
          label="複数選択オートコンプリート"
          :items="autocompleteItems"
          multiple
          chips
        />
      </div>
    </section>

    <!-- 日付・時刻系コンポーネント -->
    <section>
      <h2>日付・時刻系コンポーネント</h2>
      
      <h3>AppPopupDatePicker（日時選択）</h3>
      <div class="component-group">
        <app-popup-date-picker
          v-model="dateTimeValue"
          label="日時選択"
          :required-mark="requiredMark"
          :text-align="textAlign"
          :initial-date="new Date()"
        />
      </div>
      
      <h3>AppPopupDateOnlyPicker（日付のみ選択）</h3>
      <div class="component-group">
        <app-popup-date-only-picker
          v-model="dateOnlyValue"
          label="日付のみ選択"
          :required-mark="requiredMark"
          :text-align="textAlign"
          :initial-date="new Date()"
        />
      </div>
    </section>

    <!-- ファイル系コンポーネント -->
    <section>
      <h2>ファイル系コンポーネント</h2>
      
      <h3>AppFileInput（ファイル選択）</h3>
      <div class="component-group">
        <app-file-input
          v-model="singleFile"
          label="単一ファイル選択"
          :required-mark="requiredMark"
          :chips="false"
          accept="image/*"
        />
        <app-file-input
          v-model="multipleFiles"
          label="複数ファイル選択"
          :chips="true"
          :multiple="true"
          :clearable-chips="true"
          drop-behavior="append"
          :duplicable="false"
        />
        <h4>ドロップ動作パターン</h4>
        <app-file-input
          v-model="fileReplace"
          label="置き換えモード（デフォルト）"
          :multiple="true"
          :chips="true"
          drop-behavior="replace"
          hint="ファイルをドロップすると既存ファイルを置き換え"
        />
        <app-file-input
          v-model="fileAppend"
          label="追加モード"
          :multiple="true"
          :chips="true"
          drop-behavior="append"
          hint="ファイルをドロップすると既存ファイルに追加"
        />
        <h4>重複ファイル処理</h4>
        <app-file-input
          v-model="fileNoDuplicate"
          label="重複不可（デフォルト）"
          :multiple="true"
          :chips="true"
          :duplicable="false"
          hint="同じファイルは選択できません"
        />
        <app-file-input
          v-model="fileDuplicate"
          label="重複可能"
          :multiple="true"
          :chips="true"
          :duplicable="true"
          hint="同じファイルを複数選択可能"
        />
        <h4>空値処理パターン（noInputTo）</h4>
        <app-file-input
          v-model="fileUndefined"
          label="undefined（デフォルト）"
          no-input-to="undefined"
          hint="空の場合: undefined"
        />
        <app-file-input
          v-model="fileNull"
          label="null"
          no-input-to="null"
          hint="空の場合: null"
        />
        <app-file-input
          v-model="fileEmpty"
          label="empty"
          no-input-to="empty"
          hint="空の場合: []"
        />
        <h4>チップ表示パターン</h4>
        <app-file-input
          v-model="fileNoChips"
          label="チップ非表示"
          :multiple="true"
          :chips="false"
        />
        <app-file-input
          v-model="fileWithChips"
          label="チップ表示"
          :multiple="true"
          :chips="true"
        />
        <app-file-input
          v-model="fileClearableChips"
          label="削除可能チップ"
          :multiple="true"
          :chips="true"
          :clearable-chips="true"
        />
        <h4>ファイル制限</h4>
        <app-file-input
          v-model="fileImages"
          label="画像ファイルのみ"
          accept="image/*"
          :multiple="true"
          :chips="true"
        />
        <app-file-input
          v-model="filePDF"
          label="PDFファイルのみ"
          accept=".pdf"
          :chips="true"
        />
        <app-file-input
          v-model="fileText"
          label="テキストファイルのみ"
          accept=".txt,.csv,.json"
          :multiple="true"
          :chips="true"
        />
        <h4>状態</h4>
        <app-file-input
          v-model="fileDisabled"
          label="無効化"
          disabled
          :chips="true"
        />
      </div>
      <div v-if="singleFile && singleFile.length > 0" class="file-info">
        <h4>選択されたファイル（単一）:</h4>
        <p>{{ singleFile[0]?.name }} ({{ (singleFile[0]?.size / 1024).toFixed(2) }} KB)</p>
      </div>
      <div v-if="multipleFiles && multipleFiles.length > 0" class="file-info">
        <h4>選択されたファイル（複数）:</h4>
        <ul>
          <li v-for="file in multipleFiles" :key="file.name + file.lastModified">
            {{ file.name }} ({{ (file.size / 1024).toFixed(2) }} KB)
          </li>
        </ul>
      </div>
    </section>

    <!-- データ表示系コンポーネント -->
    <section>
      <h2>データ表示系コンポーネント</h2>
      
      <h3>AppSimpleDataTable（シンプルデータテーブル）</h3>
      <div class="component-group">
        <app-simple-data-table
          :headers="tableHeaders"
          :items="tableItems"
          item-value="id"
          :items-per-page="5"
        />
      </div>
    </section>

    <!-- 設定オプション -->
    <section>
      <h2>共通設定オプション</h2>
      <v-row>
        <v-col cols="12" md="4">
          <v-checkbox v-model="requiredMark" label="必須マーク表示" />
        </v-col>
        <v-col cols="12" md="4">
          <v-radio-group v-model="textAlign" inline label="テキスト配置">
            <v-radio label="左" value="start" />
            <v-radio label="中央" value="center" />
            <v-radio label="右" value="end" />
          </v-radio-group>
        </v-col>
        <v-col cols="12" md="4">
          <v-radio-group v-model="lineHeight" inline label="行の高さ">
            <v-radio label="デフォルト" value="default" />
            <v-radio label="快適" value="comfortable" />
            <v-radio label="コンパクト" value="compact" />
          </v-radio-group>
        </v-col>
      </v-row>
    </section>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// データテーブル用の型定義（簡易版）
interface AppDataTableHeader {
  key: string
  title: string
  sortable?: boolean
}

// ボタン系
const buttonDisabled = ref(false)
const buttonLoading = ref(false)

// 入力系 - TextField
const textValue = ref('')
const emailValue = ref('')
const passwordValue = ref('')
const textAlignStart = ref('')
const textAlignCenter = ref('')
const textAlignEnd = ref('')
const textUndefined = ref<string | undefined>(undefined)
const textNull = ref<string | null>(null)
const textEmpty = ref('')
const textWithRules = ref('')
const textWithHint = ref('')
const textWithPrefix = ref('')
const textWithSuffix = ref('')
const textCounter = ref('')
const textClearable = ref('')
const textReadonly = ref('読み取り専用の値')
const textDisabled = ref('無効化された値')

// 入力系 - NumberField
const numberValue = ref<number>()
const priceValue = ref<number>()
const numberUndefined = ref<number | undefined>(undefined)
const numberNull = ref<number | null>(null)
const numberWithMin = ref<number>()
const numberWithMax = ref<number>()
const numberWithStep = ref<number>()
const numberWithRules = ref<number>()
const numberReadonly = ref(12345)
const numberDisabled = ref(67890)

// 入力系 - Textarea
const textareaValue = ref('')
const commentValue = ref('')

// セレクト系
const selectValue = ref()
const multiSelectValue = ref([])
const optGroupValue = ref()
const autocompleteValue = ref()
const multiAutocompleteValue = ref([])
const selectUndefined = ref<string | undefined>(undefined)
const selectNull = ref<string | null>(null)
const multiSelectUndefined = ref<string[] | undefined>(undefined)
const multiSelectNull = ref<string[] | null>(null)
const multiSelectEmpty = ref<string[]>([])
const selectWithChips = ref([])
const selectMenuProps = ref()
const selectReadonly = ref('option1')
const selectDisabled = ref('option2')

// 日付・時刻系
const dateTimeValue = ref(new Date())
const dateOnlyValue = ref(new Date())

// ファイル系
const singleFile = ref<File[]>()
const multipleFiles = ref<File[]>()
// ドロップ動作パターン用
const fileReplace = ref<File[]>()
const fileAppend = ref<File[]>()
// 重複処理用
const fileNoDuplicate = ref<File[]>()
const fileDuplicate = ref<File[]>()
// 空値処理用
const fileUndefined = ref<File[] | undefined>(undefined)
const fileNull = ref<File[] | null>(null)
const fileEmpty = ref<File[]>([])
// チップ表示用
const fileNoChips = ref<File[]>()
const fileWithChips = ref<File[]>()
const fileClearableChips = ref<File[]>()
// ファイル制限用
const fileImages = ref<File[]>()
const filePDF = ref<File[]>()
const fileText = ref<File[]>()
// 状態用
const fileDisabled = ref<File[]>()

// データ表示系
const tableHeaders: AppDataTableHeader[] = [
  { key: 'id', title: 'ID', sortable: true },
  { key: 'name', title: '名前', sortable: true },
  { key: 'email', title: 'メールアドレス', sortable: false },
  { key: 'status', title: 'ステータス', sortable: true },
]

const tableItems = ref([
  { id: 1, name: '田中太郎', email: '<EMAIL>', status: 'アクティブ' },
  { id: 2, name: '佐藤花子', email: '<EMAIL>', status: '非アクティブ' },
  { id: 3, name: '山田次郎', email: '<EMAIL>', status: 'アクティブ' },
  { id: 4, name: '鈴木三郎', email: '<EMAIL>', status: '保留中' },
  { id: 5, name: '高橋四郎', email: '<EMAIL>', status: 'アクティブ' },
])

// 共通設定
const requiredMark = ref(false)
const textAlign = ref<'start' | 'center' | 'end'>('start')
const lineHeight = ref<'default' | 'comfortable' | 'compact'>('default')

// バリデーションルール
const textRules = [
  (v: string) => !!v || '必須項目です',
  (v: string) => v.length >= 3 || '3文字以上で入力してください',
]

const numberRules = [
  (v: number) => !!v || '必須項目です',
  (v: number) => v > 0 || '0より大きい値を入力してください',
]

// セレクト用データ
const simpleItems = [
  { label: 'オプション1', value: 'option1' },
  { label: 'オプション2', value: 'option2' },
  { label: 'オプション3', value: 'option3' },
  { label: 'オプション4', value: 'option4' },
]

const groupedItems = [
  {
    label: 'グループA',
    items: [
      { label: 'A-1', value: 'a1' },
      { label: 'A-2', value: 'a2' },
    ]
  },
  {
    label: 'グループB',
    items: [
      { label: 'B-1', value: 'b1' },
      { label: 'B-2', value: 'b2' },
    ]
  },
]

const autocompleteItems = [
  '東京都',
  '大阪府',
  '愛知県',
  '神奈川県',
  '埼玉県',
  '千葉県',
  '福岡県',
  '北海道',
  '兵庫県',
  '静岡県',
]
</script>
