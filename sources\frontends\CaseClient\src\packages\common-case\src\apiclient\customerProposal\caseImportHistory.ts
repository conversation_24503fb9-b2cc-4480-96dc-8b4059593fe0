import { useNuxtApp } from 'nuxt/app'
import { parseISO } from 'date-fns'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import { isString } from '@hox/base/src/utils/shared/is'
import type {
  ApiResult,
  Pagination,
} from '@hox/base/src/apiclient/shared/types'

// =====================================================================================================================
// スキーマとデータ型の定義
// =====================================================================================================================

// 案件取込履歴のスキーマ
export const caseImportHistorySchema = z.object({
  id: z.string(),
  fileName: z.string(),
  caseCategory: z.string(),
  detail: z.string().optional(),
  startedAt: z.date().optional(),
  finishedAt: z.date().optional(),
  csvImportProcessStatus: z.string(),
  // TODO: 必要に応じて他のプロパティを追加してください
  version: z.string().optional(),
})

// 案件取込履歴のデータ型
export type CaseImportHistory = z.infer<typeof caseImportHistorySchema>

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

// TODO: 検索条件の型を定義します（必要に応じて）
export type FindCaseImportHistoryCriteria = {
  // TODO: 検索条件が必要な場合はここに追加してください
  // caseCategory?: string
  // status?: string
} & Pagination

// 検索結果の型
export type FindCaseImportHistoryResultItem = CaseImportHistory
export type FindCaseImportHistoryResult = ApiResult<FindCaseImportHistoryResultItem>

/**
 * 案件取込履歴一覧を取得する
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindCaseImportHistoryItems() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<CaseImportHistory[]>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/caseimporthistory'),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: CaseImportHistory[]) {
          // 日付文字列から日付型への変換
          if (Array.isArray(data)) {
            for (const item of data) {
              if (isString(item.startedAt)) {
                item.startedAt = parseISO(item.startedAt)
              }
              if (isString(item.finishedAt)) {
                item.finishedAt = parseISO(item.finishedAt)
              }
            }
          }
          return data
        },
      }),
    ),
  )
}

/**
 * 案件取込履歴ファイルをダウンロードする
 * @param query Ref<T> ダウンロードパラメータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDownloadCaseImportHistory(
  query: Ref<{ caseImportHistoryId: string, fileName: string }>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Blob>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/caseimporthistory/download'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        // Blobレスポンスの設定
        responseType: 'blob',
        transform(data: Blob) {
          // Blobデータはそのまま返す
          return data
        },
      }),
    ),
  )
}
