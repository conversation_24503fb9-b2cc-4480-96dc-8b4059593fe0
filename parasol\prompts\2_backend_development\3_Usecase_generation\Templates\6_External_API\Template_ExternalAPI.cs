using Shared.Messaging;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.Externals.SampleExternalServicePath;

[MessageEndpoint("/smple-external-service-path/find")]
public class FindSampleExternalAPI : ISendRequest<IEnumerable<FindCustomerStaffResult>>
{
  public string? Name { get; set; }
  public DateTimeOffset? PeriodFrom { get; set; }
  public DateTimeOffset? PeriodTo { get; set; }
  public string? MemberName { get; set; }
}

public record class FindCustomerStaffResult
{
  public string Id { get; set; }
  public string Name { get; set; }
  public DateTimeOffset PeriodFrom { get; set; }
  public DateTimeOffset PeriodTo { get; set; }
  public int Amount { get; set; }
  public bool IsAssumption { get; set; }
  public string Version { get; set; }
}
