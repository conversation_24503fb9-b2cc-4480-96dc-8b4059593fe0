# 行カバレッジの閾値を設定(error, warning)
$lineRateThreshold = [double[]](60, 75)

# 分岐カバレッジの閾値を設定(error, warning)
$branchRateThreshold = [double[]](60, 75)

# 対象件数(クラス)を設定
$topNClass = 5

# 対象件数(メソッド)を設定
$topNMethod = 3

# アイコンを設定
$errorIcon = "❌"
$warningIcon = "⚠️"
$safeIcon = "✅"

<#
.SYNOPSIS
    指定された値が閾値を下回っているかどうかを判定し、エラーまたは警告のアイコンを返します。
.PARAMETER value
    判定する値。
.PARAMETER thresholds
    エラーおよび警告の閾値を含む配列。
.PARAMETER errorOrWarningStatus
    現在のエラーまたは警告のステータス。
.RETURNS
    アイコンと更新されたエラーまたは警告のステータスを返します。
#>

function Get-ErrorOrWarningIcon-Less-Than {
    param(
        [int]$value,
        [int[]]$thresholds,
        [int]$errorOrWarningStatus
    )

    if ($value -lt $thresholds[0]) {
        # エラーの場合は、エラーアイコンと、引数で渡された$errorOrWarningStatusの値または2の大きい方を返す
        return $errorIcon, [math]::Max($errorOrWarningStatus, 2)
    }
    elseif ($value -lt $thresholds[1]) {
        # 警告の場合は、警告アイコンと、引数で渡された$errorOrWarningStatusの値または1の大きい方を返す
        return $warningIcon, [math]::Max($errorOrWarningStatus, 1)
    }
    else {
        # エラーでも警告でもない場合は、引数で渡された$errorOrWarningStatusの値を返す
        return "", $errorOrWarningStatus
    }
}

<#
.SYNOPSIS
    変更されたファイルのコードカバレッジを解析し、結果のメッセージを生成します。
.PARAMETER changeStats
    変更されたファイルの統計情報を含むオブジェクト。
.PARAMETER analysisResultDirectory
    静的分析結果が保存されているディレクトリのパス。
.RETURNS
    生成されたコードカバレッジメッセージを返します。
#>

function Get-DotNet-Coverage-Message {
    param(
        [PSCustomObject]$changeStats,
        [string]$analysisResultDirectory
    )

    # 変更されたファイルの一覧を取得
    $modifiedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "A" -or $_.ShortStatus -eq "M" } | Select-Object -ExpandProperty FileName
    $renamedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "R" } | Select-Object -ExpandProperty NewFileName
    $targetFiles = $modifiedFiles + $renamedFiles

    $coverageReportFile = Join-Path $analysisResultDirectory "coverages\report\Cobertura.xml"

    # CoverageReport(Xml)を読み込む
    $coverageReportXml = [xml](Get-Content $coverageReportFile)

    $classes = @()

    foreach ($targetFile in $targetFiles) {
        $targetPath = Convert-Path $targetFile

        # CoverageReport(Xml)のfilenameが変更されたファイルのパスと一致するものをリストに格納
        $classes += $coverageReportXml.coverage.packages.package.classes.class | 
        Where-Object { $_.filename -ieq $targetPath } | 
        Select-Object *, @{Name = "targetfile"; Expression = { $targetFile } }
    }

    $coverageMessage = @()
    $errorOrWarningStatus = [int]0

    #$classes をブランチカバレッジ、行カバレッジの低い順に並び替え、topN件を取得
    $topNClasses = $classes | Sort-Object { [double]$_.'branch-rate', [double]$_.'line-rate' } | Select-Object -First $topNClass

    # クラスのカバレッジ情報とメソッドのカバレッジ情報を取得
    foreach ($class in $topNClasses) {
        # ブランチカバレッジ、行カバレッジが低い順にメソッドを並び替え、topN件を取得
        foreach ($method in $class.methods.method | Sort-Object { [double]$_.'branch-rate', [double]$_.'line-rate' } | Select-Object -First $topNMethod) {
            $method.'line-rate' = [math]::Round([double]$method.'line-rate' * 100, 2)
            $method.'branch-rate' = [math]::Round([double]$method.'branch-rate' * 100, 2)
            $method.complexity = [int]$method.complexity
      
            # 行カバレッジ、ブランチカバレッジの閾値を超えている場合はアイコンを表示
            $lineRateIcon, $errorOrWarningStatus = Get-ErrorOrWarningIcon-Less-Than $method.'line-rate' $lineRateThreshold $errorOrWarningStatus
            $branchRateIcon, $errorOrWarningStatus = Get-ErrorOrWarningIcon-Less-Than $method.'branch-rate' $branchRateThreshold $errorOrWarningStatus
      
            # $lineRateIcon、$branchRateIcon がすべて空の場合はスキップ
            if ([string]::IsNullOrEmpty($lineRateIcon) -and [string]::IsNullOrEmpty($branchRateIcon)) {
                continue
            }

            $coverageMessage += "1. [$($class.name)](?_a=files&path=/$([URI]::EscapeUriString($class.targetfile))).$($method.name)($($method.'line-rate')$lineRateIcon, $($method.'branch-rate')$branchRateIcon, $($method.complexity))`r`n"
        }
    }

    $resultMessage = @()

    if ([string]::IsNullOrEmpty($coverageMessage)) {
        $resultMessage = @"
Waring, NG なし

"@
    }
    else {
        $resultMessage = @"
<details>
<summary>詳細</summary>

$coverageMessage
</details>

"@
    }

    return @"
### コードカバレッジ(行カバレッジ, 分岐カバレッジ, 複雑度) $(($safeIcon, $warningIcon, $errorIcon)[$errorOrWarningStatus])
$resultMessage
"@
}
