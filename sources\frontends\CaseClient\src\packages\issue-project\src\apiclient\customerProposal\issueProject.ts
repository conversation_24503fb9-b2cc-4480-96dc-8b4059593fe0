import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type {
  Pagination,
  ApiResult,
} from '@hox/base/src/apiclient/shared/types'

export const issueInformationSchema = z.object({
  id: z.string(),
  issueNumber: z.number(),
  issueItem: z.number(),
  customerIssuePerception: z.number(),
  overview: z.string().max(50),
  issue: z.string().max(500),
  currentStatus: z.string().max(500),
  ideal: z.string().max(500),
  industryDetail: z.number(),
  keyPerson: z.string().max(50).nullish(),
  registeredDateTime: z.date(),
  updatedDateTime: z.date(),
})

export const issueInformationSchemaForSave = issueInformationSchema.omit({
  id: true,
  issueNumber: true,
  registeredDateTime: true,
  updatedDateTime: true,
})

export const issueInformationSchemaForUpdate = z.object({
  issueItem: z.number(),
  customerIssuePerception: z.number(),
  overview: z.string().max(10),
  issue: z.string().max(500),
  currentStatus: z.string().max(500),
  ideal: z.string().max(500),
  industryDetail: z.number(),
  keyPerson: z.string().max(50).nullish(),
})

export type IssueInformation = z.infer<typeof issueInformationSchema>

export const projectInformationSchema = z.object({
  id: z.string(),
  issueProjectTeam: z.number().nullable(),
  staffName: z.string().nullable(),
  staffId: z.string().nullable(),
  proposalFee: z.number().nullable(),
  proposalDate: z.date().nullable(),
  consultingContractDate: z.date().nullable(),
  contractFee: z.number().nullable(),
  consultingStartDate: z.date().nullable(),
  dueDate: z.date().nullable(),
  issueResolvedDate: z.date().nullable(),
  issueCanceledDate: z.date().nullable(),
  cooperationWith: z.string().max(50).nullable(),
  issueProjectId: z.string(),
})

export const projectInformationSchemaForSave = projectInformationSchema.omit({
  id: true,
  issueProjectId: true,
})

export const projectInformationSchemaForUpdate = z.object({
  issueProjectTeam: z.number().nullable(),
  staffName: z.string().nullable(),
  staffId: z.string().nullable(),
  proposalFee: z.number().nullable(),
  proposalDate: z.date().nullable(),
  consultingContractDate: z.date().nullable(),
  contractFee: z.number().nullable(),
  consultingStartDate: z.date().nullable(),
  dueDate: z.date().nullable(),
  issueResolvedDate: z.date().nullable(),
  issueCanceledDate: z.date().nullable(),
  cooperationWith: z.string().max(50).nullable(),
})

export type ProjectInformation = z.infer<typeof projectInformationSchema>

export const deadlineManagementSchema = z.object({
  id: z.string(),
  status: z.number().nullable(),
  nextActionforCustomer: z.number().nullable(),
  other: z.string().max(50).nullable(),
  issueProjectId: z.string(),
})

export const deadlineManagementSchemaForSave = deadlineManagementSchema.omit({
  id: true,
  issueProjectId: true,
})

export const deadlineManagementSchemaForUpdate = z.object({
  status: z.number().nullable(),
  nextActionforCustomer: z.number().nullable(),
  other: z.string().max(50).nullable(),
})

export type DeadlineManagement = z.infer<typeof deadlineManagementSchema>

export const issueProjectLinkSchema = z.object({
  id: z.string(),
  title: z.string(),
  url: z.string().url(),
  updater: z.string(),
  updaterId: z.string(),
  updatedDateTime: z.date(),
  issueProjectId: z.string(),
  version: z.string(),
})

export const issueProjectLinksSchema = z.array(issueProjectLinkSchema)

export const issueProjectLinkSchemaForCreate = issueProjectLinkSchema.omit({
  id: true,
  updater: true,
  updaterId: true,
  updatedDateTime: true,
  issueProjectId: true,
  version: true,
})

export const issueProjectLinksSchemaForCreate = z.array(
  issueProjectLinkSchemaForCreate,
)

export type IssueProjectLinksForCreate = z.infer<
  typeof issueProjectLinksSchemaForCreate
>

export const issueProjectLinkSchemaForSave = issueProjectLinkSchema.omit({
  issueProjectId: true,
  updater: true,
  updaterId: true,
  updatedDateTime: true,
})

export type IssueProjectLink = z.infer<typeof issueProjectLinkSchema>
export type IssueProjectLinks = z.infer<typeof issueProjectLinksSchema>

export const issueProjectSchema = z.object({
  id: z.string(),
  customerIdentificationId: z.string().uuid(),
  issueInformation: issueInformationSchema,
  projectInformation: projectInformationSchema,
  deadlineManagement: deadlineManagementSchema,
  issueProjectLinks: issueProjectLinksSchema.nullable(),
  version: z.string(),
})

export type IssueProject = z.infer<typeof issueProjectSchema>

export const issueProjectSchemaForCreate = z.object({
  customerIdentificationId: z.string().uuid(),
  issueInformation: issueInformationSchemaForSave,
  projectInformation: projectInformationSchemaForSave,
  deadlineManagement: deadlineManagementSchemaForSave,
  issueProjectLinks: z.array(z.string()).nullable(),
})

export type IssueProjectForCreate = z.infer<typeof issueProjectSchemaForCreate>

export const issueProjectSchemaForUpdate = z.object({
  id: z.string(),
  issueInformation: issueInformationSchemaForUpdate,
  projectInformation: projectInformationSchemaForUpdate,
  deadlineManagement: deadlineManagementSchemaForUpdate,
  issueProjectLinks: z.array(z.string()).nullable(),
  version: z.string(),
})

export type IssueProjectForUpdate = z.infer<typeof issueProjectSchemaForUpdate>

export const issueProjectSchemaForSearch = z.object({
  id: z.string(),
  issueNumber: z.number(),
  registeredDateTime: z.date(),
  issueItem: z.number().nullable(),
  overview: z.string(),
  staffName: z.string(),
  status: z.number().nullable(),
  updatedDateTime: z.date(),
  version: z.string(),
})

export type IssueProjectForSearch = z.infer<typeof issueProjectSchemaForSearch>

export type FindIssueProjectCriteria = {
  customerIdentificationId?: string
  issueItems?: (string | number)[]
  status?: (string | number)[]
  isNullStatus?: boolean
} & Pagination

export type FindIssueProjectResultItem = IssueProjectForSearch
export type FindIssueProjectResult = ApiResult<FindIssueProjectResultItem>

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindIssueProject(query: Ref<FindIssueProjectCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindIssueProjectResult>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/issueproject'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

function transformDates(data: any): any {
  if (!data) return data
  
  const dateFields = [
    'proposalDate',
    'consultingContractDate', 
    'consultingStartDate',
    'dueDate',
    'issueResolvedDate',
    'issueCanceledDate',
    'createdAt',
    'updatedAt'
  ]
  
  if (data.projectInformation) {
    dateFields.forEach(field => {
      if (data.projectInformation[field] && typeof data.projectInformation[field] === 'string') {
        data.projectInformation[field] = new Date(data.projectInformation[field])
      }
    })
  }
  
  dateFields.forEach(field => {
    if (data[field] && typeof data[field] === 'string') {
      data[field] = new Date(data[field])
    }
  })
  
  if (data.issueInformation) {
    if (data.issueInformation.registeredDateTime && typeof data.issueInformation.registeredDateTime === 'string') {
      data.issueInformation.registeredDateTime = new Date(data.issueInformation.registeredDateTime)
    }
    if (data.issueInformation.updatedDateTime && typeof data.issueInformation.updatedDateTime === 'string') {
      data.issueInformation.updatedDateTime = new Date(data.issueInformation.updatedDateTime)
    }
  }
  
  return data
}

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProject(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProject>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/issueproject/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return transformDates(data)
        },
      }),
    ),
  )
}

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostIssueProject(body: Ref<IssueProjectForCreate>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProject>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/issueproject'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return transformDates(data)
        },
      }),
    ),
  )
}

/**
 * 更新処理を行う
 * @param body Ref<T> 更新するデータ
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutIssueProject(body: Ref<IssueProjectForUpdate>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProject>(
    useFetch(
      () => 
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueproject',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: IssueProject) {
          return transformDates(data)
        },
      }),
    ),
  )
}

/**
 * 削除処理を行う
 * @param params Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteIssueProject(
  params: Ref<Pick<IssueProject, 'id' | 'version'>>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () => 
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/issueproject/${params.value.id}`,
        ),
      withDefaultFetchOptions({
        method: 'DELETE',
        params: computed(() => ({ version: params.value.version })),
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

export const batchUpdateStaffCommandSchema = z.object({
  customerIdentificationIdList: z.array(z.string()),
  currentStaffId: z.string(),
  changeStaffId: z.string(),
  changeStaffName: z.string(),
})

export type BatchUpdateStaffCommand = z.infer<typeof batchUpdateStaffCommandSchema>

export function useIssueProjectBatchUpdateCustomerStaff(
  body: Ref<BatchUpdateStaffCommand>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<string>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueproject/batch-update-staff',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

export const issueProjectOptionSchema = z.object({
  text: z.string(),
  value: z.union([z.string(), z.number()]),
})

export type IssueProjectOption = z.infer<typeof issueProjectOptionSchema>

export const optionsOfIssueProjectDtoSchema = z.object({
  issueStatusList: z.array(issueProjectOptionSchema),
  issueItemList: z.array(issueProjectOptionSchema),
  customerIssuePerceptionList: z.array(issueProjectOptionSchema),
  industryDetailList: z.array(issueProjectOptionSchema),
  nextActionforCustomerList: z.array(issueProjectOptionSchema),
  issueProjectTeamList: z.array(issueProjectOptionSchema),
  issueProjectTaskStatusList: z.array(issueProjectOptionSchema),
})

export type OptionsOfIssueProjectDto = z.infer<typeof optionsOfIssueProjectDtoSchema>
export type GetOptionsResult = OptionsOfIssueProjectDto

/**
 * オプションデータを取得する
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProjectOptions() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetOptionsResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueproject/get-options',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

export const issueProjectCrossCustomerDtoSchema = z.object({
  id: z.string(),
  issueNumber: z.number(),
  branchNumber: z.string(),
  customerName: z.string(),
  cifNumber: z.string(),
  registeredDateTime: z.date(),
  industryDetail: z.number(),
  issueItem: z.number(),
  overview: z.string(),
  status: z.number().nullable(),
  staffId: z.string().nullable(),
  staffName: z.string().nullable(),
  issueProjectTeam: z.number().nullable(),
  customerStaffName: z.string().nullable(),
  dueDate: z.date().nullable(),
  customerIdentificationId: z.string().uuid(),
  updatedDateTime: z.date(),
  isAccessRestricted: z.boolean(),
  version: z.string(),
})

export const issueProjectCrossCustomerSchema = z.object({
  result: issueProjectCrossCustomerDtoSchema,
  findMessage: z.string(),
})

export type IssueProjectCrossCustomer = z.infer<
  typeof issueProjectCrossCustomerSchema
>

export type FindIssueProjectCrossCustomerCriteria = {
  branchNumbers?: string[]
  customerName?: string
  customerStaffIds?: string[]
  industryDetails?: number[]
  issueItems?: number[]
  overview?: string
  statuses?: number[]
  issueProjectStaffIds?: string[]
  issueProjectTeams?: number[]
  fromDate?: Date
  toDate?: Date
} & Pagination

export type FindIssueProjectCrossCustomerResultItem = IssueProjectCrossCustomer
export type FindIssueProjectCrossCustomerResult =
  ApiResult<FindIssueProjectCrossCustomerResultItem>

/**
 * 顧客を横断してデータを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindIssueProjectCrossCustomer(
  query: Ref<FindIssueProjectCrossCustomerCriteria>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindIssueProjectCrossCustomerResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueproject/find-cross-customer',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
