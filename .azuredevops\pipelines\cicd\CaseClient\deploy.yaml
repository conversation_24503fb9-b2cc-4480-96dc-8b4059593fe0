jobs:
- deployment: CaseClientAppDeploy
  environment: ${{ parameters.env }}
  pool:
    vmImage: $(vmImageName)
  strategy:
    runOnce:
      deploy:
        steps:
        - download: current
          artifact: CaseClient
        - checkout: none

        - task: AzureRMWebAppDeployment@4
          displayName: Deploy to Azure Web App (Node.js)
          inputs:
            AppType: webAppLinux
            ConnectedServiceName: $(azureSubscription)
            WebAppName: $(CaseClient.WebAppName)
            Package: $(Pipeline.Workspace)/CaseClient/CaseClient.zip
            DeployToSlotOrASEFlag: true
            ResourceGroupName: $(resourceGroupName)
            SlotName: staging
