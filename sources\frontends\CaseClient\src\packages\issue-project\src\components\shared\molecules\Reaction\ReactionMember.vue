<template>
  <div class="reaction-icon-container">
    <v-card>
      <v-card-text>
        <!-- リアクション名 -->
        <div class="reaction-users-container">
          <label>
            {{ getReactionDisplayName(props.reactionType) }}
          </label>
        </div>

        <!-- リアクションした人 -->
        <div
          v-for="(staffName, index) in setReactionStaffNames(
            props.reactions,
            props.reactionType,
          )"
          :key="index"
          style="text-align: left; margin: 5px 0"
        >
          {{ staffName }}
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { REACTION_TYPE } from '@ibp/issue-project/src/constants/domain/reactionType'
import type {
  issueProjectDiscussionThreadReactionType,
  issueProjectDiscussionCommentReactionType,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
const props = defineProps<{
  reactionType: number
  reactions:
    | issueProjectDiscussionThreadReactionType[]
    | issueProjectDiscussionCommentReactionType[]
}>()

const getReactionDisplayName = (reactionType: number) => {
  const displayNames = [
    '', // インデックス0は空文字
    REACTION_TYPE[1], // いいね・確認しました
    REACTION_TYPE[2], // おめでとう・契約お疲れ様
    REACTION_TYPE[3], // ok
    REACTION_TYPE[4], // 驚き
  ]

  return displayNames[reactionType] || ''
}

const setReactionStaffNames = (
  reactions:
    | issueProjectDiscussionThreadReactionType[]
    | issueProjectDiscussionCommentReactionType[],
  reactionType: number,
) => {
  const selectReactions = reactions.filter(
    (x) => x.reactionType === reactionType,
  )
  const selectStaffNames = selectReactions.map((x) => x.staffName)
  return selectStaffNames
}
</script>

<style scoped>
.reaction-users-container {
  font-size: 10px;
}

.reaction-icon-container {
  position: absolute;
  font-size: 12px;
  top: 30px;
  left: 0;
  min-width: 155px;
}
</style>
