// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.GetEntity;

public record GetEntityResult(
    /* TODO: 結果のプロパティを追加します。 */
    string Id,
    string Name,
    DateTimeOffset PeriodFrom,
    DateTimeOffset PeriodTo,
    int Amount,
    bool IsAssumption,
    string Version,
    List<GetEntityResultDetail> Members
);

public record GetEntityResultDetail(string Id, string MemberId, string Name, int UnitPricePerHour);
