# @hox/base定義

## 概要

本ドキュメントは、app-architecture-template-frontend-baseで定義されているフロントエンドアーキテクチャの設計思想、構成要素、実装ガイドラインを説明します。このアーキテクチャは、Nuxt 3を基盤としたモジュラー設計を採用し、再利用可能なコンポーネントとプラグインによって構成されています。

## アーキテクチャ概要

### 基本設計思想

- **Layered Architecture**: Nuxt Layer機能を活用したモジュラー設計
- **Component-Based Design**: Atomic Designパターンに基づくコンポーネント設計
- **Plugin-Driven**: プラグインベースの機能拡張システム
- **Type Safety**: TypeScriptによる型安全性の確保
- **Testing First**: Vitestを使用したテスト駆動開発

### 技術スタック

| 技術領域 | 採用技術 |
|---------|----------|
| Framework | Nuxt 3.14+ |
| Runtime | Node.js 20.11.0 (Volta管理) |
| UI Library | Vuetify 3.6+ |
| Authentication | Azure MSAL |
| Testing | Vitest + Vue Test Utils |
| Build Tool | Vite |
| Linting | ESLint + Prettier |
| Package Manager | npm |

## ディレクトリ構造

```
src/
├── apiclient/          # API通信関連
├── app/               # Nuxtアプリケーション設定
├── assets/            # 静的アセット
├── components/        # UIコンポーネント
│   └── shared/        # 共有コンポーネント
│       ├── atom/      # 基本コンポーネント
│       ├── molecules/ # 複合コンポーネント
│       ├── pages/     # ページ固有コンポーネント
│       └── templates/ # テンプレートコンポーネント
├── composables/       # Vue Composables
│   └── shared/        # 共有ロジック
├── layouts/           # レイアウトコンポーネント
├── middleware/        # ルートミドルウェア
├── models/            # データモデル定義
├── pages/             # ページコンポーネント
├── plugins/           # Nuxtプラグイン
├── public/            # 公開ファイル
└── utils/             # ユーティリティ関数
```

## コンポーネント定義

components配下に定義されているコンポーネントを表形式で列挙し、概要を説明します。

### Atomic Components (atom/)

| コンポーネント名 | 概要 | 主な機能 |
|-----------------|------|----------|
| AppTextField | 拡張テキストフィールド | requiredMark、textAlign、noInputTo対応 |
| AppNumberField | 数値専用入力フィールド | 数値入力の専用バリデーション |
| AppTextarea | テキストエリア | requiredMark、textAlign、lineHeight設定 |
| AppBtn | 基本ボタン | 共通のボタンスタイルと属性 |
| AppMainBtn | メインボタン | プライマリアクション用 |
| AppSubBtn | サブボタン | セカンダリアクション用 |
| AppDangerousBtn | 危険操作ボタン | 削除などの危険な操作用 |
| AppAutocomplete | オートコンプリート | 自動補完機能付き選択 |
| AppSimpleSelect | シンプルセレクト | 基本的な選択ボックス |
| AppSimpleOptGroupSelect | グループ選択 | オプショングループ付き選択 |
| AppSimpleDataTable | データテーブル | 基本的なデータ表示テーブル |
| AppPopupDatePicker | 日付選択 | ポップアップ式日付ピッカー |
| AppPopupDateOnlyPicker | 日付のみ選択 | 時刻を除いた日付専用ピッカー |
| AppFileInput | ファイル入力 | ファイルアップロード機能 |

### Molecular Components (molecules/)

| コンポーネント名 | 概要 | 主な機能 |
|-----------------|------|----------|
| AppConfirmDialog | 確認ダイアログ | ユーザー操作の確認用ダイアログ |
| AppListEdit | リスト編集 | データ一覧の編集機能（追加・削除・編集） |
| AppToasts | トースト通知 | アプリケーション全体の通知管理 |

### Page Components (pages/)

| ディレクトリ | コンポーネント名 | 概要 |
|-------------|-----------------|------|
| error/ | Error | 一般的なエラー表示 |
| error/ | Error403 | 権限不足エラー表示 |
| error/ | Error404 | ページ未発見エラー表示 |
| error/ | Error504 | サーバータイムアウトエラー表示 |
| error/ | Unknown | 未知のエラー表示 |

### Template Components (templates/)

| コンポーネント名 | 概要 | 主な機能 |
|-----------------|------|----------|
| AppEditPageTmpl | 編集ページテンプレート | セクション管理、ナビゲーション機能付き |
| AppEditItemGroupTmpl | アイテムグループ編集 | グループ化されたアイテムの編集 |
| AppSearchPageTmpl | 検索ページテンプレート | 検索条件とリスト表示の統合 |
| AppSearchDialogTmpl | 検索ダイアログテンプレート | ダイアログ形式の検索機能 |
| AppSimpleDialogTmpl | シンプルダイアログテンプレート | 基本的なダイアログレイアウト |

## 共通処理ライブラリ

composables配下に定義されているロジックを表形式で列挙し、概要を説明します。

| Composable名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| useAppToasts | トーストメッセージ管理 | アプリケーション全体でのトーストメッセージ管理 | 成功・エラー・警告通知の表示 |
| useBreadcrumbs | パンくずナビ管理 | 履歴管理と現在位置の追跡 | ナビゲーションパスの表示 |
| useValidation | バリデーション | Zodスキーマベースのリアルタイムバリデーション | フォーム入力値の検証 |
| useCriteriaUrlState | URL状態同期 | 検索条件とURLクエリパラメータの同期 | 検索条件の永続化・共有 |
| usePersistState | 状態永続化 | ローカルストレージとの状態同期 | ユーザー設定の保存 |
| useRouterEx | ルーター拡張 | Nuxtルーターの拡張版 | ページ遷移の高度な制御 |
| useFlagCondition | 条件フラグ管理 | 条件フラグの管理 | 表示・機能の条件制御 |
| useWatchDataChanges | データ変更監視 | データ変更の監視 | 未保存データの検出 |
| usePluginErrorMarker | プラグインエラー管理 | プラグイン初期化エラーの追跡 | 内部エラーハンドリング |

## ユーティリティ関数

utils配下に定義されているユーティリティ関数を表形式で列挙し、概要を説明します。

### 共有ユーティリティ (shared/)

| ファイル名 | 機能 | 概要 | 主な用途 |
|-----------|------|------|----------|
| obju.ts | Object操作 | オブジェクトのプロパティ初期化、深いマージ操作 | データの初期化・統合処理 |
| is.ts | 型判定 | 包括的な型判定関数群 | 動的型チェック・バリデーション |
| DateOnly.ts | 日付処理 | 時間を除いた日付のみの型安全な処理 | 日付のみの業務ロジック |
| logging.ts | ログ管理 | 階層的ログレベル管理 | アプリケーションログ出力 |
| userInfoStore.ts | ユーザー情報管理 | セッションベースのユーザー情報保存 | 認証状態の管理 |
| typeUtils.ts | 型変換ユーティリティ | 型の変換と操作のヘルパー関数 | 型安全なデータ変換 |
| msalHelper.ts | MSAL認証ヘルパー | Azure MSAL認証の補助機能 | 認証処理の簡素化 |
| confirmBeforeUnload.ts | ページ離脱確認 | 編集中データの保護機能 | データ損失防止 |
| zodDefaultInstance.ts | Zodインスタンス | デフォルトZodバリデーター設定 | スキーマ検証の統一 |

### ログ機能 (logging/)

| ファイル名 | 機能 | 概要 |
|-----------|------|------|
| consoleReporter.ts | コンソールログ出力 | ブラウザコンソールへのログ出力 |
| serverReporter.ts | サーバーログ送信 | サーバーサイドへのログ送信 |

### その他

| ファイル名 | 機能 | 概要 |
|-----------|------|------|
| createErrors.ts | エラー生成 | 統一されたエラーオブジェクト生成 |
| redirectTo.ts | リダイレクト処理 | ページリダイレクトの統一処理 |

## データモデル

models配下に定義されているデータモデルを表形式で列挙し、概要を説明します。

| ファイル名 | 機能 | 概要 | 主な用途 |
|-----------|------|------|----------|
| DateOnly.ts | 日付専用クラス | 時刻を持たない日付のみのデータモデル | 業務日付の型安全な処理 |

### DateOnlyクラスの主要メソッド

| メソッド名 | 機能 | 概要 |
|-----------|------|------|
| fromDate() | Date変換 | Date型からDateOnlyインスタンスを生成 |
| fromString() | 文字列変換 | yyyy-MM-DD形式文字列からインスタンス生成 |
| toDate() | Date生成 | DateOnly型からDate型を生成 |
| toString() | 文字列化 | yyyy-MM-DD形式の文字列に変換 |

## API通信ライブラリ

apiclient配下に定義されているAPI通信関連の機能を表形式で列挙し、概要を説明します。

| ファイル名 | 機能 | 概要 | 主な用途 |
|-----------|------|------|----------|
| createReturnableExecuter.ts | 実行可能オブジェクト生成 | キャンセル可能なAPI実行機能 | 長時間処理の制御 |
| withDefaultFetchOptions.ts | Fetchオプション設定 | デフォルトのHTTPリクエスト設定 | API呼び出しの統一 |
| types.d.ts | 型定義 | API通信で使用する型定義 | 型安全なAPI通信 |
| index.ts | エントリポイント | API通信機能のエクスポート | 機能の統一アクセス |
| empty.ts | 空実装 | プレースホルダー実装 | 開発時の代替処理 |

## レイアウト

layouts配下に定義されているレイアウトコンポーネントを表形式で列挙し、概要を説明します。

| ファイル名 | 機能 | 概要 | 主な用途 |
|-----------|------|------|----------|
| empty.vue | 空レイアウト | 最小限のレイアウト構成 | シンプルページ用 |

## ページ

pages配下に定義されているページコンポーネントを表形式で列挙し、概要を説明します。

| ディレクトリ | ファイル名 | 機能 | 概要 |
|-------------|-----------|------|------|
| / | index.vue | トップページ | アプリケーションのホーム画面 |
| auth/ | login.vue | ログインページ | ユーザー認証画面 |
| auth/ | redirect.vue | リダイレクトページ | 認証後のリダイレクト処理 |

## プラグイン

pluginsディレクトリ配下に定義されているファイルについて表形式で列挙し、概要を説明します。

| 順序 | ファイル名 | 機能 | 概要 |
|------|----------|------|------|
| 01 | errorHandle.ts | エラーハンドリング | アプリケーション全体のエラー処理 |
| 02 | endpoints.ts | APIエンドポイント管理 | API呼び出し先の統一管理 |
| 03 | logging.ts | ログ機能 | 統一ログ出力システム |
| 04 | vuetify.ts | UIライブラリ初期化 | Vuetifyの初期設定 |
| 05 | msal.ts | 認証機能 | Azure MSAL認証システム |
| 11 | zod.ts | スキーマバリデーション | 型スキーマ検証機能 |

## ミドルウェア

middlewareディレクトリ配下に定義されているミドルウェアについて表形式で列挙し、概要を説明します。

### グローバルミドルウェア

| ファイル名 | 機能 | 概要 |
|-----------|------|------|
| 00.pluginErrorCheck.global.ts | プラグインエラーチェック | プラグイン初期化エラーの検出 |
| 01.auth.global.ts | 認証状態確認 | ユーザー認証状態の確認 |
| 02.breadcrumbs.global.ts | パンくずナビ管理 | パンくずナビゲーションの更新 |
| 03.removetoasts.global.ts | トースト通知管理 | トースト通知の自動削除 |

## テスト戦略

### テスト構成

- **環境**: Vitest + Vue Test Utils
- **カバレッジ**: Istanbul provider使用
- **レポート**: JUnit形式での出力

### テストディレクトリ構造

```
test/
├── components/        # コンポーネントテスト
├── composables/       # Composablesテスト
├── models/           # モデルテスト
├── testSupport/      # テスト支援ユーティリティ
└── utils/            # ユーティリティテスト
```

## 品質管理

### ESLint設定

- **機能**: コードスタイル統一
- **対象**: Vue、TypeScriptファイル
- **ルール**: Stylistic、tooling機能有効
