<#
.SYNOPSIS
    プルリクエストの解析結果(.NET)をコメントとして投稿します。
.PARAMETER accessToken
    Azure DevOpsのアクセストークン。
.PARAMETER analysisResultDirectory
    静的分析結果が保存されているディレクトリのパス。
.PARAMETER testProjectRootDirectory
    テストプロジェクトのルートディレクトリのパス。
.PARAMETER sourceBranch
    ソースブランチの名前。
.PARAMETER threadId
    コメントを投稿するスレッドID。
.PARAMETER title
    プルリクエスト解析結果(.NET)のタイトル。
#>

param(
    [string]$accessToken,
    [string]$analysisResultDirectory,
    [string]$testProjectRootDirectory,
    [string]$sourceBranch,
    [string]$threadId,
    [string]$title
)

Import-Module (Join-Path $PSScriptRoot "get_file_change_stats_func.ps1") -Force
Import-Module (Join-Path $PSScriptRoot "get_dotnet_coverage_message_func.ps1") -Force
Import-Module (Join-Path $PSScriptRoot "get_dotnet_metrics_message_func.ps1") -Force
Import-Module (Join-Path $PSScriptRoot "get_dotnet_testcase_message_func.ps1") -Force

$fileChangeStats = Get-File-Changes-Stats "origin/$env:SYSTEM_PULLREQUEST_TARGETBRANCHNAME"
$repositoryUri = "$collectionUri$project/_git/$repository"

$coverageMessage = Get-DotNet-Coverage-Message $fileChangeStats $analysisResultDirectory
$metricsMessage = Get-DotNet-Metrics-Message $fileChangeStats $analysisResultDirectory
$testcaseMessage = Get-DotNet-Testcase-Message $fileChangeStats $analysisResultDirectory $testProjectRootDirectory $sourceBranch $repositoryUri 

$collectionUri = "$($env:SYSTEM_TEAMFOUNDATIONCOLLECTIONURI)"
$project = "$($env:SYSTEM_TEAMPROJECT)"
$repository = "$($env:BUILD_REPOSITORY_NAME)"
$pullRequestId = "$($env:SYSTEM_PULLREQUEST_PULLREQUESTID)"

$uri = "$collectionUri$project/_apis/git/repositories/$repository/pullRequests/$pullRequestId/threads/$threadId/comments?api-version=7.0"

$message = @"
## プルリクエスト解析結果 ($title)
$coverageMessage
$metricsMessage
$testcaseMessage
"@

# JSON形式のリクエストボディを作成
$body = @{
    "parentCommentId" = 0
    "content"         = $message
    "commentType"     = "text"
} | ConvertTo-Json

# 対象のスレッドにプルリクエスト解析結果コメントを投稿
Invoke-RestMethod -Uri $uri -Method Post -Headers @{
    Authorization = "Bearer $accessToken"
} -Body $body -ContentType "application/json"
