import type { CustomerStaff } from '@ibp/issue-project/src/apiclient/customerFixedInformation/customerStaff'
import { useFindCustomerStaffByCustomerIdentificationId } from '@ibp/issue-project/src/apiclient/customerFixedInformation/customerStaff'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'

const { error: errorToast } = useAppToasts()
export type customerInfo = {
  id: string
  branchNumber?: string
  cifNumber?: string
  customerName?: string
  nameKana?: string
  nameKanji?: string
  address?: string
  phoneNumber?: string
  postalCode?: string
  customerStaff?: CustomerStaff | undefined
}

export const useCustomer = () => {
  // ** =====================================
  //  * State
  //  ====================================== */
  const customerState = {
    customerInfoState: useState<{ customerInfo: customerInfo | undefined }>(
      'customerInfo',
      () => ({
        customerInfo: undefined,
      }),
    ),

    customerStaffState: useState<{
      customerStaff: CustomerStaff | undefined
    }>('customerStaff', () => ({
      customerStaff: undefined,
    })),

    businessUnderstandingIdState: useState<{
      businessUnderstandingId: string | undefined
    }>('businessUnderstandingId', () => ({
      businessUnderstandingId: undefined,
    })),

    businessUnderstandingVersionState: useState<{
      businessUnderstandingVersion: string | undefined
    }>('businessUnderstandingVersion', () => ({
      businessUnderstandingVersion: undefined,
    })),
  }

  function getCustomerName(customerInfo: customerInfo) {
    if (!customerInfo) return ''
    const isEmpty = !customerInfo.nameKanji || customerInfo.nameKanji === ''
    return isEmpty ? customerInfo.nameKana : customerInfo.nameKanji
  }

  // ** =====================================
  //  * Getters
  //  ====================================== */
  const customerGetters = {
    getCustomerInfo() {
      if (customerState.customerInfoState.value.customerInfo) {
        customerState.customerInfoState.value.customerInfo.customerName =
          getCustomerName(customerState.customerInfoState.value.customerInfo)
        customerState.customerInfoState.value.customerInfo.customerStaff =
          customerState.customerStaffState.value.customerStaff
      }
      return customerState.customerInfoState.value.customerInfo
    },
    getBusinessUnderstandingId() {
      return customerState.businessUnderstandingIdState.value
        .businessUnderstandingId
    },
    getBusinessUnderstandingVersion() {
      return customerState.businessUnderstandingVersionState.value
        .businessUnderstandingVersion
    },
  }

  //  ** =====================================
  //  * Mutations
  //  ====================================== */
  const customerMutations = {
    addCustomerInfo(result: customerInfo) {
      customerState.customerInfoState.value.customerInfo = result
    },
    addCustomerStaff(result?: CustomerStaff) {
      customerState.customerStaffState.value.customerStaff = result
    },
    addBusinessUnderstandingId(result: string) {
      customerState.businessUnderstandingIdState.value.businessUnderstandingId =
        result
    },
    addBusinessUnderstandingVersion(result: string) {
      customerState.businessUnderstandingVersionState.value.businessUnderstandingVersion =
        result
    },
  }

  //  ** =====================================
  //  * Actions
  //  ====================================== */
  const customerActions = {
    async setCustomerInfo(customerInfo: customerInfo) {
      customerMutations.addCustomerInfo(customerInfo)

      const request = customerInfo.id
      const { data, executeWithResult: findData } =
        useFindCustomerStaffByCustomerIdentificationId(ref(request))
      const result = await findData()
      if (!result) {
        errorToast('エラーが発生しました。')
        return
      }

      const customerStaff = data.value
      if (customerStaff && customerStaff.length) {
        customerMutations.addCustomerStaff(customerStaff[0])
      } else {
        customerMutations.addCustomerStaff(undefined)
      }
    },
    setBusinessUnderstandingId(businessUnderstandingId: string) {
      customerMutations.addBusinessUnderstandingId(businessUnderstandingId)
    },
    setBusinessUnderstandingVersion(businessUnderstandingVersion: string) {
      customerMutations.addBusinessUnderstandingVersion(
        businessUnderstandingVersion,
      )
    },
  }

  return {
    customerState,
    customerGetters,
    customerMutations,
    customerActions,
  }
}
