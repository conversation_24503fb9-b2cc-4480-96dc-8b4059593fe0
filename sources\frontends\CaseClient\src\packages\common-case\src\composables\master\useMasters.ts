import { useFindAllBranchMaster, type BranchMaster } from '@ibp/common-case/src/apiclient/customerIdentifying/branchMaster'
import { useAllGetIndustryMaster, type IndustryMaster } from '@ibp/common-case/src/apiclient/customerIdentifying/industryMaster'
import { useGetAllCountry, type CountryMaster } from '@ibp/common-case/src/apiclient/customerUnderstanding/country'
import { useGetAllGeneralTransactionType, type GeneralTransactionType } from '@ibp/common-case/src/apiclient/customerProposal/generalTransactionType'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'

const { error: errorToast } = useAppToasts()

export const useMaster = () => {
  // ** =====================================
  //  * State
  //  ====================================== */
  const masterState = {
    branchesState: useState<{ branches: BranchMaster[] | undefined }>(
      'branches',
      () => ({
        branches: undefined,
      }),
    ),

    industriesState: useState<{
      industries: IndustryMaster[] | undefined
    }>('industries', () => ({
      industries: undefined,
    })),

    countriesState: useState<{ countries: CountryMaster[] | undefined }>(
      'countries',
      () => ({
        countries: undefined,
      }),
    ),

    generalTransactionTypesState: useState<{
      generalTransactionTypes: GeneralTransactionType[] | undefined
    }>('generalTransactionTypes', () => ({
      generalTransactionTypes: undefined,
    })),
  }

  // ** =====================================
  //  * Getters
  //  ====================================== */
  const masterGetters = {
    /**
     * 店番関連
     */
    branches() {
      return masterState.branchesState.value.branches
    },

    branchOptions() {
      return (
        masterState.branchesState.value.branches?.map(
          (branch: BranchMaster) => ({
            value: branch.number,
            text: `${branch.number}:${branch.name}`,
          }),
        ) ?? []
      )
    },

    /**
     * 業種関連
     */
    industries() {
      return masterState.industriesState.value.industries
    },

    industryOptions() {
      return (
        masterState.industriesState.value.industries?.map(
          (industry: IndustryMaster) => ({
            value: industry.code,
            text: industry.name,
          }),
        ) ?? []
      )
    },

    /**
     * 国関連
     */
    countries() {
      return masterState.countriesState.value.countries
    },

    countryOptions() {
      return (
        masterState.countriesState.value.countries?.map(
          (country: CountryMaster) => ({
            value: country.countryCode,
            text: country.countryName,
          }),
        ) ?? []
      )
    },

    /**
     * 案件項目関連
     */
    generalTransactionTypeOptions() {
      return (
        masterState.generalTransactionTypesState.value.generalTransactionTypes?.map(
          ({ id, name }) => ({
            value: id,
            title: name,
          }),
        ) ?? []
      )
    },

    registerableGeneralTransactionTypeOptions() {
      return (
        masterState.generalTransactionTypesState.value.generalTransactionTypes
          ?.filter(({ isRegisterable }) => isRegisterable)
          .map(({ id, name }) => ({
            value: id,
            text: name,
          })) ?? []
      )
    },

    generalTransactionTypeById: () => (id: string) => {
      const type =
        masterState.generalTransactionTypesState.value.generalTransactionTypes?.find(
          (type) => type.id === id,
        )
      return {
        isRegisterable: type?.isRegisterable ?? false,
        name: type?.name ?? '削除済みの項目',
      }
    },
  }

  //  ** =====================================
  //  * Mutations
  //  ====================================== */
  const masterMutations = {
    setBranches(branches: BranchMaster[]) {
      masterState.branchesState.value.branches = branches
    },

    setIndustries(industries: IndustryMaster[]) {
      masterState.industriesState.value.industries = industries
    },

    setCountries(countries: CountryMaster[]) {
      masterState.countriesState.value.countries = countries
    },

    setGeneralTransactionTypes(
      generalTransactionTypes: GeneralTransactionType[],
    ) {
      masterState.generalTransactionTypesState.value.generalTransactionTypes =
        generalTransactionTypes
    },
  }

  //  ** =====================================
  //  * Actions
  //  ====================================== */
  const masterActions = {
    /** 店番マスタの取得 */
    async fetchBranches() {
      const { data, executeWithResult: findData } = useFindAllBranchMaster()
      const result = await findData()

      if (!result) {
        errorToast('店番マスタが取得できませんでした。')
        return
      }
      masterMutations.setBranches(data.value ?? [])
    },

    /** 業種マスタの取得 */
    async fetchIndustries() {
      const { data, executeWithResult: findData } = useAllGetIndustryMaster()
      const result = await findData()

      if (!result) {
        errorToast('業種マスタが取得できませんでした。')
        return
      }
      masterMutations.setIndustries(data.value ?? [])
    },

    /** 国マスタの取得 */
    async fetchCountries() {
      const { data, executeWithResult: findData } = useGetAllCountry()
      const result = await findData()

      if (!result) {
        errorToast('国マスタが取得できませんでした。')
        return
      }
      masterMutations.setCountries(data.value ?? [])
    },

    /** 案件項目の取得 */
    async fetchGeneralTransactionTypes() {
      const { data, executeWithResult: findData } =
        useGetAllGeneralTransactionType()
      const result = await findData()

      if (!result) {
        errorToast('案件項目が取得できませんでした。')
        return
      }
      masterMutations.setGeneralTransactionTypes(data.value ?? [])
    },
  }

  return {
    masterState,
    masterGetters,
    masterMutations,
    masterActions,
  }
}
