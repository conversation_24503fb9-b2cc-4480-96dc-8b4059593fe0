<!-- markdownlint-disable -->

## 修正理由と修正対象
<!-- 画面の場合は対象画面名、共通機能の場合は元になった画面とどういう機能を作成したいのか -->
<!-- Please note "なし" when without report -->

## 実施したこと
<!-- 開発時に追加で何を調査したのか -->
<!-- テスト以外に確認したこと（Lintのチェックやテスターがテストできない確認内容） -->
<!-- 関係者に確認したこと -->
<!-- Please note "なし" when without report -->

## 実施しなかったこと
<!-- 確認を省略した内容や、気になっているが確認や対応ができなかったこと -->
<!-- Please note "なし" when without report -->

## 元になったソースと、元のソースからの変更点
<!-- 元になったNuxt2のソース、あるいは元になったソースは無し -->
<!-- 修正の方針（そのまま移植したいのか、機能変更した場合はどのように変更したのか、新規の場合はどのような機能になるのか）-->
<!-- Please note "なし" when without report -->

## テスト内容とテスト結果
<!-- 画面の場合はテスト結果 -->
<!-- 共通機能の場合は開発者が確認した内容 -->
<!-- Please note "なし" when without report -->

## レビューして欲しいこと
<!-- レビュー観点や、特にレビュー時に確認して欲しい内容 -->
<!-- Please note "なし" when without report -->

# Self Check Sheet
<!-- Please note Self Check Sheet in the comments section below! -->
The developers must complete the below check process before creating Pull Request.
**※Because of the limit of number of characters, please add the Self Checksheet content in the comment box.**
(The below contents were already added to the template file "[archway_developer.md - Repos](https://dev.azure.com/digital-v/product-enhancement/_git/life-design-app-for-AW?path=/.azuredevops/pull_request_template/archway_developer.md&_a=preview)")

### Coding

##### General  

1. [ ] Have you provided a complete PR description, including both the Description and the Self Check Sheet?
(Involves Neither Code Testing nor Performance Testing)

2. [ ] Have you reviewed any attached file specifications (if available)? 
(Involves Neither Code Testing nor Performance Testing)

3. [ ] Are schema, variable, file, method, and API names clear and consistent (e.g., `name + schemaFor + action`)? 
(Self Code Review)

4. [ ] Are schemas validated with full constraints, correct definitions, and proper placement? _(Avoid placing in_ `_ApiClient_` _if only used for specific pages/dialogs)_
(Self Code Review)

5. [ ] Have you replaced magic numbers or repeated values with constants?
(Self Code Review)

6. [ ] Have you cleaned up the code by removing unused variables, methods, and comments, and avoiding unnecessary blank lines?
(Self Code Review)

7. [ ] Are you formatting display data inside ApiClient? (This should be avoided)
(Self Code Review)

8. [ ] Is the conditional logic clear, with checks for all branches?
(Code Check and Manual Check)

9. [ ] Are there any console warnings/errors or ESLint issues remaining?  
(Self Code Review)

10. [ ] Has the content been confirmed with the client?
(Self Code Review)

11. [ ] Are you using any when defining types? (Avoid using any)
(Self Code Review)

12. [ ] Are API calls made with explicit parameters? (Avoid ambiguous calls like upload('1'))
(Self Code Review)

13. [ ] Is the use of async/await appropriate and cautious?
(Self Code Review)

14. [ ] Are you using existing components (app-...) instead of v-... when available?
(Self Code Review)

##### Search screen  

1. [ ] `app-search-page-tmpl` must have function in `:on-search`
(Self Code Review)
  

##### Detail and Edit screen: Fix template bugs in Wiki Developer [編集画面開発時の注意点は以下の通りです。](https://dev.azure.com/digital-v/product-enhancement/_wiki/wikis/product-enhancement.wiki/21925/Developer?anchor=%E7%B7%A8%E9%9B%86%E7%94%BB%E9%9D%A2%E9%96%8B%E7%99%BA%E6%99%82%E3%81%AE%E6%B3%A8%E6%84%8F%E7%82%B9%E3%81%AF%E4%BB%A5%E4%B8%8B%E3%81%AE%E9%80%9A%E3%82%8A%E3%81%A7%E3%81%99%E3%80%82)

1. [ ] Are you using _tempKey in editable lists (edit/remove, :list-item-key)?
(Self Code Review)
2. [ ] Is data validated before showing confirmation popups?
(Manual Testing)
 
3. [ ] Are boundary values handled properly?
(Manual Testing)
 
4. [ ] Have you called watcher.init(targetData) before router.push?
(Self Code Review)
5. [ ] Is there any business logic placed inside finally blocks? (This should be avoided)
(Self Code Review)
6. [ ] Are toast messages after save/update accurate and clear? 
(Manual Testing)
 
### Pull Request

1. [ ] Have you assigned the correct reviewer?
(Involves Neither Code Testing nor Manual Testing)

2. [ ] Have you clearly stated the User Story ID or Task ID in the Work Items?
(Involves Neither Code Testing nor Manual Testing)

3. [ ] If changes affect shared/common components or multiple screens, have you notified the entire team?
(Involves Neither Code Testing nor Manual Testing)

4. [ ] Have you assigned the correct reviewer?
(Involves Neither Code Testing nor Manual Testing)