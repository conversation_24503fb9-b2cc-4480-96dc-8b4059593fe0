import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type { Pagination, ApiResult } from '@hox/base/src/apiclient/shared/types'

// teamMember
export const teamMemberSchema = z.object({
  id: z.string(),
  teamId: z.string(),
  staffId: z.string(),
  isLeader: z.boolean(),
  version: z.string(),
})
export type TeamMember = z.infer<typeof teamMemberSchema>

// 主として扱うデータのスキーマを定義します。
export const teamSchema = z.object({
  id: z.string(),
  teamName: z.string(),
  teamMembers: z.array(teamMemberSchema),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type Team = z.infer<typeof teamSchema>

// =====================================================================================================================
// APIクライアントの定義(検索)
// =====================================================================================================================

export type FindTeamCriteria = {
  teamName?: string
} & Pagination

export type FindTeamResultItem = Team
// 検索結果の型にページングの情報を追加します。
export type FindTeamResult = ApiResult<FindTeamResultItem>

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindTeam(query: Ref<FindTeamCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindTeamResult>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/team'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(Idで検索)
// =====================================================================================================================

export type FindTeamByIdCriteria = {
  ids?: string | string[] | null
} & Pagination

export type FindTeamByIdResultItem = Team
export type FindTeamByIdResult = ApiResult<FindTeamByIdResultItem>

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindTeamById(query: Ref<FindTeamByIdCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindTeamByIdResult>(
    useFetch(
      () =>
        $endpoints.default.get('/ibp-customer-proposal/v1.0/team/findTeamById'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(Idで取得)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetTeam(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Team>(
    useFetch(
      () =>
        $endpoints.default.get(`/ibp-customer-proposal/v1.0/team/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(getAll)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetAllTeam() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Team[]>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/team/getAll'),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(getAllByStaffId)
// =====================================================================================================================

export type GetTeamMemberAllByStaffResult = {
  id: string
  staffId: string
  isLeader: boolean
  version: string
}

// Resultの型
export type GetTeamAllByStaffIdResult = {
  id: string
  teamName: string
  teamMembers: Array<GetTeamMemberAllByStaffResult>
  version: string
}

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetTeamAllByStaffId(staffId: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Team[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/team/getAllByStaffId/${staffId.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(追加)
// =====================================================================================================================

// 作成用のデータのスキーマを定義します。
export const teamSchemaForCreate = teamSchema.omit({ id: true, version: true })
// 作成用の型を作成します。
export type TeamForCreate = z.infer<typeof teamSchemaForCreate>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostTeam(body: Ref<TeamForCreate>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Team>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/team'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(更新)
// =====================================================================================================================

// 保存用のデータのスキーマを定義します。
export const teamSchemaForSave = teamSchema
// 保存用の型を作成します。
export type TeamForSave = z.infer<typeof teamSchemaForSave>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutTeam(body: Ref<TeamForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Team>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/team'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(削除)
// =====================================================================================================================

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteTeam(body: Ref<Pick<Team, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/team'),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}
