import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

// 主として扱うデータのスキーマを定義します。
export const industryMasterSchema = z.object({
  id: z.string(),
  code: z.string(),
  name: z.string(),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type IndustryMaster = z.infer<typeof industryMasterSchema>

// =====================================================================================================================
// APIクライアントの定義(全取得)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useAllGetIndustryMaster() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IndustryMaster[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-old-customer-identifying/v1.0/industrymaster/getAll',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}
