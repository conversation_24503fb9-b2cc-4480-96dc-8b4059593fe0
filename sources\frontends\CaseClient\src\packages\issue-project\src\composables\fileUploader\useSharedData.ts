import type { issueProjectDiscussionFileModelItem } from '@ibp/issue-project/src/models/issueProjectDiscussion/FileUploadType'
import type { DeleteItem } from '@ibp/issue-project/src/models/share/FileUploadType'

const dataRef = ref<{
  isFormValid: boolean
  isEnter: boolean
  dialog: boolean
  deleteTargetFile: issueProjectDiscussionFileModelItem | null
  deleteItemName: string | null
  deleteItemVersion: string | null
  chipFilesToDelete: Array<DeleteItem>
  sameFileNames: Array<string>
  inputFiles: Array<File>
  fileList: Array<string>
  isOk: boolean
  addedCommentId: string | null
  users: any
  filesToRemove: any
}>({
  isFormValid: true,
  isEnter: false,
  dialog: false,
  deleteTargetFile: null,
  deleteItemName: null,
  deleteItemVersion: null,
  chipFilesToDelete: [],
  sameFileNames: [],
  inputFiles: [],
  fileList: [],
  isOk: false,
  addedCommentId: null,
  users: [],
  filesToRemove: undefined,
})

const nowUploadFiles = ref<File[]>([])

export const useSharedData = () => {
  return {
    dataRef,
    nowUploadFiles,
  }
}
