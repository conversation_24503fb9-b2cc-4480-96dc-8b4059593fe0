{"private": true, "workspaces": ["src/packages/*"], "scripts": {"build": "cross-env NODE_ENV=\"production\" FORCE_NODE_ENV=\"production\" nuxt build", "dev": "nuxt dev --https --ssl-cert cert/cert.pem --ssl-key cert/key.pem", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "build:dev": "cross-env NODE_ENV=\"development\" FORCE_NODE_ENV=\"development\" nuxt build", "preview:dev": "dotenv-run-script .env .env.local.preview -- preview", "lint:js": "eslint .", "lint:style": "stylelint \"**/*.{vue,css,scss}\" --ignore-path .gitignore", "lint": "npm-run-all lint:js lint:style", "test": "vitest run", "coverage": "vitest run --coverage", "test:ui": "vitest --ui"}, "devDependencies": {"@nuxt/devtools": "^1.3.9", "@nuxt/eslint": "^0.3.13", "@nuxt/test-utils": "^3.13.1", "@types/lodash": "^4.17.17", "@types/node": "^20.14.11", "@types/qs": "^6.14.0", "@vitest/coverage-istanbul": "^1.6.0", "@vitest/ui": "^1.6.0", "@vue/test-utils": "^2.4.6", "cross-env": "^7.0.3", "dotenv-run-script": "^0.4.1", "eslint": "^8.57.0", "happy-dom": "^14.12.3", "msw": "^2.3.4", "npm-run-all": "^4.1.5", "postcss-html": "^1.7.0", "prettier": "^3.3.3", "sass": "^1.77.8", "stylelint": "^16.7.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "vitest": "^1.6.0"}, "dependencies": {"@azure/msal-browser": "^3.19.1", "@date-io/date-fns": "^3.0.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@hox/base": "^2.12.0", "@ibp/base": "^1.9.0", "@mdi/font": "^7.4.47", "@types/file-saver": "^2.0.7", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^10.11.0", "@vueuse/integrations": "^13.3.0", "date-fns": "^3.6.0", "defu": "^6.1.4", "file-saver": "^2.0.5", "lodash": "^4.17.21", "nuxt": "^3.12.3", "qs": "^6.14.0", "quill-delta-to-html": "^0.12.1", "quill-mention": "^4.1.0", "typescript": "^5.5.3", "ufo": "^1.5.4", "universal-cookie": "^7.2.2", "vite-plugin-vuetify": "^2.0.3", "vue": "^3.4.31", "vuetify": "^3.6.13", "zod": "^3.23.8"}, "volta": {"node": "20.11.0"}}