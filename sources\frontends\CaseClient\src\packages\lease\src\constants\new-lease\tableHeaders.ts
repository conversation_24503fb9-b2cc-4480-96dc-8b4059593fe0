import { format, parseJSON } from 'date-fns'

/**
 * リース案件一覧のテーブルヘッダー設定を取得する
 * @returns ヘッダー設定の配列
 */
export const getLeaseTableHeaders = () => {
  return [
    {
      title: '登録日',
      key: 'registeredAt',
      format: (val: any) => (val ? format(parseJSON(val), 'yyyy/MM/dd') : '-'),
      sortable: true,
    },
    {
      title: '顧客名',
      key: 'customerName',
      sortable: true,
    },
    {
      title: '案件名',
      key: 'caseName',
      sortable: true,
      type: 'maskTarget',
    },
    {
      title: '金額(千円)',
      key: 'amount',
      sortable: true,
      format: (val: any) => val ? val.toLocaleString() : '-',
    },
    {
      title: 'ステータス',
      key: 'caseStatus',
      sortable: true,
    },
    {
      title: '総合リース担当者',
      key: 'leaseStaffName',
      sortable: true,
    },
    {
      title: '案件担当者',
      key: 'staffName',
      sortable: true,
    },
    {
      title: '相談票',
      key: 'isConsultationTarget',
      sortable: true,
    },
    {
      title: '見積作成者',
      key: 'quotationCreateStaffName',
      sortable: true,
    },
    {
      title: '見積精査者',
      key: 'quotationScrutinizeStaffName',
      sortable: true,
    },
    {
      title: '顧客応諾確認者',
      key: 'customerProposalStaffName',
      sortable: true,
    },
    {
      title: '個別申請者',
      key: 'individualApplicationStaffName',
      sortable: true,
    },
    {
      title: 'お気に入り',
      key: 'isFavorite',
      sortable: true,
    },
  ]
}
