# 最新の会社基本情報が収集できている能力 - ドメイン言語定義

## 1. エンティティ（Entities）

### 法人顧客（BusinessCustomer）
- **顧客識別ID（CustomerIdentificationId）**: 顧客をIBP上で一意に識別するコード
  - 仕様: `GUID`
- **店番（BranchNumber）**: 契約主体の銀行支店を識別するコード
  - 仕様: `ALPHANUMERIC_MAX_5`
- **CIF番号（CifNumber）**: 銀行顧客を業務的に一意に識別するコード
  - 仕様: `ALPHANUMERIC_MAX_8`
- **会社名（CompanyName）**: 法人顧客の会社名
  - 仕様: `KANJI_MAX_20`
- **URL（Url）**：顧客の会社ホームページのURL
  - 仕様: `URI_FORMAT`
- **電話番号（PhoneNumber）**: 顧客の会社電話番号
  - 仕様: 値オブジェクト：電話番号
- **住所（Address）**: 顧客の会社住所
  - 仕様: 値オブジェクト：住所
- **代表者名（RepresentativeName）**: 代表者の氏名（漢字）
  - 仕様: `KANJI_MAX_20`
- **業種コード（IndustryCode）**: 業種を一意に識別するコード
  - 仕様: `ALPHANUMERIC_MAX_5`
- **従業員数（NumberOfEmployee）**：法人に所属する従業員数
  - 仕様: `NUMERIC_MAX_20`
- **設立年月日（FoundedDate）**：法人の設立年月日
  - 仕様: `DATE`
- **資本金（千円）（Capital）**：法人の資本金（単位：千円）
  - 仕様: `NUMERIC_MAX_20`
- **ローン格付（LoanRating）**：ローン格付内容
  - 仕様: `KANJI_MAX_20`
- **債務者区分（BorrowerClassification）**: 債務者の区分コード
  - 仕様: `ALPHANUMERIC_MAX_5`
- **格付更新日（LoanRatingUpdatedAt）**：ローン格付を更新した日次
  - 仕様: `DATE_FORMAT`
- **更新日時（UpdatedAt）**：最後に情報が更新された日時
  - 仕様: `DATE_FORMAT`

### 業種（IndustryMaster）
- **ID（ID）**: 業種をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **コード（Code）**: 業種を業務的に一意に識別するコード、日本標準産業分類（https://www.soumu.go.jp/main_content/000420038.csv）
  - 仕様: `ALPHANUMERIC_MAX_5`
- **大分類名称（MajorCategoryName）**: 業種大分類名称
  - 仕様: `KANJI_MAX_50`
- **中分類名称（MediumCategoryName）**: 業種中分類名称
  - 仕様: `KANJI_MAX_50`
- **小分類名称（SmallCategoryName）**: 業種小分類名称
  - 仕様: `KANJI_MAX_50`
- **細分類名称（DetailCategoryName）**: 業種細分類名称
  - 仕様: `KANJI_MAX_50`

### オウンドメディア（OwnedMedia）
- **ID（ID）**: オウンドメディアをシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 顧客をIBP上で一意に識別するコード
  - 仕様: `GUID`
- **オウンドメディア種別（MediaType）**: 発信媒体
  - 仕様: 列挙型：オウンドメディア種別 
- **URL（Url）**：オウンドメディアのURL
  - 仕様: `URI_FORMAT`
- **その他オウンドメディア（OtherMediaType）**: 種別がグループで管理されていないオウンドメディアの種類
  - 仕様: `KANJI_MAX_30`
- **備考（Note）**: その他 オウンドメディアにかかわる補足情報
  - 仕様: `KANJI_MAX_100`
- **更新日時（UpdatedAt）**：最後に情報が更新された日時
  - 仕様: `DATE_FORMAT`

### 上場情報（ListingInformation）
- **顧客識別ID（CustomerIdentificationId）**: 顧客をIBP上で一意に特定する識別子
  - 仕様: `GUID`
- **上場先（ListedStockExchange）**: 上場先市場の種類
  - 仕様: 列挙型：上場先
  - 仕様: `ALPHANUMERIC_MAX_30`
- **上場区分（ListedMarket）**: 上場区分
  - 仕様: 列挙型：上場区分
  - 仕様: `ALPHANUMERIC_MAX_30`
- **備考（Comment）**: 上場情報に関するコメント
  - 仕様: `KANJI_MAX_100`
- **更新日時（UpdatedAt）**：最後に情報が更新された日時
  - 仕様: `DATE_FORMAT`

### 株主情報（Shareholder）
- **ID（ID）**: 株主情報をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 顧客をIBP上で一意に識別するコード
  - 仕様: `GUID`
- **株主顧客識別ID（ShareholderIdentificationId）**: 顧客識別ID の顧客法人の株主の顧客識別ID
  - 仕様: `GUID`
- **株主名（ShareholderName）**: 株主の氏名（漢字）
  - 仕様: `KANJI_MAX_20`
- **株主との関係（Relation）**: 株主と顧客法人との関係
  - 仕様: 列挙型：株主との関係
  - 仕様: `ALPHANUMERIC_MAX_40`
- **出資比率（InvestmentRatio）**: 株主の出資比率（％）
  - 仕様: `ALPHANUMERIC_MAX_3`、％表記、小数第2位まで
- **更新日時（UpdatedAt）**：最後に情報が更新された日時
  - 仕様: `DATE_FORMAT`

### 役員情報（DirectorInformation）
- **ID（ID）**: 役員情報をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 顧客をIBP上で一意に識別するコード
  - 仕様: `GUID`
- **役員の顧客識別ID（DirectorCustomerIdentificationId）**: 顧客識別ID の顧客法人に所属する役員の顧客識別ID
  - 仕様: `GUID`
- **役員氏名（DirectorName）**: 役員の氏名（漢字）
  - 仕様: `KANJI_MAX_20`
- **役職（Position）**: 役職
  - 仕様: 列挙型：役職
  - 仕様: `ALPHANUMERIC_MAX_30`
- **代取との関係（RelationWithRepresentative）**: 役員と代表取締役との関係
  - 仕様: 列挙型：代取との関係
  - 仕様: `ALPHANUMERIC_MAX_10`
- **更新日時（UpdatedAt）**：最後に役員情報が更新された日時
  - 仕様: `DATE_FORMAT`
- **CCIクラブ会員（IsCciClubMember）**: CCIクラブの会員かどうかを識別する
  - 仕様: `BOOLEAN`

### 代表者経歴（ExperienceOfRepresentative）
- **顧客識別ID（CustomerIdentificationId）**: 顧客をIBP上で一意に識別するコード
  - 仕様: `GUID`
- **代表者経歴（Experience）**: 代表者の主な経歴
  - 仕様: `MAX_2000`
- **更新日時（UpdatedAt）**：最後に経歴が更新された日時
  - 仕様: `DATE_FORMAT`

### グループ会社（GroupCompany）
- **ID（ID）**: グループ会社をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 顧客をIBP上で一意に識別するコード
  - 仕様: `GUID`
- **グループ会社の顧客識別ID（GroupCompanyIdentificationId）**: グループ会社の顧客識別ID
  - 仕様: `GUID`
- **会社名（CompanyName）**: グループ会社の会社名
  - 仕様: `KANJI_MAX_20`
- **会社との関係（CompanyRelationship）**: グループ会社の関係
  - 仕様: 列挙型：会社との関係
  - 仕様: `ALPHANUMERIC_MAX_30`
- **出資比率（InvestmentRatio）**: 株主の出資比率（％）
  - 仕様: `ALPHANUMERIC_MAX_3`、パーセント表示、小数第2位までの100以下の数値
- **コメント（Comment）**: コメント
  - 仕様: `KANJI_MAX_50`
- **更新日時（UpdatedAt）**：最後に情報が更新された日時
  - 仕様: `DATE_FORMAT`

### 事業概要（BusinessOverview）
- **顧客識別ID（CustomerIdentificationId）**: 顧客を一意に特定するIBP上の識別子
  - 仕様: `GUID`
- **事業概要（Summary）**: 顧客企業の事業概要
  - 仕様: `KANJI_MAX_500`
- **沿革（History）**: 顧客企業の沿革
  - 仕様: `ALPHANUMERIC_MAX_500`
- **更新日時（UpdatedAt）**: 情報を更新した日時
  - 仕様: `DATE_FORMAT`
  
### 顧客業種（IndustryInformation）
- **ID（ID）**: 業種情報をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 顧客をIBP上で一意に特定する識別子
  - 仕様: `GUID`
- **業種コード（IndustryCode）**: 顧客事業の業種を大項目で分類した分類コード
  - 仕様: `ALPHANUMERIC_MAX_5`
- **更新日時（UpdatedAt）**: 情報を更新した日時
  - 仕様: `DATE_FORMAT`

### 事業構成（Subject）
- **ID（ID）**: 事業構成をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 顧客を一意に特定するIBP上の識別子
  - 仕様: `GUID`
- **事業内容（BusinessContent）**: 顧客企業の事業を構成する事業内容
  - 仕様: `KANJI_MAX_100`
- **構成比率（CompositionRatio）**: 当該事業の事業全体に対する構成比率
  - 仕様: `ALPHANUMERIC_MAX_5`、整数値
- **更新日時（UpdatedAt）**: 情報を更新した日時
  - 仕様: `DATE_FORMAT`

### 国内事業所（DomesticOffice）
- **ID（ID）**: 国内事業所をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 顧客を一意に特定するIBP上の識別子
  - 仕様: `GUID`
- **名称（Name）**：事業所・工場の名称
  - 仕様: `KANJI_MAX_100`
- **住所（Address）**: 国内事業所の住所
  - 仕様: `KANJI_MAX_100`
- **更新日時（UpdatedAt）**: 情報を更新した日時
  - 仕様: `DATE_FORMAT`

### 海外事業所（OverSeasOffice）
- **ID（ID）**: 海外事業所をシステム上で一意に識別するコード
  - 仕様: `GUID`
- **顧客識別ID（CustomerIdentificationId）**: 顧客を一意に特定するIBP上の識別子
  - 仕様: `GUID`
- **名称（Name）**：事業所・工場の名称
  - 仕様: `KANJI_MAX_100`
- **国名コード（CountryCode）**: 海外事業所の所在国のコード
  - 仕様: `ALPHANUMERIC_MAX_20`
- **国名（CountryName）**：海外事業所の所在国名
  - 仕様: `KANJI_MAX_20`、外部情報：国一覧（マネロンシステム）
- **都市（City）**：海外事業所の所在都市
  - 仕様: `KANJI_MAX_100`
- **更新日時（UpdatedAt）**: 情報を更新した日時
  - 仕様: `DATE_FORMAT`

### 取引経緯（BusinessBackGround）
- **顧客識別ID（CustomerIdentificationId）**: 顧客を一意に特定するIBP上の識別子
  - 仕様: `GUID`
- **取引経緯（Detail）**: 企業との取引内容の履歴
  - 仕様: `KANJI_MAX_1000`
- **更新日時（UpdatedAt）**: 情報を更新した日時
  - 仕様: `DATE_FORMAT`


## 2. 値オブジェクト（Value Objects）

### 連絡先情報（ContactInfo）
- **住所（Address）**
  - 郵便番号(PostalCode): `POSTAL_CODE_FORMAT`
  - 都道府県(Prefecture): `KANJI_MAX_10`
  - 市区町村(City): `KANJI_MAX_40`
  - 番地(StreetAddress): `KANJI_MAX_40`
  - 建物名(BuildingName): `KANJI_MAX_40`
- **電話番号（PhoneNumber）**
  - 形式: `PHONE_NUMBER_FORMAT`
  - 種別: 固定電話、携帯電話
- **メールアドレス（EmailAddress）**
  - 形式: `EMAIL_FORMAT`
  - 種別: 会社、個人

## 3. 集約（Aggregates）

### 法人顧客集約（BusinessCustomerAggregate）
- **ルートエンティティ**: 法人顧客
- **含まれるエンティティ**:
- **不変条件**:
  - 顧客識別ID
  - 店番
  - CIF番号

### オウンドメディア集約（OwnedMediaAggregate）
- **ルートエンティティ**: オウンドメディア
- **含まれるエンティティ**:
- **不変条件**:
  - ID
  - 顧客識別ID

### 上場情報集約（ListingInformationAggregate）
- **ルートエンティティ**: 上場情報
- **含まれるエンティティ**:
- **不変条件**:
  - 顧客識別ID

### 株主集約（ShareholderAggregate）
- **ルートエンティティ**: 株主
- **含まれるエンティティ**:
- **不変条件**:
  - ID
  - 顧客識別ID
  - 株主顧客識別ID

### 役員情報集約（DirectorInformationAggregate）
- **ルートエンティティ**: 役員情報
- **含まれるエンティティ**:
- **不変条件**:
  - ID
  - 顧客識別ID
  - 役員の顧客識別ID

### 代表者経歴集約（ExperienceOfRepresentativeAggregate）
- **ルートエンティティ**: 代表者経歴
- **含まれるエンティティ**:
- **不変条件**:
  - 顧客識別ID

### グループ会社集約（GroupCompanyAggregate）
- **ルートエンティティ**: グループ会社
- **含まれるエンティティ**:
- **不変条件**:
  - ID
  - 顧客識別ID
  - グループ会社の顧客識別ID

### 事業概要集約（BusinessOverviewAggregate）
- **ルートエンティティ**: 事業概要
- **含まれるエンティティ**:
- **不変条件**:
  - 顧客識別ID

### 顧客業種集約（IndustryInformationAggregate）
- **ルートエンティティ**: 顧客業種
- **含まれるエンティティ**:
  - 業種
- **不変条件**:
  - ID
  - 顧客識別ID

### 事業構成集約（BusinessCompositionAggregate）
- **ルートエンティティ**: 事業構成
- **含まれるエンティティ**:
- **不変条件**:
  - ID
  - 顧客識別ID

### 国内事業所集約（DomesticOfficeAggregate）
- **ルートエンティティ**: 国内事業所
- **含まれるエンティティ**:
- **不変条件**:
  - ID
  - 顧客識別ID

### 海外事業所集約（OverSeasOfficeAggregate）
- **ルートエンティティ**: 海外事業所
- **含まれるエンティティ**:
- **不変条件**:
  - ID
  - 顧客識別ID

### 取引経緯集約（BusinessBackgroundAggregate）
- **ルートエンティティ**: 取引経緯
- **含まれるエンティティ**:
- **不変条件**:
  - 顧客識別ID

## 4. ドメインイベント（Domain Events）

## 5. 列挙型（Enumerations）

### オウンドメディア種別（OwnedMediaType）
- 定義なし (Undefined)
- インスタグラム (Instagram)
- X (X)
- LinkedIn (LinkedIn)
- Facebook(Facebook)
- 公式LINE (OfficialLINE)
- YouTube (YouTube)
- その他 (Other)

### 上場先（ListedStockExchange）
- 定義なし（Undefined）
- 東証（TokyoStockExchangeListed）
- 名証（NagoyaStockExchangeListed）
- 札証（SapporoStockExchangeListed）
- 福証（FukuokaStockExchangeListed）
- 海外市場（OverseasListed）
- 非上場（Unlisted）

### 上場区分（ListedMarket）
- 定義なし（Undefined）
- 東証
  - プライム（TSEPrimeMarket）
  - スタンダード（TSEStandardMarket）
  - グロース（TSEGrouthMarket）
- 名証
  - プレミア市場（NSEPremierMarket）
  - メイン市場（NSEMainMarket）
  - ネクスト市場（NSENextMarket）
- 札証
  - 本則市場（SSEMainBoard）
  - アンビシャス（SSEAmbitiousMarket）
- 福証
  - 本則市場（FSEMainBoard）
  - Q-Board（FSEQBoard）

### 株主との関係（RelationshipWithShareholder）
- 定義なし（Undefined）
- 法人
  - 親会社（ParentCompany）
  - 子会社（Subsidary）
  - 関連会社（Affiliate）
  - 出資先（Investee）
  - 関係会社（出資なし）(AffiliateWithoutInvestment)
  - 取引先企業(BusinessPartnerCompany)
  - 法人オーナー資産会社(CorporateOwnerAssetCompany)
  - 従業員持株会(EmployeeStockOwnershipAssociation)
  - 投資ファンド(InvestmentFund)
  - 中小企業投資育成(SMEInvestment)
  - その他（法人）(OtherCorporation)
- 個人
  - 代表取締役（RepresentativeDirector）
  - 取締役（Director）
  - 社外取締役（OutsideDirector）
  - 執行役員（ExecutiveOfficer）
  - 社員 (Employee)
  - 代表取締役の配偶者 (RepresentativeDirectorsSpouse)
  - 代表取締役の子 (RepresentativeDirectorsChild)
  - 代表取締役の親 (RepresentativeDirectorsParent)
  - 代表取締役の家族・親戚 (RepresentativeDirectorsRelative)
  - その他（個人）(OtherIndividual)

### 役職（Position）
- 定義なし（Undefined）
- 代表取締役（RepresentativeDirector）
- 取締役（Director）
- 社外取締役（OutsideDirector）
- 執行役員（ExecutiveOfficer）
- 監査役（Auditor）

### 代取との関係（RelationshipWithRepresentativeDirector）
- 定義なし（Undefined）
- 本人（Self）
- 配偶者（Spouse）
- 子（Child）
- 家族・親族（Relative）
- 親（Parent）
- その他（Other）

### 会社との関係（RelationWithGroupCompany）
- 定義なし (Undefined)
- 100％子会社 (WhollyOwnedSubsidary)
- 子会社 (Subsidary)
- 関連会社 (Affiliate)
- 関連会社（出資なし）(AffiliateWithoutInvestment)
- その他出資先 (Other)

## 6. 業務ルール（Business Rules）
