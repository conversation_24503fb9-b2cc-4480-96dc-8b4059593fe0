# コンポーネント定義作成

English follows Japanese.

## 利用手順

1. [`prompt.md`](./prompt.md)を Edit with CopilotのAgent Modeにドラッグ&ドロップし入力ファイルとして指定します。
1. Edit with Copilotのチャットに以下の様な指示文を入力し送信します。
    - `prompt.mdの内容に従って作業してください`
    - もし、チャットを日本語以外で行う場合は以下の様な記述を追記してください。
        - 例）英語でやり取りしたい場合：`チャットは英語でお願いします。`
        - 例）ベトナム語でやり取りしたい場合：`チャットはベトナム語でお願いします。`
1. AIがチャットにて質問をする場合があるので、その際には適切な回答を行ってください。
1. 生成が完了したら、生成されたファイルの内容を確認してください。
    - 確認して問題がない場合、出力ファイルを受け入れ処理終了となります。
    - 確認して問題がある場合、修正対応として以下のいずれかを実施して修正してください。
        - 出力ファイルを全て受け入れ拒否し、頭からやり直す
        - 出力ファイルを受け入れ、問題の個所を自身の手で修正する
        - Edit with Copilotのチャットに修正したい内容を入力し、AIに修正させる

---

# Component definition generation

## Usage Procedure

1. drag and drop [`prompt.md`](./prompt.md) into Edit with Copilot's Agent Mode and specify it as an input file.
1. enter and send the following instructions in the Edit with Copilot chat.
    - `Please follow the contents of prompt.md`
    - If you want to communicate in a language other than Japanese, please add the following description.
        - Example: If you want to communicate in English: `Please chat in English.`
        - Example: If you want to communicate in Vietnamese: `Please chat in Vietnamese.`
1. the AI may ask you questions in the chat, in which case you should provide appropriate answers.
1. When the generation is completed, please check the contents of the generated file.
    - If there are no problems, the output file is accepted and the process is complete.
    - If there is a problem, do one of the following to correct it.
        - Reject all output files and start over.
        - Accept the output file and correct the problem yourself.
        - Enter what you want to fix in the Edit with Copilot chat and let the AI fix it.