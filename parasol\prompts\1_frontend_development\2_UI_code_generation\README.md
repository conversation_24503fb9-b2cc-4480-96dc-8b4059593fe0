# UIコード生成

English follows Japanese.

## 手順

1. [prompt.md](./prompt.md)ファイルを Edit with CopilotのAgent Modeにドラッグ&ドロップし入力ファイルとして指定する。
1. Edit with Copilotのチャットに以下の様な指示文を入力し送信します。
    - `ページ定義「***」について、指定したprompt.mdに従って作業してください`
    - もし、チャットを日本語以外で行う場合は以下の様な記述を追記してください。
        - 例）英語でやり取りしたい場合：`チャットは英語でお願いします。`
        - 例）ベトナム語でやり取りしたい場合：`チャットはベトナム語でお願いします。`
1. 出力されたファイルの内容を確認する。
    - 確認して問題がない場合、出力ファイルを受け入れる
    - 確認して問題がある場合、修正対応として以下のいずれかを実施する
        - 出力ファイルを全て受け入れ拒否し、頭からやり直す
        - 出力ファイルを受け入れ、問題の個所を自身の手で修正する
        - Copilotに対し修正したい内容をプロンプトで指示し、AIに修正させる

---

## UI code generation

## Usage Procedure

1. drag & drop the [prompt.md](./prompt.md) into the Agent Mode of Edit with Copilot and specify it as an input file.
1. enter and send the following instructions in the Edit with Copilot chat.
    - `Work on the page definition "***" according to the given prompt.md`
    - If you want to communicate in a language other than Japanese, please add the following description.
        - Example: If you want to communicate in English: `Please chat in English.`
        - Example: If you want to communicate in Vietnamese: `Please chat in Vietnamese.`
2. check the contents of the output file.
    - If you check and there is no problem, accept the output file
    - If you see a problem, do one of the following as a corrective action
        - Reject all output files and start over.
        - Accept the output file and correct the problem yourself.
        - Prompt Copilot what you want to fix and let AI fix it