parameters:
  rootDirectory: ''
  scriptDirectory: ''
  threadId: $(ThreadId)
  
steps:
- task: PowerShell@2
  displayName: 'post pr quality comment for checklist'
  inputs:
    targetType: 'filePath'
    filePath: '${{ parameters.scriptDirectory }}/post_comment_checklist.ps1'
    pwsh: true
    arguments: >
      -accessToken $(System.AccessToken)
      -rootDirectory ${{ parameters.rootDirectory }}  
      -threadId ${{ parameters.threadId }}