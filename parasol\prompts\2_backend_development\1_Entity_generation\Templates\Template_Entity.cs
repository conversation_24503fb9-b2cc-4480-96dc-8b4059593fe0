namespace EmployeeService_tmpl.Domain.Entities;

/// <summary>
/// 従業員エンティティ
/// </summary>
public class Employee : IRootEntity<string>
{
    /// <summary>
    /// ID
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名前（漢字）
    /// </summary>
    public string NameKanji { get; set; } = default!;

    /// <summary>
    /// 名前（カナ）
    /// </summary>
    public string NameKana { get; set; } = default!;

    /// <summary>
    /// 誕生日
    /// </summary>
    public DateTimeOffset BirthDate { get; set; }

    /// <summary>
    /// 住所
    /// </summary>
    public string Address { get; set; } = default!;

    /// <summary>
    /// バージョン
    /// </summary>
    public string Version { get; set; } = default!;

    /// <summary>
    /// 更新者
    /// </summary>
    public string UpdatedBy { get; set; } = default!;

    /// <summary>
    /// 更新日時
    /// </summary>
    public DateTimeOffset UpdatedAt { get; set; }

    /// <summary>
    /// 作成者
    /// </summary>
    public string CreatedBy { get; set; } = default!;

    /// <summary>
    /// 作成日時
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }

    /// <summary>
    /// 更新プログラム
    /// </summary>
    public string UpdatedProgram { get; set; } = default!;

    /// <summary>
    /// 削除フラグ
    /// </summary>
    public bool IsDeleted { get; set; } = false;
}