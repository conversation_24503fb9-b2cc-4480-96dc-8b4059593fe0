<script setup lang="ts">
import type { SectionConfig } from '@hox/base/src/components/shared/templates/AppEditPageTmpl.vue'
import { layoutOption } from '@ibp/base/src/constants/layout'
// TODO: 顧客識別IDを取得する場合、以下のimportを追加
import { useGetCustomerIdentificationId } from '@ibp/base/src/composables/shared/useGetCustomerIdentificationId'
// TODO: パンくずリストを設定する場合、以下のimportを追加
// import { useRouterEx } from '@hox/base/src/composables/shared/useRouterEx'
// import { useBreadcrumbs } from '@hox/base/src/composables/shared/useBreadcrumbs'
// import type { Breadcrumb } from '@hox/base/src/composables/shared/useBreadcrumbs'

useHead({
  // TODO: 画面のタイトルを設定してください。
  title: '画面タイトル',
})
definePageMeta({
  // TODO: 必要に応じてmiddlewareを設定してください。
  //middleware: ['check-accessible', 'add-access-log'],
  // TODO: 画面のタイトルを設定してください。
  title: '画面タイトル',
  // TODO: 認証が必要な画面の場合はtrueを設定してください。
  //auth: true,
  // TODO: デフォルト以外レイアウトを利用する場合は設定してください。
  //layout: 'customer',
})

const route = useRoute()
// TODO パラメータ取得処理
// クエリパラメータから取得
// const id = computed(() => route.query?.id as string)
// const caseId = computed(() => route.query?.caseId as string)
// パスパラメータから取得
// const id = computed(() => route.params?.id as string)
// const caseId = computed(() => route.params?.caseId as string)
// 顧客識別IDを取得する場合
const { customerIdentificationId } = useGetCustomerIdentificationId(route)

// パンくずリストは手動でも設定できます。
// ここでは、パンくずリストに履歴が無い場合に、履歴に検索画面を追加する例を示します。
// const { getRouteInfo } = useRouterEx()
// const { histories, replaceHistories } = useBreadcrumbs()
// const routeInfoToBreadcrumbs = (routeInfo: any): Breadcrumb => {
//   return {
//     title: routeInfo.title,
//     path: routeInfo.path,
//     shortPath: routeInfo.path,
//   }
// }
// if (histories.value.length === 0) {
//   replaceHistories([
//     routeInfoToBreadcrumbs(getRouteInfo('employee')),
//   ])
// }

const sections: SectionConfig[] = [
  {
    key: 'basic',
    title: '基本情報',
  },
  {
    key: 'ownedMedia',
    title: 'オウンドメディア',
  },
  {
    key: 'listingInformation',
    title: '上場情報',
  }
]
</script>

<template>
  <app-edit-page-tmpl
    :sections
    :can-save="false"
    :can-remove="false"
    :header-top-offset="layoutOption.CUSTOMER_HEADER_TOP_OFFSET"
  >
    <template #main-actions></template>
    <template #section-basic>
      <!-- TODO: コンポーネントの呼び出し -->
      <SampleBasicInfo 
        :customer-identification-id
      />
    </template>
    <template #section-ownedMedia>
      <SampleOwnedMediaList
        :customer-identification-id="customerIdentificationId"
      />
    </template>
    <template #section-listingInformation>
      <SampleListingInformation
        :customer-identification-id="customerIdentificationId"
      />
    </template>
  </app-edit-page-tmpl>
</template>
