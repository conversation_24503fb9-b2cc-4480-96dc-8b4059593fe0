/**
 * ===========================================
 * エンティティ詳細取得コンポーザブル テストテンプレート
 * ===========================================
 * 
 * このテンプレートは、エンティティ詳細取得用composableのユニットテストです。
 * 
 * 【テスト観点】
 * - 取得API呼び出しの正常系・異常系テスト
 * - パラメータに応じた取得処理のテスト
 * - 状態管理のテスト
 * - エラーハンドリングのテスト
 * - 計算値処理のテスト
 * - デフォルト値の管理テスト
 * 
 * 【使用方法】
 * 1. TODOコメントに従って、エンティティ固有の情報に置き換える
 * 2. 型名とインポートパスを実際のエンティティに変更する
 * 3. 関数名と変数名をエンティティに応じて変更する
 * 4. テストデータを実際のエンティティ構造に合わせて調整する
 */

import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { ref } from 'vue'
import { patchOFetchOnMsw } from '@hox/base/test/testSupport/api'

// TODO: テスト対象composableをインポート - 実際のパスに変更
import { useGetYourEntityByCustomerIdentificationId } from '@your-module/src/composables/yourEntity/useGetYourEntityByCustomerIdentificationId'

// TODO: 型定義をインポート - 実際のパスに変更
import type { YourEntity } from '@your-module/src/constants/domain/entities/yourEntity'

// テスト用モックサーバー
const server = setupServer()

function startServer() {
  server.listen({ onUnhandledRequest: 'error' })
  patchOFetchOnMsw()
}

// TODO: 取得API用のモック関数 - エンドポイントを実際のものに変更
function setupGetYourEntityAPIMock(customerIdentificationId: string | undefined, shouldSucceed: boolean = true) {
  // TODO: 成功時のレスポンスデータを実際のエンティティ構造に変更
  const successResponse: YourEntity = {
    id: 'test-entity-id',
    customerIdentificationId: customerIdentificationId || '00000000-1111-2222-3333-444444444444',
    name: 'テストエンティティ',
    status: 'active',
    amount: 1000000,
    // TODO: エンティティ固有のフィールドを追加
    description: 'テスト用の説明',
    field1: '値1',
    field2: '値2',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  }

  const errorResponse = {
    message: 'エンティティの取得に失敗しました',
    details: 'Not Found',
  }

  // TODO: APIエンドポイントのパスを実際のものに変更
  server.use(
    http.get('/api/your-entities/by-customer/:customerIdentificationId', ({ params }) => {
      if (!shouldSucceed) {
        return new HttpResponse(JSON.stringify(errorResponse), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      if (params.customerIdentificationId !== customerIdentificationId) {
        return new HttpResponse(JSON.stringify(errorResponse), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      return HttpResponse.json(successResponse)
    })
  )

  return successResponse
}

// =====================================================================================================================
// 前処理/後処理
// =====================================================================================================================

beforeEach(() => {
  vi.clearAllMocks()
  startServer()
})

afterEach(() => {
  vi.restoreAllMocks()
  server.resetHandlers()
  server.close()
})

// =====================================================================================================================
// テスト実装
// =====================================================================================================================

// TODO: describe文のcomposable名を実際のエンティティに合わせて変更してください
describe('useGetYourEntityByCustomerIdentificationId', () => {
  const testCustomerIdentificationId = '00000000-1111-2222-3333-444444444444'

  test('初期化：初期状態が正しく設定される', () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, inProgressFind } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // 初期状態の確認
    expect(fetchedYourEntity.value).toBeNull()
    expect(inProgressFind.value).toBe(false)
  })

  test('データ取得：正常系 - エンティティが正しく取得される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const expectedEntity = setupGetYourEntityAPIMock(testCustomerIdentificationId, true)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, inProgressFind, getYourEntity } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntity()

    // 取得結果の確認
    expect(fetchedYourEntity.value).toEqual(expectedEntity)
    expect(inProgressFind.value).toBe(false)
  })

  test('データ取得：異常系 - APIエラー時のハンドリング', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntityAPIMock(testCustomerIdentificationId, false)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, inProgressFind, getYourEntity } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // エラー時の動作確認
    await expect(getYourEntity()).rejects.toThrow()
    expect(fetchedYourEntity.value).toBeNull()
    expect(inProgressFind.value).toBe(false)
  })

  test('ローディング状態：データ取得中のローディング状態管理', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntityAPIMock(testCustomerIdentificationId, true)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { inProgressFind, getYourEntity } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // データ取得開始前
    expect(inProgressFind.value).toBe(false)

    // データ取得実行（非同期）
    const promise = getYourEntity()

    // データ取得中（ローディング状態の確認）
    expect(inProgressFind.value).toBe(true)

    // データ取得完了後
    await promise
    expect(inProgressFind.value).toBe(false)
  })

  test('パラメータ変更：customerIdentificationIdの変更に応じてデータが再取得される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const newCustomerIdentificationId = '11111111-2222-3333-4444-555555555555'

    setupGetYourEntityAPIMock(testCustomerIdentificationId, true)
    setupGetYourEntityAPIMock(newCustomerIdentificationId, true)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, getYourEntity } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // 初回データ取得
    await getYourEntity()
    expect(fetchedYourEntity.value?.customerIdentificationId).toBe(testCustomerIdentificationId)

    // パラメータ変更
    customerIdentificationId.value = newCustomerIdentificationId

    // 新しいパラメータでデータ取得
    await getYourEntity()
    expect(fetchedYourEntity.value?.customerIdentificationId).toBe(newCustomerIdentificationId)
  })

  test('計算値処理：複数フィールドの組み合わせ表示が正しく計算される', async () => {
    // TODO: 計算値が必要なエンティティの場合のみ実装してください
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const expectedEntity = setupGetYourEntityAPIMock(testCustomerIdentificationId, true)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, getYourEntity, computedDisplayValue } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntity()

    // 計算値の確認
    // TODO: 実際の計算ロジックに応じて期待値を調整してください
    expect(computedDisplayValue?.value).toBe('値1 / 値2')
  })

  test('デフォルト値管理：データが存在しない場合のデフォルト値設定', async () => {
    const customerIdentificationId = ref('non-existent-id')
    setupGetYourEntityAPIMock('non-existent-id', false)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, getYourEntity } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // エラー時の動作確認
    try {
      await getYourEntity()
    } catch (error) {
      // エラーが発生した場合の状態確認
      expect(fetchedYourEntity.value).toBeNull()
    }
  })

  test('存在判定：エンティティの存在判定が正しく動作する', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntityAPIMock(testCustomerIdentificationId, true)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, getYourEntity } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // データ取得前
    expect(!!fetchedYourEntity.value).toBe(false)

    // データ取得後
    await getYourEntity()
    expect(!!fetchedYourEntity.value).toBe(true)
  })

  test('データ再取得：同じパラメータでの再取得が正しく動作する', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntityAPIMock(testCustomerIdentificationId, true)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, getYourEntity } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // 初回取得
    await getYourEntity()
    const firstResult = fetchedYourEntity.value

    // 再取得
    await getYourEntity()
    const secondResult = fetchedYourEntity.value

    // 両方とも同じデータが取得されることを確認
    expect(firstResult).toEqual(secondResult)
  })

  test('バリデーション：不正なパラメータでの取得処理', async () => {
    const customerIdentificationId = ref('')
    
    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, getYourEntity } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // 空文字の場合の動作確認
    // TODO: 実際のバリデーションロジックに応じて期待値を調整してください
    try {
      await getYourEntity()
    } catch (error) {
      expect(fetchedYourEntity.value).toBeNull()
    }
  })

  test('メモリリーク対応：コンポーネント破棄時のクリーンアップ', () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    
    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntity, inProgressFind } = useGetYourEntityByCustomerIdentificationId(customerIdentificationId)

    // 初期状態の確認
    expect(fetchedYourEntity.value).toBeNull()
    expect(inProgressFind.value).toBe(false)

    // TODO: cleanup処理がある場合は、ここでテストを追加してください
    // 例: cleanup()
    // expect(fetchedYourEntity.value).toBeNull()
  })
})
