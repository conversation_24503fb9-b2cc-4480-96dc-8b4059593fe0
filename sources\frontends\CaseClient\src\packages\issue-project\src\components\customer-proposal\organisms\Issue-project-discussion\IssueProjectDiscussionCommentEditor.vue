<template>
  <v-dialog
    v-model="dataRef.dialog"
    max-width="1000px"
    @click:outside="cancel"
    @keydown.esc="cancel"
  >
    <AppSimpleDialogTmpl
      :dialog-title="dialogTitle"
      action-button-text="保存"
      :loading="threadDataRef.loading"
      :on-action="save"
      :on-cancel="cancel"
    >
      <v-row class="pb-0 pt-1">
        <!-- 目的 -->
        <v-col cols="4" class="pb-0">
          <AppAutocomplete
            v-model="arrayPurpose"
            :items="issueProjectDiscussionPurposeType"
            rows="1"
            label="目的"
            no-data-text="該当なし"
            required-mark
            clearable
            closable-chips
            :error-messages="errorMessages?.purpose"
            :disabled="threadDataRef.loading"
            :loading="threadDataRef.loading"
            @blur="validateItem('purpose')"
            @update:model-value="($event) => (dataRef.comment.purpose = $event)"
          />
        </v-col>
        <!-- 相手 -->
        <v-col cols="6" class="pb-0">
          <AppTextField
            v-if="isExternal"
            v-model="dataRef.comment.person"
            label="相手"
            rows="1"
            counter="20"
            :error-messages="errorMessages?.person"
            @blur="validateItem('person')"
            @update:model-value="validateItem('person')"
          />
        </v-col>

        <!-- 実権者 -->
        <v-col cols="2" class="pb-0">
          <v-checkbox
            v-if="isExternal"
            v-model="dataRef.comment.isPersonOfPower"
            rows="1"
            label="実権者"
            :loading="threadDataRef.loading"
          />
        </v-col>
      </v-row>

      <!-- コメント・ファイルアップローダー -->
      <v-row>
        <v-col>
          <div v-if="threadDataRef.loading">
            <v-skeleton-loader v-for="n of 3" :key="n" type="article" />
          </div>
          <div v-else>
            <RichTextEditor
              v-model="formDataRef.htmlDescription"
              :show-toolbar="true"
              :can-mention="true"
              hint-text="※顧客担当者・案件担当者には自動で更新通知が届きます"
              :error-message="errorMessages?.plainDescription"
              :staffs="props.staffs"
              :teams="props.teams"
              @update:model-value="checkPlainTextValidation"
              @blur="checkPlainTextValidation"
            />

            <!-- ファイルアップローダー -->
            <FileUploader
              ref="fileUploader"
              :uploaded-files="dataRef.uploadedFiles"
              :use-save-btn="true"
              :disable-list="true"
              @upload-file="uploadFile"
            />
          </div>
        </v-col>
      </v-row>
    </AppSimpleDialogTmpl>
  </v-dialog>
</template>

<script setup lang="ts">
import { z } from 'zod'
import _ from 'lodash'
import type { Team, TeamMember } from '@ibp/common-case/src/apiclient/customerProposal/team'
import { useThreadShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/thread/useThreadShareData'
import { issueProjectDiscussionPurposeType } from '@ibp/issue-project/src/constants/domain/issueProject'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import type fileUploaderComponent from '@ibp/common-case/src/components/organisms/FileUploader.vue'
import type { issueProjectDiscussionCommentType } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { issueProjectDiscussionCommentSchema } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type { DeleteItem } from '@ibp/issue-project/src/models/share/FileUploadType'
import { defaultInstance } from '@hox/base/src/utils/shared/zodDefaultInstance'

const { error: errorToast } = useAppToasts()

const { threadDataRef } = useThreadShareData()
const dialogTitle = '課題案件協議コメント'

// データの変更を監視して、画面離脱時に変更があった場合に確認ダイアログを表示します。
const { hasChanged } = useWatchDataChanges()

// 画面で使用するデータのデフォルトを作ります。
function createDefaultViewData() {
  // 表示する型に変更してください。
  const result = reactive(
    defaultInstance<typeof issueProjectDiscussionCommentSchema>(
      issueProjectDiscussionCommentSchema,
    ),
  )
  return result
}

// 画面で使用するデータを定義します。
const initCommentData = createDefaultViewData()

const dataRef = ref<{
  dialog: boolean
  resolve: any
  reject: any
  isAdd: boolean
  comment: issueProjectDiscussionCommentType
  // ファイル
  filesToUpload: File[]
  filesToRemove: DeleteItem[]
  uploadedFiles: Array<any>
}>({
  dialog: false,
  resolve: () => {},
  reject: () => {},
  isAdd: false,
  comment: initCommentData,
  // ファイル
  filesToUpload: [],
  filesToRemove: [],
  uploadedFiles: [],
})

const formDataRef = ref<{
  htmlDescription: string
  mentionTargetTeamMemberUserIds: string[]
}>({
  htmlDescription: '',
  mentionTargetTeamMemberUserIds: [],
})

type staffType = {
  id: string
  value: string
  romaji: string
}

const props = withDefaults(
  defineProps<{
    staffs: Array<staffType>
    teams: Array<Team>
  }>(),
  {
    staffs: () => [],
    teams: () => [],
  },
)
// 目的
const arrayPurpose = computed<string[] | undefined>(() => {
  const result = toStringArray(toRef(dataRef.value.comment, 'purpose'))
  return result.value || undefined
})

// バリデーション用にplainのDescriptionを文字列として格納する
const plainDescription = ref<boolean | undefined>(true)

const checkPlainTextValidation = async () => {
  const delta = await htmlToQuillDelta(formDataRef.value.htmlDescription)

  if (
    delta.ops.length === 0 ||
    (delta.ops.length === 1 && delta.ops[0].insert === '\n')
  ) {
    plainDescription.value = undefined
  } else {
    plainDescription.value = true
  }
  validateItem('plainDescription')
}

// バリデーション用のオブジェクト
const commentComputed = () => {
  return {
    plainDescription: plainDescription.value,
    purpose: dataRef.value.comment.purpose,
    person: dataRef.value.comment.person,
  }
}

// =====================================================================================================================
// バリデーション定義
// =====================================================================================================================
const { errorMessages, validate, validateItem } = useValidation(
  z.object({
    plainDescription: z.boolean(),
    purpose: z.string(),
    person: z.string().max(20).nullish(),
  }),
  toRef(commentComputed),
)

// =====================================================================================================================
// ダイアログ定義
// =====================================================================================================================
const fileUploader = ref<InstanceType<typeof fileUploaderComponent>>()

//
// ダイアログを開く
//

function open(
  threadId: string,
  comment: issueProjectDiscussionCommentType | null,
): Promise<{
    isOk: boolean
    data?: issueProjectDiscussionCommentType
    mentionTargetTeamMemberUserIds: string[]
  }> {
  // 初期化
  dataRef.value.uploadedFiles = []
  errorMessages.value = {}
  plainDescription.value = undefined

  if (comment === null) {
    // 初期化
    dataRef.value.comment.threadId = threadId
    dataRef.value.comment.purpose = 'Internal'
  } else {
    dataRef.value.comment = _.cloneDeep(comment)
    // 添付ファイル
    dataRef.value.uploadedFiles = comment.files ?? []
  }

  if (comment) {
    // JsonデータをHTMLに変換
    dataRef.value.isAdd = false

    if (comment.description && isValidJson(comment.description)) {
      formDataRef.value.htmlDescription = quillDeltaToHtml(comment.description)
      // QuillEditorにて改行が反映されない問題に対する暫定対応
      formDataRef.value.htmlDescription =
        formDataRef.value.htmlDescription.replaceAll('<br/>', '\n')
    }
  } else {
    dataRef.value.isAdd = true
    formDataRef.value.htmlDescription = ''
  }

  dataRef.value.dialog = true
  // 呼び出し元に Promise を返す。
  return new Promise((resolve, reject) => {
    dataRef.value.resolve = resolve
    dataRef.value.reject = reject
  })
}

function confirmLeave() {
  if (dataRef.value.dialog && hasChanged) {
    return window.confirm('編集中のものは保存されませんが、よろしいですか？')
  }
  return true
}

/**
 * ファイルアップロード
 */
const uploadFile = (items: File[], filesToRemove: DeleteItem[]) => {
  dataRef.value.filesToUpload = items
  dataRef.value.filesToRemove = filesToRemove
}

async function save() {
  const { success } = await validate()
  if (!success) return

  await fileUploader.value?.uploadFile()

  // ファイルサイズチェック
  const fileSizeValidateResult = validateFileSize(dataRef.value.filesToUpload)
  if (!fileSizeValidateResult.isValid) {
    errorToast(fileSizeValidateResult.message)
    return
  }

  dataRef.value.comment.description = JSON.stringify(
    htmlToQuillDelta(formDataRef.value.htmlDescription!),
  )

  dataRef.value.comment.mentionTargetUserIds = getMentionTargetIds(
    formDataRef.value.htmlDescription!,
    'staff',
  )

  dataRef.value.comment.mentionTargetTeamIds = getMentionTargetIds(
    formDataRef.value.htmlDescription!,
    'team',
  )
  formDataRef.value.mentionTargetTeamMemberUserIds = []
  const mentionTargetTeams = props.teams.filter((x: Team) =>
    formDataRef.value.htmlDescription?.includes(x.id),
  )

  for (let index = 0; index < mentionTargetTeams.length; index++) {
    formDataRef.value.mentionTargetTeamMemberUserIds.push(
      ...mentionTargetTeams[index].teamMembers.map(
        (x: TeamMember) => x.staffId,
      ),
    )
  }

  // 社内協議を選択したときは相手・実権者をクリア
  if (dataRef.value.comment.purpose === 'Internal') {
    dataRef.value.comment.person = undefined
    dataRef.value.comment.isPersonOfPower = undefined
  }

  dataRef.value.resolve({
    isOk: true,
    data: Object.assign({}, dataRef.value.comment),
    mentionTargetTeamMemberUserIds:
      formDataRef.value.mentionTargetTeamMemberUserIds,
  })
  dataRef.value.comment.purpose = ''
  dataRef.value.comment.person = undefined
  dataRef.value.comment.isPersonOfPower = undefined
}

/**
 * キャンセル時の処理
 */
function cancel() {
  if (hasChanged.value) {
    // 確認ダイアログを表示
    if (!confirmLeave()) {
      // ユーザーがキャンセルを選択した場合、ダイアログを閉じない
      return
    }
  }

  fileUploader.value?.clearInternalParameter()
  dataRef.value.resolve({ isOk: false })
  dataRef.value.dialog = false
}

// ダイアログを閉じる(親コンポーネントから呼び出す)
const close = (): void => {
  dataRef.value.dialog = false
}

const isExternal = computed(() => {
  return dataRef.value.comment.purpose === 'External'
})

/**
 * アップロード済ファイルも含めた全ファイル情報を返す
 */
const getUploadTargetFiles = (): File[] => {
  // 選択したファイルの配列をクローン
  const uploadTargetFiles = []

  // アップロード済ファイル情報を追加(ファイル名のみの空ファイルとして送る)
  for (const uploadedFile of dataRef.value.uploadedFiles) {
    uploadTargetFiles.push(new File([], uploadedFile.fileName))
  }
  return uploadTargetFiles
}

defineExpose({
  open,
  close,
  getUploadTargetFiles,
  dataRef,
})
</script>
