import type { issueProjectDiscussionCommentType } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useCommentShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/useCommentShareData'
import { REACTION_TYPE } from '@ibp/issue-project/src/constants/domain/reactionType'

export const useGetReactions = (type: string) => {
  const { isMyCareerEnabled } = useCommentShareData()
  const { $auth } = useNuxtApp()
  const userId = $auth.getUser()?.userId

  /**
   * コメント編集・削除アイコンの表示・非表示を判定
   */
  const showHover = (
    hover: boolean,
    comment: issueProjectDiscussionCommentType | undefined,
    type: string,
  ) => {
    if (!hover) return false
    if (comment?.registrantId !== userId) return false
    if (type === 'cross-customer') return false
    return userId && isMyCareerEnabled
  }

  const reactionTypes = computed(() => {
    if (type !== 'cross-customer') {
      return {
        good: 1,
        congratulations: 2,
        ok: 3,
        surprise: 4,
      }
    } else {
      return null
    }
  })

  const getReactionDisplayName = (reactionType: number) => {
    const displayNames = [
      '', // インデックス0は空文字
      REACTION_TYPE[1], // いいね・確認しました
      REACTION_TYPE[2], // おめでとう・契約お疲れ様
      REACTION_TYPE[3], // ok
      REACTION_TYPE[4], // 驚き
    ]

    return displayNames[reactionType] || ''
  }

  const getReactionIcon = (type: number) => {
    const reactionTypeValues = reactionTypes.value
    if (!reactionTypeValues) {
      return undefined
    }

    const reactionIcons = {
      [reactionTypeValues.good]: {
        color: '#7CB342',
        icon: 'mdi-thumb-up',
      },
      [reactionTypeValues.congratulations]: {
        color: '#D81B60',
        icon: 'mdi-hand-clap',
      },
      [reactionTypeValues.ok]: {
        color: '#FB8C00',
        icon: 'mdi-hand-okay',
      },
      [reactionTypeValues.surprise]: {
        color: '#039BE5',
        icon: 'far fa-regular fa-face-surprise 2xl',
      },
    }

    return reactionIcons[type] || undefined
  }

  return {
    showHover,
    reactionTypes,
    getReactionDisplayName,
    getReactionIcon,
  }
}
