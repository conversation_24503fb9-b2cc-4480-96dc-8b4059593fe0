import { ref, reactive } from 'vue'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useBranchMaster } from '@/packages/common-case/src/composables/customerIdentifying/useBranchMaster'
import { useIndustryMaster } from '@/packages/common-case/src/composables/customerIdentifying/useIndustryMaster'
import { useSegment } from '@/packages/common-case/src/composables/ibp-simple-analysis/useSegment'
import { getStaffAndTeamOptions } from '@/packages/common-case/src/utils/ddlOptions'
import { DEFAULT_SELECTED_CASE_STATUSES } from '@/packages/common-case/src/constants/domain/case'

/**
 * 融資リース案件オプションデータ取得用のコンポーザブル
 * マスターデータやスタッフ情報など、検索条件で利用する選択肢の取得を行う
 */
export const useLoanAndLeaseOptions = () => {
  // 処理完了などをトースト表示するために利用
  const { error: errorToast } = useAppToasts()

  // マスタデータ取得用コンポーザブル
  const { branchMasterOptions, fetchBranches } = useBranchMaster()
  const { industryMasterOptions, fetchIndustries } = useIndustryMaster()
  const { segmentOptions, fetchSegments } = useSegment()

  // スタッフと担当者のローディング状態
  const loadingStaffAndTeam = ref(false)

  // 選択肢
  const options = reactive({
    staffAndTeams: [] as any[],
  })

  /**
   * すべての選択肢データを取得する
   * @param hasCriteriaHistory 検索条件履歴の有無
   * @param searchCondition 検索条件オブジェクト（初期値設定用）
   * @returns 処理成功フラグ
   */
  async function loadAllOptions(hasCriteriaHistory: boolean, searchCondition?: any) {
    loadingStaffAndTeam.value = true
    try {
      const result = await setOptions()

      if (searchCondition && !hasCriteriaHistory) {
        searchCondition.caseStatuses = DEFAULT_SELECTED_CASE_STATUSES || []
      }
      return result
    } finally {
      loadingStaffAndTeam.value = false
    }
  }

  /**
   * マスタデータとスタッフ・チーム情報を取得する
   * @returns 処理成功フラグ
   */
  async function setOptions() {
    const [
      getStaffAndTeamOptionsResult,
    ] = await Promise.all([
      getStaffAndTeamOptions(),
      fetchBranches(), // 店番情報を取得
      fetchSegments(), // セグメント情報を取得
      fetchIndustries(), // 業種情報を取得
    ])

    if (getStaffAndTeamOptionsResult.length === 0) {
      errorToast('検索条件の選択肢が取得できませんでした。リロードしてください。')
      return false
    }

    options.staffAndTeams = getStaffAndTeamOptionsResult
    return true
  }

  return {
    // マスタデータ
    branchMasterOptions,
    industryMasterOptions,
    segmentOptions,

    // スタッフと担当者
    options,
    loadingStaffAndTeam,

    // メソッド
    loadAllOptions,
    setOptions,
  }
}
