jobs:
- deployment: CaseClientAppSwap
  environment: ${{ parameters.env }}
  pool:
    vmImage: $(vmImageName)
  strategy:
    runOnce:
      deploy:
        steps:
        - download: none
        - task: AzureAppServiceManage@0
          displayName: Swap slot for Azure Web App (Node.js)
          inputs:
            ConnectedServiceName: $(azureSubscription)
            Action: 'Swap Slots'
            WebAppName: $(CaseClient.WebAppName)
            ResourceGroupName: $(resourceGroupName)
            SourceSlot: staging

