# 課題案件編集ページ定義

## 基本情報

- ページ名 : 課題案件編集
- UIパターン : 編集画面（edit）

## 実現対象ハイレベルユースケース

- 顧客への提案活動における課題案件の情報管理（課題情報・案件情報・期日管理）
- 課題解決に向けたタスク管理とチーム内協議機能
- ファイル管理とリンク管理による情報共有

## 共通処理（ミドルウェア）定義

- [ ] アクセスログ出力
- [ ] アクセス可否チェック
- [ ] 次画面パスチェック

## パラメータ定義

| パラメータ名 | 種別 | 必須 | 型 | 説明 |
|---|---|---|---|---|
| id | query | | 文字列 | 課題案件ID（未指定の場合は新規追加モード） |
| businessUnderstandingId | query | | 文字列 | 事業性理解ID（事業性理解詳細からの遷移時） |
| customerIdentificationId | query | | 文字列 | 顧客識別ID（顧客詳細からの遷移時） |
| taskId | query | | 文字列 | タスクID（タスク関連からの遷移時） |
| communicationPlanCategory | query | | 文字列 | コミュニケーションプラン種別 |
| from | query | | 文字列 | 遷移元画面識別子（パンくずリスト構築用） |

## UI定義

### 編集フォーム構成
#### 課題情報セクション
- 課題No
    - 表示形式 : 読み取り専用テキスト
    - 説明 : 新規の場合は「未登録」、編集の場合は発行番号を表示
- 課題項目
    - 入力形式 : プルダウンリスト
    - 必須項目 : ○
- 顧客の課題感
    - 入力形式 : プルダウンリスト
    - 必須項目 : ○
- 概要
    - 入力形式 : テキストボックス
    - 必須項目 : ○
    - 文字数制限 : 50文字
- 現状
    - 入力形式 : テキストエリア
    - 必須項目 : ○
    - 文字数制限 : 500文字
- あるべき姿
    - 入力形式 : テキストエリア
    - 必須項目 : ○
    - 文字数制限 : 500文字
- 課題
    - 入力形式 : テキストエリア
    - 必須項目 : ○
    - 文字数制限 : 500文字
- 業種詳細
    - 入力形式 : プルダウンリスト
    - 必須項目 : ○
    - 説明 : 業種詳細検索サイトへのリンクあり
- キーパーソン
    - 入力形式 : テキストボックス
    - 文字数制限 : 50文字

#### 期日管理セクション
- 案件ステータス
    - 表示形式 : 読み取り専用テキスト
    - 説明 : 入力データに基づいて自動計算
- 顧客あて次のアクション
    - 入力形式 : プルダウンリスト
    - 条件必須 : 案件ステータスが「提案前」「提案中」の場合
- その他
    - 入力形式 : テキストボックス
    - 文字数制限 : 50文字
    - 条件必須 : 顧客あて次のアクションで「その他」選択時

#### 案件情報セクション
**入力グループA（基本担当情報）**
- 担当チーム
    - 入力形式 : プルダウンリスト
    - 条件必須 : 担当者が設定されている場合
- 担当者
    - 入力形式 : プルダウンリスト

**入力グループB（提案関連）**
- 提案日
    - 入力形式 : 日付選択
    - 条件必須 : 提案金額が入力されている場合
- 提案金額
    - 入力形式 : 数値入力（円表示）
    - 桁数制限 : 11桁
    - 条件必須 : 提案日が入力されている場合
    - 入力可否 : 担当チーム設定時のみ有効

**入力グループC（コンサル関連）**
- コンサル契約日
    - 入力形式 : 日付選択
    - 条件必須 : 契約金額、コンサル開始日、完了期限のいずれかが入力されている場合
- 契約金額
    - 入力形式 : 数値入力（円表示）
    - 桁数制限 : 11桁
    - 条件必須 : コンサル契約日、コンサル開始日、完了期限のいずれかが入力されている場合
- コンサル開始日
    - 入力形式 : 日付選択
    - 条件必須 : コンサル契約日、契約金額、完了期限のいずれかが入力されている場合
- 完了期限
    - 入力形式 : 日付選択
    - 条件必須 : コンサル契約日、契約金額、コンサル開始日のいずれかが入力されている場合
    - 入力可否 : 応諾済み・コンサル中時のみ有効

**入力グループD/E（完了・中止関連）**
- 課題解決日
    - 入力形式 : 日付選択
- 課題中止日
    - 入力形式 : 日付選択
- 連携先
    - 入力形式 : テキストボックス
    - 文字数制限 : 50文字

#### タスクセクション
- 表示条件 : 課題案件が既に登録されている場合のみ
- 検索条件
    - タスク担当者（複数選択可、チップ表示）
    - タスクステータス（単一選択）
    - 期日From/To（日付範囲）
- 一覧表示項目
    - 依頼日、課題概要、期日、ステータス、タスク担当者、タスク概要
- 機能
    - タスク新規追加、編集（行クリック）、検索、ページング

#### ファイルセクション
- ファイルアップロード機能
- アップロード済みファイル一覧表示
- ファイルダウンロード機能

#### リンクセクション
- 関連リンクの管理機能

### アクションボタン
#### メインアクション
- 登録/更新ボタン
    - 説明 : 新規の場合は「登録」、編集の場合は「更新」
- 削除ボタン
    - 表示条件 : 編集モードかつ削除権限がある場合

#### サブアクション
- 協議ボタン
    - 表示条件 : 課題案件IDが存在する場合
    - 遷移先 : 課題案件協議画面
- 事業性理解詳細ボタン
    - 表示条件 : 課題案件IDが存在する場合
    - 遷移先 : 事業性理解詳細画面

## イベント処理定義

### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 課題案件データと選択肢を取得し表示する | ライフサイクルフック mounted |
| 登録/更新ボタン押下 | 入力データを登録・更新する | function save |
| 削除ボタン押下 | 課題案件を削除する | function remove |
| 担当チーム/担当者変更 | 案件ステータスを再計算する | function changeStatus |
| タスク検索 | タスク一覧を検索・表示する | function searchTasks |
| タスク追加/編集 | タスク編集画面に遷移する | function toIssueProjectTaskEditPage |
| 協議ボタン押下 | 課題案件協議画面に遷移する | function toIssueProjectDiscussionPage |
| 事業性理解詳細ボタン押下 | 事業性理解詳細画面に遷移する | function toBusinessUnderstandingPage |
| ファイルアップロード | ファイルをアップロードする | function uploadFile |
| ファイルダウンロード | ファイルをダウンロードする | function downloadFile |

### 初期表示（mounted）
1. URLパラメータから課題案件IDを取得する。
2. 課題案件IDが存在する場合は既存データを取得し、存在しない場合は新規追加モードとする。
3. 各種選択肢（課題項目、顧客の課題感、業種詳細、次のアクション等）を取得する。
4. 担当者選択肢を取得する。
5. ファイル一覧を取得する（編集モードの場合）。
6. タスクステータス選択肢を取得する。
7. 案件ステータスを計算・表示する。

### 登録/更新ボタン押下（save）
1. 入力データのバリデーションを実行する。
2. バリデーション成功時、課題案件データを登録/更新APIに送信する。
3. ファイルアップロードが存在する場合は、ファイルアップロード処理を実行する。
4. 行動記録を登録する（課題解決日または課題中止日が新規設定された場合）。
5. 成功時はトースト通知を表示し、元の画面に戻る。

### 削除ボタン押下（remove）
1. 削除確認ダイアログを表示する。
2. 確認後、課題案件削除APIを呼び出す。
3. 成功時はトースト通知を表示し、元の画面に戻る。

### 担当チーム/担当者変更（changeStatus）
1. 入力された担当情報に基づいて案件ステータスを再計算する。
2. ステータスに応じて入力項目の有効/無効を制御する。
3. バリデーションを実行する。

### タスク検索（searchTasks）
1. 入力された検索条件でタスク検索APIを呼び出す。
2. 検索結果を整形し、一覧に表示する。
3. BPOチームの場合は特別な表示処理を行う。

### ファイルアップロード（uploadFile）
1. アップロード対象ファイルのサイズ・形式チェックを行う。
2. ファイルアップロードAPIを呼び出す。
3. アップロード完了後、ファイル一覧を再取得する。
