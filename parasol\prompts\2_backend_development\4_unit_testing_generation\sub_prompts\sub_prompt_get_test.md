# 単一取得UseCaseテスト作成

## 役割定義

- 日本人のベテランエンジニアとして、単一取得UseCaseハンドラーの単体テストの開発を行います。
- 既存のGetHandlerの実装を分析し、高品質で高カバレッジな単体テストを生成します。
- 語学が堪能であるためヒアリングの際には適した言語で対応しますが、成果物は日本語で出力します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 ： 
  - `parasol\architecture\backend_architecture.md`
- 共通処理使用方法リファレンス：
  - `parasol\architecture\usage_sample_backend_shared.md`
- プロジェクト設定定義 : 
  - `parasol\architecture\project_settings.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- 単一取得UseCaseテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\2_get_Usecase_test\Template_GetEntityHandlerTest.cs`
- 単一取得UseCaseスペシフィケーションテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\2_get_Usecase_test\Template_GetEntitySpecificationTest.cs`

### 参照コード

- 対象Handlerクラス : `[サービスディレクトリ]\UseCases\[Entity名]\Get[Entity名]\Get[Entity名]Handler.cs`
- 対象Specificationクラス : `[サービスディレクトリ]\UseCases\[Entity名]\Get[Entity名]\Get[Entity名]Specification.cs`
- 既存テストコード : `[サービステストディレクトリ]\UseCases\*.cs`

## 出力定義

- 出力先ディレクトリ：`[サービステストディレクトリ]\UseCases\[Entity名]\Get[Entity名]`
- Handlerファイル名フォーマット：`Get[Entity名]HandlerTest.cs`
- Specificationファイル名フォーマット：`Get[Entity名]SpecificationTest.cs`

## 制約事項

### 禁止事項

- テンプレートファイルを編集するのではなく、テンプレートの構造に従って新しいファイルを作成してください
- 当プロンプトファイルを編集するのではなく、指示内容に従って作業を実行してください

## 指示詳細

### 情報収集

1. 呼び出し元のプロンプトで特定された以下の情報を確認してください
    - 取り扱い対象の集約
    - 単一取得UseCaseで実装されている処理概要
    - 外部API呼び出しの有無
        - 呼び出し対象のAPIのエンドポイント

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください

2. 既存のGet[Entity名]Handlerの実装を読み込み、以下を分析してください：
   - Handleメソッドの処理フロー
   - IDバリデーション処理
   - Specificationの設定（Include、AsNoTracking等）
   - リポジトリアクセスのエラーハンドリング
   - 戻り値のマッピング処理
   - 関連データの取得処理

3. 既存のGet[Entity名]Specificationの実装を読み込み、以下を分析してください：
   - Include条件の設定
   - AsNoTracking設定
   - フィルタリング条件（必要に応じて）

4. 各テンプレートを元に、収集した情報にてテストを実装してください。
    - テンプレートファイルの基本構造を維持してEntity定義に合わせて修正
    - TODOコメントを参考に必要な部分を修正
    - 外部API呼び出しがある場合は、モックの設定を適切に行う

5. テンプレート修正時の注意事項：
    - namespace: SampleService を実際のサービス名に変更
    - Entity: 実際のエンティティクラス名に変更
    - GetEntityQuery、GetEntityHandler、GetEntitySpecification: 実際のクラス名に変更
    - TestData.Entity.CreateValid(): 実際のTestDataメソッドに変更
    - Status enum: 実際の列挙型に変更
    - エンティティの実際のプロパティ名に合わせて検証条件を修正

6. 機能に応じたテスト調整：
    - 実際のクエリプロパティに応じてテストメソッドを調整
    - 不要なセクションは削除し、必要なセクションを追加
    - エンティティの実際のプロパティ名に合わせて検証条件を修正

### 品質保証

以下の検証を実施し、品質に問題がないか確認してください。

1. **機能完全性**
    - **コードカバレッジ100%を目指す**
    - 全ての分岐パス（if-else、switch-case、try-catch）をカバー
    - Handleメソッドの全分岐パスがテストされているか
    - IDバリデーションの全パターンがテストされているか
    - エラーハンドリングが全パターンテストされているか
    - 関連データの取得処理がテストされているか
    - ID検証パターンを網羅
    - 関連データ（Include）の有無パターンをテスト
    - リポジトリエラーパターンをテスト

2. **アーキテクチャ準拠性**
    - 各テストが独立して実行できるか
    - FluentAssertionsを使った適切なアサーション
    - テストデータの適切な設定
    - モックが正しく設定されているか

3. **コード品質**
    - 日本語によるテストメソッド名が適切か
    - `[Fact]`/`[Theory]`属性の適切な使用
    - 既存テストコードとの一貫性

4. 上記で検証した内容に問題がある場合は、必要な修正を行ってください。
