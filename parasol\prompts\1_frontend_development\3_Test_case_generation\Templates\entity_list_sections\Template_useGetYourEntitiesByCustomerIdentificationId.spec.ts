/**
 * ===========================================
 * エンティティリスト取得コンポーザブル テストテンプレート
 * ===========================================
 * 
 * このテンプレートは、エンティティリスト取得用composableのユニットテストです。
 * 
 * 【テスト観点】
 * - 一覧取得API呼び出しの正常系・異常系テスト
 * - パラメータに応じた取得処理のテスト
 * - 状態管理のテスト
 * - エラーハンドリングのテスト
 * - フィルタリング処理のテスト
 * - ソート処理のテスト
 * - ページネーション処理のテスト
 * - データの更新・追加・削除処理のテスト
 * 
 * 【使用方法】
 * 1. TODOコメントに従って、エンティティ固有の情報に置き換える
 * 2. 型名とインポートパスを実際のエンティティに変更する
 * 3. 関数名と変数名をエンティティに応じて変更する
 * 4. テストデータを実際のエンティティ構造に合わせて調整する
 */

import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { ref } from 'vue'
import { patchOFetchOnMsw } from '@hox/base/test/testSupport/api'

// TODO: テスト対象composableをインポート - 実際のパスに変更
import { useGetYourEntitiesByCustomerIdentificationId } from '@your-module/src/composables/yourEntity/useGetYourEntitiesByCustomerIdentificationId'

// TODO: 型定義をインポート - 実際のパスに変更
import type { YourEntity } from '@your-module/src/constants/domain/entities/yourEntity'

// テスト用モックサーバー
const server = setupServer()

function startServer() {
  server.listen({ onUnhandledRequest: 'error' })
  patchOFetchOnMsw()
}

// TODO: 一覧取得API用のモック関数 - エンドポイントを実際のものに変更
function setupGetYourEntitiesAPIMock(customerIdentificationId: string | undefined, shouldSucceed: boolean = true, itemCount: number = 5) {
  // TODO: 成功時のレスポンスデータを実際のエンティティ構造に変更
  const successResponse: YourEntity[] = Array.from({ length: itemCount }, (_, index) => ({
    id: `test-entity-id-${index + 1}`,
    customerIdentificationId: customerIdentificationId || '00000000-1111-2222-3333-444444444444',
    name: `テストエンティティ${index + 1}`,
    status: index % 2 === 0 ? 'active' : 'inactive',
    amount: (index + 1) * 100000,
    category: index % 3 === 0 ? 'category-a' : index % 3 === 1 ? 'category-b' : 'category-c',
    // TODO: エンティティ固有のフィールドを追加
    description: `テスト用の説明${index + 1}`,
    field1: `値1-${index + 1}`,
    field2: `値2-${index + 1}`,
    createdAt: `2024-01-0${(index % 9) + 1}T00:00:00Z`,
    updatedAt: `2024-01-0${(index % 9) + 2}T00:00:00Z`,
  }))

  const errorResponse = {
    message: 'エンティティ一覧の取得に失敗しました',
    details: 'Internal Server Error',
  }

  // TODO: APIエンドポイントのパスを実際のものに変更
  server.use(
    http.get('/api/your-entities/by-customer/:customerIdentificationId', ({ params }) => {
      if (!shouldSucceed) {
        return new HttpResponse(JSON.stringify(errorResponse), {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      if (params.customerIdentificationId !== customerIdentificationId) {
        return HttpResponse.json([])
      }

      return HttpResponse.json(successResponse)
    })
  )

  return successResponse
}

// =====================================================================================================================
// 前処理/後処理
// =====================================================================================================================

beforeEach(() => {
  vi.clearAllMocks()
  startServer()
})

afterEach(() => {
  vi.restoreAllMocks()
  server.resetHandlers()
  server.close()
})

// =====================================================================================================================
// テスト実装
// =====================================================================================================================

// TODO: describe文のcomposable名を実際のエンティティに合わせて変更してください
describe('useGetYourEntitiesByCustomerIdentificationId', () => {
  const testCustomerIdentificationId = '00000000-1111-2222-3333-444444444444'

  test('初期化：初期状態が正しく設定される', () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, inProgressFind } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // 初期状態の確認
    expect(fetchedYourEntities.value).toEqual([])
    expect(inProgressFind.value).toBe(false)
  })

  test('データ取得：正常系 - エンティティリストが正しく取得される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const expectedEntities = setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 3)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, inProgressFind, getYourEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntities()

    // 取得結果の確認
    expect(fetchedYourEntities.value).toEqual(expectedEntities)
    expect(fetchedYourEntities.value).toHaveLength(3)
    expect(inProgressFind.value).toBe(false)
  })

  test('データ取得：空のリストが返される場合', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 0)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, inProgressFind, getYourEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntities()

    // 空リストの確認
    expect(fetchedYourEntities.value).toEqual([])
    expect(fetchedYourEntities.value).toHaveLength(0)
    expect(inProgressFind.value).toBe(false)
  })

  test('データ取得：異常系 - APIエラー時のハンドリング', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, false)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, inProgressFind, getYourEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // エラー時の動作確認
    await expect(getYourEntities()).rejects.toThrow()
    expect(fetchedYourEntities.value).toEqual([])
    expect(inProgressFind.value).toBe(false)
  })

  test('ローディング状態：データ取得中のローディング状態管理', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 3)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { inProgressFind, getYourEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得開始前
    expect(inProgressFind.value).toBe(false)

    // データ取得実行（非同期）
    const promise = getYourEntities()

    // データ取得中（ローディング状態の確認）
    expect(inProgressFind.value).toBe(true)

    // データ取得完了後
    await promise
    expect(inProgressFind.value).toBe(false)
  })

  test('パラメータ変更：customerIdentificationIdの変更に応じてデータが再取得される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const newCustomerIdentificationId = '11111111-2222-3333-4444-555555555555'

    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 2)
    setupGetYourEntitiesAPIMock(newCustomerIdentificationId, true, 3)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // 初回データ取得
    await getYourEntities()
    expect(fetchedYourEntities.value).toHaveLength(2)
    expect(fetchedYourEntities.value[0].customerIdentificationId).toBe(testCustomerIdentificationId)

    // パラメータ変更
    customerIdentificationId.value = newCustomerIdentificationId

    // 新しいパラメータでデータ取得
    await getYourEntities()
    expect(fetchedYourEntities.value).toHaveLength(3)
    expect(fetchedYourEntities.value[0].customerIdentificationId).toBe(newCustomerIdentificationId)
  })

  test('フィルタリング：ステータスによるフィルタリング', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const expectedEntities = setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 6)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities, filteredEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntities()

    // フィルタリング実行（activeのみ）
    // TODO: 実際のフィルタリングロジックに応じて調整してください
    const activeEntities = filteredEntities?.value?.filter(entity => entity.status === 'active') || []
    const expectedActiveCount = expectedEntities.filter(entity => entity.status === 'active').length

    expect(activeEntities).toHaveLength(expectedActiveCount)
    activeEntities.forEach(entity => {
      expect(entity.status).toBe('active')
    })
  })

  test('ソート：名前による昇順ソート', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const expectedEntities = setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 4)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities, sortedEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntities()

    // ソート実行
    // TODO: 実際のソートロジックに応じて調整してください
    const sorted = sortedEntities?.value?.sort((a, b) => a.name.localeCompare(b.name)) || []

    expect(sorted).toHaveLength(4)
    for (let i = 1; i < sorted.length; i++) {
      expect(sorted[i].name.localeCompare(sorted[i - 1].name)).toBeGreaterThanOrEqual(0)
    }
  })

  test('ソート：金額による降順ソート', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const expectedEntities = setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 4)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities, sortedEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntities()

    // ソート実行（金額降順）
    // TODO: 実際のソートロジックに応じて調整してください
    const sorted = sortedEntities?.value?.sort((a, b) => b.amount - a.amount) || []

    expect(sorted).toHaveLength(4)
    for (let i = 1; i < sorted.length; i++) {
      expect(sorted[i].amount).toBeLessThanOrEqual(sorted[i - 1].amount)
    }
  })

  test('ページネーション：指定したページサイズでデータが分割される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 10)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities, paginatedEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntities()

    // ページネーション（1ページ3件）
    // TODO: 実際のページネーションロジックに応じて調整してください
    const pageSize = 3
    const page1 = paginatedEntities?.value?.slice(0, pageSize) || []
    const page2 = paginatedEntities?.value?.slice(pageSize, pageSize * 2) || []

    expect(page1).toHaveLength(3)
    expect(page2).toHaveLength(3)
    expect(page1[0]).not.toEqual(page2[0])
  })

  test('検索機能：名前による部分一致検索', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    const expectedEntities = setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 5)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities, searchEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntities()

    // 検索実行
    // TODO: 実際の検索ロジックに応じて調整してください
    const searchKeyword = 'エンティティ1'
    const searchResults = searchEntities?.value?.filter(entity => 
      entity.name.includes(searchKeyword)
    ) || []

    expect(searchResults.length).toBeGreaterThan(0)
    searchResults.forEach(entity => {
      expect(entity.name).toContain(searchKeyword)
    })
  })

  test('複合条件：フィルタリング + ソート + ページネーション', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 10)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // データ取得実行
    await getYourEntities()

    // 複合条件での処理
    // TODO: 実際の複合条件ロジックに応じて調整してください
    const filtered = fetchedYourEntities.value.filter(entity => entity.status === 'active')
    const sorted = filtered.sort((a, b) => a.name.localeCompare(b.name))
    const paginated = sorted.slice(0, 3)

    expect(paginated.length).toBeLessThanOrEqual(3)
    paginated.forEach(entity => {
      expect(entity.status).toBe('active')
    })
    
    // ソート順の確認
    for (let i = 1; i < paginated.length; i++) {
      expect(paginated[i].name.localeCompare(paginated[i - 1].name)).toBeGreaterThanOrEqual(0)
    }
  })

  test('データ更新：リストの更新が正しく反映される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 3)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities, updateEntity } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // 初回データ取得
    await getYourEntities()
    const originalEntities = [...fetchedYourEntities.value]

    // エンティティ更新
    // TODO: 実際の更新ロジックに応じて調整してください
    if (updateEntity) {
      const updatedEntity = { ...originalEntities[0], name: '更新されたエンティティ' }
      await updateEntity(updatedEntity)

      expect(fetchedYourEntities.value[0].name).toBe('更新されたエンティティ')
      expect(fetchedYourEntities.value).toHaveLength(originalEntities.length)
    }
  })

  test('データ追加：新しいエンティティの追加が正しく反映される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 2)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities, addEntity } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // 初回データ取得
    await getYourEntities()
    const originalCount = fetchedYourEntities.value.length

    // エンティティ追加
    // TODO: 実際の追加ロジックに応じて調整してください
    if (addEntity) {
      const newEntity: YourEntity = {
        id: 'new-entity-id',
        customerIdentificationId: testCustomerIdentificationId,
        name: '新しいエンティティ',
        status: 'active',
        amount: 999999,
        category: 'category-a',
        description: '新規追加のエンティティ',
        field1: '新規値1',
        field2: '新規値2',
        createdAt: '2024-01-10T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z',
      }

      await addEntity(newEntity)
      expect(fetchedYourEntities.value).toHaveLength(originalCount + 1)
      expect(fetchedYourEntities.value.some(entity => entity.id === 'new-entity-id')).toBe(true)
    }
  })

  test('データ削除：エンティティの削除が正しく反映される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 3)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities, removeEntity } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // 初回データ取得
    await getYourEntities()
    const originalCount = fetchedYourEntities.value.length
    const entityToRemove = fetchedYourEntities.value[0]

    // エンティティ削除
    // TODO: 実際の削除ロジックに応じて調整してください
    if (removeEntity) {
      await removeEntity(entityToRemove.id)
      expect(fetchedYourEntities.value).toHaveLength(originalCount - 1)
      expect(fetchedYourEntities.value.some(entity => entity.id === entityToRemove.id)).toBe(false)
    }
  })

  test('バリデーション：不正なパラメータでの取得処理', async () => {
    const customerIdentificationId = ref('')
    
    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // 空文字の場合の動作確認
    // TODO: 実際のバリデーションロジックに応じて期待値を調整してください
    try {
      await getYourEntities()
    } catch (error) {
      expect(fetchedYourEntities.value).toEqual([])
    }
  })

  test('大量データ：大量のエンティティが正しく処理される', async () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    setupGetYourEntitiesAPIMock(testCustomerIdentificationId, true, 100)

    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, getYourEntities } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // 大量データ取得
    await getYourEntities()
    expect(fetchedYourEntities.value).toHaveLength(100)

    // パフォーマンスチェック（必要に応じて）
    const startTime = performance.now()
    const filtered = fetchedYourEntities.value.filter(entity => entity.status === 'active')
    const endTime = performance.now()

    expect(endTime - startTime).toBeLessThan(100) // 100ms以内での処理
    expect(filtered.length).toBeGreaterThan(0)
  })

  test('メモリリーク対応：コンポーネント破棄時のクリーンアップ', () => {
    const customerIdentificationId = ref(testCustomerIdentificationId)
    
    // TODO: composable名を実際のエンティティに合わせて変更してください
    const { fetchedYourEntities, inProgressFind } = useGetYourEntitiesByCustomerIdentificationId(customerIdentificationId)

    // 初期状態の確認
    expect(fetchedYourEntities.value).toEqual([])
    expect(inProgressFind.value).toBe(false)

    // TODO: cleanup処理がある場合は、ここでテストを追加してください
    // 例: cleanup()
    // expect(fetchedYourEntities.value).toEqual([])
  })
})
