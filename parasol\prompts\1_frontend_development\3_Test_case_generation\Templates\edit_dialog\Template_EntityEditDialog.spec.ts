import { describe, test, expect, vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { mountSuspended } from '@nuxt/test-utils/runtime'
import {
  setTestUser,
  clearTestUser,
  // TODO: 実際に使用するテストサポート関数を適切に選択してください
  getAllInputValuesAsObject,
} from '../../../testSupport'
// TODO: テスト対象コンポーネントのパスを実際のエンティティに合わせて変更してください
// 例: import YourEntityEditDialog from '@/components/your-entity/organisms/YourEntityEditDialog.vue'
import QualificationEditDialog from '@/components/employee/organisms/QualificationEditDialog.vue'

// =====================================================================================================================
// テスト用処理設定
// =====================================================================================================================
// TODO: テスト用コンポーネント名を実際のエンティティに合わせて変更してください
// 例: YourEntityEditDialog
const TestComponent = defineComponent({
  components: { QualificationEditDialog },
  setup() {
    const dialog = ref()
    return {
      dialog,
    }
  },
  render() {
    return h('div', [
      h(QualificationEditDialog, { ref: 'dialog' }),
    ])
  },
})

// TODO: ダイアログコンポーネント名を実際のエンティティに合わせて変更してください
// 例: 'your-entity-edit-dialog'
const dialogComponentName = 'qualification-edit-dialog'

// =====================================================================================================================
// 前処理/後処理
// =====================================================================================================================
// テスト開始前処理
beforeAll(() => {
  // ユーザー情報を設定
  setTestUser('テストユーザー', 'test_user', ['admin'])
})
// テスト終了後処理
afterAll(() => {
  // ユーザー情報をクリア
  clearTestUser()
})
// テストケース開始前処理
beforeEach(() => {
  // 処理なし
})
// テストケース終了時後処理
afterEach(() => {
  // モック化したモジュールを元の実装に戻す
  vi.restoreAllMocks()
})

// =====================================================================================================================
// テスト実装
// =====================================================================================================================
// TODO: describe文のテストパス名を実際のエンティティに合わせて変更してください
// 例: 'components/your-entity/organisms/YourEntityEditDialog.vue test'
describe('components/employee/organisms/QualificationEditDialog.vue test', () => {
  test('表示処理:open でダイアログが表示される', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開く
    vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // 表示非表示を確認
    expect(dialogVm.dialog).toBe(true)
  })

  test('OK処理:結果がtrueで返る', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開き、結果返却を待つ
    const openPromis = vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()
    // バリデーションで成功を返すようにする
    vi.spyOn(dialogWrapper.vm.validators, 'validate').mockResolvedValue({ success: true })

    // OK処理を実行
    await dialogVm.action()

    // ダイアログの返却値を確認
    const result = await openPromis
    expect(result).toMatchObject({
      isOk: true,
    })
  })

  test('キャンセル処理：結果がfalseで返る', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開き、結果返却を待つ
    const openPromis = vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // キャンセル処理を実行
    await dialogVm.cancel()

    // ダイアログの返却値を確認
    const result = await openPromis
    expect(result).toMatchObject({
      isOk: false,
    })
  })
  test('初期表示処理：呼び出し元から情報を渡さずにダイアログを開いた場合、入力フィールドが空で表示される', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開く
    vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // 表示非表示を確認
    expect(dialogVm.dialog).toBe(true)
    // データの内容を確認
    expect(dialogVm.targetData).toEqual({})
    // TODO: 入力フィールドのラベル名を実際のエンティティに合わせて変更してください
    const inputFields = getAllInputValuesAsObject(dialogWrapper)
    expect(inputFields).toEqual({
      資格名: undefined,
    })
  })

  test('初期表示処理：呼び出し元から情報を渡してダイアログを開いた場合、入力フィールドに設定されて表示される', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // TODO: open関数の引数を実際のエンティティのデータ構造に合わせて変更してください
    vm.dialog.value?.open({ name: '資格名テスト' })
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // 表示非表示を確認
    expect(dialogVm.dialog).toBe(true)
    // TODO: targetDataの期待値を実際のエンティティのデータ構造に合わせて変更してください
    expect(dialogVm.targetData).toEqual({ name: '資格名テスト' })
    // TODO: 入力フィールドのラベル名と値を実際のエンティティに合わせて変更してください
    const inputFields = getAllInputValuesAsObject(dialogWrapper)
    expect(inputFields).toEqual({
      資格名: '資格名テスト',
    })
  })

  test('OK処理：入力値にエラーがない場合、OKボタンを押下すると入力情報が返却される', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開き、結果返却を待つ
    const openPromis = vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // TODO: 入力値を実際のエンティティのデータ構造に合わせて変更してください
    dialogWrapper.vm.targetData.name = '資格名テスト'

    // OK処理を実行
    await dialogVm.action()

    // TODO: 期待される返却値を実際のエンティティのデータ構造に合わせて変更してください
    openPromis.then((result: any) => {
      expect(result).toEqual({
        isOk: true,
        data: { name: '資格名テスト' },
      })
    })
  })

  test('OK処理：入力値にエラーがある場合、OKボタンを押下するとエラーメッセージが表示されダイアログが閉じない', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開く
    vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // TODO: エラーケースの入力値を実際のエンティティのバリデーション条件に合わせて変更してください
    dialogWrapper.vm.targetData.name = '資格名テスト12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345'

    // OK処理を実行
    await dialogVm.action()

    // TODO: エラーメッセージの期待値を実際のエンティティのバリデーション条件に合わせて変更してください
    expect(dialogVm.errorMessages).toEqual({
      name: ['100文字以下で入力してください。'],
    })
    // ダイアログが閉じていないことを確認
    expect(dialogVm.dialog).toBe(true)
  })

  test('キャンセル処理：キャンセルボタンを押下しても情報が返却されない', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(TestComponent)
    const vm = wrapper.getCurrentComponent().proxy as any

    // ダイアログのコンポーネントを取得
    const dialogWrapper = wrapper.findComponent({ name: dialogComponentName })
    const dialogVm = dialogWrapper.getCurrentComponent().proxy as any

    // ダイアログを開き、結果返却を待つ
    const openPromis = vm.dialog.value?.open()
    // ダイアログのレンダリングを待つ
    await dialogWrapper.vm.$nextTick()

    // TODO: 入力値を実際のエンティティのデータ構造に合わせて変更してください
    dialogWrapper.vm.targetData.name = '資格名テスト'

    // キャンセル処理を実行
    await dialogVm.cancel()

    // ダイアログの返却値を確認
    openPromis.then((result: any) => {
      expect(result).toEqual({
        isOk: false,
        data: undefined,
      })
    })
  })
})
