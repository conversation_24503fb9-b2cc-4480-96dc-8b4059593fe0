using FluentValidation;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.UpdateEntity;

public class UpdateEntityValidator : AbstractValidator<UpdateEntityCommand>
{
    public UpdateEntityValidator()
    {
        /* TODO: バリデーションの定義を追加します。 */
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.Name).NotEmpty().MaximumLength(100);
        RuleFor(v => v.PeriodFrom).LessThanOrEqualTo(v => v.PeriodTo);
        RuleFor(v => v.Amount).GreaterThanOrEqualTo(0);
        RuleFor(t => t.Version).NotEmpty();
        RuleForEach(t => t.Members).ChildRules(item =>
        {
            item.RuleFor(c => c.MemberId).NotEmpty();
            item.RuleFor(c => c.UnitPricePerHour).GreaterThanOrEqualTo(0);
        });
    }
}
