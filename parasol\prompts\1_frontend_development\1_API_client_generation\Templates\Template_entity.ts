import * as z from 'zod'
// 利用する列挙型のインポート
import { accountTypes } from '../enums/accountType'
import { approvalTypes } from '../enums/approvalType'
import { conditionRegistrationCategories } from '../enums/conditionRegistrationCategory'
import { conditionRegistrationStatuses } from '../enums/conditionRegistrationStatus'
// 内部エンティティのインポートが必要な場合はここに実装

// エンティティの定義
export const conditionRegistrationSchema = z.object({
  /** ID */
  id: z.string(),
  /** 顧客識別ID */
  customerIdentificationId: z.string(),
  /** 案件ID */
  caseId: z.string(),
  /** 科目 */
  accountType: z.enum(accountTypes),
  /** 稟議形態 */
  approvalType: z.enum(approvalTypes),
  /** 条件登録区分 */
  conditionRegistrationCategory: z.enum(conditionRegistrationCategories),
  /** 稟議区分 */
  approvalCategory: z.string(),
  /** 稟議番号 */
  approvalNumber: z.number().nullish(),
  /** 取扱番号 */
  handlingNumber: z.number().nullish(),
  /** 状態 */
  conditionRegistrationStatus: z.enum(conditionRegistrationStatuses),
  /** 条件登録者ID */
  conditionRegisterId: z.string().nullish(),
  /** 条件登録者氏名 */
  conditionRegisterName: z.string().nullish(),
  /** 条件登録日時 */
  conditionRegistrationDateTime: z.date().nullish(),
  /** 決裁者ID */
  approverId: z.string().nullish(),
  /** 決裁者氏名 */
  approverName: z.string().nullish(),
  /** 決裁日時 */
  approveDateTime: z.date().nullish(),
  /** Version */
  version: z.string(),
})

// エンティティ型の定義
export type ConditionRegistration = z.infer<typeof conditionRegistrationSchema>
