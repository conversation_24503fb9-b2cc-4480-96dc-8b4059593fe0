import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

const teamMemberSchema = z.object({
  id: z.string(),
  teamId: z.string(),
  staffId: z.string(),
  isLeader: z.boolean()
})

const getTeamAllResultSchema = z.object({
  id: z.string(),
  teamName: z.string(),
  teamMembers: z.array(teamMemberSchema),
  version: z.string()
})

type TeamMember = z.infer<typeof teamMemberSchema>
type GetTeamAllResult = z.infer<typeof getTeamAllResultSchema>

/**
 * 全チームデータを取得する
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetAllTeams() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetTeamAllResult[]>(
    useFetch(
      () =>
        $endpoints.default.get('/ibp-customer-proposal/v1.0/team/getall'),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}