<!--
  注意: このページはバージョンアップ途中の遷移確認のための実装です。
  バージョンアップ時に修正が必要な画面です。
-->
<template>
  <div class="edit-page">
    <v-container>
      <v-row>
        <v-col>
          <h1>案件編集ページ</h1>
          <v-alert
            v-if="transitionInfo"
            color="info"
            icon="mdi-information"
            border
          >
            <h3>遷移情報の確認</h3>
            <div>ID: {{ transitionInfo.id }}</div>
            <div>顧客識別ID: {{ transitionInfo.customerIdentificationId }}</div>
            <div>遷移元: {{ transitionInfo.from }}</div>
          </v-alert>
          <v-btn
            color="primary"
            class="mt-4"
            @click="goBack"
          >
            一覧に戻る
          </v-btn>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
// 注意: このファイルはバージョンアップ時に修正が必要です
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// タイトルとメタデータの設定
useHead({
  title: '案件編集',
})

definePageMeta({
  title: '案件編集',
})

const route = useRoute()
const router = useRouter()

// 遷移パラメータの取得
const transitionInfo = ref<{
  id?: string
  customerIdentificationId?: string
  from?: string
}>()

onMounted(() => {
  // URLのクエリパラメータから遷移情報を取得
  const { id, customerIdentificationId, from } = route.query

  transitionInfo.value = {
    id: id as string,
    customerIdentificationId: customerIdentificationId as string,
    from: from as string,
  }

  // ログ出力
  console.log('遷移情報:', transitionInfo.value)
})

// 元の画面に戻る
function goBack() {
  // 遷移元がローンリースの場合はその画面に戻る
  if (transitionInfo.value?.from === 'LOAN_AND_LEASE_CASE') {
    router.push('/loan-and-lease')
  } else {
    // それ以外の場合はデフォルトで一覧画面に戻る
    router.back()
  }
}
</script>

<style scoped>
.edit-page {
  padding: 20px;
}
</style>
