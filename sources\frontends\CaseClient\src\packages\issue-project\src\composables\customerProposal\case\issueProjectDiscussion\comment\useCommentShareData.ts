import { useMycareer } from '@ibp/common-case/src/composables/shared/useMycareer'

const commentDataRef = ref<{
  isHighlighted: boolean // リアクションしたメンバーを表示する位置のオフセット
  selectedReactionType: number | undefined // 選択されたリアクションタイプを追跡
  // shownDetail: boolean // 詳細を表示しているか
  reactionMemberOffsetY: number // リアクションしたメンバーを表示する位置のオフセット
}>({
  isHighlighted: false,
  selectedReactionType: undefined,
  // shownDetail: true,
  reactionMemberOffsetY: 15,
})

export const useCommentShareData = () => {
  // インスタンスごとに使いたいデータ
  const dataRef = ref<{
    reactionTypeOnHover: number // マウスホバーしたときのリアクションタイプ
    shownComments: boolean
    shownDetailButton: boolean
  }>({
    reactionTypeOnHover: 0,
    shownComments: false,
    shownDetailButton: false,
  })

  // Myキャリアにアクセスできる状態か否か
  const isMyCareerEnabled = computed(() => {
    const { getIsMyCareerAccessSucceed } = useMycareer()
    return getIsMyCareerAccessSucceed()
  })

  const { $auth } = useNuxtApp()
  const authUserId = $auth.getUser()?.userId
  const authDisplayName = $auth.getUser()?.displayName

  return {
    commentDataRef,
    isMyCareerEnabled,
    authUserId,
    authDisplayName,
    dataRef,
  }
}
