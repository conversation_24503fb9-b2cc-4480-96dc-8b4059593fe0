# UseCase単体テスト実装

## 役割定義

- 日本人のベテランエンジニアとして、バックエンドのUseCase単体テストの開発を行います。
- 既存のUseCaseハンドラー実装を分析し、高品質で高カバレッジな単体テストを生成します。
- 語学が堪能であるためヒアリングの際には適した言語で対応しますが、成果物は日本語で出力します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 ： 
  - `parasol\architecture\backend_architecture.md`
- 共通処理使用方法リファレンス：
  - `parasol\architecture\usage_sample_backend_shared.md`
- プロジェクト設定定義 : 
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- ドメイン言語 ： `parasol\capability\**\ドメイン言語.md`
- API定義 : `parasol\api\*_API_definition.md`

### サブプロンプト

UseCaseタイプ別のテスト作成手順は以下のファイルを参照してください。

- 検索UseCaseテスト作成手順：`parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_find_test.md`
- 単一取得UseCaseテスト作成手順：`parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_get_test.md`
- 登録UseCaseテスト作成手順：`parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_add_test.md`
- 更新UseCaseテスト作成手順：`parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_update_test.md`
- 削除UseCaseテスト作成手順：`parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_delete_test.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- 検索UseCaseテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\Template_FindHandlerTest.cs`
- 単一取得UseCaseテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\Template_GetHandlerTest.cs`
- 登録UseCaseテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\Template_AddHandlerTest.cs`
- 更新UseCaseテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\Template_UpdateHandlerTest.cs`
- 削除UseCaseテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\Template_DeleteHandlerTest.cs`

### 参照コード

- UseCaseハンドラー実装 : `[サービスディレクトリ]\UseCases\**\*Handler.cs`
- 既存テストコード : `[サービステストディレクトリ]\UseCases\**\*Test.cs`

## 出力定義

- UseCaseテストクラス
  - 出力先ディレクトリ：`[サービステストディレクトリ]\UseCases\[エンティティ名]\[UseCaseタイプ]`
  - ファイル名フォーマット：`[エンティティ名][UseCaseタイプ]HandlerTest.cs`

## 制約事項

### 前提条件

- **対象のUseCaseハンドラーは既に実装済みであること**
- テスト用プロジェクト（`*.Tests`）が存在すること
- `UseCases`テスト用ディレクトリが存在すること

### 品質要件

- **コードカバレッジ100%を目指す**
- 全ての分岐パス（if-else、switch-case、try-catch）をカバー
- 正常系・異常系・境界値テストを包括的に実装
- エッジケースや例外パターンも漏れなくテスト

### 禁止事項

- 定義ファイルを編集する代わりに、出力ファイルに必要な内容を記述してください
- テンプレートファイルを編集する代わりに、テンプレートの構造に従って新しいファイルを作成してください
- 当プロンプトファイルを編集する代わりに、指示内容に従って作業を実行してください

## 指示詳細

### 情報収集

1. 対象エンティティが指定されていない場合、作業を一時中断し「テスト対象の集約/エンティティを選択してください。」とチャット上に出力しヒアリングを行ってください。
   - `[サービスディレクトリ]\UseCases`配下に実装済みのUseCaseディレクトリを選択肢として提示してください。
   - 対象の集約/エンティティが特定できたら後続の処理に進んでください。
   - 対象の集約/エンティティが特定できなかった場合は、再度ヒアリングを行ってください。

2. 対象UseCaseタイプが指定されていない場合、作業を一時中断し「テスト作成対象のUseCaseタイプを選択してください。」とチャット上に出力しヒアリングを行ってください。
   - 指定されたエンティティ配下に実装済みのUseCaseを選択肢として提示してください。
      - 検索（Find[エンティティ名]Handler）
      - 単一取得（Get[エンティティ名]Handler）
      - 登録（Add[エンティティ名]Handler）
      - 更新（Update[エンティティ名]Handler）
      - 削除（Delete[エンティティ名]Handler）
   - 対象のUseCaseタイプが特定できたら後続の処理に進んでください。
   - 複数のUseCaseタイプが指定された場合は、順次処理を行ってください。

3. 既存のUseCaseハンドラー実装を読み込み、以下の情報を収集してください：
   - Handleメソッドの実装詳細
   - 依存関係（DI）の注入内容
   - プライベートメソッドの存在とその実装
   - 分岐パス（if-else、switch-case、try-catch）の特定
   - 外部API呼び出しの有無と処理内容
   - 戻り値のパターン（成功・エラー）
   - 例外処理の実装内容

4. 関連するドメインエンティティとドメイン制約を確認してください：
   - `[サービスディレクトリ]\Domain\Entities\[エンティティ名]\*.cs`
   - `parasol\capability\**\ドメイン言語.md`

5. 既存のテストコードを参考に、命名規則とテストパターンを確認してください：
   - `[サービステストディレクトリ]\UseCases\*.cs`
   - 使用しているテストライブラリとアサーション方法

6. 必要な情報収集が完了したら作業を一時中断し「以下の内容でUseCaseテスト実装を行います。よろしいですか？」と出力し、ユーザーの承認を得てください。
   - 出力する内容は以下の通り
      - 対象エンティティと対象UseCaseタイプ
      - 特定された分岐パスの数
      - テスト予定ケース数（概算）
      - カバレッジ対象となる主要メソッド一覧
   - 承認が得られたら後続処理に進んでください。
   - 承認が得られない場合はユーザーの指示に従って情報の訂正を行ってください。

### 生成作業

1. 指定された各UseCaseタイプについて、完了するまで以下の内容を繰り返してください：
   1. 実装対象のUseCaseテストを一つ選択し、「[エンティティ名][UseCaseタイプ]HandlerTest を実装します」と出力してください。
   2. 対応するサブプロンプトを読み込み、カバレッジ分析と詳細なテスト作業を行ってください：
      - 検索UseCaseテスト : `parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_find_test.md`
      - 単一取得UseCaseテスト : `parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_get_test.md`
      - 登録UseCaseテスト : `parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_add_test.md`
      - 更新UseCaseテスト : `parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_update_test.md`
      - 削除UseCaseテスト : `parasol\prompts\2_backend_development\4_unit_testing_generation\sub_prompts\sub_prompt_delete_test.md`
   3. テストクラスファイルを生成し、100%カバレッジを目指したテストケースを実装してください。

### 品質保証

1. **カバレッジ完全性**
   - 既存実装の全分岐パスがテストされているか
   - 全例外パスがテストされているか
   - 全プライベートメソッドが間接的にテストされているか

2. **テスト品質**
   - 各テストケースが独立して実行できるか
   - アサーションが適切に設定されているか
   - テストデータが適切に設定されているか
   - モック・スタブが正しく設定されているか

3. **命名とコーディング規約**
   - 日本語によるテストメソッド名が適切か
   - 既存のテストコードと一貫性があるか
   - ファイル配置とnamespaceが正しいか

4. **不備が見つかった場合は修正してください**
