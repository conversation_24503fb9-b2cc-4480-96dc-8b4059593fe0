# dev-containers

開発用途にSQLServer、GremlinServerをDockerコンテナでローカル端末に構築するツール

## 前提

* docker for windows
* docker compose

## 利用方法

* ターミナルソフトを開き以下のコマンドを実行するとデータストアの環境が整う

```pwsh
docker compose -p {your project name} up -d
```

* 環境を消したい場合

```pwsh
docker compose stop
docker compose rm -f
```

その他の利用方法は[公式ドキュメント](https://docs.docker.jp/engine/reference/commandline/compose.html)を参照

## 構築概要

* Azure storage emulatorのazurite
* azure-sql-edge:2.0.0
    * データは揮発性あり(コンテナを消すと消えます)
    * ユーザ名:SA、パスワード:`P@ssw0rd!`

構築の詳細はdocker-compose.ymlを参照ください。

## 注意

sql-serverにSSMSで接続する場合は接続プロパティのオプションから「サーバー証明書を信頼する」をチェックしてください

以上
