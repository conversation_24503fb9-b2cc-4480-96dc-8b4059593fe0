import type { PublicRuntimeConfig } from 'nuxt/schema'
import type { AppendGet } from './01.endpoints'
import type { ILogger } from '~/utils/shared/logging'
import type {
  MsalWrapper,
} from '~/utils/shared/msalHelper'
// https://nuxt.com/docs/guide/directory-structure/plugins#typing-plugins

declare module '#app' {
  interface NuxtApp {
    $endpoints: AppendGet<PublicRuntimeConfig['endpoints']>
    $auth: MsalWrapper
    $logger: {
      create: (moduleName: string) => ILogger
    }
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $endpoints: AppendGet<PublicRuntimeConfig['endpoints']>
    $auth: MsalWrapper
    $logger: {
      create: (moduleName: string) => ILogger
    }
  }
}

export {}
