<#
.SYNOPSIS
    テストを実行し、カバレッジを取得したうえで、レポートをHTMLとMarkdownで生成します。
.DESCRIPTION
    テストを実行し、カバレッジを取得したうえで、レポートをHTMLとMarkdownで生成します。コマンドの引数で実行するテストを指定することができます。
    このコマンドは同じディレクトリにある最初に見つかった sln ファイルを読み取り、その中に含まれるプロジェクトのうち、末尾が .Tests で終わるプロジェクトをテストプロジェクトとして扱います。
    また、テストプロジェクトの名前から .Tests を取り除いたものを SUT プロジェクトとして扱います。

    テストプロジェクトファイルと同じ階層に coverlet.config.json ファイルを作成することで、カバレッジの除外設定を行うことができます。
    設定は coverlet の msbuild integration に利用するパラメーターをそのまま利用しています。詳細については以下を参照してください。
    https://github.com/coverlet-coverage/coverlet/blob/master/Documentation/MSBuildIntegration.md#excluding-from-coverage
    coverlet.config.json ファイルがない場合は、デフォルトの設定として **/*.g.cs ファイルを除外します。

    {
      "ExcludeByFile": ["**/*.g.cs"],
      "Exclude": [
        "[coverlet.*]*",
        "[*]Coverlet.Core*"
      },
      "ExcludeByAttribute": [
        "Obsolete", "GeneratedCodeAttribute", "CompilerGeneratedAttribute"
      ]
    }
.PARAMETER noReport
    レポートを生成しないかどうかを指定します。指定しない場合はレポートを生成します。
.PARAMETER openReport
    レポートを表示するかどうかを指定します。指定しない場合はレポートを表示しません。
.PARAMETER build
    ビルドを実行するかどうかを指定します。指定しない場合はビルドを実行しません。
.PARAMETER sln
    ソリューションファイルの名を指定します。指定しない場合は同じディレクトリにある最初に見つかった sln ファイルを利用します。
.PARAMETER reportTypes
    レポートの種類を指定します。指定しない場合は Html を生成します。指定できる値は https://github.com/danielpalme/ReportGenerator/wiki/Output-formats を参照してください。
.PARAMETER targets
    実行するテストプロジェクト名またはSUTプロジェクト名を指定します。指定しない場合は全てのテストを実行します。大文字小文字は区別されません。
.EXAMPLE
    指定したテストを実行し、レポートを表示します。
    PS> test.ps1 -openReport -targets ProjectService,Shared -reportTypes HTML,MarkdownSummary
.NOTES
    このスクリプトを実行するには `dotnet-reportgenerator-globaltool` をローカルツールとしてセットアップされている必要があります。
#>

param(
    [Alias("nr")]
    [switch]$noReport = $false,
    [Alias("or")]
    [switch]$openReport = $false,
    [Alias("b")]
    [switch]$build = $false,
    [Alias("s")]
    [string]$sln,
    [Alias("rt")]
    [string[]]$reportTypes = @('HTML'),
    [Alias("t")]
    [string[]]$targets
)

function Get-TestResult {
    param (
        [string]$testProjectName,
        [string]$resultText,
        [DateTime]$startTime
    )

    $executionTime = (Get-Date) - $startTime

    $result = [PSCustomObject]@{
        TestProjectName = $testProjectName
        ResultText = $resultText
        ExecutionTime = $executionTime.TotalSeconds.ToString() + " s"
    }

    return $result
}

# スクリプトのディレクトリ
$scriptPath = $PSScriptRoot
# 出力先ディレクトリ
$outputDir = Join-Path -Path $scriptPath -ChildPath "testresult"
# カバレッジを出力するディレクトリ
$coverageOutputDir = Join-Path -Path $outputDir -ChildPath "coverages"
# テスト結果を出力するディレクトリ
$testoutputDir = Join-Path -Path $outputDir -ChildPath "test"

#####
# テスト対象のプロジェクトを取得
#####

# $sln が指定されている場合は、絶対パスに変換します。指定されていない場合は、 $scriptPath にある最初に見つかった sln ファイルを利用します。
if ($sln) {
    $slnFile = (Convert-Path $sln)
} else {
    $slnFile = Get-ChildItem -Path $scriptPath -Filter "*.sln" | Select-Object -First 1 | ForEach-Object { $_.FullName }
}
if (-not $slnFile) {
    Write-Error "Solution file not found: $sln"
    exit 1
}

Write-Host "Solution file: $slnFile"
$slnDir = Split-Path -Path $slnFile

# ファイルの内容を読み取り、.csproj ファイルを持つプロジェクト行をフィルタリング
$projectInfo = Get-Content $slnFile | Where-Object { $_ -match 'Project\("\{[A-F0-9\-]*\}"\) = "(.+)"( *, *)"(.+\.csproj)"( *, *)"\{[A-F0-9\-]*\}"' } | ForEach-Object { [PSCustomObject]@{ 'Name' = $matches[1]; 'File' = $matches[3] } }

# SUTとテストプロジェクトの組み合わせを取得
$matchedPairs = @()

foreach ($sut in $projectInfo) {
    # テストプロジェクトの名前は SUT の名前に .Tests を付けたものとする
    $testProjectName = $sut.Name + ".Tests"
    $testProject = $projectInfo | Where-Object { $_.Name -eq $testProjectName }
    if ($testProject) {
        $matchedPairs += [PSCustomObject]@{
            'SUT'          = $sut.Name.Trim()
            'SUTFile'      = $sut.File.Trim()
            'SUTFullFile'  = Join-Path -Path $slnDir -ChildPath $sut.File.Trim()
            'TestProject'  = $testProject.Name.Trim()
            'TestProjectFile' = $testProject.File.Trim()
            'TestProjectFullFile' = Join-Path -Path $slnDir -ChildPath $testProject.File.Trim()
        }
    }
}

# matchedPairs に含まれるプロジェクトを、 $targets で指定されたプロジェクトに絞り込む。
if ($targets) {
    # $targets = $targets | ForEach-Object { $_.Trim() }
    $matchedPairs = $matchedPairs | Where-Object { ($targets -icontains  $_.SUT) -or ($targets -icontains $_.TestProject) }
}
# matchedPairs が一件もない場合はエラーにして終了する
if ($matchedPairs.Count -eq 0) {
    Write-Error "No project found: $targets"
    exit 1
}

$matchedPairs | Format-Table -Property SUT, SUTFile, TestProject, TestProjectFile

#####
# テストの前準備
#####

$executed = $false
$hasErrorOccurred = $false

## $outputDir がない場合は作成
if (-not (Test-Path $outputDir)) {
    New-Item -Path $outputDir -ItemType Directory | Out-Null
}

## $outputDir の中身を削除
if (Test-Path $outputDir) {
    Remove-Item -Path $outputDir -Recurse -Force
}

# SUTとテストプロジェクトの組み合わせを取得
$results = @()

#####
# テストの実行
#####
foreach ($pair in $matchedPairs) {
    # テストを実行してカバレッジを取得します。
    # コマンドの詳細については次を参照してください。
    # https://docs.microsoft.com/ja-jp/dotnet/core/tools/dotnet-test
    # https://github.com/coverlet-coverage/coverlet/blob/master/Documentation/MSBuildIntegration.md

    $startTime = Get-Date

    # SUTFullFile および TestProjectFullFile がない場合はエラーにしてスキップ
    if (-not (Test-Path $pair.SUTFullFile)) {
        $testResult = Get-TestResult -testProjectName $pair.TestProject -resultText "NO SUT FILE" -startTime $startTime
        $results += $testResult

        Write-Error "SUT file not found: $($pair.SUTFullFile)"
        $hasErrorOccurred = $false
        continue
    }
    if (-not (Test-Path $pair.TestProjectFullFile)) {
        $testResult = Get-TestResult -testProjectName $pair.TestProject -resultText "NO TEST FILE" -startTime $startTime
        $results += $testResult

        Write-Error "Test project file not found: $($pair.TestProjectFullFile)"
        $hasErrorOccurred = $false
        continue
    }

    # TestProjectFullFileと同じ階層にある coverlet.config.json ファイルを読み込み、カバレッジの除外設定を取得
    $exclude = "/p:ExcludeByFile=""**/*.g.cs"""
    $excludeFile = Join-Path -Path (Split-Path -Path $pair.TestProjectFullFile) -ChildPath "coverlet.config.json"
    if (Test-Path $excludeFile) {
        Write-Host "Found exclude definition file: $excludeFile"
        $excludeContent = (Get-Content $excludeFile | ConvertFrom-Json)
        $exclude = " /p:ExcludeByFile=""$($excludeContent.ExcludeByFile -join "%2c")"""
        $exclude += " /p:Exclude=""$($excludeContent.Exclude -join "%2c")"""
        $exclude += " /p:ExcludeByAttribute=""$($excludeContent.ExcludeByAttribute  -join "%2c")"""
        # $exluceFile の内容を読み込んで、改行を半角スペースに変換し、トリムする
        # $exclude = (Get-Content $excludeFile) -replace "`r`n", " " -replace "`n", " " | ForEach-Object { $_.Trim() }
    }

    Write-Host "Test project: $($pair.TestProject)"
    $testOutputFile = Join-Path -Path $testoutputDir -ChildPath ($pair.TestProject + ".trx")
    $coverageOutputFile = Join-Path -Path $coverageOutputDir -ChildPath ($pair.TestProject + ".xml")

    $command = "dotnet test ""$($pair.TestProjectFullFile)"""
    $command += " --logger ""trx;LogFileName=$testOutputFile"""
    $command += " /p:CollectCoverage=true"
    $command += " /p:CoverletOutputFormat=cobertura"
    $command += " /p:CoverletOutput=""$coverageOutputFile"""
    $command += " /p:Include=""[$($pair.SUT)]*"""
    $command += " $exclude"
    if (!$build) {
        $command += " --no-build"
    }
    Write-Host "Test command: $command"
    Invoke-Expression $command

    if ($LASTEXITCODE -ne 0) {
        $testResult = Get-TestResult -testProjectName $pair.TestProject -resultText "ERROR $LASTEXITCODE" -startTime $startTime
        $results += $testResult

        Write-Error "Error occurred while executing: $command"
        $hasErrorOccurred = $true
    } else {
        $testResult = Get-TestResult -testProjectName $pair.TestProject -resultText "SUCCESS" -startTime $startTime
        $results += $testResult
        $executed = $true
    }
}

#####
# レポートの生成
#####
if ($executed -and -not $noReport) {
    $reportTypeValue = $reportTypes -join ";"
    Write-Host "Generate report - $reportTypeValue"
    $coverageOutputFiles = Join-Path -Path $coverageOutputDir -ChildPath "*.xml"
    $reportDir = Join-Path -Path $coverageOutputDir -ChildPath "report"
    dotnet tool run reportgenerator "-reports:$coverageOutputFiles" `
        -targetdir:"$reportDir" `
        -reporttypes:"$reportTypeValue"

    $lowerCaseReportTypes = $reportTypes | ForEach-Object { $_.ToLower() }
    if($lowerCaseReportTypes -contains "html" -and $openReport) {
        # $reportDir の中にある index.html を開く
        Write-Host "Open report"
        Start-Process -FilePath (Join-Path -Path $reportDir -ChildPath "index.html")
    }
}

$results | Format-Table -Property TestProjectName, ResultText, ExecutionTime

if ($hasErrorOccurred) {
    exit 1
} else {
    exit 0
}
