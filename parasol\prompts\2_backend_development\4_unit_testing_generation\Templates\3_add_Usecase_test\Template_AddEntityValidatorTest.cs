using FluentAssertions;
using FluentValidation.TestHelper;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Domain.Enums;
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.AddEntity;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.AddEntity;

/// <summary>
/// AddEntityValidatorの単体テストクラス
/// </summary>
/// <remarks>
/// FluentValidation.TestHelperを使用してバリデーションルールを検証します。
/// MemberData + Theory パターンで大量のバリデーションケースを効率的にテストし、
/// コードカバレッジ100%を目指します。
/// SafeValueパターンを使用して正常値ベースでの異常値テストを実装します。
/// </remarks>
public class AddEntityValidatorTest
{
    #region テスト用定数

    /// <summary>
    /// null値のテスト用定数
    /// </summary>
    private static readonly string NullString = null!;

    /// <summary>
    /// 空文字のテスト用定数
    /// </summary>
    private static readonly string EmptyString = string.Empty;

    /// <summary>
    /// 単一スペースのテスト用定数
    /// </summary>
    private static readonly string SingleSpace = " ";

    /// <summary>
    /// 複数スペースのテスト用定数
    /// </summary>
    private static readonly string MultipleSpaces = "   ";

    /// <summary>
    /// タブ文字のテスト用定数
    /// </summary>
    private static readonly string TabCharacter = "\t";

    /// <summary>
    /// 改行文字のテスト用定数
    /// </summary>
    private static readonly string NewLineCharacter = "\n";

    /// <summary>
    /// NULL文字のテスト用定数
    /// </summary>
    private static readonly string NullCharacter = "\0";

    #endregion

    #region テストデータ生成メソッド

    /// <summary>
    /// エラーが発生するテストケースを生成します
    /// </summary>
    /// <returns>エラーテストケースの配列</returns>
    public static IEnumerable<object[]> CreateErrorValues() => new List<object[]>
    {
        // TODO: 実際のバリデーションルールに合わせて以下のテストケースを調整してください

        #region Name フィールドのバリデーション
        // Name: null チェック
        (SafeValue() with { Name = NullString }).ToObjectArray(),
        // Name: 空文字チェック
        (SafeValue() with { Name = EmptyString }).ToObjectArray(),
        // Name: 空白文字チェック
        (SafeValue() with { Name = SingleSpace }).ToObjectArray(),
        (SafeValue() with { Name = MultipleSpaces }).ToObjectArray(),
        // Name: 文字数上限チェック（例：100文字）
        (SafeValue() with { Name = new string('あ', 101) }).ToObjectArray(),
        // Name: 文字数下限チェック（例：1文字未満）
        (SafeValue() with { Name = "" }).ToObjectArray(),
        #endregion

        #region Description フィールドのバリデーション（オプション）
        // Description: 文字数上限チェック（例：1000文字）
        (SafeValue() with { Description = new string('い', 1001) }).ToObjectArray(),
        #endregion

        #region Status フィールドのバリデーション
        // Status: 無効な列挙値（実際のenumに合わせて調整）
        // (SafeValue() with { Status = (EntityStatus)999 }).ToObjectArray(),
        #endregion

        #region Priority フィールドのバリデーション
        // Priority: 負の値チェック
        (SafeValue() with { Priority = -1 }).ToObjectArray(),
        // Priority: 上限値チェック（例：100を超える）
        (SafeValue() with { Priority = 101 }).ToObjectArray(),
        #endregion

        #region RelatedEntityId フィールドのバリデーション
        // RelatedEntityId: 空文字チェック（nullは許可、空文字は不許可のパターン）
        (SafeValue() with { RelatedEntityId = EmptyString }).ToObjectArray(),
        // RelatedEntityId: 空白文字チェック
        (SafeValue() with { RelatedEntityId = SingleSpace }).ToObjectArray(),
        // RelatedEntityId: 文字数上限チェック
        (SafeValue() with { RelatedEntityId = new string('a', 51) }).ToObjectArray(),
        #endregion

        #region Tags フィールドのバリデーション
        // Tags: 個数上限チェック（例：10個まで）
        (SafeValue() with { Tags = Enumerable.Range(1, 11).Select(i => $"tag{i}").ToList() }).ToObjectArray(),
        // Tags: 空のタグを含むリスト
        (SafeValue() with { Tags = new List<string> { "tag1", EmptyString, "tag3" } }).ToObjectArray(),
        // Tags: nullのタグを含むリスト
        (SafeValue() with { Tags = new List<string> { "tag1", NullString, "tag3" } }).ToObjectArray(),
        // Tags: 長すぎるタグ名
        (SafeValue() with { Tags = new List<string> { new string('t', 51) } }).ToObjectArray(),
        #endregion

        #region 複合バリデーション
        // 複数フィールドが同時に無効
        (SafeValue() with { Name = NullString, Priority = -1 }).ToObjectArray(),
        (SafeValue() with { Name = new string('あ', 101), Description = new string('い', 1001) }).ToObjectArray(),
        #endregion

        #region 業務固有のバリデーション
        // TODO: 業務固有のバリデーションルールがある場合は追加してください
        // 例：特定の組み合わせが無効な場合
        // (SafeValue() with { Status = EntityStatus.Draft, Priority = 10 }).ToObjectArray(),
        #endregion

    }.ToArray();

    /// <summary>
    /// 正常なコマンドの基準値を作成します
    /// </summary>
    /// <returns>正常なAddEntityCommand</returns>
    private static AddEntityCommand SafeValue()
    {
        // TODO: 実際のコマンドプロパティに合わせて調整してください
        return new AddEntityCommand(
            Name: "正常なエンティティ名",
            Description: "正常な説明文",
            Status: EntityStatus.Active,
            Priority: 5,
            RelatedEntityId: "related-entity-1",
            Tags: new List<string> { "tag1", "tag2" },
            UploadFiles: null
        );
    }

    /// <summary>
    /// 境界値テストケースを生成します
    /// </summary>
    /// <returns>境界値テストケースの配列</returns>
    public static IEnumerable<object[]> CreateBoundaryValues() => new List<object[]>
    {
        // TODO: 実際のバリデーションルールに合わせて境界値テストケースを調整してください

        #region Name フィールドの境界値
        // Name: 最小文字数（1文字）
        (SafeValue() with { Name = "あ" }).ToObjectArray(),
        // Name: 最大文字数（100文字）
        (SafeValue() with { Name = new string('あ', 100) }).ToObjectArray(),
        #endregion

        #region Description フィールドの境界値
        // Description: 最大文字数（1000文字）
        (SafeValue() with { Description = new string('い', 1000) }).ToObjectArray(),
        // Description: null（許可される場合）
        (SafeValue() with { Description = null }).ToObjectArray(),
        #endregion

        #region Priority フィールドの境界値
        // Priority: 最小値（0）
        (SafeValue() with { Priority = 0 }).ToObjectArray(),
        // Priority: 最大値（100）
        (SafeValue() with { Priority = 100 }).ToObjectArray(),
        #endregion

        #region Tags フィールドの境界値
        // Tags: 最大個数（10個）
        (SafeValue() with { Tags = Enumerable.Range(1, 10).Select(i => $"tag{i}").ToList() }).ToObjectArray(),
        // Tags: 空リスト
        (SafeValue() with { Tags = new List<string>() }).ToObjectArray(),
        // Tags: null（許可される場合）
        (SafeValue() with { Tags = null }).ToObjectArray(),
        #endregion

    }.ToArray();

    #endregion

    #region バリデーションエラーテスト

    [Theory]
    [MemberData(nameof(CreateErrorValues))]
    public void エラーが発生する(AddEntityCommand request)
    {
        // Arrange
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.Errors.Should().NotBeEmpty("バリデーションエラーが発生すべきです");
        
        // TODO: より具体的なエラー内容の検証が必要な場合は追加してください
        // result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    #endregion

    #region 境界値テスト

    [Theory]
    [MemberData(nameof(CreateBoundaryValues))]
    public void 境界値で正常に処理される(AddEntityCommand request)
    {
        // Arrange
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.Errors.Should().BeEmpty("境界値では正常に処理されるべきです");
    }

    #endregion

    #region 正常系テスト

    [Fact]
    public void エラーが発生しない()
    {
        // Arrange
        var request = SafeValue();
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.Errors.Should().BeEmpty("正常なリクエストではエラーが発生しないべきです");
    }

    [Fact]
    public void 最小限の情報で正常に処理される()
    {
        // Arrange
        // TODO: 実際の必須フィールドのみに調整してください
        var request = new AddEntityCommand(
            Name: "最小限",
            Description: null,
            Status: EntityStatus.Draft,
            Priority: 0,
            RelatedEntityId: null,
            Tags: null,
            UploadFiles: null
        );
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void 全フィールド最大値で正常に処理される()
    {
        // Arrange
        var request = new AddEntityCommand(
            Name: new string('あ', 100), // 最大文字数
            Description: new string('い', 1000), // 最大文字数
            Status: EntityStatus.Active,
            Priority: 100, // 最大値
            RelatedEntityId: new string('r', 50), // 最大文字数
            Tags: Enumerable.Range(1, 10).Select(i => $"tag{i}").ToList(), // 最大個数
            UploadFiles: null
        );
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.Errors.Should().BeEmpty();
    }

    #endregion

    #region 個別フィールドバリデーション詳細テスト

    #region Name フィールドのバリデーションテスト

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    public void Name_必須チェック_無効な値でエラーが発生する(string invalidName)
    {
        // Arrange
        var request = SafeValue() with { Name = invalidName };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Name is required"); // TODO: 実際のメッセージに合わせて調整
    }

    [Theory]
    [InlineData(101)] // 上限+1
    [InlineData(150)] // 明らかな超過
    [InlineData(1000)] // 大幅上限超過
    public void Name_文字数上限チェック_超過でエラーが発生する(int length)
    {
        // Arrange
        var request = SafeValue() with { Name = new string('あ', length) };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Name must not exceed 100 characters"); // TODO: 実際のメッセージに合わせて調整
    }

    [Theory]
    [InlineData(1)]   // 最小値
    [InlineData(50)]  // 中間値
    [InlineData(100)] // 最大値
    public void Name_有効な文字数で正常に処理される(int length)
    {
        // Arrange
        var request = SafeValue() with { Name = new string('あ', length) };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    #endregion

    #region Priority フィールドのバリデーションテスト

    [Theory]
    [InlineData(-1)]
    [InlineData(-10)]
    [InlineData(int.MinValue)]
    public void Priority_最小値チェック_負の値でエラーが発生する(int invalidPriority)
    {
        // Arrange
        var request = SafeValue() with { Priority = invalidPriority };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Priority)
            .WithErrorMessage("Priority must be between 0 and 100"); // TODO: 実際のメッセージに合わせて調整
    }

    [Theory]
    [InlineData(101)]
    [InlineData(1000)]
    [InlineData(int.MaxValue)]
    public void Priority_最大値チェック_上限超過でエラーが発生する(int invalidPriority)
    {
        // Arrange
        var request = SafeValue() with { Priority = invalidPriority };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Priority)
            .WithErrorMessage("Priority must be between 0 and 100"); // TODO: 実際のメッセージに合わせて調整
    }

    [Theory]
    [InlineData(0)]   // 最小値
    [InlineData(50)]  // 中間値
    [InlineData(100)] // 最大値
    public void Priority_有効な値で正常に処理される(int validPriority)
    {
        // Arrange
        var request = SafeValue() with { Priority = validPriority };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Priority);
    }

    #endregion

    #region RelatedEntityId フィールドのバリデーションテスト

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("\t")]
    public void RelatedEntityId_空文字チェック_無効な値でエラーが発生する(string invalidId)
    {
        // Arrange
        var request = SafeValue() with { RelatedEntityId = invalidId };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.RelatedEntityId)
            .WithErrorMessage("RelatedEntityId must not be empty when provided"); // TODO: 実際のメッセージに合わせて調整
    }

    [Fact]
    public void RelatedEntityId_null値で正常に処理される()
    {
        // Arrange
        var request = SafeValue() with { RelatedEntityId = null };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.RelatedEntityId);
    }

    [Theory]
    [InlineData("valid-id")]
    [InlineData("entity-123")]
    [InlineData("a1b2c3d4-e5f6")]
    public void RelatedEntityId_有効な値で正常に処理される(string validId)
    {
        // Arrange
        var request = SafeValue() with { RelatedEntityId = validId };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.RelatedEntityId);
    }

    #endregion

    #region Tags フィールドのバリデーションテスト

    [Fact]
    public void Tags_個数上限チェック_超過でエラーが発生する()
    {
        // Arrange
        var tooManyTags = Enumerable.Range(1, 11).Select(i => $"tag{i}").ToList(); // 11個（上限10個を超過）
        var request = SafeValue() with { Tags = tooManyTags };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Tags)
            .WithErrorMessage("Tags must not exceed 10 items"); // TODO: 実際のメッセージに合わせて調整
    }

    [Fact]
    public void Tags_空要素チェック_空文字を含む場合エラーが発生する()
    {
        // Arrange
        var tagsWithEmpty = new List<string> { "tag1", EmptyString, "tag3" };
        var request = SafeValue() with { Tags = tagsWithEmpty };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Tags)
            .WithErrorMessage("Tags must not contain empty values"); // TODO: 実際のメッセージに合わせて調整
    }

    [Fact]
    public void Tags_null要素チェック_nullを含む場合エラーが発生する()
    {
        // Arrange
        var tagsWithNull = new List<string> { "tag1", NullString, "tag3" };
        var request = SafeValue() with { Tags = tagsWithNull };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Tags)
            .WithErrorMessage("Tags must not contain null values"); // TODO: 実際のメッセージに合わせて調整
    }

    [Theory]
    [InlineData(1)]
    [InlineData(5)]
    [InlineData(10)] // 最大個数
    public void Tags_有効な個数で正常に処理される(int tagCount)
    {
        // Arrange
        var validTags = Enumerable.Range(1, tagCount).Select(i => $"tag{i}").ToList();
        var request = SafeValue() with { Tags = validTags };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Tags);
    }

    [Fact]
    public void Tags_null値で正常に処理される()
    {
        // Arrange
        var request = SafeValue() with { Tags = null };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Tags);
    }

    [Fact]
    public void Tags_空リストで正常に処理される()
    {
        // Arrange
        var request = SafeValue() with { Tags = new List<string>() };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Tags);
    }

    #endregion

    #region Description フィールドのバリデーションテスト

    [Fact]
    public void Description_文字数上限チェック_超過でエラーが発生する()
    {
        // Arrange
        var tooLongDescription = new string('あ', 1001); // 1001文字（上限1000文字を超過）
        var request = SafeValue() with { Description = tooLongDescription };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Description)
            .WithErrorMessage("Description must not exceed 1000 characters"); // TODO: 実際のメッセージに合わせて調整
    }

    [Theory]
    [InlineData(1)]
    [InlineData(500)]
    [InlineData(1000)] // 最大文字数
    public void Description_有効な文字数で正常に処理される(int length)
    {
        // Arrange
        var request = SafeValue() with { Description = new string('あ', length) };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void Description_null値で正常に処理される()
    {
        // Arrange
        var request = SafeValue() with { Description = null };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    #endregion

    #endregion

    #region 複合バリデーションテスト（業務固有ルール）

    [Fact]
    public void 複数フィールドエラー_Name_Priority_同時エラーが発生する()
    {
        // Arrange
        var request = SafeValue() with 
        { 
            Name = NullString,  // Name必須エラー
            Priority = -1       // Priority範囲エラー
        };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name);
        result.ShouldHaveValidationErrorFor(x => x.Priority);
        result.Errors.Should().HaveCountGreaterOrEqualTo(2, "複数のエラーが同時に発生すべきです");
    }

    [Fact]
    public void 複数フィールドエラー_Name_Description_文字数超過エラーが発生する()
    {
        // Arrange
        var request = SafeValue() with 
        { 
            Name = new string('あ', 101),        // Name文字数超過
            Description = new string('い', 1001) // Description文字数超過
        };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name);
        result.ShouldHaveValidationErrorFor(x => x.Description);
        result.Errors.Should().HaveCountGreaterOrEqualTo(2, "複数のエラーが同時に発生すべきです");
    }

    [Fact]
    public void 複数フィールドエラー_Tags関連_複数の問題が同時に発生する()
    {
        // Arrange
        var problematicTags = new List<string> 
        { 
            "tag1", 
            EmptyString,  // 空文字エラー
            "tag3", 
            NullString,   // null要素エラー
            "tag5", 
            "tag6", 
            "tag7", 
            "tag8", 
            "tag9", 
            "tag10", 
            "tag11"       // 個数超過エラー（11個）
        };
        var request = SafeValue() with { Tags = problematicTags };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Tags);
        result.Errors.Should().NotBeEmpty("複数のTagsエラーが発生すべきです");
    }

    // TODO: 業務固有のバリデーションルールがある場合は以下のようなテストを追加してください

    [Fact]
    public void Status_Priority_組み合わせバリデーション_ドラフト状態では高優先度不可()
    {
        // 例：ドラフト状態では優先度は低い値のみ許可
        // Arrange
        var request = SafeValue() with 
        { 
            Status = EntityStatus.Draft, 
            Priority = 90 // ドラフトでは50以下のみ許可（仮の業務ルール）
        };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert - 業務ルールに応じて調整
        // result.ShouldHaveValidationErrorFor(x => x.Priority)
        //     .WithErrorMessage("Draft entities cannot have high priority");
        
        // 業務ルールが未実装の場合はパス
        Assert.True(true, "TODO: ドラフト状態での優先度制限が実装された場合は上記のコメントアウトを解除してください");
    }

    [Fact]
    public void RelatedEntityId_Status_組み合わせバリデーション_アクティブ状態では関連エンティティ必須()
    {
        // 例：特定のステータスでは関連エンティティは必須
        // Arrange
        var request = SafeValue() with 
        { 
            Status = EntityStatus.Active, 
            RelatedEntityId = null // Activeでは必須（仮の業務ルール）
        };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert - 業務ルールに応じて調整
        // result.ShouldHaveValidationErrorFor(x => x.RelatedEntityId)
        //     .WithErrorMessage("RelatedEntityId is required for Active entities");
        
        // 業務ルールが未実装の場合はパス
        Assert.True(true, "TODO: アクティブ状態での関連エンティティ必須ルールが実装された場合は上記のコメントアウトを解除してください");
    }

    [Fact]
    public void Tags_Name_組み合わせバリデーション_特定名称パターンでのタグ制限()
    {
        // 例：特定の名前パターンではタグ数制限が厳しい
        // Arrange
        var request = SafeValue() with 
        { 
            Name = "SYSTEM_ENTITY", // システムエンティティは特別扱い（仮の業務ルール）
            Tags = Enumerable.Range(1, 6).Select(i => $"tag{i}").ToList() // システムエンティティは5個まで
        };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert - 業務ルールに応じて調整
        // result.ShouldHaveValidationErrorFor(x => x.Tags)
        //     .WithErrorMessage("System entities cannot have more than 5 tags");
        
        // 業務ルールが未実装の場合はパス
        Assert.True(true, "TODO: システムエンティティでのタグ制限ルールが実装された場合は上記のコメントアウトを解除してください");
    }

    #endregion

    #region エラーメッセージ個別検証テスト

    [Fact]
    public void Name必須エラー_正しいメッセージが返される()
    {
        // Arrange
        var request = SafeValue() with { Name = NullString };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        var error = result.Errors.FirstOrDefault(e => e.PropertyName == nameof(AddEntityCommand.Name));
        error.Should().NotBeNull("Nameのエラーが発生すべきです");
        // TODO: 実際のエラーメッセージ仕様に合わせて調整してください
        // error!.ErrorMessage.Should().Contain("必須");
        // error!.ErrorMessage.Should().Contain("入力してください");
    }

    [Fact]
    public void Name文字数超過エラー_正しいメッセージが返される()
    {
        // Arrange
        var request = SafeValue() with { Name = new string('あ', 101) };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        var error = result.Errors.FirstOrDefault(e => e.PropertyName == nameof(AddEntityCommand.Name));
        error.Should().NotBeNull("Name文字数超過エラーが発生すべきです");
        // TODO: 実際のエラーメッセージ仕様に合わせて調整してください
        // error!.ErrorMessage.Should().Contain("100文字");
        // error!.ErrorMessage.Should().Contain("以下");
    }

    [Fact]
    public void Priority範囲外エラー_負の値_正しいメッセージが返される()
    {
        // Arrange
        var request = SafeValue() with { Priority = -1 };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        var error = result.Errors.FirstOrDefault(e => e.PropertyName == nameof(AddEntityCommand.Priority));
        error.Should().NotBeNull("Priority範囲外エラーが発生すべきです");
        // TODO: 実際のエラーメッセージ仕様に合わせて調整してください
        // error!.ErrorMessage.Should().Contain("0以上");
        // error!.ErrorMessage.Should().Contain("100以下");
    }

    [Fact]
    public void Priority範囲外エラー_上限超過_正しいメッセージが返される()
    {
        // Arrange
        var request = SafeValue() with { Priority = 101 };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        var error = result.Errors.FirstOrDefault(e => e.PropertyName == nameof(AddEntityCommand.Priority));
        error.Should().NotBeNull("Priority範囲外エラーが発生すべきです");
        // TODO: 実際のエラーメッセージ仕様に合わせて調整してください
        // error!.ErrorMessage.Should().Contain("0以上");
        // error!.ErrorMessage.Should().Contain("100以下");
    }

    [Fact]
    public void Tags個数超過エラー_正しいメッセージが返される()
    {
        // Arrange
        var tooManyTags = Enumerable.Range(1, 11).Select(i => $"tag{i}").ToList();
        var request = SafeValue() with { Tags = tooManyTags };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        var error = result.Errors.FirstOrDefault(e => e.PropertyName == nameof(AddEntityCommand.Tags));
        error.Should().NotBeNull("Tags個数超過エラーが発生すべきです");
        // TODO: 実際のエラーメッセージ仕様に合わせて調整してください
        // error!.ErrorMessage.Should().Contain("10個");
        // error!.ErrorMessage.Should().Contain("以下");
    }

    [Fact]
    public void Description文字数超過エラー_正しいメッセージが返される()
    {
        // Arrange
        var request = SafeValue() with { Description = new string('あ', 1001) };
        var validator = new AddEntityValidator();

        // Act
        var result = validator.TestValidate(request);

        // Assert
        var error = result.Errors.FirstOrDefault(e => e.PropertyName == nameof(AddEntityCommand.Description));
        error.Should().NotBeNull("Description文字数超過エラーが発生すべきです");
        // TODO: 実際のエラーメッセージ仕様に合わせて調整してください
        // error!.ErrorMessage.Should().Contain("1000文字");
        // error!.ErrorMessage.Should().Contain("以下");
    }

    #endregion

    #region パフォーマンステスト（大量データ）

    [Fact]
    public void 大量タグでのパフォーマンステスト()
    {
        // Arrange
        var manyValidTags = Enumerable.Range(1, 10).Select(i => $"tag{i:D3}").ToList(); // 10個の有効なタグ
        var request = SafeValue() with { Tags = manyValidTags };
        var validator = new AddEntityValidator();

        // Act & Assert - パフォーマンスが適切であることを確認
        var sw = System.Diagnostics.Stopwatch.StartNew();
        var result = validator.TestValidate(request);
        sw.Stop();

        result.Errors.Should().BeEmpty();
        sw.ElapsedMilliseconds.Should().BeLessThan(100, "バリデーションは高速であるべきです");
    }

    #endregion
}
