import type { issueProjectDiscussionCommentType } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'

export const useGetComment = (props: any) => {
  const { error: errorToast, success: successToast } = useAppToasts()

  const copyCommentLink = (
    threadId: string,
    comment: issueProjectDiscussionCommentType | undefined,
  ) => {
    const currentUrl = new URL(window.location.href)
    currentUrl.searchParams.set('threadId', threadId)
    currentUrl.searchParams.set('commentId', comment!.id)
    navigator.clipboard
      .writeText(currentUrl.href)
      .then(() => {
        const activeElement = document.activeElement as HTMLElement
        activeElement?.blur()
        successToast('リンクをコピーしました')
      })
      .catch((error) => {
        errorToast('リンクのコピーに失敗しました:', error)
      })
  }

  const purposeText = computed(() => {
    if (props.comment?.purpose === 'Internal') {
      return '社内協議'
    }
    return '社外協議'
  })

  const isExternal = computed(() => {
    return props.comment?.purpose === 'External'
  })

  return {
    copyCommentLink,
    purposeText,
    isExternal,
  }
}
