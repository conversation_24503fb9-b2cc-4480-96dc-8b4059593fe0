import {
  useFindUser as useGetUser<PERSON>ll<PERSON><PERSON>,
  type User,
} from '@ibp/common-case/src/apiclient/myCareer/user'
import {
  useFindAllBranchMaster,
  type BranchMaster,
} from '@ibp/common-case/src/apiclient/customerIdentifying/branchMaster'
import {
  useAllGetIndustryMaster,
  type IndustryMaster,
} from '@ibp/common-case/src/apiclient/customerIdentifying/industryMaster'
import {
  useGetAllTeam,
  type Team,
  type TeamMember,
} from '@ibp/common-case/src/apiclient/customerProposal/team'
import { isArray } from '@hox/base/src/utils/shared/is'

/**
 * 担当者の選択肢
 */
export async function getStaffOptions() {
  const { data, executeWithResult: findUser } = useGetUserAllApi(undefined)

  const result = await findUser()
  if (!result) return []

  const users = data.value.map((user: User) => ({
    value: user.userId,
    title: user.nameKanji,
  }))
  return users
}

/**
 * チームと担当者の選択肢
 */

export async function getStaffAndTeamOptions() {
  const { $auth } = useNuxtApp()
  const authUserInfo = $auth.getUser()
  const { data: teamData, executeWithResult: getAllTeam } = useGetAllTeam()
  const { data: userData, executeWithResult: findUser } =
    useGetUserAllApi(undefined)

  interface OptionItem {
    value: string
    type: string
    title: string
    teamMembers: string[]
    isMyTeam: number
  }

  // チームを取得
  const resultTeam = await getAllTeam()
  if (!resultTeam) return []

  const teamOptions = teamData.value.map((team: Team) => ({
    value: team.id,
    type: 'team',
    title: team.teamName,
    teamMembers: team.teamMembers.map((member: TeamMember) => member.staffId),
    isMyTeam: team.teamMembers.filter(
      (member: TeamMember) => member.staffId === authUserInfo?.userId,
    ).length,
  }))

  // 自身のチームのメンバー
  const myTeamMemberIds = Array.from(
    new Set(
      teamOptions
        .filter((team) => team.isMyTeam > 0)
        .map((team) => team.teamMembers)
        .flat(),
    ),
  )

  // ユーザーを取得
  const result = await findUser()
  if (!result) return []

  const userOptions = userData.value.map((user: User) => ({
    value: user.userId,
    type: 'user',
    title: user.nameKanji,
    teamMembers: [user.userId],
    isMyTeam: myTeamMemberIds.filter((memberId) => memberId === user.userId)
      .length,
  }))

  // ユーザーとチームの選択肢
  const usersAndTeams = teamOptions.concat(userOptions)

  // 自身のチームと自身のチームに所属するメンバーを先に表示する様並び替え
  usersAndTeams.sort((a, b) => {
    return b.isMyTeam - a.isMyTeam
  })

  // 自身を一番上に表示する様並び替え
  // const copiedUsersAndTeams: OptionItem[] = Object.assign({}, usersAndTeams)
  const copiedUsersAndTeams: OptionItem[] = isArray(usersAndTeams)
    ? [...usersAndTeams]
    : []

  for (const userOption of copiedUsersAndTeams) {
    if (userOption.value === authUserInfo?.userId) {
      usersAndTeams.filter((x) => x.value !== userOption.value)
      usersAndTeams.unshift(userOption)
    }
  }

  return usersAndTeams
}

/**
 * 店番の選択肢
 */
export async function getBranchOptions() {
  const { data, executeWithResult: getAllBranchMaster } =
    useFindAllBranchMaster()
  const result = await getAllBranchMaster()
  if (!result) return []

  const branches = data.value.map((branch: BranchMaster) => {
    return {
      value: branch.number,
      title: `${branch.number}:${branch.name}`,
    }
  })
  return branches
}

/**
 * 業種の選択肢
 */
export async function getIndustryOptions() {
  const { data, executeWithResult: getAllIndustryMaster } =
    useAllGetIndustryMaster()
  const result = await getAllIndustryMaster()
  if (!result) return []

  const industryMasters = data.value.map((industry: IndustryMaster) => {
    return {
      value: industry.code,
      title: industry.name,
    }
  })
  return industryMasters
}
