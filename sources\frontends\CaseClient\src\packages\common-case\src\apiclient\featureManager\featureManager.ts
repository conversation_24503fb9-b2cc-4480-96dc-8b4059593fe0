import { useNuxtApp } from 'nuxt/app'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

/**
 * データを検索する
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetFeatureFlag(featureFlag: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<boolean>(
    useFetch(
      () => $endpoints.default.get('/featuremanager/v1.0/featuremanager'),
      withDefaultFetchOptions({
        method: 'GET',
        query: { featureFlag },
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
