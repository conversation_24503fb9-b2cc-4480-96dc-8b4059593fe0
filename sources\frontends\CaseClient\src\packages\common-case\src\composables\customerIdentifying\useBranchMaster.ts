import { startOfToday } from 'date-fns'
import { useOneDayPersistState } from '@ibp/base/src/composables/shared/useOneDayPersistState'
import {
  useFindAllBranchMaster as useGetBranchMasterAllApi,
  type BranchMaster,
} from '@/packages/common-case/src/apiclient/customerIdentifying/branchMaster'

type BranchMasterOption = {
  value: string
  title: string
}

/**
 * 店番マスタ
 */
export const useBranchMaster = () => {
  // 店番マスタの一覧
  const branches = useOneDayPersistState<BranchMaster[]>('branches', () => [])

  const { data, executeWithResult } = useGetBranchMasterAllApi()

  // 店番マスタの一覧から店番マスタ選択用の一覧を取得する
  const branchMasterOptions = computed((): BranchMasterOption[] => {
    return branches.value.state?.map<BranchMasterOption>((x) => ({
      value: x.number,
      title: `${x.number}:${x.name}`,
    })) || []
  })

  // 店番マスタの一覧をAPIで取得する
  const fetchBranches = async (): Promise<void> => {
    // localStorageにセットした有効期限が今日以前だった場合、新たに店番マスタを取得
    if (branches.value.expiredAt <= startOfToday()) {
      await executeWithResult()
      branches.value.state = data.value || []
    }
  }

  return {
    fetchBranches,
    branchMasterOptions,
  }
}
