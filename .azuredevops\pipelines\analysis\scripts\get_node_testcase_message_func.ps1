# テストケース数の閾値(デフォルト)を設定(error, warning)
$testCaseThreshold = [int[]](3, 5)

# テスト対象クラスに含めるクラスのパスを設定
$includePathList = @(
    # src\componentsディレクトリの下にあるクラス
    "\src\components\*",
    # src\composablesディレクトリの下にあるクラス
    "\src\composables\*",
    # src\pagesディレクトリの下にあるクラス
    "\src\pages\*",
    # src\utilsディレクトリの下にあるクラス
    "\src\utils\*"
)

# テスト対象クラスに含めないクラスのパスを設定
$excludePathList = @(
    # テストクラス
    "\test\*",
    # src\components\sharedディレクトリの下にあるクラス
    "\src\components\shared\*",
    # src\composables\sharedディレクトリの下にあるクラス
    "\src\composables\shared\*",
    # src\pages\authディレクトリの下にあるクラス
    "\src\pages\auth\*",
    # src\pages\index.vue
    "\src\pages\index.vue",
    # src\utils\sharedディレクトリの下にあるクラス
    "\src\utils\shared\*"
)

# 対象件数を設定
$topN = 5

# アイコンを設定
$errorIcon = "❌"
$warningIcon = "⚠️"
$safeIcon = "✅"

<#
.SYNOPSIS
    テスト対象のファイルを選択します。
.PARAMETER targetPath
    チェックするファイルのパス。
.PARAMETER targetDirectory
    テスト対象のディレクトリのパス。
.RETURNS
    テスト対象のファイルパスを返します。対象外の場合は $null を返します。
#>

function Select-Test-Target-File {
    param(
        [string]$targetPath,
        [string]$targetDirectory
    )

    # $targetDirectory配下以外の場合はスキップする
    if ($targetPath -notlike "$targetDirectory*") {
        return $null
    }

    # $excludePathListに含まれるパスはスキップする
    foreach ($excludePath in $excludePathList) {
        if ($targetPath -like "$targetDirectory$excludePath") {
            return $null
        }
    }

    # $includePathListに含まれるパスはスキップしない
    foreach ($includePath in $includePathList) {
        if ($targetPath -like "$targetDirectory$includePath") {
            return $targetPath
        }
    }

    return $null
}

<#
.SYNOPSIS
    テストケースの閾値を取得します。
.PARAMETER testCase
    テストケースの情報を含むオブジェクト。
.RETURNS
    エラーおよび警告の閾値を含む配列を返します。
#>

function Get-TestCase-Threashold {
    param(
        [PSCustomObject]$testCase
    )

    # デフォルトの閾値を設定
    $errorCount = $testCaseThreshold[0]
    $warningCount = $testCaseThreshold[1]

    # ToDo: $testCaseのプロパティ値から$errorCountと$warningCountを設定する
    # $testCaseのプロパティ：Name, Count, TargetClassName, TargetClassUri, TestClassName, TestClassUri

    return [int[]]($errorCount, $warningCount)
}

<#
.SYNOPSIS
    指定された値が閾値を下回っているかどうかを判定し、エラーまたは警告のアイコンを返します。
.PARAMETER value
    判定する値。
.PARAMETER thresholds
    エラーおよび警告の閾値を含む配列。
.PARAMETER errorOrWarningStatus
    現在のエラーまたは警告のステータス。
.RETURNS
    アイコンと更新されたエラーまたは警告のステータスを返します。
#>

function Get-ErrorOrWarningIcon-Less-Than {
    param(
        [int]$value,
        [int[]]$thresholds,
        [int]$errorOrWarningStatus
    )

    if ($value -lt $thresholds[0]) {
        # エラーの場合は、エラーアイコンと、引数で渡された$errorOrWarningStatusの値または2の大きい方を返す
        return $errorIcon, [math]::Max($errorOrWarningStatus, 2)
    }
    elseif ($value -lt $thresholds[1]) {
        # 警告の場合は、警告アイコンと、引数で渡された$errorOrWarningStatusの値または1の大きい方を返す
        return $warningIcon, [math]::Max($errorOrWarningStatus, 1)
    }
    else {
        # エラーでも警告でもない場合は、引数で渡された$errorOrWarningStatusの値を返す
        return "", $errorOrWarningStatus
    }
}

<#
.SYNOPSIS
    変更されたファイルに関連するテストケース数を解析し、結果のメッセージを生成します。
.PARAMETER changeStats
    変更されたファイルの統計情報を含むオブジェクト。
.PARAMETER analysisResultDirectory
    静的分析結果が保存されているディレクトリのパス。
.PARAMETER testProjectRootDirectory
    テストプロジェクトのルートディレクトリのパス。
.PARAMETER sourceBranch
    ソースブランチの名前。
.PARAMETER repositoryUri
    リポジトリのURI。
.RETURNS
    生成されたテストケース数メッセージを返します。
#>

function Get-Node-Testcase-Message {
    param(
        [PSCustomObject]$changeStats,
        [string]$analysisResultDirectory,
        [string]$testProjectRootDirectory,
        [string]$sourceBranch,
        [string]$repositoryUri
    )

    # 変更されたファイルの一覧を取得
    $modifiedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "A" -or $_.ShortStatus -eq "M" } | Select-Object -ExpandProperty FileName
    $renamedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "R" } | Select-Object -ExpandProperty NewFileName
    $targetFiles = $modifiedFiles + $renamedFiles

    $testReportPath = Join-Path $analysisResultDirectory "test"

    $testCases = @()

    $sourceBranch = $sourceBranch -replace "refs/heads/", ""
    $targetDirectory = Convert-Path $testProjectRootDirectory

    $targetDirectoryRelative = Resolve-Path $targetDirectory -Relative
    $targetDirectoryRelative = $targetDirectoryRelative -replace "^.\\", "" -replace "\\", "/"

    $modifiedTestClassFiles = @()

    if ($targetFiles) {
        $modifiedTestClassFiles = $targetFiles.clone()
    }

    # テストクラスの拡張子を設定
    $testClassExtensions = "ts", "tsx", "js", "jsx", "mjs", "mts"

    $testReportFile = Join-Path $testReportPath "test-results.xml"

    # TestReport(trx)を読み込む
    $testReportXml = [xml](Get-Content $testReportFile)

    foreach ($targetFile in $targetFiles) {
        $targetPath = Convert-Path $targetFile

        # テスト対象クラスのファイル以外はスキップ
        $targetPath = Select-Test-Target-File -targetPath $targetPath -targetDirectory $targetDirectory

        if (-not $targetPath) {
            continue
        }

        # ファイル名からテスト対象クラスの拡張子を取得
        $targetClassExtension = [System.IO.Path]::GetExtension($targetPath)

        # 拡張子がvue, ts, tsx, js, jsx, mjs, mtsのファイル以外はスキップ
        if ($targetClassExtension -notmatch "\.(vue|ts|tsx|js|jsx|mjs|mts)$") {
            continue
        }

        # ファイル名からテスト対象クラス名を取得
        $targetClassName = [System.IO.Path]::GetFileName($targetPath)

        # テスト対象クラスのURLを生成
        $targetClassUri = "?_a=files&path=/$([URI]::EscapeUriString($targetFile))"

        $testCase = @()
        foreach ($testClassExtension in $testClassExtensions) {
            # ファイル名からテストクラス名を取得
            $testClassName = [System.IO.Path]::GetFileNameWithoutExtension($targetPath) + ".spec.$testClassExtension"

            # $targetFileからテストクラスのファイル名に変換する
            $testClassFile = $targetFile -replace "/src/", "/test/" -replace "$targetClassExtension$", ".spec.$testClassExtension" 

            # ファイルが存在しない場合はスキップ
            if (-not (Test-Path $testClassFile)) {
                continue
            }

            # 変更されたテストクラスのファイルを取得
            $modifiedTestClassFile = $modifiedTestClassFiles | Where-Object { $_ -ieq $testClassFile }

            # テストクラスのURLを生成
            if ($modifiedTestClassFile) {
                $testClassUri = "?_a=files&path=/$([URI]::EscapeUriString($modifiedTestClassFile))"
            }
            else {
                $testClassUri = $repositoryUri + "?path=$([URI]::EscapeUriString($testClassFile + "&version=GB$sourceBranch"))"
            }

            # $targetFileからテストクラス名に変換する
            $testClassFullName = $targetFile -replace "^$targetDirectoryRelative", "" -replace "/src/", "../test/" -replace "$targetClassExtension$", ".spec.$testClassExtension" 

            # TestReport(trx)のclassNameがテストクラス名と一致するテストメソッドをグルーピングしてテストケース数を取得
            $testCase = $testReportXml.testsuites.testsuite.testcase |
            Where-Object { $_.classname -ieq $testClassFullName } | 
            Group-Object { $_.classname } | 
            Select-Object Name, Count, 
            @{Name = "TargetClassName"; Expression = { $targetClassName } }, 
            @{Name = "TargetClassUri"; Expression = { $targetClassUri } }, 
            @{Name = "TestClassName"; Expression = { $testClassName } }, 
            @{Name = "TestClassUri"; Expression = { $testClassUri } }
        }

        if ($testCase) {
            $testCases += $testCase
        }
        else {
            $testCases += [PSCustomObject]@{
                Name            = $testClassFullName
                Count           = 0
                TargetClassName = $targetClassName
                TargetClassUri  = $targetClassUri
                TestClassName   = [System.IO.Path]::GetFileNameWithoutExtension($targetPath) + ".spec.ts"
                TestClassUri    = ""
            }
        }
    }

    $testMethodCountMessage = @()
    $errorOrWarningStatusTestMethodCount = [int]0

    # $testCases をテストメソッド数でソートしてtopN件を取得する
    $topNTestCases = $testCases | Sort-Object -Property Count | Select-Object -First $topN

    foreach ($testCase in $topNTestCases) {
        # テストケース数の閾値を取得
        $testCaseCountThreshold = Get-TestCase-Threashold $testCase
        $testMethodCountIcon, $errorOrWarningStatusTestMethodCount = Get-ErrorOrWarningIcon-Less-Than $testCase.Count $testCaseCountThreshold $errorOrWarningStatusTestMethodCount

        # $testMethodCountIconが空の場合はスキップ
        if ([string]::IsNullOrEmpty($testMethodCountIcon)) {
            continue
        }

        if ($testCase.Count -eq 0) {
            $testMethodCountMessage += "1. [$($testCase.TargetClassName)]($($testCase.TargetClassUri)) -> $($testCase.TestClassName)($($testCase.Count)$testMethodCountIcon)`r`n"
        }
        else {
            $testMethodCountMessage += "1. [$($testCase.TargetClassName)]($($testCase.TargetClassUri)) -> [$($testCase.TestClassName)]($($testCase.TestClassUri))($($testCase.Count)$testMethodCountIcon)`r`n"
        }
    }

    $resultTestMethodMessage = @()

    if ([string]::IsNullOrEmpty($testMethodCountMessage)) {
        $resultTestMethodMessage = @"
Waring, NG なし

"@
    }
    else {
        $resultTestMethodMessage = @"
<details>
<summary>詳細</summary>

$testMethodCountMessage
</details>

"@
    }

    return @"
### テスト対象クラス -> テストクラス(テストケース数) $(($safeIcon, $warningIcon, $errorIcon)[$errorOrWarningStatusTestMethodCount])
$resultTestMethodMessage
"@
}
