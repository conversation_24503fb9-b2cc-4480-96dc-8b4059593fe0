<template>
  <div v-if="threadDataRef.loading">
    <v-skeleton-loader v-for="n of 3" :key="n" type="article" />
  </div>

  <div v-else-if="!threadDataRef.loading && commentDataRef">
    <v-hover v-slot="{ isHovering, props }">
      <v-card
        :class="{ 'highlighted-card': commentDataRef.isHighlighted }"
        :elevation="isHovering ? 16 : 2"
        v-bind="props"
        variant="outlined"
      >
        <v-card v-if="isHovering" class="icon-container">
          <v-row>
            <!-- 削除アイコン -->
            <v-col
              v-if="
                showHover(isHovering, definedProps.comment, definedProps.type)
              "
              cols="auto"
            >
              <v-icon
                title="削除"
                icon="mdi-delete"
                class="icon"
                color="red"
                @click="removeComment"
              />
            </v-col>
            <!-- 編集アイコン -->
            <v-col
              v-if="
                showHover(isHovering, definedProps.comment, definedProps.type)
              "
              cols="auto"
              @click="openCommentEditor"
            >
              <v-icon title="編集" icon="mdi-pencil" class="icon" />
            </v-col>
            <!-- リンクコピーアイコン -->
            <v-col>
              <v-icon
                title="スレッドのリンクをコピー"
                icon="mdi-link"
                class="icon"
                @click="
                  copyCommentLink(definedProps.threadId, definedProps.comment)
                "
              />
            </v-col>
            <!-- リアクションアイコン -->
            <v-col
              v-for="reactionType in reactionTypes"
              :key="reactionType"
              cols="auto"
            >
              <v-icon
                v-if="reactionType !== 4"
                :title="getReactionDisplayName(reactionType)"
                :color="getReactionIcon(reactionType)?.color"
                :icon="getReactionIcon(reactionType)?.icon"
                :class="{
                  iconborder:
                    commentDataRef.selectedReactionType === reactionType,
                }"
                class="pr-1 icon"
                @click="saveReaction(reactionType, definedProps.comment)"
                @add-comment-reaction="addReaction"
                @update-comment-reaction="updateReaction"
                @delete-comment-reaction="deleteReaction"
              />
              <font-awesome-icon
                v-if="reactionType === 4"
                :title="getReactionDisplayName(reactionType)"
                :color="getReactionIcon(reactionType)?.color"
                :icon="getReactionIcon(reactionType)?.icon"
                class="font-awesome-icon"
                :class="{
                  iconborder:
                    commentDataRef.selectedReactionType === reactionType,
                }"
                @click="saveReaction(reactionType, definedProps.comment)"
              />
            </v-col>
          </v-row>
        </v-card>

        <div
          ref="commentBody"
          class="py-1 px-3"
          :class="{ 'hidden-text-container': !shownDetail }"
        >
          <v-row dense>
            <v-col cols="auto" class="d-flex align-center">
              <!-- 登録者・登録日時 -->
              <div ref="creatorDiv">
                <span class="text-subtitle-1 pr-1">
                  {{ definedProps.comment?.registrant }}
                </span>
                <span class="pl-1 text-subtitle-2 time-color">
                  {{
                    format(
                      definedProps.comment!.registeredDateTime,
                      'yyyy/MM/dd HH:mm',
                    )
                  }}
                </span>
              </div>

              <!-- リアクション -->
              <ReactionView
                :reactions="definedProps.comment?.reactions"
                :type="definedProps.type"
                @show-reaction-members="showReactionMembers"
                @update-reaction="updateReaction"
                @add-reaction="addReaction"
                @delete-reaction="deleteReaction"
              />
            </v-col>
          </v-row>

          <!-- コメント内容 -->
          <v-row dense>
            <v-col class="text-subtitle-1">
              <div>
                <p class="mt-1 mb-0">【目的】{{ purposeText }}</p>
              </div>
              <div v-if="isExternal">
                <p class="mt-1 mb-0">
                  【相手】{{ definedProps.comment?.person }}
                </p>
                <p
                  v-if="definedProps.comment?.isPersonOfPower"
                  class="mt-1 mb-0"
                >
                  【実権者】☑
                </p>
              </div>
              <div class="mb-1" v-html="htmlDescription" />
            </v-col>
          </v-row>
        </div>

        <div v-if="commentDataReactive.shownDetailButton">
          <v-btn
            variant="text"
            size="x-small"
            color="primary"
            @click="changeShownDetailState(!shownDetail)"
          >
            {{ shownDetail ? '簡易表示' : '詳細表示' }}
          </v-btn>
        </div>

        <!-- 添付ファイル -->
        <div v-if="definedProps.comment?.files">
          <div
            v-for="file in definedProps.comment.files"
            :key="file.fileName"
            style="display: inline"
          >
            <v-chip
              color="green"
              label
              class="me-2 mb-3 ms-3 file-border"
              @click="download(file.fileName)"
            >
              <v-icon color="#6495ef" class="mr-2"> mdi-download </v-icon>
              <span class="ellipsis"> {{ file.fileName }} </span>
            </v-chip>
          </div>
        </div>

        <!-- リアクションしたメンバーの表示 -->
        <div ref="reactionMemberDiv" class="reactionMember">
          <ReactionMember
            v-if="commentDataReactive.reactionTypeOnHover > 0"
            :reactions="definedProps.comment?.reactions!"
            :reaction-type="commentDataReactive.reactionTypeOnHover"
          />
        </div>
      </v-card>
    </v-hover>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns'
import type { issueProjectDiscussionCommentType } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useCommentShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/useCommentShareData'
import { useGetReactions } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/reaction/useGetCommentReactions'
import { useGetComment } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/useGetComment'
import type {
  IssueProjectDiscussionCommentReactionForDelete,
  IssueProjectDiscussionCommentReactionForSave,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import { useThreadShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/thread/useThreadShareData'
import { isValidJson } from '@ibp/common-case/src/utils/json'

const { threadDataRef } = useThreadShareData()
const { $auth } = useNuxtApp()
const userId = $auth.getUser()?.userId

const definedProps = withDefaults(
  defineProps<{
    comment: issueProjectDiscussionCommentType | undefined
    type: string
    threadId: string
  }>(),
  {
    comment: undefined,
    type: '',
    threadId: '',
  },
)

const emits = defineEmits([
  'remove-comment',
  'update-comment',
  'delete-comment-reaction',
  'update-comment-reaction',
  'add-comment-reaction',
  'download',
])

// コメント、リアクションComposable
const {
  commentDataRef,
  dataRef: commentDataReactive,
} = useCommentShareData()
const { showHover, reactionTypes, getReactionDisplayName, getReactionIcon } =
  useGetReactions(definedProps.type)
const { copyCommentLink, purposeText, isExternal } = useGetComment(definedProps)

// refのcommentBodyを定義します。
const commentBody = ref<HTMLDivElement | null>()

onMounted(() => {
  // 自分が押したリアクションの判別
  const reaction = definedProps.comment?.reactions?.find(
    (x) => x.staffId === userId,
  )
  commentDataRef.value.selectedReactionType = reaction?.reactionType

  nextTick(async () => {
    if (!commentBody.value) return

    // 9emをpxに変換
    const fontSize = getComputedStyle(document.documentElement).fontSize
    const maxHeight = 9 * parseFloat(fontSize) // max-height: 9em; (.hidden-text-containerより)

    // maxHeightを超える場合は詳細ボタンを表示
    if (commentBody.value.clientHeight >= maxHeight) {
      commentDataReactive.value.shownDetailButton = true
    }
  })
})

// =====================================================================================================================
// コメント関連
// =====================================================================================================================

/**
 * コメント削除ボタン押下時の処理
 */
const removeComment = () => {
  emits('remove-comment', definedProps.comment)
}

/**
 * コメント編集ボタン押下時の処理
 */
const openCommentEditor = () => {
  emits('update-comment', definedProps.comment)
}

const htmlDescription = computed(() => {
  if (!definedProps.comment?.description) return null
  if (!isValidJson(definedProps.comment?.description)) return null
  // JsonデータをHTMLに変換
  return quillDeltaToHtml(definedProps.comment?.description)
})

const saveReaction = (
  newReactionType: number,
  comment: issueProjectDiscussionCommentType | undefined,
) => {
  const reactions = comment?.reactions || []

  // 同じリアクションが2回連続押されたらborder-bottomを解除
  commentDataRef.value.selectedReactionType =
    commentDataRef.value.selectedReactionType === newReactionType
      ? undefined
      : newReactionType

  // リアクションユーザーのIDを取得
  const reactionUsers = reactions.map((x) => x.staffId)
  const selectReactionUsers = reactions
    .filter((x) => Number(x.reactionType) === newReactionType)
    .map((x) => x.staffId)

  // ユーザーがリアクションを持っているか確認
  if (reactionUsers.includes(userId)) {
    const targetReaction = reactions.find((x) => x.staffId === userId)

    if (selectReactionUsers.includes(userId)) {
      // 登録済みのリアクションを選択すると削除
      if (targetReaction) {
        // IssueProjectDiscussionThread.vueのIssueProjectDiscussionCommentに対してemit
        deleteReaction({
          id: targetReaction.id,
          version: targetReaction.version,
        })
      }
    } else if (targetReaction) {
      // 別のリアクションを選択すると更新
      // IssueProjectDiscussionThread.vueのIssueProjectDiscussionCommentに対してemit
      updateReaction(targetReaction, newReactionType)
    }
  } else {
    // リアクションを追加
    // IssueProjectDiscussionThread.vueのIssueProjectDiscussionCommentに対してemit
    addReaction(newReactionType)
  }
}

// =====================================================================================================================
// コメントのリアクション関連
// =====================================================================================================================
/**
 * コメントのリアクションを追加
 */
const addReaction = (newReactionType: number) => {
  commentDataRef.value.selectedReactionType = newReactionType
  emits('add-comment-reaction', newReactionType, definedProps.comment?.id)
}

/**
 * コメントのリアクションを更新
 */
const updateReaction = (
  sendData: IssueProjectDiscussionCommentReactionForSave,
  newReactionType: number,
) => {
  commentDataRef.value.selectedReactionType = newReactionType
  emits('update-comment-reaction', sendData, newReactionType)
}

/**
 * コメントのリアクションを削除
 */
const deleteReaction = (
  sendData: IssueProjectDiscussionCommentReactionForDelete,
) => {
  commentDataRef.value.selectedReactionType = undefined
  emits('delete-comment-reaction', sendData, definedProps.comment?.id)
}

/**
 * リアクションしたメンバーの表示
 */
// div要素のcreatorの参照を保持
type createrType = {
  clientWidth: number
}
const creatorDiv = ref<createrType>()

// div要素のreactionMemberの参照を保持
type reactionMemberType = {
  style: {
    top: string
    left: string
  }
}
const reactionMemberDiv = ref<reactionMemberType>()

const showReactionMembers = async (reactionType: number, index: number) => {
  while (!creatorDiv.value) {
    await new Promise((resolve) => setTimeout(resolve, 100)) // 100ミリ秒待機
  }
  const creatorWidth = creatorDiv.value.clientWidth

  while (!reactionMemberDiv.value) {
    await new Promise((resolve) => setTimeout(resolve, 100)) // 100ミリ秒待機
  }

  const reactionMember = reactionMemberDiv.value

  // 表示位置
  reactionMember.style.top = commentDataRef.value.reactionMemberOffsetY + 'px'
  reactionMember.style.left = creatorWidth + index * 36 + 'px' // 36 ⇒ アイコンの横幅(px)

  commentDataReactive.value.reactionTypeOnHover = reactionType
}

/**
 * ファイルダウンロード
 */
const download = (fileName: string) => {
  emits('download', definedProps.comment?.id, fileName)
}

/**
 * 詳細表示の状態を変更する
 */

const shownDetail = ref<boolean>(false)
const changeShownDetailState = (isShownDetail: boolean) => {
  shownDetail.value = isShownDetail
}

defineExpose({ changeShownDetailState })
</script>

<style scoped>
.icon-container {
  position: absolute;
  background-color: #f5f5f5;
  max-width: 300px;
  min-width: 50px;
  padding-top: 5px;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 3px;
  text-align: center;
  right: 0px;
  display: inline-block;
}

.font-awesome-icon {
  height: 22px;
}

.font-awesome-icon:hover {
  opacity: 0.5;
}

.highlighted-card {
  background: #ec6d1f12;
  border: 1.5px solid #ec6c1f;
  animation: fadeOut 5s; /* アニメーションの適用 */
}

.hidden-text-container {
  max-height: 9em;
  overflow: hidden;
}

.reactionMember {
  z-index: 100;
  position: absolute;
}

.iconborder {
  border-bottom: 2px solid;
}

.icon:hover {
  opacity: 0.5;
}

.nodelete {
  opacity: 0.5;
}

.time-color {
  color: #616161;
}
</style>
