import * as qs from 'qs'

interface QueryParams {
  id?: string | null
  businessUnderstandingId?: string | null
  customerIdentificationId?: string | null
  from?: string | null
  tab?: string | null
  status?: string | null
  isNullStatus?: string | null
  issueItems?: string | null
  branchNumbers?: string | null
  customerName?: string | null
  customerStaffIds?: string | null
  industryDetails?: string | null
  overview?: string | null
  statuses?: string | null
  issueProjectStaffIds?: string | null
  issueProjectTeams?: string | null
  fromDateUTC?: string | null
  toDateUTC?: string | null
  registrantIds?: string | null
  discussionTypeIds?: string | null
  staffIds?: string | null
  issueProjectTaskStatus?: string | null
  strExpiredAtFrom?: string | null
  strExpiredAtTo?: string | null
  userIds?: string | null
  pageSizeInput?: string | null
  cifNumber?: string | null
  caseCategories?: string | null
  caseStatus?: string | null
  caseStatuses?: string | null
  preConsultationStandardTarget?: string | null
  isFavorite?: string | null
  industryCodes?: string | null
  loanRatings?: string | null
  transactionPolicies?: string | null
  subjectTypes?: string | null
  useOfFundsTypes?: string | null
  fromAmount?: string | null
  toAmount?: string | null
  fromInterestRate?: string | null
  toInterestRate?: string | null
  q?: Record<string, any> | null // qはオブジェクト型として定義
}

const parseQuery = (query: any): QueryParams => {
  const parsed = qs.parse(query) as QueryParams

  const {
    id = null,
    businessUnderstandingId = null,
    customerIdentificationId = null,
    from = null,
    tab = null,
    status = null,
    isNullStatus = null,
    issueItems = null,
    branchNumbers = null,
    customerName = null,
    customerStaffIds = null,
    industryDetails = null,
    overview = null,
    statuses = null,
    issueProjectStaffIds = null,
    issueProjectTeams = null,
    fromDateUTC = null,
    toDateUTC = null,
    registrantIds = null,
    discussionTypeIds = null,
    staffIds = null,
    issueProjectTaskStatus = null,
    strExpiredAtFrom = null,
    strExpiredAtTo = null,
    userIds = null,
    pageSizeInput = null,
    cifNumber = null,
    caseCategories = null,
    caseStatus = null,
    caseStatuses = null,
    preConsultationStandardTarget = null,
    isFavorite = null,
    industryCodes = null,
    loanRatings = null,
    transactionPolicies = null,
    subjectTypes = null,
    useOfFundsTypes = null,
    fromAmount = null,
    toAmount = null,
    fromInterestRate = null,
    toInterestRate = null,
    q = null,
  } = parsed

  return {
    id,
    businessUnderstandingId:
      businessUnderstandingId || q?.businessUnderstandingId || null,
    customerIdentificationId:
      customerIdentificationId || q?.customerIdentificationId || null,
    from: from || q?.from || null,
    tab: tab || q?.tab || null,
    status: status || q?.status || null,
    isNullStatus: isNullStatus || q?.isNullStatus || null,
    issueItems: issueItems || q?.issueItems || null,
    branchNumbers: branchNumbers || q?.branchNumbers || null,
    customerName: customerName || q?.customerName || null,
    customerStaffIds: customerStaffIds || q?.customerStaffIds || null,
    industryDetails: industryDetails || q?.industryDetails || null,
    overview: overview || q?.overview || null,
    statuses: statuses || q?.statuses || null,
    issueProjectStaffIds:
      issueProjectStaffIds || q?.issueProjectStaffIds || null,
    issueProjectTeams: issueProjectTeams || q?.issueProjectTeams || null,
    pageSizeInput: pageSizeInput || q?.pageSizeInput || null,
    fromDateUTC: fromDateUTC || q?.fromDateUTC || null,
    toDateUTC: toDateUTC || q?.toDateUTC || null,
    registrantIds: registrantIds || q?.registrantIds || null,
    discussionTypeIds: discussionTypeIds || q?.discussionTypeIds || null,
    staffIds: staffIds || q?.staffIds || null,
    issueProjectTaskStatus:
      issueProjectTaskStatus || q?.issueProjectTaskStatus || null,
    strExpiredAtFrom: strExpiredAtFrom || q?.strExpiredAtFrom || null,
    strExpiredAtTo: strExpiredAtTo || q?.strExpiredAtTo || null,
    userIds: userIds || q?.userIds || null,
    cifNumber: cifNumber || q?.cifNumber || null,
    caseCategories: caseCategories || q?.caseCategories || null,
    caseStatus: caseStatus || q?.caseStatus || null,
    caseStatuses: caseStatuses || q?.caseStatuses || null,
    preConsultationStandardTarget:
      preConsultationStandardTarget || q?.preConsultationStandardTarget || null,
    isFavorite: isFavorite || q?.isFavorite || null,
    industryCodes: industryCodes || q?.industryCodes || null,
    loanRatings: loanRatings || q?.loanRatings || null,
    transactionPolicies: transactionPolicies || q?.transactionPolicies || null,
    subjectTypes: subjectTypes || q?.subjectTypes || null,
    useOfFundsTypes: useOfFundsTypes || q?.useOfFundsTypes || null,
    fromAmount: fromAmount || q?.fromAmount || null,
    toAmount: toAmount || q?.toAmount || null,
    fromInterestRate: fromInterestRate || q?.fromInterestRate || null,
    toInterestRate: toInterestRate || q?.toInterestRate || null,
  }
}

export { parseQuery }
