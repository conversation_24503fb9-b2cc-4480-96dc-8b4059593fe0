/* eslint-disable @stylistic/spaced-comment */
import withNuxt from './.nuxt/eslint.config.mjs'
import stylistic from '@stylistic/eslint-plugin'

export default withNuxt(
  {
    files: ['**/*.vue', '**/*.ts'],
    rules: {
      'no-console': 'warn',
      '@typescript-eslint/no-explicit-any': 'off',
      'vue/multi-word-component-names': 'off',
    },
  },
  {
    files: ['**/*.vue'],
    rules: {
      'vue/html-indent': [
        'error',
        2,
        {
          attribute: 1,
          baseIndent: 1,
          closeBracket: 0,
          alignAttributesVertically: true,
        },
      ],
      'vue/no-multiple-template-root': 'off',
      'vue/multi-word-component-names': 'off',
      'vue/require-v-for-key': 'error',
      'vue/no-use-v-if-with-v-for': 'error',
      'vue/html-quotes': ['error', 'double', { avoidEscape: true }],
      'vue/mustache-interpolation-spacing': ['error', 'always'],
      // vuetify の datatable でカラムのスロットが v-slot:item.colname という形式になるので無効化
      'vue/valid-v-slot': 'off',
    },
  },
  stylistic.configs.customize({
    indent: 2,
    quotes: 'single',
    semi: false,
    braceStyle: '1tbs',
  }),
  {
    // override stylistic config
    files: ['**/*.vue', '**/*.ts'],
    rules: {
      // 以前の規約との互換性のために設定
      '@stylistic/operator-linebreak': 'off',
      '@stylistic/arrow-parens': 'off',
    },
  },
)
