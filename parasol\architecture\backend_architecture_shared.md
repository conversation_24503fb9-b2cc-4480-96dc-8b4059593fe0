### Shared

## 概要

Sharedライブラリは、app-architecture-template-backend-sharedの基盤となる共通ライブラリです。バックエンドアプリケーションで共通利用される汎用的な機能、ユーティリティ、パターンを提供し、他のライブラリの基盤として機能します。

## 主要機能

### アプリケーション基盤
- **環境管理**: 実行環境の判定、設定情報の取得
- **例外処理**: 標準的な例外クラス群、エラー管理の統一
- **ユーザー管理**: 現在のユーザー情報、認証・認可の基盤

### データアクセス支援
- **リポジトリパターン**: 汎用リポジトリ操作の拡張
- **仕様パターン**: 柔軟なクエリ構築、動的クエリ生成
- **ページング**: APIレスポンスの標準化

### メッセージング基盤
- **イベント/コマンド**: メッセージングの共通実装
- **ルーティング**: イベント名・エンドポイント指定
- **トレーシング**: メッセージ文脈の管理

### バリデーション・制約
- **ドメイン制約**: ビジネスルールの実装
- **入力検証**: 各種値検証ロジック
- **柔軟な検証**: カスタムバリデーションの実装

### パイプライン処理
- **認可・認証**: ユーザー認可の抽象化
- **例外解決**: 例外処理の統一
- **トレーシング**: OpenTelemetry連携

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| ApplicationEnvironment | アプリ環境情報 | 実行環境や設定情報の取得 | 環境依存処理の共通化 |
| ApplicationEnvironmentExtension | 拡張メソッド | 環境情報の拡張操作 | 環境判定の簡易化 |
| ResultCodeException | 例外 | 結果コード付き例外 | エラー管理の標準化 |
| NoopDataImport / NoopDataRemove | データ操作 | 何もしないデータインポート/削除 | テストや初期化用 |
| RepositoryExtensions | リポジトリ拡張 | 汎用リポジトリ操作の拡張 | DB操作の効率化 |
| PaginatedResultExtension | ページング | ページング結果の拡張 | APIレスポンス標準化 |
| ProblemType / ProblemExtPropertyKey | エラー型 | 問題種別・拡張プロパティ定義 | エラー応答の標準化 |
| ClaimsPrincipalExtensions | 認証拡張 | ClaimsPrincipalの拡張 | ユーザー情報取得 |
| CurrentUser | ユーザー情報 | 現在のユーザー情報管理 | 認証・認可処理 |
| QueryStringService | クエリ操作 | クエリストリング操作 | リクエスト解析 |
| KatakanaUtil | 文字列変換 | カタカナ変換ユーティリティ | 入力補正 |
| IEnumerableExtensions | コレクション拡張 | IEnumerableの拡張 | データ操作効率化 |
| ExpressionUtil | 式ツリー操作 | Expressionの操作支援 | 動的クエリ生成 |
| PredicateBuilder | 条件式生成 | 複雑な条件式の構築 | LINQクエリ構築 |
| JsonConverter | JSON変換 | 辞書型等のJSON変換 | シリアライズ/デシリアライズ |
| TimeProviderExtension | 日時拡張 | 日時操作の拡張 | 日付計算・テスト |
| DefaultMessagePublisher / DefaultMessageSender | メッセージ送信 | イベント/コマンド送信の共通実装 | メッセージング基盤 |
| MessageContext / MessageContextAccessor | メッセージ文脈 | メッセージの文脈情報管理 | トレーシング・監査 |
| EventNameAttribute / MessageEndpointAttribute | 属性 | イベント名・エンドポイント指定 | ルーティング制御 |
| MessagingLogger | ログ | メッセージング用ロガー | 監査・デバッグ |
| ServiceCollectionExtensions | DI拡張 | DI登録の拡張 | サービス登録簡易化 |
| ChangeConflictException / HttpStatusException / MultiStatusException / DataNotFoundException / UnauthorizedException / ValidationException | 例外 | 標準的な例外クラス群 | エラー処理の統一 |
| ValidationExceptionItem | 例外詳細 | バリデーション例外の詳細 | 入力エラー通知 |
| DomainConstraint / DomainConstraintViolation / DomainConstraintExtensions | ドメイン制約 | ドメイン制約の定義・検証 | ビジネスルール実装 |
| FindByIdSpecification / FindByIdsSpecification | 仕様パターン | IDによる検索仕様 | リポジトリクエリ |
| RepositoryFunctions / RepositoryFunctionsExtensions | リポジトリ関数 | 汎用リポジトリ関数 | DB操作共通化 |
| LessThanValidator / GreaterThanValidator / MaxLengthValidator / MinLengthValidator / NotEmptyValidator / NotNullValidator / PatternValidator / BetweenValidator / StringBetweenValidator / StringGreaterThanValidator / StringLessThanValidator / CustomValidator | バリデーション | 各種値検証ロジック | 入力・ドメイン検証 |
| CustomValidatorResult | バリデーション結果 | カスタムバリデーションの結果 | 柔軟な検証結果管理 |
| DomainException / DomainViolationException | ドメイン例外 | ドメイン層の例外 | ビジネスルール違反通知 |
| SpecificationBuilder / SpecificationBuilderExtensions / BaseSpecification / DefaultSpecificationEvaluator / AbstractSpecificationEvaluator / ISpecification / ISpecificationEvaluator | 仕様パターン | 仕様パターンの構築・評価 | 柔軟なクエリ構築 |
| IncludePart / OrderByPart / SkipPart / TakePart / WherePart / ThenIncludePartItem / PlainIncludePartItem / AsSplitQueryPart / AsNoTrackingPart | クエリ部品 | クエリ構築用部品 | 複雑なDBクエリ生成 |
| OrderByEvaluator / SkipEvaluator / WhereEvaluator / TakeEvaluator | クエリ評価 | クエリ部品の評価 | DBクエリ最適化 |
| RoleAuthorizationAttribute / RoleAuthorizationBehavior / ExceptionResolveBehavior / OpenTelemetryBehavior / ToStringInOutValueCollector | パイプライン | 認可・例外・トレーシング等のパイプライン | API/サービスの共通処理 |
| CurrentUserAuthorizer | 認可 | ユーザー認可の抽象クラス | アクセス制御 |
| StripResultFilter | フィルタ | レスポンス整形フィルタ | API応答標準化 |
| IsExternalInit | Polyfill | C#9以降のinitサポート | 後方互換性維持 |
