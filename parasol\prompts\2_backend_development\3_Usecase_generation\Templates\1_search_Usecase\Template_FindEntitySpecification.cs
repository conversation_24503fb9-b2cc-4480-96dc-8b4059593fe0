using Shared.Spec;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.FindEntity;

public class FindEntitySpecification : BaseSpecification<Domain.Entities.Entity>
{
    public FindEntitySpecification(FindEntityQuery request)
    {
        Query
            // TODO: 検索条件を設定してください。
            .WhereIfNotEmpty(request.Name, e => e.Name.Contains(request.Name!))
            .WhereIf(request.PeriodFrom is not null && request.PeriodTo is null, e => e.PeriodTo >= request.PeriodFrom)
            .WhereIf(request.PeriodFrom is null && request.PeriodTo is not null, e => e.PeriodFrom <= request.PeriodTo)
            .WhereIf(request.PeriodFrom is not null && request.PeriodTo is not null, e =>
                (e.PeriodFrom >= request.PeriodFrom || e.PeriodTo >= request.PeriodFrom) &&
                (e.PeriodFrom <= request.PeriodTo || e.PeriodTo <= request.PeriodTo))
#pragma warning disable CA1307 // 明確さのために StringComparison を指定する
            // StringComparisonを指定するとクエリに変換できなくなるためエラーを抑制
            .WhereIfNotEmpty(request.MemberName, e => e.Members.Any(v => v.Member.Name.Contains(request.MemberName!)))
#pragma warning restore CA1307
            .AsNoTracking();
    }
}
