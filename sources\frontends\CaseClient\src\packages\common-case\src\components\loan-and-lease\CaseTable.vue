﻿<template>
  <div>
    <AppSearchPageTmpl
      :page-index="props.pageIndex"
      :sort="props.sort"
      class="case-container"
      :list-item-key="'id'"
      :list-items="props.items"
      :list-items-total="props.total"
      :can-remove="false"
      :can-add="false"
      :can-edit="false"
      :list-headers="headers"
      :loading="props.loading"
      :page-size="props.pageSize"
      @search="onSearch"
      @click-row="onEdit"
      @update:page-index="emit('update:pageIndex', $event)"
      @update:sort="emit('update:sort', $event)"
    >
      <!-- カスタムno-dataメッセージ用のスロット -->
      <template v-if="shouldShowCustomNoData" #search-result>
        <CustomNoDataMessage
          :message="props.searchResultMessage!"
          :headers="headers"
        />
      </template>

      <!-- アクセス制限項目のマスキング -->
      <template #list-item.caseName="{ item }">
        <div v-if="item.isAccessRestricted" class="mask-item">&nbsp;</div>
        <div v-else>{{ item.caseName }}</div>
      </template>
      <template #list-item.subjectType="{ item }">
        <div v-if="item.isAccessRestricted" class="mask-item">&nbsp;</div>
        <div v-else>{{ item.subjectType }}</div>
      </template>
      <template #list-item.amount="{ item }">
        <div v-if="item.isAccessRestricted" class="mask-item">&nbsp;</div>
        <div v-else>{{ item.amount }}</div>
      </template>
      <template #list-item.period="{ item }">
        <div v-if="item.isAccessRestricted" class="mask-item">&nbsp;</div>
        <div v-else>{{ item.period }}</div>
      </template>
      <template #list-item.interestRate="{ item }">
        <div v-if="item.isAccessRestricted" class="mask-item">&nbsp;</div>
        <div v-else>{{ item.interestRate }}</div>
      </template>

      <!-- 検索条件スロット -->
      <template #criteria>
        <slot name="criteria"/>
      </template>

    </AppSearchPageTmpl>
  </div>
</template>

<script setup lang="ts">
/**
 * 融資リース案件テーブル表示コンポーネント
 *
 * 機能:
 * 1. 案件情報の一覧表示
 * 2. ページング処理
 * 3. ソート機能
 * 4. 行クリックでの編集画面遷移
 * 5. アクセス制限項目のマスキング
 */

// === 外部インポート ===
import { getLoanAndLeaseTableHeaders } from '@/packages/common-case/src/constants/loan-and-lease/tableHeaders'
import type { SortItem } from '@hox/base/src/components/shared/types'
import type { SearchOptions } from '@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue'
import CustomNoDataMessage from '@/packages/common-case/src/components/share/CustomNoDataMessage.vue'

// === プロパティ定義 ===
const props = defineProps({
  // テーブルデータ項目
  items: {
    type: Array as () => any[],
    default: () => [],
  },
  // 総データ件数（ページングで使用）
  total: {
    type: Number,
    default: 0,
  },
  // ローディング状態
  loading: {
    type: Boolean,
    default: false,
  },
  // 現在のページ番号
  pageIndex: {
    type: Number,
    default: 1,
  },
  // 1ページあたりの表示件数
  pageSize: {
    type: Number,
    default: 20,
  },
  // ソート状態
  sort: {
    type: Array as () => SortItem[],
    default: () => [],
  },
  // 検索結果メッセージ
  searchResultMessage: {
    type: String,
    default: undefined,
  },
})

// === イベント定義 ===
const emit = defineEmits([
  'update:pageIndex', // ページ変更イベント
  'update:sort', // ソート変更イベント
  'search', // 検索実行イベント
  'edit', // 行クリックによる編集イベント
])

// === テーブル設定 ===
// ヘッダー定義を外部ファイルから取得（表示項目と順序）
const headers = getLoanAndLeaseTableHeaders()

// カスタムno-dataメッセージを表示するかどうかの判定
const shouldShowCustomNoData = computed(() => {
  return props.items.length === 0 && !props.loading && props.searchResultMessage
})

// === イベントハンドラ ===
/**
 * 検索処理
 * 親コンポーネントに検索イベントを委譲
 */
function onSearch(options: SearchOptions) {
  emit('search', options)
  return Promise.resolve(true)
}

/**
 * 編集処理
 * 行クリック時に親コンポーネントに編集イベントを委譲
 */
function onEdit(event: any) {
  emit('edit', event)
}
</script>

<style scoped>
.mask-item {
  background-color: #ddd;
}

.case-container :deep(.th-padding) {
  padding: 0 0 0 8px;
}
</style>
