<template>
  <!-- 投稿時期での絞り込み -->
  <!-- 照会期間 -->
  <v-row align="center">
    <v-col align="right"><strong>照会期間：</strong></v-col>
    <v-col
    ><AppPopupDatePicker
      v-model="dataRef.fromDate"
      :disabled="threadDataRef.loading"
      label="開始日"
      clearable
      @update:model-value="updateFromDate"
    /></v-col>
    <v-col
    ><AppPopupDatePicker
      v-model="dataRef.toDate"
      :disabled="threadDataRef.loading"
      label="終了日"
      clearable
      @update:model-value="updateToDate"
    /></v-col>
    <v-col>
      <AppSimpleSelect
        v-model="dataRef.discussionType"
        :disabled="threadDataRef.loading"
        :loading="threadDataRef.loading"
        :items="dataRef.options.discussionTypeList"
        label="協議種別"
        clearable
        dense
        @update:model-value="updateDiscussionType"
      />
    </v-col>
    <v-col>
      <AppMainBtn
        :disabled="threadDataRef.loading || !userId"
        @click="openThreadEditor(null, true)"
      >
        <v-icon> mdi-pencil </v-icon>
        <span> 新しい投稿 </span>
      </AppMainBtn>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useGetIssueProjectDiscussionOptions } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionType'
import type { IssueProjectDiscussionTypeItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionType'
import { useThreadShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/thread/useThreadShareData'
import type { FindIssueProjectDiscussionThreadResultItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'

const { threadDataRef } = useThreadShareData()

type IssueProjectDiscussionType = {
  title: string
  value: string
}

const dataRef = ref<{
  fromDate: Date | undefined
  toDate: Date | undefined
  discussionType: Array<IssueProjectDiscussionType>
  options: {
    discussionTypeList: Array<IssueProjectDiscussionType>
  }
}>({
  fromDate: undefined,
  toDate: undefined,
  discussionType: [],
  options: {
    discussionTypeList: [],
  },
})

const emit = defineEmits([
  'add-thread',
  'update:fromDate',
  'update:toDate',
  'update:discussionType',
])

const { $auth } = useNuxtApp()
const userId = $auth.getUser()?.userId

// =====================================================================================================================
// APIクライアントの定義(issueProjectDiscussionTypeのgetOptions)
// =====================================================================================================================
const {
  data: getIssueProjectDiscussionOptionsData,
  executeWithResult: getIssueProjectDiscussionOptions,
} = useGetIssueProjectDiscussionOptions()

onMounted(async () => {
  await getIssueProjectDiscussionOptions()
  dataRef.value.options.discussionTypeList =
    getIssueProjectDiscussionOptionsData.value.map(
      (val: IssueProjectDiscussionTypeItem) => {
        return {
          title: val.text,
          value: val.value,
        }
      },
    )
})

const openThreadEditor = (
  threadData: FindIssueProjectDiscussionThreadResultItem | null,
  isAdd: boolean,
) => {
  emit('add-thread', threadData, isAdd)
}

const updateFromDate = (fromDate: Date) => {
  emit('update:fromDate', fromDate)
}

const updateToDate = (toDate: Date) => {
  emit('update:toDate', toDate)
}

const updateDiscussionType = (discussionType: number) => {
  emit('update:discussionType', discussionType)
}
</script>
