# Usecase定義作成

## 役割定義

- 日本人のベテランエンジニアとして、バックエンドの開発を行います。
- 必要な情報を収集し必要に応じてヒアリングを行いながら、適切な処理実装を行います。
- 語学が堪能であるためヒアリングの際には適した言語で対応しますが、成果物は日本語で出力します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 ： 
  - `parasol\architecture\backend_architecture.md`
- 共通処理使用方法リファレンス：
  - `parasol\architecture\usage_sample_backend_shared.md`
- プロジェクト設定定義 : 
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- ドメイン言語 ： `parasol\capability\**\ドメイン言語.md`
- API定義 : `parasol\api\*_API_definition.md`

### テンプレート

※サブプロンプトの内容に従ってください。

### 参照コード

- Entityクラス : `[サービスディレクトリ]\Domain\Entities\**\*.cs`

## 出力定義

※サブプロンプトの内容に従ってください。

## 制約事項

※サブプロンプトの内容に従ってください。

## 指示詳細

### 情報収集

1. API定義が指定されていない場合、作業を一時中断し。「API定義を選択してください。」とチャット上に出力しヒアリングを行ってください。
   - `parasol\api`配下にファイルが存在する場合は、それを選択肢として提示してください。
   - 対象のAPI定義が特定できたら後続の処理に進んでください。
   - 対象のAPI定義が特定できなかった場合は、再度ヒアリングを行ってください。
   - API定義が存在しない場合や、ユーザーがAPI定義を指定しない意思を示した場合は、後続の処理に進んでください。
2. API定義が特定できた場合、API定義を読み込み以下の情報を収集してください
   1. 取り扱い対象の集約/エンティティを特定します。
   2. 実装が必要なUsecase処理を特定します。
      - Usecaseのパターンは以下の通り
         - 検索（find）
         - 単一取得（get）
         - 登録（add）
         - 更新（update）
         - 削除（delete）
      - 検索や単一取得は条件違いで複数定義が必要な場合、適切なプレフィックスを付与して区別してください。
3. API定義が指定されていない場合、以下の処理を行って情報を収集してください。
   1. 作業を一時中断し、「取り扱いたいい集約/エンティティを選択してください。」とチャット上に出力しヒアリングを行ってください。
      - `parasol\capability\**\ドメイン言語.md`配下に定義されている集約/エンティティを選択肢として提示してください。
      - 対象の集約/エンティティが特定できたら後続の処理に進んでください。
      - 対象の集約/エンティティが特定できなかった場合は、再度ヒアリングを行ってください。
   2. 作業を一時中断し、「実装したいUsecase処理を指定してください」とチャット上に出力しヒアリングを行ってください。
      - Usecaseのパターンは以下の通り
         - 検索（find）
         - 単一取得（get）
         - 登録（add）
         - 更新（update）
         - 削除（delete）
      - 指定されているドメインが集約（ルートエンティティ）ではない場合、登録・更新・削除は選択できないので、チャット上にその旨を出力し、再度ヒアリングを行ってください。
4. ドメイン言語を読み込み、対象の集約/エンティティの情報を読み込んでください。
   - 対象が集約（もしくはルートエンティティ）の場合は、以下の情報を収集してください。
      - 集約のルートエンティティのプロパティ
      - 集約内のエンティティ
      - 登録・更新・削除時に他の集約の登録・更新・削除の要否
         - 対象集約の備考に記載がある場合は、その内容を参考にしてください。
         - 更新先の集約の備考に記載されている可能性があるので、ドメイン言語上に存在するすべての集約の備考を確認してください。
         - API定義が指定されている場合は、API定義の処理内容に記載されている可能性があるので、そちらも参照してください
   - 対象が集約ではなくエンティティの場合は、以下の情報を収集してください。
      - 対象エンティティのプロパティ
      - 所属する集約（どこかに所属している場合のみ）
5. 必要な情報収集が完了したら作業を一時中断し「以下の内容でUsecase実装を行います。よろしいですか？」と出力し、ユーザーの承認を得てください。
   - 出力する内容は以下の通り
      - 取り扱う集約/エンティティ
      - 必要なUsecase一覧
         - Usecase処理の処理概要
         - 他のAPI呼び出しの有無
            - 呼び出し対象のAPIのエンドポイント
   - 承認が得られたら後続処理に進んでください。
   - 承認が得られない場合はユーザーの指示に従って情報の訂正を行ってください。

### 生成作業

1. APIのエンドポイントから呼び出されるUsecaseについて、全て実装が完了するまで以下の内容を繰り返してください。
   1. 実装が完了していないUsecaseを一つ選択し、「〇〇Usecaseを実装します」と出力してください。
   2. 対応するサブプロンプトを読み込み、作業を行ってください。
   - 検索Usecase : `parasol\prompts\2_backend_development\3_Usecase_generation\sub_prompts\sub_prompt_find.md`
   - 単一取得Usecase : `parasol\prompts\2_backend_development\3_Usecase_generation\sub_prompts\sub_prompt_get.md`
   - 登録Usecase : `parasol\prompts\2_backend_development\3_Usecase_generation\sub_prompts\sub_prompt_add.md`
   - 更新Usecase : `parasol\prompts\2_backend_development\3_Usecase_generation\sub_prompts\sub_prompt_update.md`
   - 削除Usecase : `parasol\prompts\2_backend_development\3_Usecase_generation\sub_prompts\sub_prompt_delete.md`

### 品質保証

1. **機能完全性**
   - 必要なUsecaseが全て実装されているか
   - 取り扱う集約/エンティティの内容が正しいか
