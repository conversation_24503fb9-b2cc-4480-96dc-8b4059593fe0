# テストケース数の閾値(デフォルト)を設定(error, warning)
$testCaseThreshold = [int[]](3, 5)

# テスト対象クラスに含めるクラスのパスを設定
$includePathList = @(
    # UseCaseディレクトリの直下にドメイン名のディレクトリが挟まった下にある、末尾がHandlerのクラス
    "*\UseCases\*Handler.cs",
    # UseCaseディレクトリの直下にドメイン名のディレクトリが挟まった下にある、末尾がValidatorのクラス
    "*\UseCases\*Validator.cs", 
    # Servicesディレクトリの下にあるクラス
    "*\Services\*",
    # Infrastructure\QueryServicesの下にあるクラス
    "*\Infrastructure\QueryServices\*",
    # Infrastructure\Repositoriesの下にあるクラス
    "*\Infrastructure\Repositories\*"
)

# テスト対象クラスに含めないクラスのパスを設定
$excludePathList = @(
    # テストプロジェクト配下のテストクラス
    "\*.Tests\*"
)

# 対象件数を設定
$topN = 5

# アイコンを設定
$errorIcon = "❌"
$warningIcon = "⚠️"
$safeIcon = "✅"

<#
.SYNOPSIS
    テスト対象のファイルを選択します。
.PARAMETER targetPath
    チェックするファイルのパス。
.PARAMETER targetDirectory
    テスト対象のディレクトリのパス。
.RETURNS
    テスト対象のファイルパスを返します。対象外の場合は $null を返します。
#>

function Select-Test-Target-File {
    param(
        [string]$targetPath,
        [string]$targetDirectory
    )

    # $targetDirectory配下以外の場合はスキップする
    if ($targetPath -notlike "$targetDirectory*") {
        return $null
    }

    # $excludePathListに含まれるパスはスキップする
    foreach ($excludePath in $excludePathList) {
        if ($targetPath -like "$targetDirectory$excludePath") {
            return $null
        }
    }

    # $includePathListに含まれるパスはスキップしない
    foreach ($includePath in $includePathList) {
        if ($targetPath -like "$targetDirectory$includePath") {
            return $targetPath
        }
    }

    return $null
}

<#
.SYNOPSIS
    テストケースの閾値を取得します。
.PARAMETER testCase
    テストケースの情報を含むオブジェクト。
.RETURNS
    エラーおよび警告の閾値を含む配列を返します。
#>

function Get-TestCase-Threashold {
    param(
        [PSCustomObject]$testCase
    )

    # デフォルトの閾値を設定
    $errorCount = $testCaseThreshold[0]
    $warningCount = $testCaseThreshold[1]

    # ToDo: $testCaseのプロパティ値から$errorCountと$warningCountを設定する
    # $testCaseのプロパティ：Name, Count, TargetClassName, TargetClassUri, TestClassName, TestClassUri

    return [int[]]($errorCount, $warningCount)
}

<#
.SYNOPSIS
    指定された値が閾値を下回っているかどうかを判定し、エラーまたは警告のアイコンを返します。
.PARAMETER value
    判定する値。
.PARAMETER thresholds
    エラーおよび警告の閾値を含む配列。
.PARAMETER errorOrWarningStatus
    現在のエラーまたは警告のステータス。
.RETURNS
    アイコンと更新されたエラーまたは警告のステータスを返します。
#>

function Get-ErrorOrWarningIcon-Less-Than {
    param(
        [int]$value,
        [int[]]$thresholds,
        [int]$errorOrWarningStatus
    )

    if ($value -lt $thresholds[0]) {
        # エラーの場合は、エラーアイコンと、引数で渡された$errorOrWarningStatusの値または2の大きい方を返す
        return $errorIcon, [math]::Max($errorOrWarningStatus, 2)
    }
    elseif ($value -lt $thresholds[1]) {
        # 警告の場合は、警告アイコンと、引数で渡された$errorOrWarningStatusの値または1の大きい方を返す
        return $warningIcon, [math]::Max($errorOrWarningStatus, 1)
    }
    else {
        # エラーでも警告でもない場合は、引数で渡された$errorOrWarningStatusの値を返す
        return "", $errorOrWarningStatus
    }
}

<#
.SYNOPSIS
    変更されたファイルに関連するテストケース数を解析し、結果のメッセージを生成します。
.PARAMETER changeStats
    変更されたファイルの統計情報を含むオブジェクト。
.PARAMETER analysisResultDirectory
    静的分析結果が保存されているディレクトリのパス。
.PARAMETER testProjectRootDirectory
    テストプロジェクトのルートディレクトリのパス。
.PARAMETER sourceBranch
    ソースブランチの名前。
.PARAMETER repositoryUri
    リポジトリのURI。
.RETURNS
    生成されたテストケース数メッセージを返します。
#>

function Get-DotNet-Testcase-Message {
    param(
        [PSCustomObject]$changeStats,
        [string]$analysisResultDirectory,
        [string]$testProjectRootDirectory,
        [string]$sourceBranch,
        [string]$repositoryUri
    )

    # 変更されたファイルの一覧を取得
    $modifiedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "A" -or $_.ShortStatus -eq "M" } | Select-Object -ExpandProperty FileName
    $renamedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "R" } | Select-Object -ExpandProperty NewFileName
    $targetFiles = $modifiedFiles + $renamedFiles

    $testReportPath = Join-Path $analysisResultDirectory "test"

    $testCases = @()

    $sourceBranch = $sourceBranch -replace "refs/heads/", ""
    $targetDirectory = Convert-Path $testProjectRootDirectory

    $targetDirectoryRelative = Resolve-Path $targetDirectory -Relative
    $targetDirectoryRelative = $targetDirectoryRelative -replace "^.\\", "" -replace "\\", "/"

    $modifiedTestClassFiles = @()

    if ($targetFiles) {
        $modifiedTestClassFiles = $targetFiles.clone()
    }

    Get-ChildItem -Path $testReportPath -Filter "*.Tests.trx" | ForEach-Object {
        # TestReport(trx)を読み込む
        $testReportXml = [xml](Get-Content $_.FullName)

        foreach ($targetFile in $targetFiles) {
            $targetPath = Convert-Path $targetFile

            # テスト対象クラスのファイル以外はスキップ
            $targetPath = Select-Test-Target-File -targetPath $targetPath -targetDirectory $targetDirectory

            if (-not $targetPath) {
                continue
            }

            # ファイル名からクラス名を取得
            $targetClassName = [System.IO.Path]::GetFileNameWithoutExtension($targetPath)

            # テスト対象クラスのURLを生成
            $targetClassUri = "?_a=files&path=/$([URI]::EscapeUriString($targetFile))"

            # $targetFileからテストクラス名に変換する
            $testClassFullName = $targetFile -replace "^$targetDirectoryRelative/", "" -replace "/", "." -replace ".cs$", ""
            $testClassFullName = $testClassFullName -replace "^(.*?)(\..*)$", '$1.Tests$2'
            $testClassFullName = $testClassFullName + "Test"

            # $targetFileからテストクラスのファイル名に変換する
            $testClassFile = $targetFile -replace "^$targetDirectoryRelative/", "" -replace ".cs$", ""
            $testClassFile = $testClassFile -replace "^(.*?)(\/.*)$", '$1.Tests$2'
            $testClassFile = $targetDirectoryRelative + "/" + $testClassFile + "Test.cs"

            # 変更されたテストクラスのファイルを取得
            $modifiedTestClassFile = $modifiedTestClassFiles | Where-Object { $_ -ieq $testClassFile }

            # テストクラスのURLを生成
            if ($modifiedTestClassFile) {
                $testClassUri = "?_a=files&path=/$([URI]::EscapeUriString($modifiedTestClassFile))"
            }
            else {
                $testClassUri = $repositoryUri + "?path=$([URI]::EscapeUriString($testClassFile + "&version=GB$sourceBranch"))"
            }

            # TestReport(trx)のclassNameがテストクラス名と一致するテストメソッドをグルーピングしてテストケース数を取得
            $testCase = $testReportXml.TestRun.TestDefinitions.UnitTest.TestMethod |
            Where-Object { $_.className -ieq $testClassFullName } | 
            Group-Object { $_.className } | 
            Select-Object Name, Count, 
            @{Name = "TargetClassName"; Expression = { $targetClassName } }, 
            @{Name = "TargetClassUri"; Expression = { $targetClassUri } }, 
            @{Name = "TestClassName"; Expression = { $targetClassName + "Test" } }, 
            @{Name = "TestClassUri"; Expression = { $testClassUri } }
    
            if ($testCase) {
                $testCases += $testCase
            }
            else {
                $testCases += [PSCustomObject]@{
                    Name            = $testClassFullName
                    Count           = 0
                    TargetClassName = $targetClassName
                    TargetClassUri  = $targetClassUri
                    TestClassName   = $targetClassName + "Test"
                    TestClassUri    = ""
                }
            }
        }
    }

    $testMethodCountMessage = @()
    $errorOrWarningStatusTestMethodCount = [int]0

    # $testCases をテストメソッド数でソートしてtopN件を取得する
    $topNTestCases = $testCases | Sort-Object -Property Count | Select-Object -First $topN

    foreach ($testCase in $topNTestCases) {
        # テストケース数の閾値を取得
        $testCaseCountThreshold = Get-TestCase-Threashold $testCase
        $testMethodCountIcon, $errorOrWarningStatusTestMethodCount = Get-ErrorOrWarningIcon-Less-Than $testCase.Count $testCaseCountThreshold $errorOrWarningStatusTestMethodCount

        # $testMethodCountIconが空の場合はスキップ
        if ([string]::IsNullOrEmpty($testMethodCountIcon)) {
            continue
        }

        if ($testCase.Count -eq 0) {
            $testMethodCountMessage += "1. [$($testCase.TargetClassName)]($($testCase.TargetClassUri)) -> $($testCase.TestClassName)($($testCase.Count)$testMethodCountIcon)`r`n"
        }
        else {
            $testMethodCountMessage += "1. [$($testCase.TargetClassName)]($($testCase.TargetClassUri)) -> [$($testCase.TestClassName)]($($testCase.TestClassUri))($($testCase.Count)$testMethodCountIcon)`r`n"
        }
    }

    $resultTestMethodMessage = @()

    if ([string]::IsNullOrEmpty($testMethodCountMessage)) {
        $resultTestMethodMessage = @"
Waring, NG なし

"@
    }
    else {
        $resultTestMethodMessage = @"
<details>
<summary>詳細</summary>

$testMethodCountMessage
</details>

"@
    }

    return @"
### テスト対象クラス -> テストクラス(テストケース数) $(($safeIcon, $warningIcon, $errorIcon)[$errorOrWarningStatusTestMethodCount])
$resultTestMethodMessage
"@
}
