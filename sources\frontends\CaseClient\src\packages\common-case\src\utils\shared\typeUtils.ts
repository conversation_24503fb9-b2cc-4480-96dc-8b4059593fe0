type IsStringUnion<T> = T extends string
  ? string extends T
    ? false
    : true
  : false
type HasUndefined<T> = undefined extends T ? true : false

/**
 * オブジェクトのプロパティの型を再帰的にstringに変換します。
 * 主にURLのクエリパラメータからデシリアライズした値を型付けするために使用します。
 */
export type ConvertStringDeep<T> = T extends any[]
  ? string[]
  : T extends object
    ? {
        [P in keyof T]: ConvertStringDeep<T[P]>
      }
    : HasUndefined<T> extends true
      ? IsStringUnion<T> extends true
        ? T
        : string | undefined
      : IsStringUnion<T> extends true
        ? T
        : string

/**
 * オブジェクトの date 型のプロパティの型を string 型に変換します。
 * 主にJSON.parseした値を型付けするために使用します。
 */
export type DateToString<T> = {
  [P in keyof T]: T[P] extends Date ? string : T[P]
}

/**
 * 指定したプロパティを省略可能にします。
 **/
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/**
 * 指定したプロパティの型を指定した型に変更します。
 */
export type ModifyProperty<T, K extends keyof T, NewType> = {
  [P in keyof T]: P extends K ? NewType : T[P];
}
