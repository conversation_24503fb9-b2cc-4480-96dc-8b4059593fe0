import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

// 主として扱うデータのスキーマを定義します。
export const customerStaffSchema = z.object({
  id: z.string(),
  customerIdentificationId: z.string(),
  staffId: z.string(),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type CustomerStaff = z.infer<typeof customerStaffSchema>

// =====================================================================================================================
// APIクライアントの定義(検索)
// =====================================================================================================================

/**
 * 顧客識別IDでデータを検索する
 * @param customerIdentificationId Ref<T> 取得するデータの顧客識別ID
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindCustomerStaffByCustomerIdentificationId(
  customerIdentificationId: MaybeRefOrGetter<string>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<CustomerStaff[]>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-fixed-information/v1.0/customerstaff/find',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body: computed(() => ({
          customerIdentificationIds: [toValue(customerIdentificationId)],
        })),
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(取得)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetCustomerStaff(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<CustomerStaff>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-fixed-information/v1.0/customerstaff/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(追加)
// =====================================================================================================================

// 作成用のデータのスキーマを定義します。
export const customerStaffSchemaForCreate = customerStaffSchema.omit({
  id: true,
  version: true,
})
// 作成用の型を作成します。
export type CustomerStaffForCreate = z.infer<
  typeof customerStaffSchemaForCreate
>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostCustomerStaff(body: Ref<CustomerStaffForCreate>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<CustomerStaff>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-fixed-information/v1.0/customerstaff',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(更新)
// =====================================================================================================================

// 保存用のデータのスキーマを定義します。
export const customerStaffSchemaForSave = customerStaffSchema
// 保存用の型を作成します。
export type CustomerStaffForSave = z.infer<typeof customerStaffSchemaForSave>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutCustomerStaff(body: Ref<CustomerStaffForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<CustomerStaff>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-fixed-information/v1.0/customerstaff',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(削除)
// =====================================================================================================================

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteCustomerStaff(
  body: Ref<Pick<CustomerStaff, 'id' | 'version'>>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-fixed-information/v1.0/customerstaff',
        ),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(アップロード)
// =====================================================================================================================

/**
 * アップロード
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */

export function useUploadCustomerStaffFiles(body: Ref<FormData>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<string>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-fixed-information/v1.0/customerstaff/upload-file',
        ),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(csvアップロードで担当者登録)
// =====================================================================================================================

export type RunCustomerStaffUpdateBatchCriteria = {
  folderName: string
  updaterId: string | undefined
  updaterName: string | undefined
}

/**
 * Function起動
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useRunBatchCustomerStaffUpdate(
  query: Ref<RunCustomerStaffUpdateBatchCriteria>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-fixed-information/v1.0/customerstaff/run-batch',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義(担当者変更)
// =====================================================================================================================

export type BatchUpdateCustomerStaffCommand = {
  customerIdentificationIdList: string[]
  changeStaffId: string
}

export function useBatchUpdateCustomerStaff(
  body: Ref<BatchUpdateCustomerStaffCommand>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-fixed-information/v1.0/customerstaff/batch-update-staff',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
