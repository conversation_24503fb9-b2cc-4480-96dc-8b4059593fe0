# TODO: パイプラインの実行時に swap するWebAppを決定できるように default と values の値を指定してください。
parameters:
  - name: SwapTarget
    type: string
    default: app-appname-frontend-production-jpwest
    values:
      - app-appname-frontend-production-jpwest
      - app-appname-service-production-jpwest

trigger: none

stages:
- stage: SwapSlot
  jobs:
  - deployment: Swap
    environment: production-swap
    pool:
      vmImage: windows-2019
    strategy:
      runOnce:
        deploy:
          steps:
          - download: none
          - task: AzureAppServiceManage@0
            displayName: Swap slot for Azure Web App
            inputs:
              ConnectedServiceName: $(azureSubscription)
              Action: 'Swap Slots'
              WebAppName: ${{ parameters.SwapTarget }}
              ResourceGroupName: $(resourceGroupName)
              SourceSlot: staging