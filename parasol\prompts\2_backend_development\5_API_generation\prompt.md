# API実装

## 役割定義

バックエンド開発者として、指定されたユースケースを扱うAPIのControllerを作成します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 ： 
  - `parasol\architecture\backend_architecture.md`
- 共通処理使用方法リファレンス：
  - `parasol\architecture\usage_sample_backend_shared.md`
- プロジェクト設定定義 : 
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- API定義：`parasol\api\*_api_definition.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- Controllerテンプレート：`parasol\prompts\2_backend_development\4_API_generation\Templates\Template_EntityController.cs`

### 参照コード

- ユースケースHandlerクラス：`各ユースケースのHandlerクラス`

## 出力定義

- Controller出力先ディレクトリ：`[プロジェクトディレクトリ]\Controllers`
- Controllerファイル名フォーマット：`[扱うアグリゲートのルートエンティティの物理名]Controller.cs`

## 制約事項

### 禁止事項

- 定義ファイルを編集する代わりに、出力ファイルに必要な内容を記述してください
- テンプレートファイルを編集する代わりに、テンプレートを基に新しいControllerファイルを作成してください
- 当プロンプトファイルを編集する代わりに、指示内容に従って作業を実行してください
- 指示していない内容を編集する代わりに、必要な作業のみを実行してください

## 指示詳細

### 情報収集

1. Controllerテンプレートファイルを読み込み、基本構造を把握してください
1. 各ユースケースのHandlerクラスを読み込み、実装すべきメソッドを特定してください
1. 扱うアグリゲートのルートエンティティの物理名を特定してください

### 生成作業

1. テンプレートファイルを基に新しいControllerファイルを作成してください
   - ファイル名とクラス名を適切に設定
   - 必要なメソッドのみを残し、不要なメソッドは削除
   - 必要に応じてusing文と名前空間を調整
1. Handlerクラスに対応するControllerメソッドを実装してください

### 品質保証

1. 以下の内容を検証してください
   - C#の構文エラーがないか
   - 必要なusing文がすべて含まれているか
   - 名前空間とクラス名が正しく設定されているか
   - ASP.NET Coreの規約に従ったコントローラーとアクションメソッドか
   - 依存性注入（DI）の設定が正しく行われているか
   - 命名規則が.NET規約に従っているか
   - 不備が見つかった場合は修正してください
