import { z } from 'zod'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import type { Ref } from 'vue'

/**
 * 顧客識別案件検索条件バリデーション
 */
export const useIdentifiedCaseValidation = (searchCondition: Ref<any>) => {
  // バリデーションスキーマ定義
  const { errorMessages, validate, validateItem } = useValidation(
    z.object({
      // カテゴリ
      caseCategories: z
        .array(z.string())
        .nullish(),
      // ステータス
      caseStatuses: z
        .array(z.string())
        .nullish(),
      // 案件担当者
      staffIds: z
        .array(z.string())
        .nullish(),
      // 案件項目
      generalTransactionTypeIds: z
        .array(z.string())
        .nullish(),
      // 期日From
      fromDate: z
        .date()
        .nullish(),
      // 期日To
      toDate: z
        .date()
        .nullish(),
    }),
    searchCondition,
  )

  /**
   * 全てのバリデーションを実行
   * @returns バリデーション結果 (成功: true, 失敗: false)
   */
  const validateAll = async () => {
    const { success } = await validate()
    return success
  }

  return {
    errorMessages,
    validate,
    validateItem,
    validateAll,
  }
}
