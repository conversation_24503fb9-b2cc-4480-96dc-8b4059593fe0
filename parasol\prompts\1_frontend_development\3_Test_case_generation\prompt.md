# テストケース実装

## 役割定義

あなたはフロントエンドアプリケーションのテストを実装するベテランエンジニアです。指定されたVueコンポーネントの種類を判定し、適切なVitestによる単体テストを生成してください。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義：
  - `parasol\architecture\frontend_architecture_hoxbase.md`
  - `parasol\architecture\frontend_architecture_ibpbase.md`
- プロジェクト設定定義：
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- ドメイン言語定義：`parasol\capability\**\ドメイン言語.md`

### サブプロンプト

コンポーネント種類別のテスト作成手順は以下のファイルを参照してください。

- 検索画面テスト作成手順：`parasol\prompts\4_Test_case_generation\sub_prompts\sub_prompt_search_page.md`
- 編集画面テスト作成手順：`parasol\prompts\4_Test_case_generation\sub_prompts\sub_prompt_edit_page.md`
- 検索ダイアログテスト作成手順：`parasol\prompts\4_Test_case_generation\sub_prompts\sub_prompt_search_dialog.md`
- 編集ダイアログテスト作成手順：`parasol\prompts\4_Test_case_generation\sub_prompts\sub_prompt_edit_dialog.md`
- アグリゲート編集フォームテスト作成手順：`parasol\prompts\4_Test_case_generation\sub_prompts\sub_prompt_aggregate_edit_forms.md`
- エンティティ詳細セクションテスト作成手順：`parasol\prompts\4_Test_case_generation\sub_prompts\sub_prompt_entity_detail_sections.md`
- エンティティリストセクションテスト作成手順：`parasol\prompts\4_Test_case_generation\sub_prompts\sub_prompt_entity_list_sections.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- 検索画面テンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\search_page\`
- 編集画面テンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\edit_page\`
- 検索ダイアログテンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\search_dialog\`
- 編集ダイアログテンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\edit_dialog\`
- アグリゲート編集フォームテンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\aggregate_edit_forms\`
- エンティティ詳細セクションテンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\entity_detail_sections\`
- エンティティリストセクションテンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\entity_list_sections\`

## 出力定義

出力対象ファイルの出力先ディレクトリや出力ファイル名のフォーマットは各サブプロンプトに定義されています。

## 制約事項

以下の制約に従ってください。

### 禁止事項

- 定義ファイルを編集する代わりに、必要な内容は出力ファイルに記述してください
- テンプレートファイルを編集する代わりに、テンプレートを参考にしてテストコードを生成してください
- 当プロンプトファイルを編集する代わりに、記載されている指示に従ってテスト実装を行ってください

## 指示詳細

### 情報収集

1. テスト対象となるVueコンポーネントファイルを読み込み、コンポーネントの種類を特定してください。
   - ファイルパスとコンポーネント内容から以下を判定してください：
     - `pages`配下の`index.vue` → 検索画面
     - `pages`配下の`edit.vue` → 編集画面
     - `components`配下の`*SearchDialog.vue` → 検索ダイアログ
     - `components`配下の`*EditDialog.vue` → 編集ダイアログ
     - `components`配下の`Create[アグリゲート名].vue`または`Edit[アグリゲート名].vue` → アグリゲート編集フォーム
     - `components`配下の`*DetailSection.vue` → エンティティ詳細セクション
     - `components`配下の`*ListSection.vue` → エンティティリストセクション

1. 特定されたコンポーネントの種類に応じて、対応するサブプロンプトを選択してください。

1. ドメイン言語ファイルを読み込み、必要なエンティティとフィールド情報を取得してください。

### 生成作業

1. 選択したサブプロンプトの作成手順に従って、対応するテストファイルを作成してください。

1. テンプレートファイルを参考に、対象コンポーネントに適したテストコードを生成してください。

### 品質保証

1. 作業完了後、以下の内容を検証し、必要に応じて修正を行ってください：
   - 正しいコンポーネント種類が判定されているか
   - 適切なサブプロンプトが選択されているか
   - サブプロンプトに従ったテストファイルが生成されているか