<template>
  <div>
    <v-row dense>
      <v-col dense>
        <app-autocomplete
          v-model="localSearchCondition.branchNumbers"
          :items="branchMasterOptions || []"
          label="店番"
          clearable
          multiple
          chips
          closable-chips
        />
      </v-col>
      <v-col dense>
        <app-text-field
          v-model="localSearchCondition.cifNumber"
          label="CIF番号"
          type="number"
          counter="8"
          clearable
          :error-messages="errorMessages?.cifNumber"
          @blur="validateItem('cifNumber')"
          @update:model-value="validateItem('cifNumber')"
        />
      </v-col>
      <v-col dense>
        <app-text-field
          v-model="localSearchCondition.customerName"
          label="氏名（漢字 または カナ）"
          style="white-space: break-spaces"
          hint="部分一致検索ができます。"
          persistent-hint
          clearable
          :error-messages="errorMessages?.customerName"
        />
      </v-col>
      <v-col dense>
        <app-autocomplete
          v-model="localSearchCondition.staffIds"
          :items="staffAndTeams"
          label="案件担当者"
          clearable
          multiple
          chips
          closable-chips
          :loading="loadingStaffAndTeam"
        />
      </v-col>
    </v-row>
    <v-row dense>
      <v-col cols="12" md="2" dense>
        <app-autocomplete
          v-model="localSearchCondition.caseCategories"
          :items="dynamicLeaseLoanCaseCategories"
          item-title="title"
          item-value="value"
          label="カテゴリ"
          data-testid="category"
          clearable
          multiple
          chips
          closable-chips
        />
      </v-col>
      <v-col cols="12" md="2" dense>
        <app-autocomplete
          v-model="localSearchCondition.caseStatuses"
          :items="CASE_STATUSES"
          item-title="title"
          item-value="value"
          label="ステータス"
          clearable
          multiple
          chips
          closable-chips
        />
      </v-col>
      <v-col cols="12" md="2" class="d-inline-flex">
        <v-checkbox
          v-model="localSearchCondition.preConsultationStandardTarget"
          label="事前相談基準対象先"
          true-value="true"
          false-value="false"
        />
      </v-col>
      <v-col cols="12" md="2" class="d-inline-flex">
        <v-checkbox
          v-model="localSearchCondition.isFavorite"
          label="お気に入り"
          true-value="true"
          false-value="false"
        />
      </v-col>
      <v-col cols="12" md="2" class="d-inline-flex">
        <v-checkbox
          v-model="localSearchCondition.isOnSiteConfirmationNotFinished"
          label="資金トレース未了"
          true-value="true"
          false-value="false"
        />
      </v-col>
      <!-- 検索結果件数表示 -->
      <v-col cols="12" md="2" class="d-inline-flex justify-end align-end">
        <SearchResultArea :count="resultsCount" />
      </v-col>
    </v-row>

    <!-- 詳細検索条件（アコーディオン） -->
    <v-row dense class="mb-4">
      <v-col>
        <v-expansion-panels v-model="expandedPanel" flat @update:model-value="handlePanelChange">
          <v-expansion-panel :bg-color="'grey-lighten-3'">
            <v-expansion-panel-title>
              <v-row>
                <v-col align="right">
                  {{ isPanelExpanded ? '閉じる' : '詳細検索条件' }}
                </v-col>
              </v-row>
            </v-expansion-panel-title>
            <v-expansion-panel-text>
              <v-row dense>
                <v-col dense>
                  <app-autocomplete
                    v-model="localSearchCondition.industryCodes"
                    :items="industryMasterOptions || []"
                    label="業種"
                    clearable
                    multiple
                    chips
                    closable-chips
                  />
                </v-col>
                <v-col dense>
                  <app-autocomplete
                    v-model="localSearchCondition.loanRatings"
                    :items="loanRatings"
                    item-title="title"
                    item-value="value"
                    label="格付"
                    data-testid="loanRatings"
                    clearable
                    multiple
                    chips
                    closable-chips
                  />
                </v-col>
                <v-col dense>
                  <app-autocomplete
                    v-model="localSearchCondition.transactionPolicies"
                    :items="transactionPolicies"
                    item-title="title"
                    item-value="value"
                    label="取引方針"
                    data-testid="transactionPolicies"
                    clearable
                    multiple
                    chips
                    closable-chips
                  />
                </v-col>
                <v-col dense>
                  <app-autocomplete
                    v-model="localSearchCondition.subjectTypes"
                    :items="SUBJECT_TYPES"
                    item-title="title"
                    item-value="value"
                    label="科目"
                    data-testid="subjectTypes"
                    clearable
                    multiple
                    chips
                    closable-chips
                  />
                </v-col>
                <v-col dense>
                  <app-autocomplete
                    v-model="localSearchCondition.useOfFundsTypes"
                    :items="USE_OF_FUNDS_TYPES"
                    item-title="title"
                    item-value="value"
                    label="資金使途"
                    data-testid="useOfFundsTypes"
                    clearable
                    multiple
                    chips
                    closable-chips
                  />
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="2">
                  <app-popup-date-picker
                    v-model="localSearchCondition.fromDate"
                    label="期日 From"
                    display-text-format="yyyy/MM/dd"
                    clearable
                    dense
                    :error-messages="errorMessages?.fromDate"
                  />
                </v-col>
                <v-col cols="12" md="1" align="center">～</v-col>
                <v-col cols="12" md="2">
                  <app-popup-date-picker
                    v-model="localSearchCondition.toDate"
                    label="期日 To"
                    display-text-format="yyyy/MM/dd"
                    clearable
                    dense
                    :error-messages="errorMessages?.toDate"
                  />
                </v-col>
                <v-col cols="12" md="1" />
                <v-col cols="12" md="3">
                  <app-autocomplete
                    v-model="localSearchCondition.customerStaffIds"
                    :items="staffAndTeams"
                    label="顧客担当者"
                    clearable
                    multiple
                    chips
                    closable-chips
                    :loading="loadingStaffAndTeam"
                    dense
                  />
                </v-col>
                <v-col cols="12" md="1" />
                <v-col cols="12" md="2">
                  <app-autocomplete
                    v-model="localSearchCondition.segmentIds"
                    :items="segmentOptions || []"
                    label="セグメント"
                    clearable
                    multiple
                    chips
                    closable-chips
                    dense
                  />
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="2">
                  <app-text-field
                    v-model.number="localSearchCondition.fromAmount"
                    type="number"
                    label="金額 From (千円)"
                    clearable
                    dense
                  />
                </v-col>
                <v-col cols="12" md="1" align="center">～</v-col>
                <v-col cols="12" md="2">
                  <app-text-field
                    v-model.number="localSearchCondition.toAmount"
                    type="number"
                    label="金額 To (千円)"
                    clearable
                    dense
                  />
                </v-col>
                <v-col cols="12" md="1" />
                <v-col cols="12" sm="2">
                  <app-text-field
                    v-model.number="localSearchCondition.fromInterestRate"
                    type="number"
                    step="0.00001"
                    label="金利 From (%)"
                    clearable
                    dense
                    :error-messages="errorMessages?.fromInterestRate"
                    @blur="validateItem('fromInterestRate')"
                    @update:model-value="validateItem('fromInterestRate')"
                  />
                </v-col>
                <v-col cols="12" md="1" align="center">～</v-col>
                <v-col cols="12" sm="2">
                  <app-text-field
                    v-model.number="localSearchCondition.toInterestRate"
                    type="number"
                    step="0.00001"
                    label="金利 To (%)"
                    clearable
                    dense
                    :error-messages="errorMessages?.toInterestRate"
                    @blur="validateItem('toInterestRate')"
                    @update:model-value="validateItem('toInterestRate')"
                  />
                </v-col>
              </v-row>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
/**
 * 融資リース案件検索条件コンポーネント
 *
 * 機能:
 * 1. 検索条件入力フォームの提供
 * 2. 入力値のバリデーション
 * 3. 詳細検索条件の表示/非表示切り替え
 * 4. 検索条件変更の親コンポーネントへの通知
 */

// === 外部インポート ===
import { computed, ref, watch } from 'vue'
import { isArray } from '@hox/base/src/utils/shared/is'
import SearchResultArea from '~/packages/common-case/src/components/share/SearchResultArea.vue'
import { useFeatureManager } from '@/packages/common-case/src/composables/shared/useFeatureManager'

// === マスタデータ・ドメイン定数 ===
import {
  LEASE_LOAN_CASE_CATEGORIES, // 融資リースカテゴリ一覧
  FUNDING_SUPPORT_CASE_CATEGORIES, // 資金使途支援カテゴリ一覧
  CASE_STATUSES, // 案件ステータス一覧
  SUBJECT_TYPES, // 科目種別一覧
  USE_OF_FUNDS_TYPES, // 資金使途種別一覧
} from '@/packages/common-case/src/constants/domain/case'
import {
  loanRatings, // 融資格付一覧
  transactionPolicies, // 取引方針一覧
} from '@/packages/common-case/src/constants/domain/common'

// === プロパティ定義 ===
const props = defineProps({
  // 検索条件オブジェクト
  searchCondition: {
    type: Object,
    required: true,
  },
  // マスタデータ選択肢
  branchMasterOptions: {
    type: Array,
    default: () => [],
  },
  industryMasterOptions: {
    type: Array,
    default: () => [],
  },
  segmentOptions: {
    type: Array,
    default: () => [],
  },
  // スタッフ・チーム情報
  staffAndTeams: {
    type: Array,
    default: () => [],
  },
  loadingStaffAndTeam: {
    type: Boolean,
    default: false,
  },
  // バリデーション
  errorMessages: {
    type: [Object, null],
    default: null,
  },
  // 検索結果件数
  resultsCount: {
    type: Number,
    default: 0,
  },
})

// === イベント定義 ===
const emit = defineEmits([
  'update:searchCondition', // 検索条件更新イベント
  'validateItem', // 項目検証イベント
])

// === 検索条件管理 ===
/**
 * 検索条件のローカルコピーを作成し、v-modelでバインドする
 * 親コンポーネントとの双方向バインディングを実現
 */
const localSearchCondition = ref({ ...props.searchCondition })

// 検索条件が変更されたら親コンポーネントに通知
watch(localSearchCondition, (newValue) => {
  emit('update:searchCondition', newValue)
}, { deep: true })

// 親コンポーネントからの変更を監視して反映
watch(() => props.searchCondition, (newValue) => {
  // ディープコピーを作成して参照を切り替える
  localSearchCondition.value = { ...newValue }
}, { deep: true })

/**
 * 項目のバリデーション実行
 * 親コンポーネントに検証イベントを通知
 * @param field バリデーション対象フィールド名
 */
function validateItem(field: string) {
  emit('validateItem', field)
}

// === 詳細検索条件パネル制御 ===
/**
 * アコーディオンの開閉状態を管理
 */
const expandedPanel = ref<any>([])

/**
 * パネルが開いているかどうかを判定
 * Vuetifyのv-expansion-panelsの仕様に合わせた判定ロジック
 */
const isPanelExpanded = computed(() => {
  return expandedPanel.value === 0 ||
    expandedPanel.value === '0' ||
    (isArray(expandedPanel.value) && expandedPanel.value.length > 0)
})

/**
 * アコーディオンパネルの開閉状態変更処理
 * @param val パネル状態値
 */
function handlePanelChange(val: any) {
  expandedPanel.value = val
}

// === カテゴリの取得 ===
/**
 * 機能フラグ管理機能から資金使途支援機能フラグを取得
 * このフラグによって表示するカテゴリーが動的に変わる
 */
const { getFundingSupportFeatureFlag, fetchFeatureFlag } = useFeatureManager()

/**
 * 資金使途支援カテゴリーの値一覧を取得
 * フィルタリング処理で使用する
 */
const fundingSupportValues = computed(() => FUNDING_SUPPORT_CASE_CATEGORIES.map(item => item.value))

/**
 * 動的に表示するカテゴリー一覧を生成
 * 資金使途支援機能がONの場合: 全カテゴリーを表示
 * 資金使途支援機能がOFFの場合: 資金使途支援カテゴリーを除外して表示
 */
const dynamicLeaseLoanCaseCategories = computed(() => {
  if (hasFundingSupportFeatureFlag.value) {
    return LEASE_LOAN_CASE_CATEGORIES
  }
  return LEASE_LOAN_CASE_CATEGORIES.filter(
    category => !fundingSupportValues.value.includes(category.value),
  )
})

/**
 * 資金使途支援機能のフラグ状態を管理するリアクティブな値
 */
const hasFundingSupportFeatureFlag = ref<boolean | undefined>(false)

// コンポーネントの初期化時にフラグを取得
fetchFeatureFlag().then(() => {
  hasFundingSupportFeatureFlag.value = getFundingSupportFeatureFlag.value
})
</script>
