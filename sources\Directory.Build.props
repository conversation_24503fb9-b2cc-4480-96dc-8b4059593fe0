<Project>
	<Import Project="./IbpArchitecture.props" />
	<PropertyGroup>
		<LangVersion>12.0</LangVersion>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<MinVerTagPrefix>v</MinVerTagPrefix>
    	<MinVerDefaultPreReleaseIdentifiers>preview.0</MinVerDefaultPreReleaseIdentifiers>
		<EnforceCodeStyleInBuild>false</EnforceCodeStyleInBuild>
		<RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
		<DisableImplicitNuGetFallbackFolder>true</DisableImplicitNuGetFallbackFolder>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="MinVer" Version="5.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Nut.AssemblyInfo" Version="1.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Nut.ResxBridge" Version="1.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.CodeAnalysis.Metrics" Version="3.3.4" />
	</ItemGroup>

	<!--コミットのハッシュ値の取得-->
	<Target Name="GetCommitHash" BeforeTargets="EmbedMetadaAssemblyAttributes">
		<!-- CommitHash -->
		<Exec Command="git -C $(ProjectDir) rev-parse HEAD" ConsoleToMSBuild="true" IgnoreExitCode="true" ContinueOnError="WarnAndContinue">
			<Output TaskParameter="ConsoleOutput" PropertyName="GetCommitHashResult" />
			<Output TaskParameter="ExitCode" PropertyName="GetCommitHashExitCode" />
		</Exec>
		<PropertyGroup Condition="$(GetCommitHashExitCode) == '0'">
			<CommitHash>$(GetCommitHashResult)</CommitHash>
		</PropertyGroup>
		<PropertyGroup Condition="$(GetCommitHashExitCode) != '0'">
			<CommitHash></CommitHash>
		</PropertyGroup>
	</Target>

	<!-- バージョン情報をプログラムから扱いやすくするために(ThisAssemblyで取得できるよう)AssemblyMetadataAttributeに埋め込み -->
	<Target Name="EmbedMetadaAssemblyAttributes" AfterTargets="MinVer">
		<ItemGroup>
			<AssemblyAttribute Include="System.Reflection.AssemblyMetadataAttribute">
				<_Parameter1>ApplicationVersion</_Parameter1>
				<_Parameter2>$(MinVerVersion)</_Parameter2>
			</AssemblyAttribute>
			<AssemblyAttribute Include="System.Reflection.AssemblyMetadataAttribute">
				<_Parameter1>CommitHash</_Parameter1>
				<_Parameter2>$(CommitHash)</_Parameter2>
			</AssemblyAttribute>

		</ItemGroup>
		<ItemGroup Condition="$(ArchitectureVersion) != ''">
			<AssemblyAttribute Include="System.Reflection.AssemblyMetadataAttribute">
				<_Parameter1>ArchitectureVersion</_Parameter1>
				<_Parameter2>$(ArchitectureVersion)</_Parameter2>
			</AssemblyAttribute>
		</ItemGroup>
		<ItemGroup Condition="$(ArchitectureCommitHash) != ''">
			<AssemblyAttribute Include="System.Reflection.AssemblyMetadataAttribute">
				<_Parameter1>ArchitectureCommitHash</_Parameter1>
				<_Parameter2>$(ArchitectureCommitHash)</_Parameter2>
			</AssemblyAttribute>
		</ItemGroup>
	</Target>

	<!-- InternalVisibleToAttributeを埋め込む -->
	<!-- https://www.meziantou.net/declaring-internalsvisibleto-in-the-csproj.htm -->
	<Target Name="AddInternalsVisibleTo" BeforeTargets="BeforeCompile" Condition="!$(AssemblyName.EndsWith('.Tests'))">
		<!-- サフィックスがついてるプロジェクトを追加 -->
		<ItemGroup>
			<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
				<_Parameter1>$(AssemblyName).Tests</_Parameter1>
			</AssemblyAttribute>
		</ItemGroup>
	</Target>
</Project>
