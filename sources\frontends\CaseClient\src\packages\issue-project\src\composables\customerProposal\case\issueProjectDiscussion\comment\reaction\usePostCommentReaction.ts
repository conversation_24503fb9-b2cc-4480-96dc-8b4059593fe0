import { useCommentShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/useCommentShareData'
import { usePostIssueProjectDiscussionCommentReaction } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import type { IssueProjectDiscussionCommentReactionForCreate } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type { FindIssueProjectDiscussionThreadResultItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { isArray } from '@hox/base/src/utils/shared/is'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'

export const usePostCommentReaction = () => {
  const { authDisplayName } = useCommentShareData()
  const { error: errorToast, success: successToast } = useAppToasts()
  // データの変更を監視して、画面離脱時に変更があった場合に確認ダイアログを表示します。
  const { init, hasChanged, restart } = useWatchDataChanges()
  const watcher = { init, hasChanged, restart }
  const { $auth } = useNuxtApp()
  const userId = $auth.getUser()?.userId

  /**
   *  コメントのリアクション追加
   */
  const postCommentReaction = async (
    newReactionType: number,
    threadId: string,
    sendCommentId: string,
    refThreadsData: FindIssueProjectDiscussionThreadResultItem[],
  ) => {
    const sendData = {
      commentId: sendCommentId,
      staffId: userId,
      staffName: authDisplayName!,
      reactionType: newReactionType,
      updatedDateTime: new Date(),
    }

    const {
      data: addCommentReactionData,
      executeWithResult: addCommentReaction,
    } = usePostIssueProjectDiscussionCommentReaction(
      ref<IssueProjectDiscussionCommentReactionForCreate>(sendData),
    )

    const result = await addCommentReaction().catch((e) => {
      // 失敗だった場合の処理を行います。
      if (!e.hasProblemDetails) {
        errorToast('リアクションの追加に失敗しました。')
      }
      if (e.data.type === '/validation-error') {
        errorToast(e.data.errors)
      } else if (e.data.type === '/conflict') {
        errorToast(
          'すでに別のユーザーがデータを登録・変更しているためリアクションを登録できませんでした。',
        )
      } else {
        errorToast('リアクションの追加に失敗しました。')
      }
      return false
    })

    // APIがエラーだった場合は処理を中断します。
    if (!result) return

    // 追加リアクションをtargetDataに入れる
    const targetThreadIndex = refThreadsData.findIndex((x) => x.id === threadId)

    const targetThread = refThreadsData[targetThreadIndex]
    const targetCommentIndex = targetThread.comments.findIndex(
      (x) => x.id === addCommentReactionData.value.commentId,
    )

    isArray(
      refThreadsData[targetThreadIndex].comments[targetCommentIndex].reactions,
    )
      ? refThreadsData[targetThreadIndex].comments[
        targetCommentIndex
      ].reactions.push(addCommentReactionData.value)
      : (refThreadsData[targetThreadIndex].comments[
          targetCommentIndex
        ].reactions = [addCommentReactionData.value])

    successToast('リアクションを登録しました。')
    watcher.init(refThreadsData)
  }

  return {
    postCommentReaction,
  }
}
