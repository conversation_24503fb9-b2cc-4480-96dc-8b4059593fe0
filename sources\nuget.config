<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <!--To inherit the global NuGet package sources remove the <clear/> line below -->
    <clear />
    <add key="nuget" value="https://api.nuget.org/v3/index.json" />
    <add key="shared-modules-org@Local" value="https://pkgs.dev.azure.com/digital-v/_packaging/shared-modules-org%40Local/nuget/v3/index.json" />
  </packageSources>
  <packageSourceMapping>
    <!-- key value for <packageSource> should match key values from <packageSources> element -->
    <packageSource key="nuget">
      <package pattern="*" />
    </packageSource>
    <packageSource key="shared-modules-org@Local">
      <package pattern="Shared*" />
    </packageSource>
  </packageSourceMapping>
</configuration>
