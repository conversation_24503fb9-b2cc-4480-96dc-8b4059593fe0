# エンティティリストセクションテスト作成

## 役割定義

あなたはエンティティリストセクションのテストを実装するベテランエンジニアです。指定されたエンティティリストセクションコンポーネントとその関連composableに対してVitestを使用した包括的な単体テストを生成してください。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- プロジェクト設定定義：
  - `parasol\architecture\project_settings.md`

### 仕様定義

実装対象の仕様は以下のファイルに定義されています。

- テスト対象セクションコンポーネント：`[指定されたエンティティリストセクションのVueファイル]`
- テスト対象Composable：`[指定されたエンティティリスト取得composableのTSファイル]`
- ドメイン言語定義：`parasol\capability\**\ドメイン言語.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- セクションコンポーネントテストテンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\entity_list_sections\Template_EntityListSection.spec.ts`
- Composableテストテンプレート：`parasol\prompts\1_frontend_development\4_Test_case_generation\Templates\entity_list_sections\Template_useGetYourEntitiesByCustomerIdentificationId.spec.ts`

## 出力定義

出力対象ファイルの出力先ディレクトリや出力ファイル名のフォーマットは以下に従ってください。

- テストファイル出力先ディレクトリ：`test\[テスト対象ファイルのsrc配下の相対パス]`
- セクションコンポーネントテストファイル名：`[扱うアグリゲートのルートエンティティの物理名]ListSection.spec.ts`
- Composableテストファイル名：`useGet[扱うアグリゲートのルートエンティティの物理名]sByCustomerIdentificationId.spec.ts`

例：
- `src\components\customer\organisms\CustomerListSection.vue` → `test\components\customer\organisms\`
- `src\composables\customer\useGetCustomersByCustomerIdentificationId.ts` → `test\composables\customer\`

## 制約事項

以下の制約に従ってください。

### 禁止事項

- 定義ファイルを編集する代わりに、必要な内容は出力ファイルに記述してください
- テンプレートファイルを編集する代わりに、テンプレートを参考にしてテストコードを生成してください
- 当プロンプトファイルを編集する代わりに、記載されている指示に従ってテスト実装を行ってください
- 指示していない内容を編集する代わりに、指定された作業のみを実行してください

## 指示詳細

### 情報収集

1. 各定義ファイルの読み込みを行ってください。
   - テスト対象のエンティティリストセクションVueファイルを読み込み、実装内容を確認してください
   - テスト対象のエンティティリスト取得composable TSファイルを読み込み、実装内容を確認してください
   - テスト対象コンポーネントで扱うエンティティに基づいて、該当するドメイン言語ファイルを読み込んでください
   - テストテンプレートファイルを読み込んでください

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください。

1. セクションコンポーネントテストファイルを作成してください。
   - テンプレートファイルを基に内容を生成してください
   - 初期表示を含む全てのイベント処理についてテストを作成してください
   - エンティティリストの表示、テーブル表示についてテストを作成してください
   - ローディング状態、エラー状態、データなし状態についてテストを作成してください
   - 行クリック・選択状態・イベント発火についてテストを作成してください
   - フィルタリング、ソート、ページネーション機能があればテストを作成してください
   - プロパティの受け渡しについてテストを作成してください

1. Composableテストファイルを作成してください。
   - テンプレートファイルを基に内容を生成してください
   - 一覧取得APIの正常系・異常系・空データテストを作成してください
   - パラメータに応じた取得処理のテストを作成してください
   - ローディング状態管理のテストを作成してください
   - エラーハンドリングのテストを作成してください
   - フィルタリング、ソート、ページネーション、検索機能があればテストを作成してください
   - CRUD操作、複合条件処理、大量データ処理があればテストを作成してください
   - リアクティビティのテストを作成してください

### 品質保証

1. 作成したテストファイルの検証を行ってください：
   - 出力先・出力ファイル名フォーマットに従って、ファイルが正しく出力されているか
   - 初期表示を含む全てのイベント処理にテストが作成されているか
   - エンティティリストセクション特有の機能（リスト表示、テーブル表示、フィルタリング、ソート、ページネーション等）にテストが作成されているか
   - テンプレートに従ったモック定義が使用されているか
   - TypeScriptの構文エラーがないか
   - Vitestの記法が正しく使用されているか
   - 必要なimport文がすべて含まれているか
   - 正しい構文で記述されているか
   - 実行されないコードや冗長なコードがないか
   - 改行やインデントが適切に設定されているか
