import { useNuxtApp } from 'nuxt/app'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

// =====================================================================================================================
// APIクライアントの定義 (ダウンロード)
// =====================================================================================================================

// ダウンロードの型を定義します。
export type downloadIssueProjectDiscussionThreadFile = {
  threadId: string
  fileName: string
}

/**
 * ダウンロードする
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDownloadIssueProjectDiscussionThreadFile(query: Ref<downloadIssueProjectDiscussionThreadFile>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Blob>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-proposal/v1.0/issueprojectdiscussionthreadfile/download'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}
