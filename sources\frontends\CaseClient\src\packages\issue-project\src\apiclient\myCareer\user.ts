import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

// ユーザー情報のスキーマを定義します
export const userSchema = z.object({
  userId: z.string(),
  mainCompanyId: z.string(),
  mainCompanyName: z.string(),
  nameKanji: z.string(),
  nameRomaji: z.string(),
  mainBranchNumber: z.string(),
  mainBranchName: z.string(),
  mainPosition: z.string(),
  currentJobDescription: z.string(),
  isManager: z.boolean(),
})

// 主として扱うデータのデータ型を定義します
export type User = z.infer<typeof userSchema>

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

// 検索条件の型を定義します
export type FindUserCriteria = {
  userId?: string | undefined
  name?: string | undefined
  branchNumbers?: string[] | undefined
}

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindUser(query?: MaybeRefOrGetter<FindUserCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<User[]>(
    useFetch(
      () => $endpoints.default.get('/mycareer-user/v1.0/useranalysis'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data.returnSearchUsersResults
        },
      }),
    ),
  )
}