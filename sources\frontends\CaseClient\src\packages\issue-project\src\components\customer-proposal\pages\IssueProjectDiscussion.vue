<template>
  <!-- バインドするデータが null / undefined の可能性も考慮しておくこと -->
  <app-search-page-tmpl
    v-model:pageIndex="pagination.pageIndex"
    v-model:sort="pagination.sort"
    :page-size="pagination.pageSize"
    :list-item-key="'id'"
    :list-headers="headers"
    :list-items="dataRef?.items"
    :list-items-total="data?.total"
    :loading="loading"
    :on-search="search"
    :can-edit="false"
    :can-add="false"
    :can-remove="false"
    :on-click-row="toDetail"
  >
    <template #expanded-item="{ item }">
      <td :colspan="headers.length">
        <div class="expand-thread">
          <IssueProjectDiscussionThread
            :loading="loading"
            :thread="item"
            class="mb-3"
            type="cross-customer"
            @download-thread-file="downloadThreadFile"
            @download-comment-file="downloadCommentFile"
          />
        </div>
      </td>
    </template>

    <template #[`list-item.overview`]="{ item }">
      <div v-if="item.isAccessRestricted" class="mask-item">&nbsp;</div>
      <div v-else>{{ item.overview }}</div>
    </template>

    <template #[`list-item.title`]="{ item }">
      <div v-if="item.isAccessRestricted" class="mask-item">&nbsp;</div>
      <div v-else>{{ item.title }}</div>
    </template>

    <template #criteria>
      <v-row>
        <v-col>
          <AppPopupDatePicker
            v-model="fromDate"
            label="登録日From"
            clearable
            dense
            :error-messages="errorMessages?.fromDate"
            @blur="validateItem('fromDate')"
            @update:model-value="validateItem('fromDate')"
          />
        </v-col>
        <v-col>
          <AppPopupDatePicker
            v-model="toDate"
            label="登録日To"
            clearable
            dense
            :error-messages="errorMessages?.toDate"
            @blur="validateItem('toDate')"
            @update:model-value="validateItem('toDate')"
          />
        </v-col>
        <v-col>
          <AppAutocomplete
            v-model="searchCondition.registrantIds"
            :items="dataRef.options.staff"
            label="登録者"
            clearable
            dense
            multiple
            chips
            closable-chips
            :disabled="loading"
            :loading="loading"
          />
        </v-col>
        <v-col>
          <AppAutocomplete
            v-model="searchCondition.customerStaffIds"
            :items="dataRef.options.staff"
            label="顧客担当者"
            clearable
            dense
            multiple
            chips
            closable-chips
            :disabled="loading"
            :loading="loading"
          />
        </v-col>
      </v-row>

      <v-row>
        <v-col>
          <AppAutocomplete
            v-model="searchCondition.issueProjectStaffIds"
            :items="dataRef.options.staff"
            label="案件担当者"
            clearable
            dense
            multiple
            chips
            closable-chips
            :disabled="loading"
            :loading="loading"
          />
        </v-col>

        <v-col>
          <AppAutocomplete
            v-model="searchCondition.issueProjectTeams"
            :items="issueProjectTeamList"
            label="案件担当チーム"
            clearable
            dense
            multiple
            chips
            closable-chips
            :disabled="loading"
            :loading="loading"
          />
        </v-col>

        <v-col>
          <AppAutocomplete
            v-model="searchCondition.discussionTypeIds"
            :items="dataRef.options.discussionTypeList"
            label="協議種別"
            clearable
            dense
            multiple
            chips
            closable-chips
            :disabled="loading"
            :loading="loading"
          />
        </v-col>
      </v-row>
    </template>
  </app-search-page-tmpl>
  <app-confirm-dialog ref="confirmDialog" type="remove" />
</template>
<script setup lang="ts">
import { z } from 'zod'
import { reactive, computed, ref, onMounted } from 'vue'
import { endOfDay, format, startOfDay } from 'date-fns'
import type { SearchOptions } from '@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue'
import { useCriteriaUrlState } from '@hox/base/src/composables/shared/useCriteriaUrlState'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import type { ConvertStringDeep } from '@ibp/common-case/src/utils/shared/typeUtils'
import { useFIndIssueProjectDiscussionThreadCrossCustomer as useFindApi } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import type {
  FindIPDTCrossCustomerQuery,
  FindIPDTCrossCustomerQueryResult,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import { useGetIssueProjectDiscussionOptions } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionType'
import { useGetIssueProjectOptions } from '@ibp/issue-project/src/apiclient/customerProposal/issueProject'
import { issueProjectTeamList } from '@ibp/issue-project/src/constants/domain/issueProject'
import type { IssueProjectDiscussionTypeItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionType'
import {
  convertToStringArray,
  convertStringArrayToNumberArray,
} from '@ibp/common-case/src/utils/shared/requestValueConverter'
import { useDownloadIssueProjectDiscussionThreadFile } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThreadFile'
import type { downloadIssueProjectDiscussionThreadFile } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThreadFile'
import { useThreadShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/thread/useThreadShareData'
import { useDownloadIssueProjectDiscussionCommentFile } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentFile'
import type { downloadIssueProjectDiscussionCommentFile } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentFile'
import type {
  Pagination,
} from '@hox/base/src/apiclient/shared/types'
import type { SortItem } from '@hox/base/src/components/shared/types'
import { toDateRef } from '@ibp/common-case/src/utils/converters'

// =====================================================================================================================
// 画面の定義
// =====================================================================================================================

useHead({
  title: '課題案件協議一覧',
})

  // 画面で入力する検索条件型の定義(APIで要求されているPagination部分を除いたもの)
  // 検索条件の型に変更してください。
  type Criteria = Omit<FindIPDTCrossCustomerQuery, keyof Pagination>

// 検索条件を設定する画面項目とバインドするデータを定義します。
const searchCondition = reactive<Criteria>({
  fromDate: undefined,
  toDate: undefined,
  registrantIds: [],
  customerStaffIds: [],
  issueProjectStaffIds: [],
  issueProjectTeams: [],
  discussionTypeIds: [],
})

// リアクティブなデータを定義
const fromDate = toDateRef(toRef(searchCondition, 'fromDate'))
const toDate = toDateRef(toRef(searchCondition, 'toDate'))
const isControlPressed = ref(false)

  type IssueProjectDiscussionType = {
    title: string
    value: string
  }

  type StaffAndTeamOptionsType = {
    value: string
    type: string
    title: string
    teamMembers: string[]
    isMyTeam: number
  }

const dataRef = ref<{
  items: Array<FindIPDTCrossCustomerQueryResult>
  options: {
    discussionTypeList: Array<IssueProjectDiscussionType>
    staff: Array<StaffAndTeamOptionsType>
  }
}>({
  items: [],
  options: {
    discussionTypeList: [],
    staff: [],
  },
})

// 検索条件を定義します。
// ページングやソートの変更時に、最初に検索されたときの条件を利用するために
// 画面項目とバインドするデータとは独立して定義します。
const criteria = ref<Criteria | null>(null)

// ページング
const pagination = reactive({
  pageIndex: 1,
  pageSize: 20,
  sort: [] as SortItem[],
})

// 検索結果
const headers = [
  {
    title: '顧客名',
    key: 'customerName',
    sortable: true,
    editPrevention: false,
  },
  {
    title: '課題概要',
    key: 'overview',
    sortable: true,
    editPrevention: false,
  },
  {
    title: '登録日',
    key: 'registeredDateTime',
    sortable: true,
    editPrevention: false,
    value: (item) => {
      return format(item.registeredDateTime, 'yyyy/MM/dd') ?? undefined
    },
  },
  {
    title: '協議名',
    key: 'title',
    sortable: true,
    editPrevention: false,
  },
  {
    title: '顧客担当者',
    key: 'customerStaffName',
    sortable: true,
    editPrevention: false,
  },
  {
    title: '案件担当者',
    key: 'issueProjectStaffName',
    sortable: true,
    editPrevention: false,
  },
  {
    title: '案件担当チーム',
    key: 'issueProjectTeam',
    sortable: true,
    editPrevention: false,
  },
  {
    title: '協議内容',
    type: 'data-table-expand',
    key: 'data-table-expand',
    sortable: false,
    width: '100',
    editPrevention: true,
  },
]

//
// バリデーション定義
//
const { errorMessages, validate, validateItem } = useValidation(
  z.object({
    fromDate: z.string(),
    toDate: z.string().refine(
      (toDate) => {
        // 登録日Toが登録日Fromより未来かどうか
        const fromDate = startOfDay(searchCondition.fromDate ?? '') // fromDateの開始時刻を取得
        const toDateStart = startOfDay(toDate) // toDateの開始時刻を取得
        return fromDate <= toDateStart
      },
      {
        message: '登録日Toは登録日Fromより後の日付でなければなりません',
      },
    )
      .refine(
        (toDate) => {
          // 開始日、終了日は1年以内に収まるか
          if (!searchCondition.fromDate) return true

          // fromDateを日付オブジェクトに変換
          const fromDate = new Date(searchCondition.fromDate)

          // 1年後の日付を計算
          const limitDate = new Date(fromDate)
          limitDate.setFullYear(limitDate.getFullYear() + 1)

          // toDateを日付オブジェクトに変換
          const toDateObj = new Date(toDate)

          // 時刻を無視して日付のみで比較
          const toDateOnly = new Date(
            toDateObj.getFullYear(),
            toDateObj.getMonth(),
            toDateObj.getDate(),
          )
          const limitDateOnly = new Date(
            limitDate.getFullYear(),
            limitDate.getMonth(),
            limitDate.getDate(),
          )

          return toDateOnly <= limitDateOnly
        },
        {
          message: '開始日、終了日は1年以内に収まるように入力してください',
        },
      ),
  }),
  toRef(searchCondition),
)

watch(
  () => searchCondition.fromDate,
  (newfromDate) => {
    // 登録日Toが登録日Fromより未来かどうか
    const fromDate = startOfDay(newfromDate ?? '') // fromDateの開始時刻を取得
    const toDateStart = startOfDay(searchCondition.toDate ?? '') // toDateの開始時刻を取得
    if (fromDate <= toDateStart && errorMessages.value) {
      errorMessages.value.toDate = []
    }
  },
)

// 処理完了などをトースト表示するために利用します。
const { error: errorToast } = useAppToasts()
// 複数のフラグでローディング状態を管理する場合は useFlagCondition を利用します。
const { hasTrue: loading, addFlag } = useFlagCondition()

// 指定されているクエリの中から検索条件以外のものを抜き出して保持しておきます。
// ※検索条件の履歴に積むときに消えないように追加するためです。
// e.g. 画面表示時にクエリパラメーターで val=a が指定されている場合、検索条件を変更してクエリパラメーターに保持されても val=a は消えないようになります。
const additionalQueryParameter: { [key: string]: string } = (() => {
  const route = useRoute()
  const query = route.query as { [key: string]: string }
  const excludeKeys = Object.keys(searchCondition).concat([
    'q',
    'pageIndex',
    'pageSize',
    'sort',
  ])
  return Object.fromEntries(
    Object.entries(query).filter(([key]) => !excludeKeys.includes(key)),
  )
})()

// =====================================================================================================================
// APIクライアントの定義(検索)
// =====================================================================================================================

// 検索クエリを定義します。
const query = computed(() => {
  const sort = pagination.sort.reduce((prev, curr) => {
    prev.push(`${curr.key}${curr.order === 'desc' ? ' desc' : ''}`)
    return prev
  }, [] as string[])
  const paginationQuery = {
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
    sort,
  }

  const request = createRequestSearchCondition()
  return {
    ...(request ?? {}),
    ...paginationQuery,
    tab: 'issue-project-discussion',
  }
})

// api の定義 パラメーターには reactive(ref / reactive) な値を使います。
// 検索APIクライアントを定義します。
const {
  data,
  executeWithResult: findData,
  inProgress: findInProgress,
} = useFindApi(query)
addFlag(findInProgress)

// 検索履歴を積む設定を取得します。
const {
  state: criteriaHistory,
  push: pushCriteria,
  onChange,
  hasState,
} = useCriteriaUrlState<FindIPDTCrossCustomerQuery>()

// 検索履歴が変わった(戻るが押された)際の処理を記述します。
onChange((state) => {
  if (!state) {
    // 初回アクセスに戻った場合に undefined になる
    data.value = {
      // データを初期化します。
      items: [],
      total: 0,
    }
    return
  }
  // 検索条件を復元し検索を実行する
  restoreCriteria(state)
  search({
    noStoreCriteria: true,
  })
})

// =====================================================================================================================
// APIクライアントの定義(issueProjectDiscussionTypeのgetOptions)
// =====================================================================================================================
const {
  data: getIssueProjectDiscussionOptionsData,
  executeWithResult: getIssueProjectDiscussionOptions,
} = useGetIssueProjectDiscussionOptions()

// =====================================================================================================================
// APIクライアントの定義(issueProjectのgetOptions)
// =====================================================================================================================
const { executeWithResult: getIssueProjectOptions } =
    useGetIssueProjectOptions()

// =====================================================================================================================
// 画面初期化処理
// =====================================================================================================================

onMounted(() => {
  setOptions()
  window.addEventListener('keydown', handleKeyDown)
  window.addEventListener('keyup', handleKeyUp)

  // 画面直接アクセスの場合などに検索履歴がある場合は復元する
  if (hasState.value && criteriaHistory.value) {
    restoreCriteria(criteriaHistory.value)
    search({
      noStoreCriteria: true,
    })
  } else {
    searchCondition.fromDate = new Date().toISOString()
    searchCondition.toDate = new Date().toISOString()

    search({
      noStoreCriteria: true,
    })
    // 初期化
    data.value = {
      items: [],
      total: 0,
    }
  }
})

// =====================================================================================================================
// 画面離れたときの処理
// =====================================================================================================================
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
  window.removeEventListener('keyup', handleKeyUp)
})

// =====================================================================================================================
// 画面で使用するメソッドの定義
// =====================================================================================================================

// 検索の処理を行います。
async function search(options: SearchOptions = {}) {
  const { success } = await validate()
  if (!success) {
    return false
  }

  // ソートの変更かページングの変更の場合で、かつ検索条件が空(まだ一度も検索されていない)の場合は検索しない
  if (
    (options.isSortChangeRequest || options.isPaginationRequest) &&
    !criteria.value
  ) {
    return true
  }
  // ソート/ページング以外の場合は検索条件を更新する
  if (!options.isSortChangeRequest && !options.isPaginationRequest) {
    // reactive / ref の状態を要調査
    criteria.value = Object.assign({}, searchCondition)
  }
  await findData()
  if (!options.noStoreCriteria) {
    // 検索条件を履歴に積みます。
    // 検索条件以外のパラメーターも消えないように追加します。
    pushCriteria({ ...query.value, ...additionalQueryParameter })
  }
  return true
}

// URLのクエリに指定されていた検索条件を復元します。
// 多くの場合はそのまま利用できますが、検索条件の型が変わっている場合は適宜修正してください。
function restoreCriteria(
  // 検索条件の型を設定します。
  criteriaHistory: ConvertStringDeep<FindIPDTCrossCustomerQuery>,
) {
  // 検索条件を復元します。
  searchCondition.fromDate = criteriaHistory.fromDate ?? undefined
  searchCondition.toDate = criteriaHistory.toDate ?? undefined
  searchCondition.registrantIds =
      convertToStringArray(criteriaHistory.registrantIds) ?? []
  searchCondition.customerStaffIds =
      convertToStringArray(criteriaHistory.customerStaffIds) ?? []
  searchCondition.issueProjectStaffIds =
      convertToStringArray(criteriaHistory.issueProjectStaffIds) ?? []
  searchCondition.issueProjectTeams =
      convertStringArrayToNumberArray(criteriaHistory.issueProjectTeams) ?? []
  searchCondition.discussionTypeIds =
      convertToStringArray(criteriaHistory.discussionTypeIds) ?? []

  // ソート条件を保持されているカタチからオブジェクトのカタチに変換します。
  function convertToSortFormat(querySort: string[] | string | undefined) {
    const sort = Array.isArray(querySort)
      ? querySort
      : !querySort
          ? []
          : [querySort]
    return (sort ?? []).map((s) => {
      const [key, order] = s.split(' ')
      return {
        key,
        order: order === 'desc' ? 'desc' : ('asc' as 'asc' | 'desc'),
      }
    })
  }
  pagination.pageIndex = parseFloat(criteriaHistory.pageIndex) || 1
  pagination.pageSize = parseFloat(criteriaHistory.pageSize) || 20
  pagination.sort = convertToSortFormat(criteriaHistory.sort)
}

// 検索条件を組み立てます。
function createRequestSearchCondition() {
  const condition = Object.assign({}, searchCondition)
  // 型チェックを行い、fromDateがundefinedでないことを確認
  if (condition.fromDate) {
    const fromDate = startOfDay(condition.fromDate)
    condition.fromDate = fromDate.toISOString() // UTC文字列に変換
  }
  if (condition.toDate) {
    const toDate = endOfDay(condition.toDate)
    condition.toDate = toDate.toISOString() // UTC文字列に変換
  }

  return condition
}

/**
   *  ドロップダウンリストの設定
   */

async function setOptions() {
  const [
    getDiscussionTypeResult,
    getIssueProjectOptionsResult,
    getStaffAndTeamOptionsResult,
  ] = await Promise.all([
    getIssueProjectDiscussionOptions(),
    getIssueProjectOptions(),
    getStaffAndTeamOptions(),
  ])
  if (
    !getDiscussionTypeResult ||
    !getIssueProjectOptionsResult ||
    getStaffAndTeamOptionsResult.length === 0
  ) {
    errorToast('検索条件の選択肢が取得できませんでした。リロードしてください。')
    return false
  }

  dataRef.value.options.discussionTypeList =
      getIssueProjectDiscussionOptionsData.value.map(
        (val: IssueProjectDiscussionTypeItem) => {
          return {
            title: val.text,
            value: val.value,
          }
        },
      )
  dataRef.value.options.staff = getStaffAndTeamOptionsResult

  return true
}

const handleKeyDown = (event: { key: string }) => {
  if (event.key === 'Control') {
    isControlPressed.value = true
  }
}

const handleKeyUp = (event: { key: string }) => {
  if (event.key === 'Control') {
    isControlPressed.value = false
  }
}

/**
   * 課題案件協議画面に遷移します。
   */
function toDetail(e: any) {
  const currentUrl = new URL(window.location.href)
  const path = `/case/issue-project/issue-project-discussion?issueProjectId=${e.row.item.issueProjectId}&customerIdentificationId=${e.row.item.customerIdentificationId}&from=top-issueProjectDiscussionTab`

  const routeLocation = {
    path: currentUrl.origin + path,
  }

  // ctrlキーを押しながらクリックした場合は別タブで開く
  if (isControlPressed.value) {
    return navigateTo(routeLocation, {
      open: { target: '_blank' },
      external: true,
    })
  } else {
    return navigateTo(routeLocation, {
      external: true,
    })
  }
}

watch(
  () => data.value?.items,
  (searchApiResult) => {
    dataRef.value.items =
        searchApiResult.map((item: FindIPDTCrossCustomerQueryResult) => ({
          ...item,
          issueProjectTeam: item.issueProjectTeam
            ? issueProjectTeamList.find(
              (x) => x.value === Number(item.issueProjectTeam),
            )?.title || '-'
            : '-',
        })) ?? []
  },
)

/**
   * スレッドの添付ファイルのダウンロード
   */

const { threadDataRef } = useThreadShareData()

const downloadThreadFile = async (threadId: string, fileName: string) => {
  threadDataRef.value.loading = true

  // APIクライアントの定義(スレッドのファイルダウンロード)
  const { data: downloadData, executeWithResult: downloadThreadFile } =
      useDownloadIssueProjectDiscussionThreadFile(
        ref<downloadIssueProjectDiscussionThreadFile>({
          threadId,
          fileName,
        }),
      )

  const result = await downloadThreadFile()

  // APIがエラーだった場合は処理を中断します。
  if (!result) {
    return
  }

  // Blobオブジェクトの作成
  const blob = new Blob([downloadData.value])
  const link = document.createElement('a')
  // リンク生成
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  // リンククリック実行
  link.click()
  // Blobへの参照を削除する
  URL.revokeObjectURL(link.href)

  threadDataRef.value.loading = false
}

/**
   * コメントの添付ファイルのダウンロード
   */
const downloadCommentFile = async (commentId: string, fileName: string) => {
  threadDataRef.value.loading = true

  const { data: commentFileData, executeWithResult: downloadCommentFile } =
      useDownloadIssueProjectDiscussionCommentFile(
        ref<downloadIssueProjectDiscussionCommentFile>({
          commentId,
          fileName,
        }),
      )

  await downloadCommentFile().catch((e) => {
    // 失敗だった場合の処理を行います。
    if (!e.hasProblemDetails) {
      errorToast('コメントの添付ファイルのダウンロードに失敗しました。')
    }
    return false
  })

  if (!commentFileData) return

  // Blobオブジェクトの作成
  const blob = new Blob([commentFileData.value])
  const link = document.createElement('a')
  // リンク生成
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  // リンククリック実行
  link.click()
  // Blobへの参照を削除する
  URL.revokeObjectURL(link.href)

  threadDataRef.value.loading = false
}
</script>

  <style scoped>
  .mask-item {
    background-color: #ddd;
  }

  .expand-thread {
    margin-top: 10px;
    max-height: 500px;
    overflow: auto;
    overflow-x: hidden;
  }
  </style>
