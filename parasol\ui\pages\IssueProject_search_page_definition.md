# ページ定義

## 基本情報

- ページ名 : 課題案件一覧
- UIパターン : 検索画面（search）

## 実現対象ハイレベルユースケース

- 課題案件を管理する
  - 課題案件の検索
  - 課題案件の一覧表示
  - 課題案件詳細の確認
  - 課題案件編集画面への遷移

## 共通処理（ミドルウェア）定義

- [ ] アクセスログ出力
- [ ] アクセス可否チェック
- [ ] 次画面パスチェック

## パラメータ定義

| パラメータ名 | 種別 | 必須 | 型 | 説明 |
|---|---|---|---|---|
| customerIdentificationId | query | | 文字列 | 顧客識別ID（指定時は顧客理解レイアウトで表示） |
| from | query | | 文字列 | 遷移元画面識別子 |
| tab | query | | 文字列 | 表示するタブ（case, issue-project, issue-project-task, issue-project-discussion） |

## UI定義

### タブ表示
- 案件一覧タブ
    - 説明 : 一般的な案件の検索・一覧表示
- **課題案件一覧タブ（現在表示中）**
    - 説明 : 課題案件の検索・一覧表示
- 課題案件タスク一覧タブ
    - 説明 : 課題案件のタスク一覧表示（顧客識別時のみ表示）
- 課題案件協議一覧タブ
    - 説明 : 課題案件の協議一覧表示（顧客識別時のみ表示）

### 検索条件
- 店番
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 取引先名
    - 入力形式 : テキストボックス
    - 説明 : 部分一致検索
- 顧客担当者
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 業種詳細
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 課題項目
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 概要
    - 入力形式 : テキストボックス
    - 説明 : 部分一致検索
- 案件ステータス
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 案件担当者
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 案件担当チーム
    - 入力形式 : プルダウンリスト
    - 説明 : 複数選択可能、チップ表示
- 登録日From
    - 入力形式 : 日付選択
    - 説明 : "yyyy/MM/dd"形式
- 登録日To
    - 入力形式 : 日付選択
    - 説明 : "yyyy/MM/dd"形式
- 検索ボタン
    - 説明 : ※イベント処理参照

### 注意事項表示
- 検索条件注意事項
    - 説明 : 課題項目または概要を指定する場合、表示制限先は検索結果に出力されない旨を表示

### 検索結果一覧
- 表示項目
    - 課題No（issueNumber）
    - 登録日（registeredDateTime）
        - 説明 : "yyyy/MM/dd"形式で表示
    - 店番（branchNumber）
    - 取引先名（customerName）
    - 業種詳細（industryDetail）
    - 課題項目（issueItem）
        - 説明 : アクセス制限対象の場合はマスキング表示
    - 課題概要（overview）
        - 説明 : アクセス制限対象の場合はマスキング表示
    - 案件ステータス（status）
    - 完了期日（dueDate）
        - 説明 : "yyyy/MM/dd"形式で表示
    - 案件担当チーム（issueProjectTeam）
    - 案件担当者（staffName）
    - 顧客担当者（customerStaffName）
    - 最終更新日（updatedDateTime）
        - 説明 : "yyyy/MM/dd"形式で表示
- 機能
    - 行クリック編集
        - 説明 : 課題案件編集画面に遷移
    - ページング
        - 説明 : 20件単位でページング表示
    - ソート機能
        - ソート可能項目 : 全項目

## イベント処理定義

### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 検索条件選択肢を取得し表示する | ライフサイクルフック mounted |
| 検索ボタン押下 | 入力された条件で課題案件を検索し結果を表示する | function search |
| 編集ボタン押下/行クリック | 課題案件編集画面に遷移する | function edit |
| 検索条件クリア | 検索条件と結果をクリアする | function clear |
| 検索条件復元 | URLヒストリから検索条件を復元する | function criteriaResotred |

### 初期表示（mounted）
1. 店番オプション一覧を取得する。
2. 担当者・チームオプション一覧を取得する。
3. 課題案件固有のオプション（業種詳細、課題項目、ステータス）を取得する。
4. 検索条件の選択肢を設定する。

### 検索ボタン押下（search）
1. 入力データのバリデーションを実行する。
2. 初期検索フラグをチェックし、falseの場合は検索を実行しない。
3. 課題案件検索APIを呼び出し、結果を取得する。
4. 検索結果を整形し、一覧に表示する。
5. アクセス制限対象の課題項目・概要はマスキング表示する。
6. 検索履歴をブラウザに保存する。

### 編集ボタン押下/行クリック（edit）
1. 対象課題案件のIDをパラメータとして、課題案件編集画面に遷移する。

### 検索条件クリア（clear）
1. 検索条件と過去の検索履歴をクリアする。
2. 検索結果一覧をクリアする。

### 検索条件復元（criteriaResotred）
1. URLクエリパラメータから検索条件を復元する。
2. 日時条件（fromDate, toDate）をDateオブジェクトに変換する。
3. 復元完了を通知する。
