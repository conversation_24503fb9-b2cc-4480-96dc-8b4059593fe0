/**
 * インタラクションカテゴリーグループ
 */
export const INTERACTION_CATEGORY_GROUP = {
  Undefined: '未定義',
  BusinessUnderstandingAndRelationLevel: '事業性理解・リレーションレベル',
  BusinessModel: '理解状況(商流図・SWOT等)',
  BusinessUnderstandingInternalDiscussion: '社内協議の管理',
  BusinessUnderstandingExternalDiscussion: '社外協議の管理',
  OurPolicyUnderstanding: '当社の方針・施策をご理解いただく',
  CustomerIdeasUnderstanding: 'お客さまの考え方を理解する',
  SharingOfFinance: '財務の共有',
  HypotheticalDiscussionOfIssue: '課題の仮説協議',
  Todo: 'ToDo',
  OurPolicyUnderstandingInternalDiscussion: '当社の方針・施策をご理解いただくの社内協議',
  CustomerIdeasUnderstandingInternalDiscussion: 'お客さまの考え方を理解するの社内協議',
  SharingOfFinanceInternalDiscussion: '財務の共有の社内協議',
  HypotheticalDiscussionOfIssueInternalDiscussion: '課題の仮説協議の社内協議',
  TodoInternalDiscussion: 'ToDoの社内協議',
  OurPolicyUnderstandingExternalDiscussion: '当社の方針・施策をご理解いただくの社外協議',
  CustomerIdeasUnderstandingExternalDiscussion: 'お客さまの考え方を理解するの社外協議',
  SharingOfFinanceExternalDiscussion: '財務の共有の社外協議',
  HypotheticalDiscussionOfIssueExternalDiscussion: '課題の仮説協議の社外協議',
  TodoExternalDiscussion: 'ToDoの社外協議',
  IssueProject: '課題案件',
  LoanCase: '融資案件',
  LeaseCase: 'リース案件',
  GeneralTransactionCase: '総合取引案件',
  CorporateInsuranceCase: '法人保険案件',
  InvestmentCase: '投資案件',
  BusinessMatchingCase: 'ビジネスマッチング案件',
  IssueProjectInternalDiscussion: '課題案件の社内協議',
  LoanCaseInternalDiscussion: '融資案件の社内協議',
  LeaseCaseInternalDiscussion: 'リース案件の社内協議',
  GeneralTransactionCaseInternalDiscussion: '総合取引案件の社内協議',
  CorporateInsuranceCaseInternalDiscussion: '法人保険案件の社内協議',
  InvestmentCaseInternalDiscussion: '投資案件の社内協議',
  BusinessMatchingCaseInternalDiscussion: 'ビジネスマッチング案件の社内協議',
  IssueProjectExternalDiscussion: '課題案件の社外協議',
  LoanCaseExternalDiscussion: '融資案件の社外協議',
  LeaseCaseExternalDiscussion: 'リース案件の社外協議',
  GeneralTransactionCaseExternalDiscussion: '総合取引案件の社外協議',
  CorporateInsuranceCaseExternalDiscussion: '法人保険案件の社外協議',
  InvestmentCaseExternalDiscussion: '投資案件の社外協議',
  BusinessMatchingCaseExternalDiscussion: 'ビジネスマッチング案件の社外協議',
  Online: 'オンライン',
  Seminar: 'セミナー',
} as const

export type InteractionCategoryGroupType = keyof typeof INTERACTION_CATEGORY_GROUP

export const interactionCategoryGroupKeys = Object.keys(
  INTERACTION_CATEGORY_GROUP,
) as InteractionCategoryGroupType[]
