# page vue file generation prompt

- this is the prompt for creating nuxt 3 vue 3 page file.

## input file

- src\packages\template\src\pages\employee_template.vue
- a page vue file of Nuxt2 project

## prompt

- Please update `VUE PAGE FILE` and `RELATIVE PATH` to actual name and path.

```md
Please generate a Nuxt 3 page Vue file using the Composition API.

## Requirements:

- The page should replicate the content, structure, and behavior of an existing Nuxt 2 Vue file named `VUE PAGE FILE`. Ensure the layout, props, methods, and template structure are equivalent.
- Use the `employee_template.vue` file as a reference for coding style and structure.
- Follow Nuxt 3 conventions, including usage of `<script setup>`, `useRoute`, `useFetch`, and other Composition API utilities where appropriate.
- Save the generated file to the following relative path: `RELATIVE PATH`.

## Notes:

- Include all necessary imports and logic in Composition API style.
- If there are any unclear parts in the Nuxt 2 file that cannot be translated directly, mark them with TODO comments in the generated code.
```

