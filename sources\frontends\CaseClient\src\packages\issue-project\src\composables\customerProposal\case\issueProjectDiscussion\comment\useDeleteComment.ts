import type { IssueProjectDiscussionCommentForDelete } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionComment'
import { useDeleteIssueProjectDiscussionComment } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionComment'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type {
  FindIssueProjectDiscussionThreadResultItem,
  issueProjectDiscussionCommentType,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'

export const useDeleteComment = () => {
  const { error: errorToast, success: successToast } = useAppToasts()
  const { init, hasChanged, restart } = useWatchDataChanges()
  const watcher = { init, hasChanged, restart }

  //
  // コメント削除
  //
  const deleteComment = async (
    sendData: IssueProjectDiscussionCommentForDelete,
    comment: issueProjectDiscussionCommentType,
    refThreadsData: FindIssueProjectDiscussionThreadResultItem[],
  ) => {
    const { executeWithResult: deleteComment } =
      useDeleteIssueProjectDiscussionComment(
        ref<IssueProjectDiscussionCommentForDelete>(sendData),
      )

    const result = await deleteComment().catch((e) => {
      // 失敗だった場合の処理を行います。
      if (!e.hasProblemDetails) {
        errorToast('コメントの削除に失敗しました。')
      }
      if (e.data.type === '/validation-error') {
        errorToast(e.data.errors)
      } else if (e.data.type === '/conflict') {
        errorToast(
          'すでに別のユーザーがデータを削除しているためコメントを削除できませんでした。',
        )
      } else {
        errorToast('コメントの削除に失敗しました。')
      }
      return false
    })

    // APIがエラーだった場合は処理を中断します。
    if (!result) return

    // スレッド検索
    const targetThreadIndex = refThreadsData.findIndex(
      (x) => x.id === comment.threadId,
    )

    // targetDataから削除
    const commentIndex = refThreadsData[targetThreadIndex].comments.findIndex(
      (x) => x.id === comment.id,
    )

    refThreadsData[targetThreadIndex].comments.splice(commentIndex, 1)

    successToast('コメントを削除しました。')
    watcher.init(refThreadsData)
    return true
  }

  return {
    deleteComment,
  }
}
