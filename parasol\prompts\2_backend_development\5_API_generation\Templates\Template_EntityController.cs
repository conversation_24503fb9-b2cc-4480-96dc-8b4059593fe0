// TODO: サービスの名前空間を変更してください。
using Service.UseCases.Employee.AddEmployee;
using Service.UseCases.Employee.BatchDeleteEmployee;
using Service.UseCases.Employee.DeleteEmployee;
using Service.UseCases.Employee.FindEmployee;
using Service.UseCases.Employee.GetEmployee;
using Service.UseCases.Employee.GetEmployeeQualification;
using Service.UseCases.Employee.UpdateEmployee;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Nut.Results;
using Shared.AspNetCore.Mvc;

// TODO: 名前空間をプロジェクトにあわせて変更してください。
namespace Api.Controllers;

[ApiController]
[Route("v{version:apiVersion}/[controller]")]
public class EmployeeController(IMediator mediator) : ControllerBase
{
    [HttpGet]
    public Task<IActionResult> Find([FromQuery] FindEmployeeQuery query)
        => mediator.Send(query).Map(r => Ok(r)).ToApiResult();

    [HttpGet("{id}")]
    public Task<IActionResult> Get(string id)
        => mediator.Send(new GetEmployeeQuery(id)).Map(r => Ok(r)).ToApiResult();

    [HttpPost(":deleteAll")]
    public Task<IActionResult> DeleteAll([FromBody] IEnumerable<BatchDeleteEmployeeCommandItem> items)
        => mediator.Send(new BatchDeleteEmployeeCommand(items.ToList()))
            .FlatMap(() => Result.Ok(NoContent())).ToApiResult();

    [HttpPost]
    public Task<IActionResult> Post([FromBody] AddEmployeeCommand command)
        => mediator.Send(command)
            .FlatMap(id => mediator.Send(new GetEmployeeQuery(id))) // IDをもとに新しい結果を取得
            .Map(r => Created(string.Empty, r)).ToApiResult();

    [HttpPut]
    public Task<IActionResult> Put([FromBody] UpdateEmployeeCommand command)
        => mediator.Send(command)
            .FlatMap(id => mediator.Send(new GetEmployeeQuery(id))) // IDをもとに新しい結果を取得
            .Map(r => Ok(r))
            .ToApiResult();

    [HttpDelete]
    public Task<IActionResult> Delete([FromBody] DeleteEmployeeCommand command)
        => mediator.Send(command)
            .FlatMap(_ => Result.Ok(NoContent()))
            .ToApiResult();

    [HttpGet("{employeeId}/qualifications")]
    public Task<IActionResult> GetQualifications(string employeeId)
        => mediator.Send(new GetEmployeeQualificationQuery(employeeId))
            .Map(r => Ok(r))
            .ToApiResult();
}
