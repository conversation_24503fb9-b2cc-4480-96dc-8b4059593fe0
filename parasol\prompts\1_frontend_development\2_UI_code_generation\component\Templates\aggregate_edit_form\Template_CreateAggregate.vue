<!--
===========================================
アグリゲート作成コンポーネント テンプレート
===========================================

このテンプレートは、アグリゲートルートエンティティの作成画面コンポーネントの
基本構造を提供します。

【使用方法】
1. TODOコメントに従って、エンティティ固有の情報に置き換える
2. 不要なセクションは削除し、必要なセクションを追加する
3. 各セクションのコンポーネント名を実際のエンティティに合わせて変更する
4. パスやcomposable名をエンティティに応じて変更する
-->

<script setup lang="ts">
// 共通のcomposablesをインポート
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import { confirmBeforeUnload } from '@hox/base/src/utils/shared/confirmBeforeUnload'
import type { SectionConfig } from '@hox/base/src/components/shared/templates/AppEditPageTmpl.vue'
import { layoutOption } from '@ibp/base/src/constants/layout'

// TODO: エンティティの型をインポート（例: Sample → YourEntity）
import type { Sample } from '../constants/domain/entities/Sample'
// TODO: エンティティ固有のcomposablesをインポート（例: useCreateSample → useCreateYourEntity）
import { useCreateSample } from './useCreateSample'

// TODO: UI定義の内容に応じてセクション構成を変更してください
const sections: SectionConfig[] = [
  {
    key: 'basic',
    title: '基本情報',
  },
  // TODO: 以下は例です。実際のエンティティに合わせて変更してください
  {
    key: 'detail',
    title: '詳細情報',
  },
  {
    key: 'settings',
    title: '設定',
  },
]

// 共通のprops定義
const props = defineProps<{
  customerIdentificationId: string
  caseId: string
}>()

const caseId = toRef(props, 'caseId')
const customerIdentificationId = toRef(props, 'customerIdentificationId')

const inProgress = ref(true)

// TODO: エンティティ固有のcomposableを使用（例: useCreateSample → useCreateYourEntity）
// 返り値のプロパティ名もエンティティに応じて変更（例: SampleModel → yourEntityModel）
const {
  SampleModel, // TODO: yourEntityModel に変更
  inProgress: inProgressForCreate,
  errorMessages,
  fetchSampleCase, // TODO: fetchYourEntityCase に変更
  applySampleCase, // TODO: applyYourEntityCase に変更
  validateItem,
} = useCreateSample({
  caseId,
  customerIdentificationId,
})

// ローディング状態の管理（通常変更不要）
const { hasTrue: loading } = useFlagCondition(
  inProgress,
  inProgressForCreate,
)

// データ変更監視（通常変更不要）
const { hasChanged, init: initWatchDataChanges } = useWatchDataChanges()

// ページ離脱前の確認（通常変更不要）
confirmBeforeUnload(hasChanged, '')

// TODO: 必要に応じてアクション関数を定義してください
// 例: 申請、一時保存、キャンセルなど、エンティティに応じたアクション
/*
async function onApply() {
  // 申請処理
  try {
    const result = await applyYourEntityCase()
    // 成功時の処理（画面遷移など）
    await navigateTo({ path: '/your-entity/list' })
  } catch (error) {
    // エラーハンドリング
  }
}

async function onDraft() {
  // 一時保存処理
  try {
    await draftYourEntity()
    // 成功メッセージ表示など
  } catch (error) {
    // エラーハンドリング
  }
}

function onCancel() {
  // キャンセル処理（前画面に戻る）
  history.back()
}
*/

// コンポーネントマウント時の初期化処理
onMounted(async () => {
  try {
    // TODO: 関数名をエンティティに応じて変更、不要な場合は削除
    const [SampleCase] = await Promise.all([
      fetchSampleCase(), // TODO: fetchYourEntityCase() に変更
    ])
    // TODO: 関数名とケース変数名をエンティティに応じて変更
    applySampleCase(
      SampleCase,
    )
    // TODO: モデル名をエンティティに応じて変更
    initWatchDataChanges(SampleModel)
  } finally {
    inProgress.value = false
  }
})
</script>
<template>
  <!-- 編集ページテンプレート（通常変更不要） -->
  <app-edit-page-tmpl
    :sections
    :loading
    :can-save="false"
    :can-remove="false"
    :header-top-offset="layoutOption.CUSTOMER_HEADER_TOP_OFFSET"
  >
    <!-- メインアクション（操作ボタンエリア） -->
    <template #main-actions>
      <!-- TODO: エンティティに応じてボタンを調整・実装してください -->
      <!-- 
      アクションボタンの例:
      <app-main-btn density="compact" :loading @click="onApply">申請</app-main-btn>
      <app-sub-btn density="compact" :loading @click="onDraft">一時保存</app-sub-btn>
      <app-sub-btn density="compact" @click="onCancel">キャンセル</app-sub-btn>
      -->
    </template>
    
    <!-- 基本情報セクション -->
    <!-- TODO: `<template #section-[セクションのキー]>` -->
    <template #section-basic>
      <!-- TODO: 作成したコンポーネント名に応じて変更 -->
      <!-- 例: SampleFormコンポーネントの場合 -->
      <sample-form
        :item="SampleModel"
        :loading
      />
    </template>
    
    <!-- 詳細セクション -->
    <!-- TODO: `<template #section-[セクションのキー]>` -->
    <template #section-detail>
      <!-- TODO: 作成したコンポーネント名に応じて変更 -->
      <!-- 例: SampleDetailFormコンポーネントの場合 -->
      <sample-detail-form
        v-model="SampleModel"
        :error-messages
        :loading
        @validate="validateItem"
      />
    </template>
    
    <!-- 設定セクション -->
    <!-- TODO: `<template #section-[セクションのキー]>` -->
    <template #section-settings>
      <!-- TODO: 作成したコンポーネント名に応じて変更 -->
      <!-- 例: SampleSettingFormコンポーネントの場合 -->
      <sample-setting-form
        v-model="SampleModel"
        :error-messages
        :loading
        @validate="validateItem"
      />
    </template>
  </app-edit-page-tmpl>
</template>

<!--
===========================================
テンプレート使用時の変更点チェックリスト
===========================================

【必須変更事項】
□ エンティティの型定義をインポート（Sample → YourEntity）
□ エンティティ固有のcomposablesをインポート（useCreateSample → useCreateYourEntity）
□ sectionsの構成を実際のエンティティに応じて完全に書き換え
□ composableの返り値プロパティ名をエンティティに応じて変更（xxxModel、fetchXxxCase等）
□ onMounted内の関数呼び出しをエンティティに応じて変更
□ templateセクションを実際のエンティティ用に完全に書き換え
□ main-actionsテンプレート内にアクションボタンを実装

【オプション変更事項】
□ 必要なアクション関数を追加（エンティティ固有の操作）
□ 各Formコンポーネントのpropsの調整（:schema-shapeなど特殊なpropsがある場合）
-->