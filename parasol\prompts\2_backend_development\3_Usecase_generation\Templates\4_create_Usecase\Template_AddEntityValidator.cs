using FluentValidation;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.AddEntity;

public class AddEntityValidator : AbstractValidator<AddEntityCommand>
{
    public AddEntityValidator()
    {
        // TODO: バリデーションの定義を追加します。
        RuleFor(v => v.Name).NotEmpty().MaximumLength(100);
        RuleFor(v => v.PeriodFrom).LessThanOrEqualTo(v => v.PeriodTo);
        RuleFor(v => v.Amount).GreaterThanOrEqualTo(0);
        RuleForEach(t => t.Members).ChildRules(item =>
        {
            item.RuleFor(c => c.MemberId).NotEmpty();
            item.RuleFor(c => c.UnitPricePerHour).GreaterThanOrEqualTo(0);
        });
    }
}
