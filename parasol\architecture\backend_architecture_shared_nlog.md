### Shared.NLog

## 概要

Shared.NLogライブラリは、NLogを使用したログ出力の拡張機能を提供するライブラリです。例外情報の整形、JSON形式でのログ出力、ログ解析の効率化などの機能を提供し、NLogを使用したログ出力アプリケーションの品質向上と運用効率化を支援します。

## 主要機能

### 例外処理
- **例外ラップ**: 例外情報のラップ・整形
- **詳細情報**: スタックトレース、内部例外の詳細管理
- **ログ標準化**: 例外ログ出力の標準化

### JSON形式出力
- **JSON整形**: 例外情報のJSON形式での出力
- **構造化ログ**: 構造化されたログ情報の提供
- **解析効率**: ログ解析ツールでの効率的な処理

### ログ品質向上
- **一貫性**: ログ出力形式の一貫性確保
- **可読性**: 人間が読みやすいログ形式
- **検索性**: ログ検索・フィルタリングの効率化

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| ExceptionWrapper | 例外ラップ | 例外情報のラップ・整形 | ログ出力の標準化 |
| JsonExceptionLayoutRenderer | JSON出力 | 例外情報のJSON整形 | ログ解析効率化 |
