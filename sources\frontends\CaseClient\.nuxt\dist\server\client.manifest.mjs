export default {
  "@vite/client": {
    "prefetch": true,
    "isEntry": true,
    "file": "@vite/client",
    "css": [],
    "module": true,
    "resourceType": "script"
  },
  "D:/aspire/ibp-case-app-fork/sources/frontends/CaseClient/node_modules/nuxt/dist/app/entry.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "isEntry": true,
    "file": "D:/aspire/ibp-case-app-fork/sources/frontends/CaseClient/node_modules/nuxt/dist/app/entry.js"
  }
}