using MediatR;
using Nut.Results;
using SampleService.Domain;
using Shared.Domain;
using Shared.Spec;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.GetEntity;

public class GetEntityHandler(IRepositoryFactory repositoryFactory) : IRequestHandler<GetEntityQuery, Result<GetEntityResult>>
{
    public Task<Result<GetEntityResult>> Handle(GetEntityQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request);

        // TODO: 利用するリポジトリに置き換えてください。
        var repository = repositoryFactory.GetRepository<Domain.Entities.Entity>();
        // TODO: キー項目が違う場合は条件を修正してください。
        var spec = new FindByIdSpecification<Domain.Entities.Entity, string>(request.Id).AsNoTracking().AsSplitQuery().Include(v => v.Members, m =>
        {
            m.ThenInclude(v => v.Member);
        });
        return repository.SingleAsync(spec)
            .Map(v => new GetEntityResult(
                v.Id,
                v.Name, v.PeriodFrom, v.PeriodTo, v.Amount, v.IsAssumption, v.Version,
                v.Members.Select(m => new GetEntityResultDetail(m.Id, m.Member.Id, m.Member.Name, m.UnitPricePerHour)).ToList()));
    }
}
