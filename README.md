# Introduction

IBPのサービス立ち上げ時にあらかじめ必要な変更が入った状態でソースを展開します

アーキテクチャテンプレートの展開機能に加え、以下の変更が行われます
* バックエンド
    * Aspireに必要なServiceDefaultプロジェクトの作成
    * ローカル実行時ポートの設定
    * CurrentUserクラスの変更
    * ApiManagement/KeyVaultの設定
    * 認証設定
* フロントエンド
    * baseUrlの設定
    * レイヤーの設定
    * 認証設定

# Getting Started

* [ここ](https://portal.azure.com/#@digitalvaluedev.onmicrosoft.com/resource/subscriptions/4b430ca8-e54e-4ec4-8b81-115e986c278f/resourceGroups/rg-crm-jpeast/providers/Microsoft.Storage/storageAccounts/stibptemplate/storagebrowser)から最新版のテンプレートパッケージをダウンロードしてインストール
* 以下のコマンドでパラメーターを確認

    ```ps
    dotnet new IBP-sln -h
    ```

* パラメーターを指定してテンプレートを展開
* テンプレートのインストールなどの操作は[こちら](https://dev.azure.com/digital-v/app-architecture-template/_wiki/wikis/docs/25238/%E3%83%86%E3%83%B3%E3%83%97%E3%83%AC%E3%83%BC%E3%83%88)を参照