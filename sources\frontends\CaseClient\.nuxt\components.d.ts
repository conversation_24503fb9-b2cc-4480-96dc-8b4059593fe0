
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
interface _GlobalComponents {
      'CaseCsvUploadDialog': typeof import("../src/packages/common-case/src/components/import/CaseCsvUploadDialog.vue")['default']
    'CaseImportHistoryCard': typeof import("../src/packages/common-case/src/components/import/CaseImportHistoryCard.vue")['default']
    'CaseTable': typeof import("../src/packages/common-case/src/components/loan-and-lease/CaseTable.vue")['default']
    'SearchCriteria': typeof import("../src/packages/common-case/src/components/loan-and-lease/SearchCriteria.vue")['default']
    'FileUploader': typeof import("../src/packages/common-case/src/components/organisms/FileUploader.vue")['default']
    'BoolDotIndicator': typeof import("../src/packages/common-case/src/components/share/BoolDotIndicator.vue")['default']
    'CustomNoDataMessage': typeof import("../src/packages/common-case/src/components/share/CustomNoDataMessage.vue")['default']
    'SearchResultArea': typeof import("../src/packages/common-case/src/components/share/SearchResultArea.vue")['default']
    'IssueProjectDiscussionDate': typeof import("../src/packages/issue-project/src/components/customer-proposal/molecules/issue-project-discussion/IssueProjectDiscussionDate.vue")['default']
    'IssueProjectDiscussionComment': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionComment.vue")['default']
    'IssueProjectDiscussionCommentEditor': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionCommentEditor.vue")['default']
    'IssueProjectDiscussionThread': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThread.vue")['default']
    'IssueProjectDiscussionThreadEditor': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThreadEditor.vue")['default']
    'IssueProjectLink': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLink.vue")['default']
    'IssueProjectLinkAddDialog': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLinkAddDialog.vue")['default']
    'IssueProjectDiscussion': typeof import("../src/packages/issue-project/src/components/customer-proposal/pages/IssueProjectDiscussion.vue")['default']
    'RichTextEditor': typeof import("../src/packages/issue-project/src/components/shared/RichTextEditor.vue")['default']
    'ReactionMember': typeof import("../src/packages/issue-project/src/components/shared/molecules/Reaction/ReactionMember.vue")['default']
    'ReactionView': typeof import("../src/packages/issue-project/src/components/shared/molecules/Reaction/ReactionView.vue")['default']
    'ConfirmOverwriteDialog': typeof import("../src/packages/issue-project/src/components/shared/organisms/ConfirmOverwriteDialog.vue")['default']
    'Case': typeof import("../src/packages/case-work-flow-optimization/src/components/case/Case.vue")['default']
    'CaseSearchCriteria': typeof import("../src/packages/case-work-flow-optimization/src/components/case/CaseSearchCriteria.vue")['default']
    'IdentifiedCase': typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCase.vue")['default']
    'IdentifiedCaseSearchCriteria': typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCaseSearchCriteria.vue")['default']
    'IdentifiedCaseTable': typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCaseTable.vue")['default']
    'SelectCaseTypeDialog': typeof import("../src/packages/case-work-flow-optimization/src/components/customer-case/SelectCaseTypeDialog.vue")['default']
    'LeaseSearchCriteria': typeof import("../src/packages/lease/src/components/new-lease/LeaseSearchCriteria.vue")['default']
    'LeaseTable': typeof import("../src/packages/lease/src/components/new-lease/LeaseTable.vue")['default']
    'BellIcon': typeof import("../node_modules/@ibp/base/src/components/Icon/BellIcon.vue")['default']
    'AppIconBtn': typeof import("../node_modules/@ibp/base/src/components/Layout/AppIconBtn.vue")['default']
    'BaseLayout': typeof import("../node_modules/@ibp/base/src/components/Layout/BaseLayout.vue")['default']
    'BasicCard': typeof import("../node_modules/@ibp/base/src/components/Layout/BasicCard.vue")['default']
    'CustomIcon': typeof import("../node_modules/@ibp/base/src/components/Layout/CustomIcon.vue")['default']
    'CustomerHeader': typeof import("../node_modules/@ibp/base/src/components/Layout/CustomerHeader.vue")['default']
    'CustomerStaffMenu': typeof import("../node_modules/@ibp/base/src/components/Layout/CustomerStaffMenu.vue")['default']
    'EditableCustomerStaffMenu': typeof import("../node_modules/@ibp/base/src/components/Layout/EditableCustomerStaffMenu.vue")['default']
    'InformationIcon': typeof import("../node_modules/@ibp/base/src/components/Layout/InformationIcon.vue")['default']
    'SearchCustomerStaffUserDialog': typeof import("../node_modules/@ibp/base/src/components/Layout/SearchCustomerStaffUserDialog.vue")['default']
    'MenuCards': typeof import("../node_modules/@ibp/base/src/components/Menu/MenuCards.vue")['default']
    'NotificationCircle': typeof import("../node_modules/@ibp/base/src/components/Notification/NotificationCircle.vue")['default']
    'TimeLine': typeof import("../node_modules/@ibp/base/src/components/TimeLine/TimeLine.vue")['default']
    'AppAutocomplete': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppAutocomplete.vue")['default']
    'AppBtn': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppBtn.vue")['default']
    'AppDangerousBtn': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppDangerousBtn.vue")['default']
    'AppFileInput': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppFileInput.vue")['default']
    'AppMainBtn': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppMainBtn.vue")['default']
    'AppNumberField': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppNumberField.vue")['default']
    'AppPopupDateOnlyPicker': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppPopupDateOnlyPicker.vue")['default']
    'AppPopupDatePicker': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppPopupDatePicker.vue")['default']
    'AppSimpleDataTable': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleDataTable.vue")['default']
    'AppSimpleOptGroupSelect': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleOptGroupSelect.vue")['default']
    'AppSimpleSelect': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleSelect.vue")['default']
    'AppSubBtn': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSubBtn.vue")['default']
    'AppTextField': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppTextField.vue")['default']
    'AppTextarea': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppTextarea.vue")['default']
    'UseSharedBtnAttr': typeof import("../node_modules/@hox/base/src/components/shared/atom/useSharedBtnAttr")['default']
    'AppConfirmDialog': typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppConfirmDialog.vue")['default']
    'AppListEdit': typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppListEdit.vue")['default']
    'AppToasts': typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppToasts.vue")['default']
    'Error': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error.vue")['default']
    'Error403': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error403.vue")['default']
    'Error404': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error404.vue")['default']
    'Error504': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error504.vue")['default']
    'Unknown': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Unknown.vue")['default']
    'AppEditItemGroupTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppEditItemGroupTmpl.vue")['default']
    'AppEditPageTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppEditPageTmpl.vue")['default']
    'AppSearchDialogTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSearchDialogTmpl.vue")['default']
    'AppSearchPageTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue")['default']
    'AppSimpleDialogTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSimpleDialogTmpl.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'ClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
    'NuxtPicture': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
    'NuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyCaseCsvUploadDialog': typeof import("../src/packages/common-case/src/components/import/CaseCsvUploadDialog.vue")['default']
    'LazyCaseImportHistoryCard': typeof import("../src/packages/common-case/src/components/import/CaseImportHistoryCard.vue")['default']
    'LazyCaseTable': typeof import("../src/packages/common-case/src/components/loan-and-lease/CaseTable.vue")['default']
    'LazySearchCriteria': typeof import("../src/packages/common-case/src/components/loan-and-lease/SearchCriteria.vue")['default']
    'LazyFileUploader': typeof import("../src/packages/common-case/src/components/organisms/FileUploader.vue")['default']
    'LazyBoolDotIndicator': typeof import("../src/packages/common-case/src/components/share/BoolDotIndicator.vue")['default']
    'LazyCustomNoDataMessage': typeof import("../src/packages/common-case/src/components/share/CustomNoDataMessage.vue")['default']
    'LazySearchResultArea': typeof import("../src/packages/common-case/src/components/share/SearchResultArea.vue")['default']
    'LazyIssueProjectDiscussionDate': typeof import("../src/packages/issue-project/src/components/customer-proposal/molecules/issue-project-discussion/IssueProjectDiscussionDate.vue")['default']
    'LazyIssueProjectDiscussionComment': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionComment.vue")['default']
    'LazyIssueProjectDiscussionCommentEditor': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionCommentEditor.vue")['default']
    'LazyIssueProjectDiscussionThread': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThread.vue")['default']
    'LazyIssueProjectDiscussionThreadEditor': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThreadEditor.vue")['default']
    'LazyIssueProjectLink': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLink.vue")['default']
    'LazyIssueProjectLinkAddDialog': typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLinkAddDialog.vue")['default']
    'LazyIssueProjectDiscussion': typeof import("../src/packages/issue-project/src/components/customer-proposal/pages/IssueProjectDiscussion.vue")['default']
    'LazyRichTextEditor': typeof import("../src/packages/issue-project/src/components/shared/RichTextEditor.vue")['default']
    'LazyReactionMember': typeof import("../src/packages/issue-project/src/components/shared/molecules/Reaction/ReactionMember.vue")['default']
    'LazyReactionView': typeof import("../src/packages/issue-project/src/components/shared/molecules/Reaction/ReactionView.vue")['default']
    'LazyConfirmOverwriteDialog': typeof import("../src/packages/issue-project/src/components/shared/organisms/ConfirmOverwriteDialog.vue")['default']
    'LazyCase': typeof import("../src/packages/case-work-flow-optimization/src/components/case/Case.vue")['default']
    'LazyCaseSearchCriteria': typeof import("../src/packages/case-work-flow-optimization/src/components/case/CaseSearchCriteria.vue")['default']
    'LazyIdentifiedCase': typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCase.vue")['default']
    'LazyIdentifiedCaseSearchCriteria': typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCaseSearchCriteria.vue")['default']
    'LazyIdentifiedCaseTable': typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCaseTable.vue")['default']
    'LazySelectCaseTypeDialog': typeof import("../src/packages/case-work-flow-optimization/src/components/customer-case/SelectCaseTypeDialog.vue")['default']
    'LazyLeaseSearchCriteria': typeof import("../src/packages/lease/src/components/new-lease/LeaseSearchCriteria.vue")['default']
    'LazyLeaseTable': typeof import("../src/packages/lease/src/components/new-lease/LeaseTable.vue")['default']
    'LazyBellIcon': typeof import("../node_modules/@ibp/base/src/components/Icon/BellIcon.vue")['default']
    'LazyAppIconBtn': typeof import("../node_modules/@ibp/base/src/components/Layout/AppIconBtn.vue")['default']
    'LazyBaseLayout': typeof import("../node_modules/@ibp/base/src/components/Layout/BaseLayout.vue")['default']
    'LazyBasicCard': typeof import("../node_modules/@ibp/base/src/components/Layout/BasicCard.vue")['default']
    'LazyCustomIcon': typeof import("../node_modules/@ibp/base/src/components/Layout/CustomIcon.vue")['default']
    'LazyCustomerHeader': typeof import("../node_modules/@ibp/base/src/components/Layout/CustomerHeader.vue")['default']
    'LazyCustomerStaffMenu': typeof import("../node_modules/@ibp/base/src/components/Layout/CustomerStaffMenu.vue")['default']
    'LazyEditableCustomerStaffMenu': typeof import("../node_modules/@ibp/base/src/components/Layout/EditableCustomerStaffMenu.vue")['default']
    'LazyInformationIcon': typeof import("../node_modules/@ibp/base/src/components/Layout/InformationIcon.vue")['default']
    'LazySearchCustomerStaffUserDialog': typeof import("../node_modules/@ibp/base/src/components/Layout/SearchCustomerStaffUserDialog.vue")['default']
    'LazyMenuCards': typeof import("../node_modules/@ibp/base/src/components/Menu/MenuCards.vue")['default']
    'LazyNotificationCircle': typeof import("../node_modules/@ibp/base/src/components/Notification/NotificationCircle.vue")['default']
    'LazyTimeLine': typeof import("../node_modules/@ibp/base/src/components/TimeLine/TimeLine.vue")['default']
    'LazyAppAutocomplete': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppAutocomplete.vue")['default']
    'LazyAppBtn': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppBtn.vue")['default']
    'LazyAppDangerousBtn': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppDangerousBtn.vue")['default']
    'LazyAppFileInput': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppFileInput.vue")['default']
    'LazyAppMainBtn': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppMainBtn.vue")['default']
    'LazyAppNumberField': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppNumberField.vue")['default']
    'LazyAppPopupDateOnlyPicker': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppPopupDateOnlyPicker.vue")['default']
    'LazyAppPopupDatePicker': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppPopupDatePicker.vue")['default']
    'LazyAppSimpleDataTable': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleDataTable.vue")['default']
    'LazyAppSimpleOptGroupSelect': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleOptGroupSelect.vue")['default']
    'LazyAppSimpleSelect': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleSelect.vue")['default']
    'LazyAppSubBtn': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSubBtn.vue")['default']
    'LazyAppTextField': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppTextField.vue")['default']
    'LazyAppTextarea': typeof import("../node_modules/@hox/base/src/components/shared/atom/AppTextarea.vue")['default']
    'LazyUseSharedBtnAttr': typeof import("../node_modules/@hox/base/src/components/shared/atom/useSharedBtnAttr")['default']
    'LazyAppConfirmDialog': typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppConfirmDialog.vue")['default']
    'LazyAppListEdit': typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppListEdit.vue")['default']
    'LazyAppToasts': typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppToasts.vue")['default']
    'LazyError': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error.vue")['default']
    'LazyError403': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error403.vue")['default']
    'LazyError404': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error404.vue")['default']
    'LazyError504': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error504.vue")['default']
    'LazyUnknown': typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Unknown.vue")['default']
    'LazyAppEditItemGroupTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppEditItemGroupTmpl.vue")['default']
    'LazyAppEditPageTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppEditPageTmpl.vue")['default']
    'LazyAppSearchDialogTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSearchDialogTmpl.vue")['default']
    'LazyAppSearchPageTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue")['default']
    'LazyAppSimpleDialogTmpl': typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSimpleDialogTmpl.vue")['default']
    'LazyNuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'LazyNuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'LazyNuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'LazyClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'LazyDevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'LazyServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'LazyNuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'LazyNuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'LazyNuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'LazyNuxtImg': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
    'LazyNuxtPicture': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
    'LazyNuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'LazyNoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'LazyLink': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'LazyBase': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'LazyTitle': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'LazyMeta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'LazyStyle': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'LazyHead': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'LazyHtml': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'LazyBody': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'LazyNuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'LazyNuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const CaseCsvUploadDialog: typeof import("../src/packages/common-case/src/components/import/CaseCsvUploadDialog.vue")['default']
export const CaseImportHistoryCard: typeof import("../src/packages/common-case/src/components/import/CaseImportHistoryCard.vue")['default']
export const CaseTable: typeof import("../src/packages/common-case/src/components/loan-and-lease/CaseTable.vue")['default']
export const SearchCriteria: typeof import("../src/packages/common-case/src/components/loan-and-lease/SearchCriteria.vue")['default']
export const FileUploader: typeof import("../src/packages/common-case/src/components/organisms/FileUploader.vue")['default']
export const BoolDotIndicator: typeof import("../src/packages/common-case/src/components/share/BoolDotIndicator.vue")['default']
export const CustomNoDataMessage: typeof import("../src/packages/common-case/src/components/share/CustomNoDataMessage.vue")['default']
export const SearchResultArea: typeof import("../src/packages/common-case/src/components/share/SearchResultArea.vue")['default']
export const IssueProjectDiscussionDate: typeof import("../src/packages/issue-project/src/components/customer-proposal/molecules/issue-project-discussion/IssueProjectDiscussionDate.vue")['default']
export const IssueProjectDiscussionComment: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionComment.vue")['default']
export const IssueProjectDiscussionCommentEditor: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionCommentEditor.vue")['default']
export const IssueProjectDiscussionThread: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThread.vue")['default']
export const IssueProjectDiscussionThreadEditor: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThreadEditor.vue")['default']
export const IssueProjectLink: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLink.vue")['default']
export const IssueProjectLinkAddDialog: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLinkAddDialog.vue")['default']
export const IssueProjectDiscussion: typeof import("../src/packages/issue-project/src/components/customer-proposal/pages/IssueProjectDiscussion.vue")['default']
export const RichTextEditor: typeof import("../src/packages/issue-project/src/components/shared/RichTextEditor.vue")['default']
export const ReactionMember: typeof import("../src/packages/issue-project/src/components/shared/molecules/Reaction/ReactionMember.vue")['default']
export const ReactionView: typeof import("../src/packages/issue-project/src/components/shared/molecules/Reaction/ReactionView.vue")['default']
export const ConfirmOverwriteDialog: typeof import("../src/packages/issue-project/src/components/shared/organisms/ConfirmOverwriteDialog.vue")['default']
export const Case: typeof import("../src/packages/case-work-flow-optimization/src/components/case/Case.vue")['default']
export const CaseSearchCriteria: typeof import("../src/packages/case-work-flow-optimization/src/components/case/CaseSearchCriteria.vue")['default']
export const IdentifiedCase: typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCase.vue")['default']
export const IdentifiedCaseSearchCriteria: typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCaseSearchCriteria.vue")['default']
export const IdentifiedCaseTable: typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCaseTable.vue")['default']
export const SelectCaseTypeDialog: typeof import("../src/packages/case-work-flow-optimization/src/components/customer-case/SelectCaseTypeDialog.vue")['default']
export const LeaseSearchCriteria: typeof import("../src/packages/lease/src/components/new-lease/LeaseSearchCriteria.vue")['default']
export const LeaseTable: typeof import("../src/packages/lease/src/components/new-lease/LeaseTable.vue")['default']
export const BellIcon: typeof import("../node_modules/@ibp/base/src/components/Icon/BellIcon.vue")['default']
export const AppIconBtn: typeof import("../node_modules/@ibp/base/src/components/Layout/AppIconBtn.vue")['default']
export const BaseLayout: typeof import("../node_modules/@ibp/base/src/components/Layout/BaseLayout.vue")['default']
export const BasicCard: typeof import("../node_modules/@ibp/base/src/components/Layout/BasicCard.vue")['default']
export const CustomIcon: typeof import("../node_modules/@ibp/base/src/components/Layout/CustomIcon.vue")['default']
export const CustomerHeader: typeof import("../node_modules/@ibp/base/src/components/Layout/CustomerHeader.vue")['default']
export const CustomerStaffMenu: typeof import("../node_modules/@ibp/base/src/components/Layout/CustomerStaffMenu.vue")['default']
export const EditableCustomerStaffMenu: typeof import("../node_modules/@ibp/base/src/components/Layout/EditableCustomerStaffMenu.vue")['default']
export const InformationIcon: typeof import("../node_modules/@ibp/base/src/components/Layout/InformationIcon.vue")['default']
export const SearchCustomerStaffUserDialog: typeof import("../node_modules/@ibp/base/src/components/Layout/SearchCustomerStaffUserDialog.vue")['default']
export const MenuCards: typeof import("../node_modules/@ibp/base/src/components/Menu/MenuCards.vue")['default']
export const NotificationCircle: typeof import("../node_modules/@ibp/base/src/components/Notification/NotificationCircle.vue")['default']
export const TimeLine: typeof import("../node_modules/@ibp/base/src/components/TimeLine/TimeLine.vue")['default']
export const AppAutocomplete: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppAutocomplete.vue")['default']
export const AppBtn: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppBtn.vue")['default']
export const AppDangerousBtn: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppDangerousBtn.vue")['default']
export const AppFileInput: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppFileInput.vue")['default']
export const AppMainBtn: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppMainBtn.vue")['default']
export const AppNumberField: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppNumberField.vue")['default']
export const AppPopupDateOnlyPicker: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppPopupDateOnlyPicker.vue")['default']
export const AppPopupDatePicker: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppPopupDatePicker.vue")['default']
export const AppSimpleDataTable: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleDataTable.vue")['default']
export const AppSimpleOptGroupSelect: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleOptGroupSelect.vue")['default']
export const AppSimpleSelect: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleSelect.vue")['default']
export const AppSubBtn: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSubBtn.vue")['default']
export const AppTextField: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppTextField.vue")['default']
export const AppTextarea: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppTextarea.vue")['default']
export const UseSharedBtnAttr: typeof import("../node_modules/@hox/base/src/components/shared/atom/useSharedBtnAttr")['default']
export const AppConfirmDialog: typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppConfirmDialog.vue")['default']
export const AppListEdit: typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppListEdit.vue")['default']
export const AppToasts: typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppToasts.vue")['default']
export const Error: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error.vue")['default']
export const Error403: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error403.vue")['default']
export const Error404: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error404.vue")['default']
export const Error504: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error504.vue")['default']
export const Unknown: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Unknown.vue")['default']
export const AppEditItemGroupTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppEditItemGroupTmpl.vue")['default']
export const AppEditPageTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppEditPageTmpl.vue")['default']
export const AppSearchDialogTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSearchDialogTmpl.vue")['default']
export const AppSearchPageTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue")['default']
export const AppSimpleDialogTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSimpleDialogTmpl.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const ClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
export const NuxtPicture: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
export const NuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyCaseCsvUploadDialog: typeof import("../src/packages/common-case/src/components/import/CaseCsvUploadDialog.vue")['default']
export const LazyCaseImportHistoryCard: typeof import("../src/packages/common-case/src/components/import/CaseImportHistoryCard.vue")['default']
export const LazyCaseTable: typeof import("../src/packages/common-case/src/components/loan-and-lease/CaseTable.vue")['default']
export const LazySearchCriteria: typeof import("../src/packages/common-case/src/components/loan-and-lease/SearchCriteria.vue")['default']
export const LazyFileUploader: typeof import("../src/packages/common-case/src/components/organisms/FileUploader.vue")['default']
export const LazyBoolDotIndicator: typeof import("../src/packages/common-case/src/components/share/BoolDotIndicator.vue")['default']
export const LazyCustomNoDataMessage: typeof import("../src/packages/common-case/src/components/share/CustomNoDataMessage.vue")['default']
export const LazySearchResultArea: typeof import("../src/packages/common-case/src/components/share/SearchResultArea.vue")['default']
export const LazyIssueProjectDiscussionDate: typeof import("../src/packages/issue-project/src/components/customer-proposal/molecules/issue-project-discussion/IssueProjectDiscussionDate.vue")['default']
export const LazyIssueProjectDiscussionComment: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionComment.vue")['default']
export const LazyIssueProjectDiscussionCommentEditor: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionCommentEditor.vue")['default']
export const LazyIssueProjectDiscussionThread: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThread.vue")['default']
export const LazyIssueProjectDiscussionThreadEditor: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThreadEditor.vue")['default']
export const LazyIssueProjectLink: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLink.vue")['default']
export const LazyIssueProjectLinkAddDialog: typeof import("../src/packages/issue-project/src/components/customer-proposal/organisms/IssueProjectLink/IssueProjectLinkAddDialog.vue")['default']
export const LazyIssueProjectDiscussion: typeof import("../src/packages/issue-project/src/components/customer-proposal/pages/IssueProjectDiscussion.vue")['default']
export const LazyRichTextEditor: typeof import("../src/packages/issue-project/src/components/shared/RichTextEditor.vue")['default']
export const LazyReactionMember: typeof import("../src/packages/issue-project/src/components/shared/molecules/Reaction/ReactionMember.vue")['default']
export const LazyReactionView: typeof import("../src/packages/issue-project/src/components/shared/molecules/Reaction/ReactionView.vue")['default']
export const LazyConfirmOverwriteDialog: typeof import("../src/packages/issue-project/src/components/shared/organisms/ConfirmOverwriteDialog.vue")['default']
export const LazyCase: typeof import("../src/packages/case-work-flow-optimization/src/components/case/Case.vue")['default']
export const LazyCaseSearchCriteria: typeof import("../src/packages/case-work-flow-optimization/src/components/case/CaseSearchCriteria.vue")['default']
export const LazyIdentifiedCase: typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCase.vue")['default']
export const LazyIdentifiedCaseSearchCriteria: typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCaseSearchCriteria.vue")['default']
export const LazyIdentifiedCaseTable: typeof import("../src/packages/case-work-flow-optimization/src/components/case/IdentifiedCaseTable.vue")['default']
export const LazySelectCaseTypeDialog: typeof import("../src/packages/case-work-flow-optimization/src/components/customer-case/SelectCaseTypeDialog.vue")['default']
export const LazyLeaseSearchCriteria: typeof import("../src/packages/lease/src/components/new-lease/LeaseSearchCriteria.vue")['default']
export const LazyLeaseTable: typeof import("../src/packages/lease/src/components/new-lease/LeaseTable.vue")['default']
export const LazyBellIcon: typeof import("../node_modules/@ibp/base/src/components/Icon/BellIcon.vue")['default']
export const LazyAppIconBtn: typeof import("../node_modules/@ibp/base/src/components/Layout/AppIconBtn.vue")['default']
export const LazyBaseLayout: typeof import("../node_modules/@ibp/base/src/components/Layout/BaseLayout.vue")['default']
export const LazyBasicCard: typeof import("../node_modules/@ibp/base/src/components/Layout/BasicCard.vue")['default']
export const LazyCustomIcon: typeof import("../node_modules/@ibp/base/src/components/Layout/CustomIcon.vue")['default']
export const LazyCustomerHeader: typeof import("../node_modules/@ibp/base/src/components/Layout/CustomerHeader.vue")['default']
export const LazyCustomerStaffMenu: typeof import("../node_modules/@ibp/base/src/components/Layout/CustomerStaffMenu.vue")['default']
export const LazyEditableCustomerStaffMenu: typeof import("../node_modules/@ibp/base/src/components/Layout/EditableCustomerStaffMenu.vue")['default']
export const LazyInformationIcon: typeof import("../node_modules/@ibp/base/src/components/Layout/InformationIcon.vue")['default']
export const LazySearchCustomerStaffUserDialog: typeof import("../node_modules/@ibp/base/src/components/Layout/SearchCustomerStaffUserDialog.vue")['default']
export const LazyMenuCards: typeof import("../node_modules/@ibp/base/src/components/Menu/MenuCards.vue")['default']
export const LazyNotificationCircle: typeof import("../node_modules/@ibp/base/src/components/Notification/NotificationCircle.vue")['default']
export const LazyTimeLine: typeof import("../node_modules/@ibp/base/src/components/TimeLine/TimeLine.vue")['default']
export const LazyAppAutocomplete: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppAutocomplete.vue")['default']
export const LazyAppBtn: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppBtn.vue")['default']
export const LazyAppDangerousBtn: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppDangerousBtn.vue")['default']
export const LazyAppFileInput: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppFileInput.vue")['default']
export const LazyAppMainBtn: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppMainBtn.vue")['default']
export const LazyAppNumberField: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppNumberField.vue")['default']
export const LazyAppPopupDateOnlyPicker: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppPopupDateOnlyPicker.vue")['default']
export const LazyAppPopupDatePicker: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppPopupDatePicker.vue")['default']
export const LazyAppSimpleDataTable: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleDataTable.vue")['default']
export const LazyAppSimpleOptGroupSelect: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleOptGroupSelect.vue")['default']
export const LazyAppSimpleSelect: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSimpleSelect.vue")['default']
export const LazyAppSubBtn: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppSubBtn.vue")['default']
export const LazyAppTextField: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppTextField.vue")['default']
export const LazyAppTextarea: typeof import("../node_modules/@hox/base/src/components/shared/atom/AppTextarea.vue")['default']
export const LazyUseSharedBtnAttr: typeof import("../node_modules/@hox/base/src/components/shared/atom/useSharedBtnAttr")['default']
export const LazyAppConfirmDialog: typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppConfirmDialog.vue")['default']
export const LazyAppListEdit: typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppListEdit.vue")['default']
export const LazyAppToasts: typeof import("../node_modules/@hox/base/src/components/shared/molecules/AppToasts.vue")['default']
export const LazyError: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error.vue")['default']
export const LazyError403: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error403.vue")['default']
export const LazyError404: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error404.vue")['default']
export const LazyError504: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Error504.vue")['default']
export const LazyUnknown: typeof import("../node_modules/@hox/base/src/components/shared/pages/error/Unknown.vue")['default']
export const LazyAppEditItemGroupTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppEditItemGroupTmpl.vue")['default']
export const LazyAppEditPageTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppEditPageTmpl.vue")['default']
export const LazyAppSearchDialogTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSearchDialogTmpl.vue")['default']
export const LazyAppSearchPageTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue")['default']
export const LazyAppSimpleDialogTmpl: typeof import("../node_modules/@hox/base/src/components/shared/templates/AppSimpleDialogTmpl.vue")['default']
export const LazyNuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const LazyNuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const LazyNuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const LazyClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const LazyDevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const LazyServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const LazyNuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const LazyNuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const LazyNuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const LazyNuxtImg: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
export const LazyNuxtPicture: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
export const LazyNuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const LazyNoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const LazyLink: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const LazyBase: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const LazyTitle: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const LazyMeta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const LazyStyle: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const LazyHead: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const LazyHtml: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const LazyBody: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const LazyNuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const LazyNuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>

export const componentNames: string[]
