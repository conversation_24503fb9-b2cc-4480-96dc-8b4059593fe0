<template>
  <div>
    <VRow dense>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.caseCategories"
          :items="caseOptions.categories || []"
          label="カテゴリ"
          data-testid="category"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.caseCategories"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.caseStatuses"
          :items="caseOptions.caseStatuses || []"
          label="ステータス"
          data-testid="status"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.caseStatuses"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.staffIds"
          :items="caseOptions.staffIds || []"
          label="案件担当者"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.staffIds"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
    </VRow>
    <VRow dense>
      <VCol dense>
        <AppPopupDatePicker
          v-model="localSearchCondition.fromDate"
          label="期日 From"
          display-text-format="yyyy/MM/dd"
          clearable
          dense
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.fromDate"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppPopupDatePicker
          v-model="localSearchCondition.toDate"
          label="期日 To"
          display-text-format="yyyy/MM/dd"
          clearable
          dense
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.toDate"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.generalTransactionTypeIds"
          :items="caseOptions.generalTransactionTypeOptions || []"
          label="案件項目"
          clearable
          multiple
          chips
          closable-chips
          :disabled="loading"
          :loading="loading"
          :error-messages="errorMessages?.generalTransactionTypeIds"
          @update:model-value="updateSearchCondition"
        />
      </VCol>
    </VRow>
  </div>
</template>

<script setup lang="ts">
import { deepClone, deepEqual } from '~/packages/common-case/src/utils/shared/objectUtils'

/**
 * 顧客識別案件検索条件コンポーネント
 *
 * 機能:
 * 1. 検索条件の入力フォーム
 * 2. バリデーション処理
 * 3. 検索条件の親コンポーネントへの通知
 */

// === プロパティ定義 ===
const props = defineProps({
  // 検索条件オブジェクト
  searchCondition: {
    type: Object,
    required: true,
  },
  // 案件関連オプション
  caseOptions: {
    type: Object,
    required: true,
  },
  // ローディング状態
  loading: {
    type: Boolean,
    default: false,
  },
  // エラーメッセージ
  errorMessages: {
    type: [Object, null],
    default: null,
  },
})

// === イベント定義 ===
const emit = defineEmits([
  'update:search-condition', // 検索条件更新イベント
])

// === ローカル状態 ===
// 検索条件のローカルコピー
const localSearchCondition = ref(deepClone(props.searchCondition))

// 内部更新フラグ（無限ループ防止用）
let isInternalUpdate = false

// === 監視処理 ===
// 検索条件が変更されたら親コンポーネントに通知
watch(localSearchCondition, (newValue) => {
  // 内部更新の場合はイベント発火をスキップ
  if (isInternalUpdate) {
    isInternalUpdate = false
    return
  }
  emit('update:search-condition', newValue)
}, { deep: true })

// 親コンポーネントからの変更を監視して反映
watch(() => props.searchCondition, (newValue) => {
  // 値が実際に変更された場合のみ更新
  if (!deepEqual(localSearchCondition.value, newValue)) {
    // 内部更新フラグを設定してからローカル状態を更新
    isInternalUpdate = true
    localSearchCondition.value = deepClone(newValue)
  }
}, { deep: true })

// === イベントハンドラ ===
/**
 * 検索条件更新処理
 */
function updateSearchCondition() {
  emit('update:search-condition', localSearchCondition.value)
}

</script>
