import * as path from 'path'
import { defineVitestConfig } from '@nuxt/test-utils/config'
import { configDefaults, coverageConfigDefaults } from 'vitest/config'

export default defineVitestConfig({
  test: {
    environment: 'nuxt',
    pool: 'forks',
    globals: true,
    reporters: ['junit', 'default'],
    alias: {
      '@': path.resolve(__dirname, './src'),
      '~': path.resolve(__dirname, './src'),
    },
    exclude: [
      ...configDefaults.exclude,
      '**/node_modules/**',
      '**/.nuxt/**',
      '../virtual:nuxt:**',
    ],
    coverage: {
      enabled: true,
      provider: 'istanbul',
      reporter: ['cobertura', 'html'],
      exclude: [
        ...coverageConfigDefaults.exclude,
        'node_modules/**',
        '**/.nuxt/**',
        '../virtual:nuxt:**',
        '**/shared/**',
      ],
    },
  },
})
