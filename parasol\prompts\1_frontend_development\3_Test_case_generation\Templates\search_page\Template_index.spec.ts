import { describe, test, expect, vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { flushPromises } from '@vue/test-utils'
import { mountSuspended, mockNuxtImport } from '@nuxt/test-utils/runtime'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { format } from 'date-fns'
import {
  setupMockUseLayout,
  setupMockUseDisplay,
  getMockUseRouteDefaultImpl,
  patchOFetchOnMsw,
  getRequestJsonFromMswHandler,
  getVdataTableRowsAsObjectList,
  setTestUser,
  clearTestUser,
} from '../../testSupport'

// TODO: テスト対象コンポーネントのパスを実際のエンティティに合わせて変更してください
// 例: import index from '@/pages/your-entity/index.vue'
import index from '@/pages/employee/index.vue'
import { useAppToasts } from '~/composables/shared/useAppToasts'

// =====================================================================================================================
// 定数定義
// =====================================================================================================================
// TODO: ベースURLを実際のエンティティに合わせて変更してください
// 例: const BASE_URL = 'http://localhost:3000/your-entity'
const BASE_URL = 'http://localhost:3000/employee'
const window = globalThis.window

// =====================================================================================================================
// テスト用処理設定
// =====================================================================================================================
// APIのモックサーバー定義
const server = setupServer()

// useRoute のモック化
mockNuxtImport('useRoute', () => {
  return () => {
    return getMockUseRouteDefaultImpl()
  }
})

// navigateToのモック化
const { mockNavigateTo } = vi.hoisted(() => ({
  mockNavigateTo: vi.fn(),
}))
mockNuxtImport('navigateTo', () => mockNavigateTo)

function setUpMocks() {
  setupMockUseLayout()
  setupMockUseDisplay()
}

// =====================================================================================================================
// 前処理/後処理
// =====================================================================================================================
// テスト開始前処理
beforeAll(() => {
  // ユーザー情報を設定
  setTestUser('テストユーザー', 'test_user', ['admin'])
})
// テスト終了後処理
afterAll(() => {
  // APIモックサーバーを閉じる
  server.close()
  // ユーザー情報をクリア
  clearTestUser()
})
// テストケース開始前処理
beforeEach(() => {
  // デフォルトURL設定
  window.location.href = BASE_URL
  setUpMocks()
})
// テストケース終了時後処理
afterEach(() => {
  // APIのモックハンドラをリセット
  server.resetHandlers()
  // mockNuxtImportのモックを初期状態に
  mockNavigateTo.mockReset()
  // モック化したモジュールを元の実装に戻す
  vi.restoreAllMocks()
  // トーストが表示されている場合はクリア
  const { forceClear } = useAppToasts()
  forceClear()
})

// =====================================================================================================================
// テストデータ定義
// =====================================================================================================================

// TODO: APIから返されるテストデータを実際のエンティティの構造に合わせて変更してください
// APIから返されるダミーデータの元の値を作成します。
const responseSourceData = [...Array(3).keys()].map(index => {
  return {
    id: index,
    version: `100${index}`,
    nameKanji: `nameKanji_${index}`,
    nameKana: `nameKana_${index}`,
    birthDate: new Date(2000, 0, index),
    address: `address_${index}`,
    branchName: `branchName_${index}`,
    qualificationCount: index,
  }
})

// =====================================================================================================================
// テスト実装
// =====================================================================================================================
describe('Entity Search Page Test', () => {
  test('画面初期化処理：URLのクエリパラメータがない場合、検索されずに画面が表示される', async () => {
    // テスト対象をマウント
    const wrapper = await mountSuspended(index)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 検索結果が0件であることを確認
    expect(vm.data.value.items.length).toBe(0)
    expect(vm.data.value.total).toBe(0)
  })

  test('画面初期化処理：URLのクエリパラメータがある場合、検索条件が設定され検索処理が実行される', async () => {
    // TODO: APIのモックエンドポイントを実際のエンティティのAPIエンドポイントに変更してください
    // 例: server.use(http.get('/v1.0/your-entities', searchHandler))
    // APIのモックEndpointを登録
    const searchHandler = vi.fn(() => HttpResponse.json({ items: responseSourceData, total: responseSourceData.length * 2 }))
    server.use(http.get('/v1.0/employee', searchHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // TODO: 実際のエンティティの検索条件に合わせてURLクエリパラメータを変更してください
    // 例: window.location.href = BASE_URL + '?name=test&status=active&pageIndex=2&pageSize=15'
    // URL設定を上書き
    window.location.href = BASE_URL + '?q=&name=test&address=test2&pageIndex=2&pageSize=15&sort=name%20desc&sort=address%20asc&test=val'

    // テスト対象をマウント
    const wrapper = await mountSuspended(index)
    const vm = wrapper.getCurrentComponent().proxy as any
    // 非同期処理が完了するまで待機
    await flushPromises()

    // APIが呼び出されたことを確認
    expect(searchHandler).toHaveBeenCalled()
    // TODO: 検索条件データを実際のエンティティの項目に合わせて変更してください
    // 例: expect(vm.searchCondition).toEqual({ name: 'test', status: 'active' })
    // 検索条件データの確認
    expect(vm.searchCondition).toEqual({ name: 'test', address: 'test2' })
    expect(vm.criteria.value).toEqual({ name: 'test', address: 'test2' })
    // ページング設定の確認の確認
    // TODO: URLで引き渡した値と一致しているかを確認してください。
    expect(vm.pagination).toEqual({
      pageIndex: 2,
      pageSize: 15,
      sort: [{ key: 'name', order: 'desc' }, { key: 'address', order: 'asc' }],
    })
    // APIに渡されたクエリの確認
    expect(vm.query).toEqual({
      name: 'test',
      address: 'test2',
      pageIndex: 2,
      pageSize: 15,
      sort: ['name desc', 'address'],
    })
  })

  // TODO: 検索条件ごとにケースを作成してください。
  test('検索処理：正しいクエリパラメータにてAPIが呼び出されデータが取得できる', async () => {
    // APIのモックを登録
    const searchHandler = vi.fn(() => HttpResponse.json({ items: responseSourceData, total: responseSourceData.length * 2 }))
    server.use(http.get('/v1.0/employee', searchHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象をマウント
    const wrapper = await mountSuspended(index)
    const vm = wrapper.getCurrentComponent().proxy as any

    // TODO: 画面上の検索条件を実際のエンティティの項目に合わせて変更してください
    // 例: vm.searchCondition.name = 'test'
    // 画面上の検索条件を設定
    vm.searchCondition.name = 'test'
    vm.searchCondition.address = 'test2'

    // 検索処理を実行
    await vm.search()

    // APIが呼び出されたことを確認
    expect(searchHandler).toHaveBeenCalled()
    // 保持されている検索条件の確認
    expect(vm.criteria.value).toEqual({ name: 'test', address: 'test2' })
    // Paginationの確認
    expect(vm.pagination).toEqual({
      pageIndex: 1,
      pageSize: 10,
      sort: [],
    })
    // APIに渡されたクエリの確認
    expect(vm.query).toEqual({
      name: 'test',
      address: 'test2',
      pageIndex: 1,
      pageSize: 10,
      sort: [],
    })
    // 検索結果を確認
    expect(vm.data.value.items.length).toBe(responseSourceData.length)
    expect(vm.data.value.items).toEqual(responseSourceData)
    expect(vm.data.value.total).toBe(responseSourceData.length * 2)

    // 画面表示されている一覧の内容をv-date-tableから取得し、正しくバインドされているかを確認
    const datas = getVdataTableRowsAsObjectList(wrapper)
    expect(datas.length).toBe(responseSourceData.length)
    // 1件目のデータを検証
    const firstRowData = datas[0]

    // APIから返るデータを比較用に変換
    const responseFirstRow = responseSourceData[0]
    // TODO: 一覧にバインドされている項目を実際のエンティティに合わせて変更してください
    // 例: 'ID': responseFirstRow.id + '', 'Name': responseFirstRow.name
    const destRowData = {
      'ID': responseFirstRow.id + '',
      '住所': responseFirstRow.address,
      '名前(カナ)': responseFirstRow.nameKana,
      '名前(漢字)': responseFirstRow.nameKanji,
      '所持資格数': responseFirstRow.qualificationCount + '',
      '誕生日': format(responseFirstRow.birthDate, 'yyyy/MM/dd'),
      '部署名': responseFirstRow.branchName,
    }

    expect(firstRowData).toEqual(destRowData)
  })

  test('追加処理：追加処理が呼ばれた場合、編集画面への遷移が実行される', async () => {
    // テスト対象をマウント
    const wrapper = await mountSuspended(index)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 追加処理を実行
    await vm.add()

    // TODO: 正しいパスを実際のエンティティに合わせて変更してください
    // 例: expect(mockNavigateTo).toHaveBeenCalledWith({ path: '/your-entity/edit' })
    // 正しいパスで遷移処理が呼ばれていることを確認
    expect(mockNavigateTo).toHaveBeenCalledWith({
      path: '/employee/edit',
    })
  })

  test('編集処理：編集処理が呼ばれた場合、正しいクエリパラメータにて編集画面への遷移が実行される', async () => {
    // テスト対象をマウント
    const wrapper = await mountSuspended(index)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 編集処理を実行
    await vm.edit({ item: { id: '123' } })

    // TODO: 正しいパスを実際のエンティティに合わせて変更してください
    // 例: expect(mockNavigateTo).toHaveBeenCalledWith({ path: '/your-entity/edit', query: { id: '123' } })
    // 正しいパスで遷移処理が呼ばれていることを確認
    expect(mockNavigateTo).toHaveBeenCalledWith({
      path: '/employee/edit',
      query: { id: '123' },
    })
  })

  test('削除処理：削除対象が選択されている場合、削除APIが呼び出され正しく処理が終了する', async () => {
    // TODO: 削除APIのエンドポイントを実際のエンティティに合わせて変更してください
    // 例: server.use(http.post('/v1.0/your-entities/:deleteAll', deleteAllHandler))
    const deleteAllHandler = vi.fn(() => new HttpResponse(null, { status: 204 }))
    server.use(http.post('/v1.0/employee/:deleteAll', deleteAllHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象をマウント
    const wrapper = await mountSuspended(index)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをspyして「はい」を返すように設定
    const mockDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockDialog.mockReturnValue(true)

    // 一覧で選択されている値を設定
    vm.listSelectedItems.value = responseSourceData

    // 削除処理を実行
    const flg = await vm.remove()

    // トーストが表示されていること
    const { messages } = useAppToasts()
    expect(messages.value[0]).toMatchObject({
      color: 'primary',
    })
    // 削除APIが呼び出されたことを確認
    expect(deleteAllHandler).toHaveBeenCalled()
    const reqBody = await getRequestJsonFromMswHandler(deleteAllHandler)
    // 削除APIに渡されたデータが正しいことを確認
    expect(reqBody).toEqual(responseSourceData.map(r => {
      return { id: r.id, version: r.version }
    }))
    // 削除処理が成功したことを確認
    expect(flg).toBe(true)
  })
})
