import { convertToDate, convertToStringArray } from '@ibp/common-case/src/utils/shared/requestValueConverter'

// stringのRefをDateのRefに変換
export function toDateRef(model: Ref<string | undefined | null>) {
  return computed({
    get() {
      return convertToDate(model.value ?? undefined)
    },
    set(value) {
      model.value = value?.toISOString()
    },
  })
}

// stringのRefをstring配列に変換
export function toStringArray(model: Ref<string | undefined | null>) {
  return computed(() => {
    return convertToStringArray(model.value ?? undefined)
  })
}
