import { reactive, ref, computed, type Ref, type ComputedRef } from 'vue'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { isUndefinedOrNull, isUnusable, isString, isBoolean } from '@hox/base/src/utils/shared/is'
import type { SearchOptions } from '@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue'
import type { SortItem } from '@hox/base/src/components/shared/types'
import { useCriteriaUrlState } from '@hox/base/src/composables/shared/useCriteriaUrlState'
import {
  useFindCase as useFindApi,
  type FindCaseCriteria as FindApiCriteria,
} from '@ibp/common-case/src/apiclient/customerProposal/case'
import type { Pagination } from '@hox/base/src/apiclient/shared/types'
import { startOfDay, endOfDay } from 'date-fns'
import { getCaseTableHeaders } from '../../constants/case/tableHeaders'

// === マスタデータ・ドメイン定数 ===
import {
  SEARCH_CASE_STATUSES as CASE_STATUSES,
  DEFAULT_SELECTED_CASE_STATUSES_WITH_ISSUE_PROJECT,
} from '@ibp/common-case/src/constants/domain/case'

type Criteria = Omit<FindApiCriteria, keyof Pagination>

/**
 * 案件検索用のコンポーザブル
 * 検索条件の管理、検索処理、結果の加工を行う
 */
export const useCaseSearch = (
  validateAll: () => Promise<boolean>,
  branchOptions?: Ref<{ title: string, value: string }[]> | ComputedRef<{ title: string, value: string }[]>,
) => {
  // 検索条件を設定
  const searchCondition = reactive<Criteria>({
    branchNumbers: undefined,
    cifNumber: undefined,
    customerName: undefined,
    industryCodes: undefined,
    caseCategories: undefined,
    caseStatuses: undefined,
    fromDate: null,
    toDate: null,
    staffIds: undefined,
    customerStaffIds: undefined,
    isFavorite: false,
    generalTransactionTypeIds: undefined,
  })

  // 検索条件の保持
  const criteria = ref<Criteria | null>(null)

  // ページング
  const pagination = reactive({
    pageIndex: 1,
    pageSize: 20,
    sort: [] as SortItem[],
  })

  // 検索結果
  const data = ref<any>({ items: [], total: 0 })
  const searchResultMessage = ref<string | undefined>(undefined)

  // 処理完了などをトースト表示するために利用
  const { error: errorToast, success: successToast } = useAppToasts()

  // ローディング状態
  const { hasTrue: loading, addFlag } = useFlagCondition()

  // 指定されているクエリの中から検索条件以外のものを抜き出して保持
  const additionalQueryParameter = (() => {
    const route = useRoute()
    const query = route.query as { [key: string]: string }
    const excludeKeys = Object.keys(searchCondition).concat([
      'q',
      'pageIndex',
      'pageSize',
      'sort',
    ])
    return Object.fromEntries(
      Object.entries(query).filter(([key]) => !excludeKeys.includes(key)),
    )
  })()

  // 検索クエリの定義
  const query = computed(() => {
    const sort = pagination.sort.reduce((prev, curr) => {
      prev.push(`${curr.key}${curr.order === 'desc' ? ' desc' : ''}`)
      return prev
    }, [] as string[])
    const paginationQuery = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      sort,
    }
    const processedCriteria = createRequestSearchCondition(criteria.value)
    return { ...(processedCriteria ?? {}), ...paginationQuery }
  })
  // APIクライアントの設定
  const {
    executeWithResult: findData,
    inProgress: findInProgress,
  } = useFindApi(query)

  // ローディング状態に追加
  addFlag(findInProgress)

  // 検索履歴を積む設定を取得
  const {
    state: criteriaHistory,
    push: pushCriteria,
    onChange,
    hasState,
  } = useCriteriaUrlState<FindApiCriteria>()
  /**
   * 検索前のバリデーションを実行
   * @param options 検索オプション
   * @returns バリデーション成功時はtrue、失敗時はfalse
   */
  async function validateSearchConditions(options: SearchOptions): Promise<boolean> {
    // バリデーションチェック（ソートやページング変更時以外）
    if (!options.isSortChangeRequest && !options.isPaginationRequest) {
      const valid = await validateAll()
      if (!valid) {
        return false
      }
    }
    return true
  }

  /**
   * 検索実行前の条件チェック
   * @param options 検索オプション
   * @returns 検索実行可能な場合はtrue、スキップする場合はfalse
   */
  function shouldExecuteSearch(options: SearchOptions): boolean {
    // ソートの変更かページングの変更の場合で、かつ検索条件が空(まだ一度も検索されていない)の場合は検索しない
    if (
      (options.isSortChangeRequest || options.isPaginationRequest) &&
      !criteria.value
    ) {
      return false
    }
    return true
  }

  /**
   * 検索条件を更新
   * @param options 検索オプション
   */
  function updateSearchCriteria(options: SearchOptions): void {
    // ソート/ページング以外の場合は検索条件を更新する
    if (!options.isSortChangeRequest && !options.isPaginationRequest) {
      criteria.value = createRequestSearchCondition(searchCondition)
    }
  }

  /**
   * API呼び出しを実行
   * @returns API呼び出し結果
   */
  async function executeApiCall(): Promise<any> {
    return await findData().catch((err) => {
      handleApiError(err)
      return false
    })
  }

  /**
   * APIエラーを処理
   * @param err エラーオブジェクト
   */
  function handleApiError(err: any): void {
    const errorData = err.response.data || {}
    if (errorData.type === '/sql-timeout') {
      errorToast(
        'タイムアウトエラーが発生しました。検索結果が少なくなるよう検索条件を調整してください。',
      )
    } else {
      throw err
    }
  }

  /**
   * 検索結果レスポンスを処理・整形
   * @param result API呼び出し結果
   */
  function processSearchResponse(result: any): void {
    if (result && typeof result === 'object' && 'data' in result) {
      const resultData = result.data as any
      if (resultData.result) {
        // 検索結果あり
        data.value = {
          items: createListItems(resultData.result.items),
          total: resultData.result.total,
          result: resultData.result,
        }
      } else {
        // 検索結果なし（result: null）の場合、テーブルを空にして件数を0にする
        data.value = {
          items: [],
          total: 0,
          result: { items: [], total: 0 },
        }
      }
      // エラーメッセージがある場合は表示
      searchResultMessage.value = resultData.findFailedMessage
      if (!isBoolean(resultData.success) && resultData?.findFailedMessage) {
        errorToast(resultData?.findFailedMessage)
      }
      if (!isBoolean(resultData.success) && resultData?.toastMessage) {
        successToast(resultData?.toastMessage)
      }
    }
  }

  /**
   * 期日関連のパラメータをフィルタリング
   * @param currentCriteria 現在の検索条件
   * @returns フィルタリング済みの追加クエリパラメータ
   */
  function filterDateParameters(currentCriteria: FindApiCriteria | null): Record<string, any> {
    const filteredParams = { ...additionalQueryParameter }

    // 期日データが削除された場合、URLからも除去
    if (!currentCriteria?.fromDate) {
      delete filteredParams.fromDate
    }
    if (!currentCriteria?.toDate) {
      delete filteredParams.toDate
    }

    return filteredParams
  }

  /**
   * 検索履歴をURLに更新
   * @param options 検索オプション
   */
  function updateUrlHistory(options: SearchOptions): void {
    if (!options.noStoreCriteria) {
      const currentCriteria = createRequestSearchCondition(criteria.value)
      const filteredAdditionalParams = filterDateParameters(currentCriteria)

      // 検索条件をURLの履歴に積む
      pushCriteria({ ...query.value, ...filteredAdditionalParams })
    }
  }

  /**
   * 検索処理のメイn関数
   * @param options 検索オプション
   * @returns 検索成功時はtrue、失敗時はfalse
   */
  async function search(options: SearchOptions = {}) {
    try {
      // 1. バリデーション
      const isValid = await validateSearchConditions(options)
      if (!isValid) {
        return false
      }

      // 2. 検索実行可否チェック
      if (!shouldExecuteSearch(options)) {
        return true
      }

      // 3. 検索条件の更新
      updateSearchCriteria(options)

      // 4. API呼び出し
      const result = await executeApiCall()
      if (result === false) {
        return false
      }

      // 5. レスポンス処理・整形
      processSearchResponse(result)

      // 6. URL履歴の更新
      updateUrlHistory(options)

      return true
    } catch (error) {
      errorToast('検索処理でエラーが発生しました')
      return false
    }
  }

  /**
   * 検索条件をクリア
   */
  function clear() {
    criteria.value = null
    // 検索条件などをクリア
    searchCondition.branchNumbers = undefined
    searchCondition.cifNumber = undefined
    searchCondition.customerName = undefined
    searchCondition.industryCodes = undefined
    searchCondition.caseCategories = undefined
    searchCondition.caseStatuses = undefined
    searchCondition.fromDate = null
    searchCondition.toDate = null
    searchCondition.staffIds = undefined
    searchCondition.customerStaffIds = undefined
    searchCondition.isFavorite = false
    searchCondition.generalTransactionTypeIds = undefined

    data.value = { items: [], total: 0 }
  }

  /**
   * URLのヒストリをもとに検索条件を復元する処理
   */
  function criteriaRestored(e: { value: any, restoreCompleted: () => void }) {
    // 引数の value プロパティにクエリ文字列をもとにしたオブジェクトが渡される
    const searchConditionFromQuery = e.value

    // 検索条件の復元
    Object.assign(searchCondition, searchConditionFromQuery)

    e.restoreCompleted()
  }

  /**
   * 検索条件の構築
   */
  function createRequestSearchCondition(condition: any) {
    if (!condition) return null

    const newCondition = { ...condition }

    // CIF番号をゼロ埋め
    if (newCondition?.cifNumber) {
      newCondition.cifNumber = paddingZeroToLeft(newCondition.cifNumber, 8)
    }

    // 日付フィールドの変換
    if (newCondition?.fromDate) {
      const fromDate = startOfDay(new Date(newCondition.fromDate))
      newCondition.fromDate = fromDate.toISOString()
    } else {
      // 期日データがない場合、URLクエリから除去する
      delete newCondition.fromDate
    }

    if (newCondition?.toDate) {
      const toDate = endOfDay(new Date(newCondition.toDate))
      newCondition.toDate = toDate.toISOString()
    } else {
      // 期日データがない場合、URLクエリから除去する
      delete newCondition.toDate
    }

    return newCondition
  }

  /**
   * CIF番号をゼロ埋めする関数
   */
  function paddingZeroToLeft(target: string, length: number = 8): string | null {
    if (isUnusable(target) || target === '') {
      return null
    }
    let zeroText = ''
    for (let index = 0; index < length; index++) {
      zeroText += '0'
    }
    return `${zeroText}${target}`.slice(-length)
  }
  /**
   * 検索結果を整形します
   * @param items 検索結果
   * @returns 整形された検索結果
   */
  function createListItems(items: any) {
    if (isUndefinedOrNull(items)) return items

    // テーブルヘッダーの定義を取得
    const headers = getCaseTableHeaders(branchOptions?.value)

    const today = startOfDay(new Date())

    for (const item of items) {
      const caseStatus = CASE_STATUSES.find((x) => x.title === item.caseStatus)

      // 期日が過ぎている && ステータスが完了していない場合は行ごと赤色にする
      if (
        !isUndefinedOrNull(item.expiredAt) &&
        today > startOfDay(new Date(item.expiredAt)) &&
        DEFAULT_SELECTED_CASE_STATUSES_WITH_ISSUE_PROJECT.includes(
          caseStatus?.value ?? '',
        )
      ) {
        item.class = 'case-highlight-row'
      }

      // 表示制限
      if (item.isAccessRestricted) {
        item.isMaskTargetItem = true
      }

      // 各フィールドに対してフォーマット処理を適用
      for (const header of headers) {
        if (header.format && item[header.key] !== undefined) {
          // 元のデータを保存（ソートなどで必要になる場合がある）
          item[`${header.key}Raw`] = item[header.key]
          // フォーマット関数を適用
          item[header.key] = header.format(item[header.key])
        }
      }
    }
    return items
  }

  /**
   * URLのクエリに指定されていた検索条件を復元します
   */
  function restoreCriteria(criteriaFromHistory: any) {
    // criteriaFromHistory が存在しない場合は処理を中断
    if (!criteriaFromHistory) {
      return
    }

    // 検索条件を復元
    searchCondition.branchNumbers = isString(criteriaFromHistory.branchNumbers)
      ? [criteriaFromHistory.branchNumbers]
      : criteriaFromHistory.branchNumbers
    searchCondition.cifNumber = criteriaFromHistory.cifNumber ?? undefined
    searchCondition.customerName = criteriaFromHistory.customerName ?? undefined
    searchCondition.industryCodes = isString(criteriaFromHistory.industryCodes)
      ? [criteriaFromHistory.industryCodes]
      : criteriaFromHistory.industryCodes
    searchCondition.caseCategories = isString(criteriaFromHistory.caseCategories)
      ? [criteriaFromHistory.caseCategories]
      : criteriaFromHistory.caseCategories
    searchCondition.caseStatuses = isString(criteriaFromHistory.caseStatuses)
      ? [criteriaFromHistory.caseStatuses]
      : criteriaFromHistory.caseStatuses
    // fromDate の復元処理
    if (criteriaFromHistory.fromDate) {
      const fromDate = new Date(criteriaFromHistory.fromDate)
      searchCondition.fromDate = isNaN(fromDate.getTime()) ? null : fromDate
    } else {
      searchCondition.fromDate = null
    }

    // toDate の復元処理
    if (criteriaFromHistory.toDate) {
      const toDate = new Date(criteriaFromHistory.toDate)
      searchCondition.toDate = isNaN(toDate.getTime()) ? null : toDate
    } else {
      searchCondition.toDate = null
    }
    searchCondition.staffIds = isString(criteriaFromHistory.staffIds)
      ? [criteriaFromHistory.staffIds]
      : criteriaFromHistory.staffIds
    searchCondition.customerStaffIds = isString(criteriaFromHistory.customerStaffIds)
      ? [criteriaFromHistory.customerStaffIds]
      : criteriaFromHistory.customerStaffIds
    searchCondition.isFavorite =
      criteriaFromHistory.isFavorite !== undefined
        ? criteriaFromHistory.isFavorite === 'true'
        : false
    searchCondition.generalTransactionTypeIds = isString(
      criteriaFromHistory.generalTransactionTypeIds,
    )
      ? [criteriaFromHistory.generalTransactionTypeIds]
      : criteriaFromHistory.generalTransactionTypeIds

    // ソート条件を保持されているカタチからオブジェクトのカタチに変換します。
    function convertToSortFormat(querySort: string[] | string | undefined) {
      const sort = Array.isArray(querySort)
        ? querySort
        : !querySort
            ? []
            : [querySort]
      return (sort ?? []).map((s) => {
        const [key, order] = s.split(' ')
        return {
          key,
          order: order === 'desc' ? 'desc' : ('asc' as 'asc' | 'desc'),
        }
      })
    }
    pagination.pageIndex = parseFloat(criteriaFromHistory.pageIndex) || 1
    pagination.pageSize = parseFloat(criteriaFromHistory.pageSize) || 20
    pagination.sort = convertToSortFormat(criteriaFromHistory.sort)
  }

  // 検索履歴が変わった(戻るが押された)際の処理
  onChange((state: FindApiCriteria | null) => {
    if (!state) {
      // 初回アクセスに戻った場合に undefined になる
      data.value = {
        items: [],
        total: 0,
      }
      return
    }
    // 検索条件を復元し検索を実行する
    restoreCriteria(state)
    search({ noStoreCriteria: true })
  })

  return {
    searchCondition,
    criteria,
    pagination,
    data,
    searchResultMessage,
    loading,
    criteriaHistory,
    hasState,
    search,
    clear,
    criteriaRestored,
    restoreCriteria,
  }
}
