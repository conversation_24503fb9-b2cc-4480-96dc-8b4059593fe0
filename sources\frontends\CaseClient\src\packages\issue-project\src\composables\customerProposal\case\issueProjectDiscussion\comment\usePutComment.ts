import type {
  issueProjectDiscussionCommentType,
  FindIssueProjectDiscussionThreadResultItem,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useThreadShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/thread/useThreadShareData'
import { usePutIssueProjectDiscussionComment } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionComment'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type { GetIssueProjectDiscussionCommentResult } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionComment'
import type { GetInteractionLogResult } from '@ibp/common-case/src/apiclient/customerInteraction/interactionLog'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import type { FileObjectType } from '@ibp/issue-project/src/pages/issue-project/issue-project-discussion/index.vue'

export const usePutComment = () => {
  const { threadDataRef, authUserId, authDisplayName } = useThreadShareData()
  const { error: errorToast, success: successToast } = useAppToasts()
  const { init, hasChanged, restart } = useWatchDataChanges()
  const watcher = { init, hasChanged, restart }

  const updateComment = async (
    comment: issueProjectDiscussionCommentType | undefined,
    customerIdentificationId: string,
    refThreadsData: FindIssueProjectDiscussionThreadResultItem[],
    saveInteractionLog: (
      savedData: GetIssueProjectDiscussionCommentResult,
      customerIdentificationId: string,
      interactionCategory: string,
      threadData: FindIssueProjectDiscussionThreadResultItem[],
      issueItemName: string | undefined,
    ) => Promise<GetInteractionLogResult | boolean>,
    issueItemName: string | undefined,
    mentionTargetTeamMemberUserIds: string[],
    fileObject: FileObjectType,
    customerName: string,
  ) => {
    try {
      threadDataRef.value.loading = true

      const sendData = createSendCommentData(
        comment,
        false,
        customerIdentificationId,
        mentionTargetTeamMemberUserIds,
        fileObject,
        customerName,
      )

      // Api定義
      const { data: updateCommentData, executeWithResult: updateComment } =
        usePutIssueProjectDiscussionComment(ref<FormData>(sendData))

      const result = await updateComment().catch((e: any) => {
        // バックエンド到達前の通信系エラー
        if (e.cause?.message === 'Failed to fetch') {
          errorToast('保存に失敗しました。添付ファイルを開いていないか確認をお願いします。開いていない場合は通信エラーの可能性がありますので、時間をおいて再度実行してください。')
          return false
        }
        // 失敗だった場合の処理を行います。
        if (!e.hasProblemDetails) {
          errorToast(
            '保存に失敗しました。',
          )
        }
        if (e.data.type === '/validation-error') {
          errorToast(e.data.errors)
        } else if (e.data.type === '/conflict') {
          errorToast(
            'すでに別のユーザーがデータを登録・変更しているため保存できませんでした。',
          )
        } else {
          errorToast(
            '保存に失敗しました。',
          )
        }
        return false
      })

      // APIがエラーだった場合は処理を中断します。
      if (!result) return false

      // スレッド検索
      const targetThreadIndex = refThreadsData.findIndex(
        (x) => x.id === updateCommentData.value.threadId,
      )

      // 更新コメントをrefThreadsDataに入れる
      const commentIndex = refThreadsData[targetThreadIndex].comments.findIndex(
        (x) => x.id === updateCommentData.value.id,
      )

      refThreadsData[targetThreadIndex].comments.splice(
        commentIndex,
        1,
        updateCommentData.value,
      )

      // 行動記録の保存
      const interactionCategory = `IssueProjectComment_${updateCommentData.value.purpose}_Update`

      const saveInteractLogResult = await saveInteractionLog(
        updateCommentData.value,
        customerIdentificationId,
        interactionCategory,
        refThreadsData,
        issueItemName,
      ).catch((e: any) => {
        // 失敗だった場合の処理を行います。
        if (!e.hasProblemDetails) {
          throw e
        }
        return false
      })

      // 行動記録の保存まで成功していた場合に、成功メッセージを表示
      if (saveInteractLogResult) {
        successToast('コメントを保存しました。')
      }

      watcher.init(refThreadsData)
      return true
    } finally {
      threadDataRef.value.loading = false
    }
  }

  /**
   * コメント用送信データの作成
   */
  const createSendCommentData = (
    data: any,
    isAdd: boolean,
    customerIdentificationId: string,
    mentionTargetTeamMemberUserIds: string[],
    fileObject: FileObjectType,
    customerName: string,
  ) => {
    const sendData = Object.assign({}, data)
    const formData = new FormData()

    // 登録
    if (isAdd) {
      formData.append('threadId', sendData.threadId)
      formData.append('registeredDateTime', new Date().toISOString())
      formData.append('registrant', authDisplayName)
      formData.append('registrantId', authUserId)
      formData.append('description', sendData.description)
      // 更新
    } else {
      formData.append('id', sendData.id)
      formData.append('registrantId', sendData.registrantId)
      formData.append('description', sendData.description)
      formData.append('version', sendData.version)
    }
    formData.append('purpose', sendData.purpose)
    if (sendData.person) formData.append('person', sendData.person)
    if (sendData.isPersonOfPower) {
      formData.append('isPersonOfPower', sendData.isPersonOfPower)
    }
    formData.append('customerIdentificationId', customerIdentificationId)
    formData.append('customerName', customerName)

    if (sendData.mentionTargetUserIds) {
      sendData.mentionTargetUserIds.forEach((value: string) => {
        formData.append('mentionTargetUserIds[]', value)
      })
    }
    if (sendData.mentionTargetTeamIds) {
      sendData.mentionTargetTeamIds.forEach((value: string) => {
        formData.append('mentionTargetTeamIds[]', value)
      })
    }
    if (mentionTargetTeamMemberUserIds) {
      mentionTargetTeamMemberUserIds.forEach((value: string) => {
        formData.append('mentionTargetTeamMemberUserIds[]', value)
      })
    }

    // ファイル
    formData.append('threadId', sendData.threadId)
    formData.append('updaterId', authUserId)
    formData.append('updater', authDisplayName)
    formData.append('updaterName', authDisplayName)

    if (fileObject.filesToUpload.length > 0) {
      for (const file of fileObject.filesToUpload) {
        formData.append('uploadFiles', file)
      }
    }
    if (fileObject.filesToRemove.length > 0) {
      for (const fileToRemove of fileObject.filesToRemove) {
        formData.append('filesToRemove', fileToRemove.deleteItemName)
      }
    }
    return formData
  }

  return {
    updateComment,
  }
}
