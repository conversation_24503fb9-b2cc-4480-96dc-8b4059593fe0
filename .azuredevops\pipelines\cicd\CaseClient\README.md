必要な変数を設定してください。テンプレートの実装として必要な値を次にしめします。アプリケーションの要件によっては内容が可変するため、都度調整してください。

| 変数名 | 概要 | 例 |
|--|--|--|
| {アプリケーション名}.WebAppName| アプリケーションのAzure Web Appsの名前を指定します| アプリケーション名前 |
| {アプリケーション名}.Auth.Authority | WebUIアプリケーションで利用するAzure ADで発行される Authority を指定します | https://login.microsoftonline.com/{TenantId} |
| {アプリケーション名}.Auth.ClientId | WebUIアプリケーションで利用するAzure ADで発行されるClientIDを指定します |  |
| {アプリケーション名}.Auth.Scopes  | Azure ADで定義したスコープを指定します | api://xxxx/access_as_user |
| {アプリケーション名}.DefaultEndpoint  | WebUIアプリケーションで利用するAPIのエンドポイントのベースURLを指定します |http://localhost:3000/api/ |

上記の`{アプリケーション名}`は多くの場合、プロジェクト名と一致します。テンプレートから展開された`deploy.yaml`を参照してください。