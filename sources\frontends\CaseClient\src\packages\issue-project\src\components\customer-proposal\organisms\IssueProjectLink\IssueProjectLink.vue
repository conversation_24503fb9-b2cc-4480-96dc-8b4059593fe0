<template>
  <v-container>
    <v-row>
      <v-col align="right">
        <app-main-btn
          :disabled="!$auth.getUser()?.userId"
          @click="openDialog(null, 'add')"
        >
          <v-icon small>mdi-plus</v-icon>登録
        </app-main-btn>
      </v-col>
    </v-row>
    <v-skeleton-loader v-if="loading" type="list-item-three-line" />
    <v-data-table
      v-else
      no-data-text="データがありません"
      class="pointer"
      :headers="headers"
      :items="targetData"
      @click:row="clickRow"
    >
      <template #[`item.title`]="{ item }">
        <a :href="item.url" target="_blank">{{ item.title }}</a>
      </template>

      <template #[`item.updatedDateTime`]="{ item }">
        {{ formattedDateTime(item.updatedDateTime) }}
      </template>

      <template #[`item.action`]="{ item }">
        <app-icon-btn
          icon="mdi-content-copy"
          help-message="クリップボードにコピー"
          @click.stop="copy(item)"
        />
        <app-icon-btn
          icon="mdi-pencil"
          help-message="編集"
          :disabled="!$auth.getUser()?.userId"
          @click.stop="openDialog(item, 'update')"
        />
        <app-icon-btn
          icon="mdi-trash-can-outline"
          help-message="削除"
          :disabled="!$auth.getUser()?.userId"
          @click.stop="deleteRow(item)"
        />
      </template>
    </v-data-table>
    <issue-project-link-add-dialog ref="editDialog" />
    <app-confirm-dialog ref="confirmDialog" />
  </v-container>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { format } from 'date-fns'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { 
  type IssueProjectLinkForGet,
  type IssueProjectLinkForCreate
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectLink'

interface Props {
  issueProjectId: string
  isAdd: boolean
  issueProjectLinksData: IssueProjectLinkForGet[]
}

const props = withDefaults(defineProps<Props>(), {
  issueProjectId: '',
  isAdd: false,
  issueProjectLinksData: () => []
})

const emit = defineEmits<{
  'update-issue-project-links-data': [data: IssueProjectLinkForGet[]]
}>()

const { success: successToast, error: errorToast } = useAppToasts()

const { $auth } = useNuxtApp()

const targetData = ref<IssueProjectLinkForGet[]>([])
const loading = ref(false)
const editDialog = ref()
const confirmDialog = ref()

const headers = [
  { title: 'タイトル', key: 'title', sortable: true },
  { title: '更新者', key: 'updater', sortable: true },
  {
    title: '更新日',
    key: 'updatedDateTime',
    sortable: true,
    width: 150
  },
  { title: '', key: 'action', sortable: false, width: 150 }
]

function formattedDateTime(item: Date | null): string {
  if (!item) return '-'
  return format(item, 'yyyy/MM/dd HH:mm')
}

function convertForGetToCreate(item: IssueProjectLinkForGet): IssueProjectLinkForCreate {
  return {
    title: item.title,
    url: item.url,
    updater: item.updater,
    updaterId: item.updaterId,
    issueProjectId: item.issueProjectId
  }
}

function convertCreateToForGet(
  createData: IssueProjectLinkForCreate, 
  originalItem?: IssueProjectLinkForGet
): IssueProjectLinkForGet {
  return {
    id: originalItem?.id || `temp_${Date.now()}`,
    title: createData.title,
    url: createData.url,
    updater: createData.updater,
    updaterId: createData.updaterId,
    updatedDateTime: new Date(),
    issueProjectId: createData.issueProjectId,
    version: originalItem?.version || '1'
  }
}

async function openDialog(item: IssueProjectLinkForGet | null, type: 'add' | 'update'): Promise<void> {
  let dialogData: IssueProjectLinkForCreate

  if (type === 'add') {
    dialogData = {
      title: '',
      url: '',
      updater: $auth.getUser()?.displayName || '',
      updaterId: $auth.getUser()?.userId || '',
      issueProjectId: props.issueProjectId
    }
  } else if (type === 'update' && item) {
    dialogData = convertForGetToCreate(item)
    dialogData.updater = $auth.getUser()?.displayName || ''
    dialogData.updaterId = $auth.getUser()?.userId || ''
  } else {
    return
  }

  const result = await editDialog.value.open(dialogData, type)
  
  if (result?.isOk && type === 'add') {
    const newItem = convertCreateToForGet(result.data)
    targetData.value.push(newItem)
  }
  
  if (result?.isOk && type === 'update' && item) {
    const updatedItem = convertCreateToForGet(result.data, item)
    const itemIndex = targetData.value.findIndex(i => i.id === item.id)
    if (itemIndex !== -1) {
      targetData.value[itemIndex] = updatedItem
    }
  }
  
  if (!result) {
    errorToast(`${item?.title}の保存に失敗しました`)
  }
}

function deleteRow(item: IssueProjectLinkForGet): void {
  targetData.value = targetData.value.filter((e) => e !== item)
}

function clickRow(item: IssueProjectLinkForGet): void {
  if (item.url) {
    window.open(item.url, '_blank')
  }
}

async function copy(item: IssueProjectLinkForGet): Promise<void> {
  try {
    if (item.url) {
      await navigator.clipboard.writeText(item.url)
      successToast(`${item.title}のURLをクリップボードにコピーしました`)
    }
  } catch (error) {
    errorToast('URLのコピーに失敗しました')
  }
}

// watch(
//   () => props.issueProjectLinksData,
//   (updated) => {
//     targetData.value = [...updated]
//   },
//   { immediate: true }
// )

// watch(
//   targetData,
//   (updated) => {
//     emit('update-issue-project-links-data', updated)
//   },
//   { deep: true }
// )

onMounted(() => {
  targetData.value = [...props.issueProjectLinksData]
})
</script>

<style lang="scss" scoped>
.link {
  color: #00a63c;
  text-decoration: underline solid #00a63c;
}

.pointer :deep(tr) {
  cursor: pointer;
}
</style>