### Shared.AzureBlob

## 概要

Shared.AzureBlobライブラリは、Azure Blob Storageとの連携機能を提供するライブラリです。Blobのアップロード・ダウンロード、存在確認、メタデータ管理などの操作を抽象化し、Azure Blob Storageを使用したアプリケーションの開発効率と保守性向上を支援します。

## 主要機能

### クライアント管理
- **クライアント生成**: BlobServiceClientインスタンスの生成・管理
- **接続管理**: 接続文字列、認証情報に応じたクライアント生成
- **抽象化**: カスタムBlobクライアントの拡張実装基盤

### ストレージ操作
- **ファイル管理**: Blobのアップロード・ダウンロード・存在確認
- **メタデータ**: コンテンツタイプ、サイズ等の情報管理
- **一括操作**: 複数Blobの一括処理支援

### 設定・認証
- **接続設定**: 接続文字列、認証方式の柔軟な設定
- **認証切替**: 異なる認証方式への対応
- **設定管理**: Azure Blob Storage利用時の各種設定値管理

### 拡張性
- **カスタム実装**: BlobStorageClientProviderによる拡張実装
- **プロバイダ対応**: 異なるBlobストレージプロバイダへの対応
- **プラグイン**: 機能拡張のためのプラグイン機構

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| BlobServiceClientProvider | クライアント生成 | Azure.Storage.Blobsの`BlobServiceClient`インスタンスを生成・管理するサービス。接続文字列や認証情報に応じたクライアント生成を担う。 | Blobストレージへの共通アクセス、認証切替 |
| BlobServiceClientProviderOptions | 設定 | `BlobServiceClientProvider`の動作設定。接続文字列や認証方式など、クライアント生成に必要な情報を保持。 | 接続先切替・認証方式の柔軟な設定 |
| BlobStorageClientProvider | 抽象クライアント | BlobStorageClient生成のための抽象基底。拡張実装時の基盤として利用。 | カスタムBlobクライアントの拡張実装 |
| BlobStorageClient | ストレージ操作 | Blobのアップロード・ダウンロード・存在確認等、ストレージ操作を提供するクライアント。 | ファイル管理・データ永続化 |
| BlobSettings | 設定 | Azure Blobストレージ利用時の各種設定値（接続文字列等）を管理。 | 接続・運用設定の一元管理 |
