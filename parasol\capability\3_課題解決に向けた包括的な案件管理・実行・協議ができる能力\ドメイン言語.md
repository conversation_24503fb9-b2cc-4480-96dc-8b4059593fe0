# ドメイン言語定義

## 1. エンティティ（Entities）

### 融資案件（支払承諾）（AcceptanceAndGuaranteeCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **保証予定金額（Amount）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **保証料率（InterestRate）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **保証料率（自由記述）（InterestRateCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **ESG関連（RelatedEsg）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **事前相談基準対象（PreConsultationStandardTarget）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **震災関連（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
  - **返済方式（RepaymentType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **担保区分（CollateralType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **担保・保証人（自由記述）（CollateralOrGuaranteeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **保証区分（GuaranteeType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **SME流入経路（TrafficSource）**: 
    - 仕様: TrafficSource列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **不成約事由（CancelTypeOfLoan）**: 
    - 仕様: CancelTypeOfLoan列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **取下げ・失注・謝絶理由（CancelReason）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(500)
- 関連情報参照用プロパティ
  - **保証人（Guarantors）**: 
    - 参照情報: 保証人エンティティのコレクション
    - 仕様: Nullable

### 事業性理解月次ファイル（BusinessUnderstandingMonthlyFile）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(26)
  - **ファイル名称（FileName）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(300)
  - **基準日（BaseDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須

### 案件（Case）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
    - 仕様: HasDefaultValueSql("NEWID()")
  - **顧客識別ID（CustomerIdentificationId）**: 
    - 仕様: GUID型
    - 仕様: 必須
  - **案件種類（CaseCategory）**: 
    - 仕様: CaseCategory列挙型
    - 仕様: 必須
    - 仕様: 文字列として保存
    - 備考: 継承階層の判別子として使用
  - **案件名（CaseName）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **案件ステータス（CaseStatus）**: 
    - 仕様: CaseStatus列挙型
    - 仕様: 必須
    - 仕様: 文字列として保存
  - **案件概要（CaseOutline）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(500)
  - **期日（ExpiredAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **案件担当者ID（StaffId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **案件担当者名（StaffName）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **案件登録日（RegisteredAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **案件更新日（CaseUpdatedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **アクセス制限対象か（IsAccessRestricted）**: 
    - 仕様: ブール型
    - デフォルト値: false
- 集約内エンティティを表すプロパティ
  - **案件ファイル（CaseFiles）**: 
    - 参照情報: 案件ファイルエンティティのコレクション
    - 仕様: Nullable
  - **案件リンク（CaseLinks）**: 
    - 参照情報: 案件リンクエンティティのコレクション
    - 仕様: Nullable
  - **案件更新情報（CaseUpdateInformation）**: 
    - 参照情報: 案件更新情報エンティティ
    - 仕様: 必須
  - **案件お気に入り情報（CaseFavoriteInformations）**: 
    - 参照情報: 案件お気に入り情報エンティティのコレクション
    - 仕様: Nullable
- 関連情報参照用プロパティ
  - **案件協議スレッド（CaseDiscussionThreads）**: 
    - 参照情報: 案件協議スレッドエンティティのコレクション
    - 仕様: Nullable

### 案件協議返信（CaseDiscussionReply）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
    - 仕様: HasDefaultValueSql("NEWID()")
  - **協議スレッドID（CaseDiscussionThreadId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 備考: CaseDiscussionThreadエンティティへの外部キー（Cascade削除）
  - **登録者ID（RegistrantId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録者名（RegistrantName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録日時（RegisteredAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **目的（Purpose）**: 
    - 仕様: CaseDiscussionPurpose列挙型
    - 仕様: 必須
    - 仕様: 文字列として保存
    - デフォルト値: Internal
  - **相手（Person）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(20)
  - **実権者（IsPersonOfPower）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **メンション（個人）（MentionTargetUserIds）**: 
    - 仕様: 文字列のコレクション
    - 仕様: Nullable
    - 仕様: JSON形式で保存
  - **メンション（チーム）（MentionTargetTeamIds）**: 
    - 仕様: 文字列のコレクション
    - 仕様: Nullable
    - 仕様: JSON形式で保存
  - **協議コメント（Description）**: 
    - 仕様: 文字列
    - 仕様: 必須
- 集約内エンティティを表すプロパティ
  - **添付ファイル（Files）**: 
    - 参照情報: 案件協議返信ファイルエンティティのコレクション
    - 仕様: Nullable
- 関連情報参照用プロパティ
  - **リアクション（Reactions）**: 
    - 参照情報: 案件協議返信リアクションエンティティのコレクション
    - 仕様: Nullable
- IFileProcessableプロパティ
  - **登録者（Registrant）**: 
    - 備考: CreatedByプロパティのエイリアス（NotMapped）

### 案件協議返信リアクション（CaseDiscussionReplyReaction）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(26)
  - **協議返信ID（CaseDiscussionReplyId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
    - 備考: CaseDiscussionReplyエンティティへの外部キー（Cascade削除）
  - **リアクションタイプ（ReactionType）**: 
    - 仕様: ReactionType列挙型
    - 仕様: 必須
    - 仕様: 文字列として保存
  - **更新者ID（UpdaterId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(7)
  - **更新者（UpdaterName）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **更新日時（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須

### 案件協議スレッド（CaseDiscussionThread）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
    - 仕様: HasDefaultValueSql("NEWID()")
  - **案件ID（CaseId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 備考: Caseエンティティへの外部キー（Cascade削除）
  - **協議スレッド種類（ThreadType）**: 
    - 仕様: CaseDiscussionThreadType列挙型
    - 仕様: 必須
    - 仕様: 文字列として保存
    - 備考: 継承階層の判別子として使用
  - **協議スレッドタイトル（ThreadName）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(100)
    - 備考: 仕様上は50文字制限だがリース協議スレッド用に100文字で設定
  - **登録者ID（RegistrantId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録者名（RegistrantName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録日時（RegisteredAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **目的（Purpose）**: 
    - 仕様: CaseDiscussionPurpose列挙型
    - 仕様: 必須
    - 仕様: 文字列として保存
  - **相手（Person）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(20)
  - **実権者（IsPersonOfPower）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **メンションHTML（MentionTargetsHtml）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **メンション（個人）（MentionTargetUserIds）**: 
    - 仕様: 文字列のコレクション
    - 仕様: Nullable
    - 仕様: JSON形式で保存
  - **メンション（チーム）（MentionTargetTeamIds）**: 
    - 仕様: 文字列のコレクション
    - 仕様: Nullable
    - 仕様: JSON形式で保存
  - **協議コメント（Description）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(10000)
  - **一覧の表示内容（DisplayText）**: 
    - 仕様: 文字列
    - 仕様: 必須
- 集約内エンティティを表すプロパティ
  - **添付ファイル（Files）**: 
    - 参照情報: 案件協議スレッドファイルエンティティのコレクション
    - 仕様: Nullable
- 関連情報参照用プロパティ
  - **協議返信（Replies）**: 
    - 参照情報: 案件協議返信エンティティのコレクション
    - 仕様: Nullable
  - **リアクション（Reactions）**: 
    - 参照情報: 案件協議スレッドリアクションエンティティのコレクション
    - 仕様: Nullable
- IFileProcessableプロパティ
  - **登録者（Registrant）**: 
    - 備考: CreatedByプロパティのエイリアス（NotMapped）

### 案件協議スレッドリアクション（CaseDiscussionThreadReaction）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(26)
  - **協議スレッドID（CaseDiscussionThreadId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
    - 備考: CaseDiscussionThreadエンティティへの外部キー（Cascade削除）
  - **リアクションタイプ（ReactionType）**: 
    - 仕様: ReactionType列挙型
    - 仕様: 必須
    - 仕様: 文字列として保存
  - **更新者ID（UpdaterId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(7)
  - **更新者（UpdaterName）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **更新日時（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須

### 案件お気に入り情報（CaseFavoriteInformation）

- 独自プロパティ
  - **案件ID（CaseId）**: 
    - 仕様: 文字列
    - 仕様: MaxLength(50)
    - 備考: Caseエンティティへの外部キー（Cascade削除）
- 継承プロパティ（FavoriteInformationから）
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: MaxLength(50)
    - 仕様: HasDefaultValueSql("NEWID()")
  - **ユーザーID（UserId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **お気に入り種類（FavoriteCategory）**: 
    - 仕様: FavoriteCategory列挙型
    - 仕様: 文字列として保存
    - 備考: 継承階層の判別子（固定値：Case）
  - **お気に入りかどうか（IsFavorite）**: 
    - 仕様: ブール型
    - 仕様: 必須

### 案件ファイル（CaseFile）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **ファイル名（FileName）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(300)
  - **案件ID（CaseId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 備考: Caseエンティティへの外部キー（Cascade削除）

### 案件インポート履歴（CaseImportHistory）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: MaxLength(50)
    - 仕様: HasDefaultValueSql("NEWID()")
  - **ファイル名（FileName）**: 
    - 仕様: 文字列
    - 仕様: MaxLength(300)
  - **案件種別（CaseCategory）**: 
    - 仕様: CaseCategory列挙型
    - 仕様: 文字列として保存
  - **CSV取込ステータス（CsvImportProcessStatus）**: 
    - 仕様: CsvImportProcessStatus列挙型
    - 仕様: 文字列として保存
  - **詳細（Detail）**: 
    - 仕様: 文字列
  - **処理開始日時（StartedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **処理終了日時（FinishedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable

### 案件インポート関係（CaseImportRelation）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **案件取込履歴ID（CaseImportHistoryId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
    - 仕様: インデックス設定あり
  - **案件ID（CaseId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)

### 案件リンク（CaseLink）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **タイトル（Title）**: 
    - 仕様: 文字列
    - 仕様: MaxLength(100)
  - **URL（Url）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **案件ID（CaseId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 備考: Caseエンティティへの外部キー（Cascade削除）

### 案件更新情報（CaseUpdateInformation）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **案件ID（CaseId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
    - 備考: Caseエンティティとの1対1関係（Cascade削除）
  - **案件を含む関連情報の最終更新日（LastUpdatedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
- インデックス情報
  - **削除フラグインデックス（IsDeleted）**: 
    - 仕様: 非クラスター化インデックス

### 商業手形案件（CommercialBillCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **金額（Amount）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **適用基準金利（ApplicableBaseInterestRate）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **利率（InterestRate）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **利率（自由記述）（InterestRateCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **ベースレート（BaseInterestRate）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **稟議スプレッド（Spread）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **資金使途コード（LoanPurposeCode）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **資金使途（自由記述）（LoanPurposeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **ESG関連（RelatedEsg）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **事前相談基準対象（PreConsultationStandardTarget）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **震災関連（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
  - **担保区分（CollateralType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **担保・保証人（自由記述）（CollateralOrGuaranteeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **保証区分（GuaranteeType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **SME流入経路（TrafficSource）**: 
    - 仕様: TrafficSource列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **不成約事由（CancelTypeOfLoan）**: 
    - 仕様: CancelTypeOfLoan列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **取下げ・失注・謝絶理由（CancelReason）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(500)
- 関連情報参照用プロパティ
  - **保証人（Guarantors）**: 
    - 参照情報: 保証人エンティティのコレクション
    - 仕様: Nullable

### 法人生命保険案件（CorporateLifeInsuranceCase）

- 基本プロパティ（Caseクラスから継承）
- 備考: 追加の固有プロパティなし

### 法人損害保険案件（CorporateNonlifeInsuranceCase）

- 基本プロパティ（Caseクラスから継承）
- 備考: 追加の固有プロパティなし

### 与信限度額案件（CreditLimitCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **科目（AccountType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **極度額（Amount）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **期間（月数）（Period）**: 
    - 仕様: 整数型
    - 仕様: Nullable
  - **適用基準金利（ApplicableBaseInterestRate）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **利率（InterestRate）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **利率（自由記述）（InterestRateCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **ベースレート（BaseInterestRate）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **稟議スプレッド（Spread）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **資金使途コード（LoanPurposeCode）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **資金使途（自由記述）（LoanPurposeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **ESG関連（RelatedEsg）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **事前相談基準対象（PreConsultationStandardTarget）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **震災関連（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
  - **現地現物確認者（OnSitePhysicalConfirmer）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **現地現物確認日時（OnSitePhysicalConfirmationDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **返済方式（RepaymentType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **担保区分（CollateralType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **担保・保証人（自由記述）（CollateralOrGuaranteeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **保証区分（GuaranteeType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **SME流入経路（TrafficSource）**: 
    - 仕様: TrafficSource列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **商品（CreditLimitProductCode）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **不成約事由（CancelTypeOfLoan）**: 
    - 仕様: CancelTypeOfLoan列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **取下げ・失注・謝絶理由（CancelReason）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(500)
- 関連情報参照用プロパティ
  - **保証人（Guarantors）**: 
    - 参照情報: 保証人エンティティのコレクション
    - 仕様: Nullable

### 証書貸付案件（DeedLoanCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **科目（AccountType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **金額（Amount）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **期間（月数）（Period）**: 
    - 仕様: 整数型
    - 仕様: Nullable
  - **適用基準金利（ApplicableBaseInterestRate）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **利率（InterestRate）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **利率（自由記述）（InterestRateCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **ベースレート（BaseInterestRate）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **稟議スプレッド（Spread）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
    - 仕様: HasPrecision(18, 5)
  - **資金使途コード（LoanPurposeCode）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **資金使途（自由記述）（LoanPurposeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **ESG関連（RelatedEsg）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **事前相談基準（PreConsultationStandardTarget）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **震災関連（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
  - **返済方式（RepaymentType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **返済財源コード（RepaymentSourceCode）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **担保区分（CollateralType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **担保・保証人（自由記述）（CollateralOrGuaranteeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **保証区分（GuaranteeType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **現地現物確認者（OnSitePhysicalConfirmer）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **現地現物確認日時（OnSitePhysicalConfirmationDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **SME流入経路（TrafficSource）**: 
    - 仕様: TrafficSource列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **不成約事由（CancelTypeOfLoan）**: 
    - 仕様: CancelTypeOfLoan列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **取下げ・失注・謝絶理由（CancelReason）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(500)
- 関連情報参照用プロパティ
  - **保証人（Guarantors）**: 
    - 参照情報: 保証人エンティティのコレクション
    - 仕様: Nullable

### 締切管理（DeadlineManagement）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
  - **ステータス（Status）**: 
    - 仕様: IssueStatus列挙型
    - 仕様: Nullable
  - **顧客あて次のアクション（NextActionforCustomer）**: 
    - 仕様: NextActionforCustomer列挙型
    - 仕様: Nullable
  - **その他（Other）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **課題案件ID（IssueProjectId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 備考: IssueProjectエンティティとの1対1関係（Cascade削除）

### 外部完結リース案件（ExternallyCompletedLeaseCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **震災関連であるか（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false

### お気に入り情報（FavoriteInformation）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: MaxLength(50)
    - 仕様: HasDefaultValueSql("NEWID()")
  - **ユーザーID（UserId）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 仕様: MaxLength(50)
  - **お気に入り種類（FavoriteCategory）**: 
    - 仕様: FavoriteCategory列挙型
    - 仕様: 文字列として保存
    - 備考: 継承階層の判別子として使用
  - **お気に入りかどうか（IsFavorite）**: 
    - 仕様: ブール型
    - 仕様: 必須
- 継承情報
  - 備考: CaseFavoriteInformation、IssueProjectFavoriteInformationの基底クラス

### 外国為替取引案件（ForeignExchangeTransactionCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **稟議形態（ApprovalType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **金額（Amount）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **ESG関連（RelatedEsg）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **事前相談基準対象（PreConsultationStandardTarget）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **震災関連（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
  - **担保区分（CollateralType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **担保・保証人（自由記述）（CollateralOrGuaranteeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **保証区分（GuaranteeType）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)
  - **不成約事由（CancelTypeOfLoan）**: 
    - 仕様: CancelTypeOfLoan列挙型
    - 仕様: Nullable
    - 仕様: 文字列として保存
  - **取下げ・失注・謝絶理由（CancelReason）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(500)
- 関連情報参照用プロパティ
  - **保証人（Guarantors）**: 
    - 参照情報: 保証人エンティティのコレクション
    - 仕様: Nullable

### 一般案件協議スレッド（GeneralCaseDiscussionThread）

- 基本プロパティ（CaseDiscussionThreadクラスから継承）
- 備考: 追加の固有プロパティなし

### 一般取引案件（GeneralTransactionCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **総合取引案件項目（GeneralTransactionTypeId）**: 
    - 仕様: 文字列
    - 仕様: Nullable
    - 仕様: MaxLength(50)

### 一般取引種別（GeneralTransactionType）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **名称（Name）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録有効フラグ（IsRegisterable）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **表示順序（Order）**: 
    - 仕様: 整数型
    - 仕様: 必須

### 保証人（Guarantor）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **関連する案件のID（CaseId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **顧客を識別するID（CustomerIdentificationId）**: 
    - 仕様: GUID型
    - 仕様: Nullable
  - **支店の番号（BranchNumber）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **顧客情報ファイル番号（CifNumber）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **保証人の氏名（GuarantorName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **主債務者との関係（Relationship）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **関係の詳細情報（RelationshipDetail）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **物上保証人かどうかを示すフラグ（IsCollateralProvider）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **削除フラグ（IsDeleted）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false

### 課題情報（IssueInformation）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **課題No（IssueNumber）**: 
    - 仕様: 整数型
    - 仕様: 必須
  - **課題項目（IssueItem）**: 
    - 仕様: IssueItem列挙型
    - 仕様: 必須
    - 備考: 経営戦略、原価管理、ICT利活用などの項目
  - **顧客の課題感（CustomerIssuePerception）**: 
    - 仕様: CustomerIssuePerception列挙型
    - 仕様: 必須
    - 備考: 最優先取組み課題、優先取組み課題、今後取組み課題、情報収集
  - **概要（Overview）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **課題（Issue）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **現状(問題点)（CurrentStatus）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **あるべき姿（Ideal）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **業種詳細（IndustryDetail）**: 
    - 仕様: IndustryDetail列挙型
    - 仕様: 必須
    - 備考: 農業、林業、漁業など業種の詳細
  - **キーマン（KeyPerson）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **登録日（RegisteredDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **更新日（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **課題案件ID（IssueProjectId）**: 
    - 仕様: 文字列
    - 仕様: 必須

### 課題プロジェクト（IssueProject）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **顧客識別Id（CustomerIdentificationId）**: 
    - 仕様: GUID型
    - 仕様: 必須
  - **アクセス制限対象か（IsAccessRestricted）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
- 集約内エンティティを表すプロパティ
  - **課題情報（IssueInformation）**: 
    - 参照情報: 課題情報エンティティ
    - 仕様: Nullable
  - **案件情報（ProjectInformation）**: 
    - 参照情報: 案件情報エンティティ
    - 仕様: Nullable
  - **期日管理（DeadlineManagement）**: 
    - 参照情報: 期日管理エンティティ
    - 仕様: Nullable
  - **リンク（IssueProjectLinks）**: 
    - 参照情報: 課題プロジェクトリンクエンティティのコレクション
    - 仕様: Nullable
- 関連情報参照用プロパティ
  - **タスク（IssueProjectTask）**: 
    - 参照情報: 課題プロジェクトタスクエンティティのコレクション
    - 仕様: Nullable
  - **ファイル（IssueProjectFiles）**: 
    - 参照情報: 課題プロジェクトファイルエンティティのコレクション
    - 仕様: Nullable
  - **協議（IssueProjectDiscussionThreads）**: 
    - 参照情報: 課題プロジェクト協議スレッドエンティティのコレクション
    - 仕様: Nullable
  - **課題案件お気に入り情報（IssueProjectFavoriteInformations）**: 
    - 参照情報: 課題プロジェクトお気に入り情報エンティティのコレクション
    - 仕様: Nullable

### 課題プロジェクト協議コメント（IssueProjectDiscussionComment）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **スレッドID（ThreadId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録日時（RegisteredDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **登録者（Registrant）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録者ID（RegistrantId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **説明（Description）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **目的（Purpose）**: 
    - 仕様: DiscussionPurpose列挙型
    - 仕様: 必須
    - 備考: 社内協議、社外協議
  - **相手（Person）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **実権者（IsPersonOfPower）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **メンション対象ユーザーID（MentionTargetUserIds）**: 
    - 仕様: 文字列のコレクション
    - 仕様: Nullable
  - **メンション対象チームID（MentionTargetTeamIds）**: 
    - 仕様: 文字列のコレクション
    - 仕様: Nullable
- 集約内エンティティを表すプロパティ
  - **添付ファイル（Files）**: 
    - 参照情報: 課題プロジェクト協議コメントファイルエンティティのコレクション
    - 仕様: 必須
- 関連情報参照用プロパティ
  - **リアクション（Reactions）**: 
    - 参照情報: 課題プロジェクト協議コメントリアクションエンティティのコレクション
    - 仕様: 必須

### 課題プロジェクト協議コメントファイル（IssueProjectDiscussionCommentFile）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **ファイル名称（FileName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **社内協議ID（CommentId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新日時（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **更新者ID（UpdaterId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新者名（UpdaterName）**: 
    - 仕様: 文字列
    - 仕様: 必須

### 課題プロジェクト協議コメントリアクション（IssueProjectDiscussionCommentReaction）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **コメントID（CommentId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **リアクションタイプ（ReactionType）**: 
    - 仕様: ReactionType列挙型
    - 仕様: 必須
    - 備考: いいね・確認しました、おめでとう・契約お疲れ様、OK、驚き
  - **更新者ID（StaffId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新者（StaffName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新日時（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須

### 課題プロジェクト協議フォームテンプレート（IssueProjectDiscussionFormTemplate）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **協議種別ID（DiscussionTypeId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **協議種別（DiscussionType）**: 
    - 仕様: IssueProjectDiscussionType列挙型
    - 仕様: 必須
    - 備考: スレッド・協議のタイプ
  - **テンプレートバージョン（TemplateVersion）**: 
    - 仕様: 整数型
    - 仕様: 必須
  - **キー（Key）**: 
    - 仕様: 文字列
    - 仕様: 必須
    - 備考: スレッド・コメントのキー（入力項目名）
  - **入力タイプ（InputType）**: 
    - 仕様: InputType列挙型
    - 仕様: 必須
    - 備考: タイトル、テキストボックス、テキストエリア、整数、実数、日付、選択式など
  - **選択項目の初期値（OptionDefaultValue）**: 
    - 仕様: 整数型
    - 仕様: Nullable
  - **自由入力項目の初期値（InputDefaultText）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **選択項目（Option）**: 
    - 仕様: KeyValuePair<string, int>のコレクション
    - 仕様: Nullable
    - 備考: ラジオボタン、ドロップダウンリスト等
  - **サフィックス（Suffix）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **入力可否（Enabled）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **入力必須（IsRequired）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **表示順序（Order）**: 
    - 仕様: 整数型
    - 仕様: 必須
    - 備考: Keyの表示順(0,1,2...)

### 課題プロジェクト協議スレッド（IssueProjectDiscussionThread）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **課題プロジェクトID（IssueProjectId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録日時（RegisteredDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **登録者（Registrant）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **登録者ID（RegistrantId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **協議種別（DiscussionType）**: 
    - 仕様: IssueProjectDiscussionType列挙型
    - 仕様: 必須
  - **協議種別ID（DiscussionTypeId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **テンプレートの協議目的（PurposeTypeOfTemplate）**: 
    - 仕様: PurposeTypeOfTemplate列挙型
    - 仕様: 必須
    - 備考: 社内協議、社外協議、社内外協議
  - **協議目的（Purpose）**: 
    - 仕様: DiscussionPurpose列挙型
    - 仕様: 必須
  - **相手（Person）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **実権者（IsPersonOfPower）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **内容（Descriptions）**: 
    - 仕様: KeyValuePair<string, string>のコレクション
    - 仕様: 必須
  - **ひな形のバージョン（TemplateVersion）**: 
    - 仕様: 整数型
    - 仕様: 必須
  - **アクセス制限対象か（IsAccessRestricted）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
  - **メンション対象HTML（MentionTargetsHtml）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **メンション対象ユーザーID（MentionTargetUserIds）**: 
    - 仕様: 文字列のコレクション
    - 仕様: Nullable
  - **メンション対象チームID（MentionTargetTeamIds）**: 
    - 仕様: 文字列のコレクション
    - 仕様: Nullable
- 集約内エンティティを表すプロパティ
  - **添付ファイル（Files）**: 
    - 参照情報: 課題プロジェクト協議スレッドファイルエンティティのコレクション
    - 仕様: 必須
  - **コメント（Comments）**: 
    - 参照情報: 課題プロジェクト協議コメントエンティティのコレクション
    - 仕様: 必須
- 関連情報参照用プロパティ
  - **リアクション（Reactions）**: 
    - 参照情報: 課題プロジェクト協議スレッドリアクションエンティティのコレクション
    - 仕様: 必須

### 課題プロジェクト協議スレッドファイル（IssueProjectDiscussionThreadFile）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **ファイル名称（FileName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **添付先のID（ThreadId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新日時（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **更新者ID（UpdaterId）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **更新者名（UpdaterName）**: 
    - 仕様: 文字列
    - 仕様: Nullable

### 課題プロジェクト協議スレッドリアクション（IssueProjectDiscussionThreadReaction）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **スレッドID（ThreadId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **リアクションタイプ（ReactionType）**: 
    - 仕様: ReactionType列挙型
    - 仕様: 必須
    - 備考: いいね・確認しました、おめでとう・契約お疲れ様、OK、驚き
  - **更新者ID（StaffId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新者（StaffName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新日時（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須

### 課題プロジェクト協議種別（IssueProjectDiscussionType）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **協議種別名称（DiscussionTypeName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **稟議系協議か否か（IsApprovalDiscussion）**: 
    - 仕様: ブール型
    - 仕様: 必須
  - **無効状態の協議種別か否か（IsInactive）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
  - **目的（PurposeTypeOfTemplate）**: 
    - 仕様: PurposeTypeOfTemplate列挙型
    - 仕様: 必須
    - 備考: 社内協議、社外協議、社内外協議
  - **表示順序（Order）**: 
    - 仕様: 整数型
    - 仕様: Nullable

### 課題プロジェクトお気に入り情報（IssueProjectFavoriteInformation）

- 基本プロパティ（FavoriteInformationクラスから継承）
- 固有プロパティ
  - **課題案件ID（IssueProjectId）**: 
    - 仕様: 文字列
    - 仕様: 必須

### 課題プロジェクトファイル（IssueProjectFile）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **ファイル名（FileName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新者（Updater）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新者ID（UpdaterId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新日時（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **課題案件ID（IssueProjectId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **バージョン（Version）**: 
    - 仕様: 文字列
    - 仕様: 必須

### 課題プロジェクトリンク（IssueProjectLink）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **タイトル（Title）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **URL（Url）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新者（Updater）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新者ID（UpdaterId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新日時（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **課題案件ID（IssueProjectId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **バージョン（Version）**: 
    - 仕様: 文字列
    - 仕様: 必須

### 課題プロジェクトタスク（IssueProjectTask）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **顧客識別ID（CustomerIdentificationId）**: 
    - 仕様: GUID型
    - 仕様: 必須
  - **依頼日（RequestedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **期日（ExpiredAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **担当者ID（StaffId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **タスク登録者ID（RegistrantId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **概要（Overview）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **詳細（Detail）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **ステータス（IssueProjectTaskStatus）**: 
    - 仕様: IssueProjectTaskStatus列挙型
    - 仕様: 必須
    - 備考: 着手前、着手中、完了済み
  - **課題案件ID（IssueProjectId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **更新日（UpdatedDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: 必須
  - **アクセス制限対象か（IsAccessRestricted）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
- 集約内エンティティを表すプロパティ
  - **タスク添付ファイル（TaskFiles）**: 
    - 参照情報: タスクファイルエンティティのコレクション
    - 仕様: Nullable
- 関連情報参照用プロパティ
  - **課題案件（IssueProject）**: 
    - 参照情報: 課題プロジェクトエンティティ
    - 仕様: 必須

### リース案件協議スレッド（LeaseCaseDiscussionThread）

- 基本プロパティ（CaseDiscussionThreadクラスから継承）
- 固有プロパティ
  - **協議スレッドタイトル項目（ThreadNameTypes）**: 
    - 仕様: ThreadNameType列挙型のコレクション
    - 仕様: 必須
    - 備考: 金額申請、金利申請、期間申請、新規先、その他
  - **協議スレッドタイトル項目で「その他」を選んだ際の協議スレッドタイトルテキスト（ThreadNameForOther）**: 
    - 仕様: 文字列
    - 仕様: Nullable

### 融資案件協議スレッド（LoanCaseDiscussionThread）

- 基本プロパティ（CaseDiscussionThreadクラスから継承）
- 固有プロパティ
  - **保証契約が必要となる具体的な理由（ReasonForGuaranty）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **どのような改善を図れば保証契約の変更・解除の可能性が高まるか（HowToImproveForGuaranty）**: 
    - 仕様: 文字列
    - 仕様: 必須

### 新規投資案件（NewInvestmentCase）

- 基本プロパティ（Caseクラスから継承）
- 備考: 追加の固有プロパティなし

### 新規リース案件（NewLeaseCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **リース案件区分（LeaseCaseType）**: 
    - 仕様: LeaseCaseType列挙型
    - 仕様: Nullable
    - 備考: 銀行→総合リース、総合リース単独
  - **金額（Amount）**: 
    - 仕様: 10進数型
    - 仕様: 必須
  - **リース状況（LeaseSituations）**: 
    - 仕様: LeaseSituation列挙型のコレクション
    - 仕様: Nullable
  - **リース担当者ID（LeaseStaffId）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **リース担当者名（LeaseStaffName）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **不成約区分（CancelType）**: 
    - 仕様: CancelType列挙型
    - 仕様: Nullable
  - **不成約事由（CancelReason）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **物件種類（PropertyCategory）**: 
    - 仕様: PropertyCategory列挙型
    - 仕様: Nullable
  - **物件状態（PropertyStatus）**: 
    - 仕様: PropertyStatus列挙型
    - 仕様: Nullable
  - **物件契約種別（PropertyContractTypes）**: 
    - 仕様: PropertyContractType列挙型のコレクション
    - 仕様: Nullable
  - **契約予定日（ContractScheduledAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **商談番号（BusinessMeetingNumber）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **見積番号（QuotationNumber）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **支払いサイクル（PaymentCycle）**: 
    - 仕様: PaymentCycle列挙型
    - 仕様: Nullable
  - **残価設定（ResidualValueSettings）**: 
    - 仕様: ResidualValueSetting列挙型のコレクション
    - 仕様: Nullable
  - **見積調達（QuotationProcurement）**: 
    - 仕様: QuotationProcurement列挙型
    - 仕様: Nullable
  - **月間予定走行距離（ScheduledMonthlyMileage）**: 
    - 仕様: 整数型
    - 仕様: Nullable
  - **設置場所（InstallationLocation）**: 
    - 仕様: InstallationLocation列挙型
    - 仕様: Nullable
  - **設置場所その他（InstallationLocationCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **見積備考（QuotationNote）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **契約書日付（ContractDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **借受証日付（AcceptanceCertificateDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **案件情報入力進捗（CaseInputStatus）**: 
    - 仕様: WorkStatus列挙型
    - 仕様: Nullable
  - **案件情報入力完了日時（CaseInputCompletedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **相談票対象（IsConsultationTarget）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **相談票進捗（ConsultationStatus）**: 
    - 仕様: ConsultationStatus列挙型
    - 仕様: Nullable
  - **相談票承認日時（ConsultationCompletedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **見積作成担当者ID（QuotationCreateStaffId）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **見積作成担当者名（QuotationCreateStaffName）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **見積作成進捗（QuotationCreateStatus）**: 
    - 仕様: WorkStatus列挙型
    - 仕様: Nullable
  - **見積作成完了日時（QuotationCreateCompletedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **見積精査担当者ID（QuotationScrutinizeStaffId）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **見積精査担当者名（QuotationScrutinizeStaffName）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **見積精査進捗（QuotationScrutinizeStatus）**: 
    - 仕様: WorkStatus列挙型
    - 仕様: Nullable
  - **見積精査完了日時（QuotationScrutinizeCompletedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **顧客提案結果（CustomerProposalResult）**: 
    - 仕様: ProposalResult列挙型
    - 仕様: Nullable
  - **顧客提案完了日時（CustomerProposalCompletedAt）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
- 関連情報参照用プロパティ
  - **リース内容（LeaseDetails）**: 
    - 参照情報: LeaseDetailDtoのコレクション
    - 仕様: Nullable

### 新規融資案件（NewLoanCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **科目（SubjectType）**: 
    - 仕様: SubjectType列挙型
    - 仕様: Nullable
  - **金額（Amount）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **期間（ヶ月）（Period）**: 
    - 仕様: 整数型
    - 仕様: Nullable
  - **金利（InterestRate）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **金利その他（InterestRateCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **資金使途（UseOfFundsType）**: 
    - 仕様: UseOfFundsType列挙型
    - 仕様: Nullable
  - **資金使途その他（UseOfFundsCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **返済方法（RepaymentMethodType）**: 
    - 仕様: RepaymentMethodType列挙型
    - 仕様: Nullable
  - **返済方法その他（RepaymentMethodCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **担保・保証人（CollateralOrGuarantee）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **担保・保証人その他（CollateralOrGuaranteeCustom）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **不成約事由（CancelTypeOfLoan）**: 
    - 仕様: CancelTypeOfLoan列挙型
    - 仕様: Nullable
  - **取下げ・失注・謝絶理由（CancelReason）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **事前相談基準対象（PreConsultationStandardTarget）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **ESG関連（RelatedEsg）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **震災関連であるか（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false
  - **現地現物確認者（OnSitePhysicalConfirmer）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **現地現物確認日時（OnSitePhysicalConfirmationDateTime）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **流入経路（TrafficSource）**: 
    - 仕様: TrafficSource列挙型
    - 仕様: Nullable

### その他リース案件（OtherLeaseCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **震災関連であるか（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false

### その他融資案件（OtherLoanCase）

- 基本プロパティ（Caseクラスから継承）
- 固有プロパティ
  - **事前相談基準対象（PreConsultationStandardTarget）**: 
    - 仕様: ブール型
    - 仕様: Nullable
  - **震災関連であるか（IsEarthquakeRelated）**: 
    - 仕様: ブール型
    - 仕様: 必須
    - デフォルト値: false

### プロジェクト情報（ProjectInformation）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **案件担当チーム（IssueProjectTeam）**: 
    - 仕様: IssueProjectTeam列挙型
    - 仕様: Nullable
    - 備考: 経営戦略、ICT、人事戦略、事業承継M&Aなど
  - **案件担当者名（StaffName）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **案件担当者ID（StaffId）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **提案金額（ProposalFee）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **提案日（ProposalDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **コンサル契約日（ConsultingContractDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **契約金額（ContractFee）**: 
    - 仕様: 10進数型
    - 仕様: Nullable
  - **コンサル開始日（ConsultingStartDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **完了期限（DueDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **課題解決日（IssueResolvedDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **課題中止日（IssueCanceledDate）**: 
    - 仕様: 日時型（DateTimeOffset）
    - 仕様: Nullable
  - **連携先（CooperationWith）**: 
    - 仕様: 文字列
    - 仕様: Nullable
  - **課題案件ID（IssueProjectId）**: 
    - 仕様: 文字列
    - 仕様: 必須

### タスクファイル（TaskFile）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **ファイル名（FileName）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **ファイルパス（FilePath）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **タスクID（IssueProjectTaskId）**: 
    - 仕様: 文字列
    - 仕様: 必須

### チーム（Team）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **チーム名（TeamName）**: 
    - 仕様: 文字列
    - 仕様: 必須
- 集約内エンティティを表すプロパティ
  - **チームメンバー（TeamMembers）**: 
    - 参照情報: チームメンバーエンティティのコレクション
    - 仕様: 必須

### チームメンバー（TeamMember）

- 基本プロパティ
  - **ID（Id）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **所属チームID（TeamId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **担当者ID（StaffId）**: 
    - 仕様: 文字列
    - 仕様: 必須
  - **リーダー（IsLeader）**: 
    - 仕様: ブール型
    - 仕様: 必須

### 当座貸越案件（UnorganizedLoanCase）

- 基本プロパティ（Caseクラスから継承）
- 備考: 追加の固有プロパティなし

## 2. 値オブジェクト（Value Objects）

TODO

## 3. 集約（Aggregates）

TODO

## 4. ドメインイベント（Domain Events）

TODO

## 5. 列挙型（Enumerations）

### 案件ステータス（CaseStatus）

- Undefined
  - 表示名: "未定義"
- BeforeProposal
  - 表示名: "提案前"
- UnderProposal
  - 表示名: "提案中"
- Accepted
  - 表示名: "応諾済み"
- Finished
  - 表示名: "完了"
- CancelBeforeProposal
  - 表示名: "提案前取下"
- CancelAfterProposal
  - 表示名: "提案後失注"
- CancelWithOurCircumstance
  - 表示名: "謝絶"

### 案件カテゴリ（CaseCategory）

- Undefined
  - 表示名: "未定義"
- NewLoan
  - 表示名: "融資案件（新規）"
- OtherLoan
  - 表示名: "融資案件（その他）"
- UnorganizedLoan
  - 表示名: "融資案件（未整理）"
- DeedLoan
  - 表示名: "融資案件（証書貸付）"
- CommercialBill
  - 表示名: "融資案件（商業手形）"
- AcceptanceAndGuarantee
  - 表示名: "融資案件（支払承諾）"
- ForeignExchangeTransaction
  - 表示名: "融資案件（外為取引）"
- CreditLimit
  - 表示名: "融資案件（極度）"
- NewLease
  - 表示名: "リース案件（新規）"
- ExternallyCompletedLease
  - 表示名: "リース案件（他社満了）"
- OtherLease
  - 表示名: "リース案件（その他）"
  - 備考: 未使用：リース案件（他社満了）に置き換えられたため現在使われない
- GeneralTransaction
  - 表示名: "総合取引案件"
- CorporateLifeInsurance
  - 表示名: "法人保険案件（生保）"
- CorporateNonlifeInsurance
  - 表示名: "法人保険案件（損保）"
- NewInvestment
  - 表示名: "投資案件（新規）"
- IssueProject
  - 表示名: "課題案件"
- BusinessMatchingBuy
  - 表示名: "ビジネスマッチング(買)"
- BusinessMatchingSell
  - 表示名: "ビジネスマッチング(売)"
- MAndAMatchingBuy
  - 表示名: "M&Aマッチング(買)"
- MAndAMatchingSell
  - 表示名: "M&Aマッチング(売)"
- RecruitMatchingPermanentJob
  - 表示名: "人材マッチング(正社員)"

### 案件協議目的（CaseDiscussionPurpose）

- Undefined
  - 表示名: "未定義"
- Internal
  - 表示名: "社内協議"
- External
  - 表示名: "社外協議"

### 案件協議スレッド種別（CaseDiscussionThreadType）

- Undefined
  - 表示名: "未定義"
- General
  - 表示名: "汎用"
- Loan
  - 表示名: "融資"
- Lease
  - 表示名: "リース"

### 解約種別（CancelType）

- Undefined
  - 表示名: "未定義"
- SelfFinancing
  - 表示名: "自己資金対応"
- PostponementOrCancell
  - 表示名: "設備投資延期/中止"
- LeaseByOther
  - 表示名: "他社リース"
- FinancingByOurCompany
  - 表示名: "融資対応(当社）"
- FinancingByOther
  - 表示名: "融資対応（他社）"
- Other
  - 表示名: "その他"

### 融資解約種別（CancelTypeOfLoan）

- Undefined
  - 表示名: "未定義"
- SelfFinancing
  - 表示名: "自己資金"
- LoanFromOtherBanksByRelationships
  - 表示名: "他行調達（リレーション）"
- LoanFromOtherBanksByInterestRates
  - 表示名: "他行調達（金利）"
- LoanFromOtherBanksByOther
  - 表示名: "他行調達（他条件：詳細はコメント欄記載）"
- LeaseByOurCompany
  - 表示名: "当社グループリース"
- LeaseByOther
  - 表示名: "他社リース"
- PostponementOrCancell
  - 表示名: "設備投資延期/中止"
- Other
  - 表示名: "その他"

### 相談票進捗（ConsultationStatus）

- Undefined
  - 表示名: "未定義"
- InApplication
  - 表示名: "申請中"
- Approval
  - 表示名: "承認"

### CSVインポート処理状況（CsvImportProcessStatus）

- Undefined
  - 表示名: "未定義"
- InProgress
  - 表示名: "処理中"
- Succeeded
  - 表示名: "成功"
- Failed
  - 表示名: "失敗"
- Deleted
  - 表示名: "削除済"

### お客様の課題認識（CustomerIssuePerception）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- HighestPriority
  - 値: 1
  - 表示名: "最優先取組み課題"
- Priority
  - 値: 2
  - 表示名: "優先取組み課題"
- Future
  - 値: 3
  - 表示名: "今後取組み課題"
- InformationGathering
  - 値: 4
  - 表示名: "情報収集"

### 締切管理ステータス（DeadlineManagementStatus）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- DeadlineManagementStatus1
  - 値: 1
  - 表示名: "期日管理ステータス1"
- DeadlineManagementStatus2
  - 値: 2
  - 表示名: "期日管理ステータス2"
- DeadlineManagementStatus3
  - 値: 3
  - 表示名: "期日管理ステータス3"
  - 備考: 選択肢の内容が決まったら修正する

### 協議目的（DiscussionPurpose）

- Undefined
  - 表示名: "未定義"
- Internal
  - 表示名: "社内協議"
- External
  - 表示名: "社外協議"

### お気に入りカテゴリ（FavoriteCategory）

- Undefined
  - 表示名: "未定義"
- Case
  - 表示名: "案件"
- IssueProject
  - 表示名: "課題案件"

### 業種詳細（IndustryDetail）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- Agriculture
  - 値: 1
  - 表示名: "農業"
- Forestry
  - 値: 2
  - 表示名: "林業"
- Fisheries
  - 値: 3
  - 表示名: "漁業（水産養殖業を除く)"
- Aquaculture
  - 値: 4
  - 表示名: "水産養殖業"
- Mining
  - 値: 5
  - 表示名: "鉱業、採石業、砂利採取業"
- GeneralConstruction
  - 値: 6
  - 表示名: "総合工事業"
- SpecificConstruction
  - 値: 7
  - 表示名: "職別工事業（設備工事業を除く)"
- FacilityConstruction
  - 値: 8
  - 表示名: "設備工事業"
- FoodManufacturing
  - 値: 9
  - 表示名: "食料品製造業"
- BeverageTobaccoFeedManufacturing
  - 値: 10
  - 表示名: "飲料・たばこ・飼料製造業"
- TextileManufacturing
  - 値: 11
  - 表示名: "繊維工業"
- WoodProductManufacturing
  - 値: 12
  - 表示名: "木材・木製品製造業（家具を除く)"
- FurnitureEquipmentManufacturing
  - 値: 13
  - 表示名: "家具・装備品製造業"
- PulpPaperPaperProductManufacturing
  - 値: 14
  - 表示名: "パルプ・紙・紙加工品製造業"
- PrintingRelatedServices
  - 値: 15
  - 表示名: "印刷・同関連業"
- ChemicalIndustry
  - 値: 16
  - 表示名: "化学工業"
- PetroleumCoalProductManufacturing
  - 値: 17
  - 表示名: "石油製品・石炭製品製造業"
- PlasticProductManufacturing
  - 値: 18
  - 表示名: "プラスチック製品製造業（別掲を除く）"
- RubberProductManufacturing
  - 値: 19
  - 表示名: "ゴム製品製造業"
- LeatherProductManufacturing
  - 値: 20
  - 表示名: "なめし革・同製品・毛皮製造業"
- CeramicsStoneProductManufacturing
  - 値: 21
  - 表示名: "窯業・土石製品製造業"
- IronSteelIndustry
  - 値: 22
  - 表示名: "鉄鋼業"
- NonferrousMetalManufacturing
  - 値: 23
  - 表示名: "非鉄金属製造業"
- MetalProductManufacturing
  - 値: 24
  - 表示名: "金属製品製造業"
- WeldingMachineryManufacturing
  - 値: 25
  - 表示名: "はん用機械器具製造業"
- ProductionMachineryManufacturing
  - 値: 26
  - 表示名: "生産用機械器具製造業"
- BusinessMachineryManufacturing
  - 値: 27
  - 表示名: "業務用機械器具製造業"
- ElectronicComponentManufacturing
  - 値: 28
  - 表示名: "電子部品・デバイス・電子回路製造業"
- ElectricMachineryManufacturing
  - 値: 29
  - 表示名: "電気機械器具製造業"
- InformationCommunicationMachineryManufacturing
  - 値: 30
  - 表示名: "情報通信機械器具製造業"
- TransportationMachineryManufacturing
  - 値: 31
  - 表示名: "輸送用機械器具製造業"
- OtherManufacturing
  - 値: 32
  - 表示名: "その他の製造業"
- ElectricPowerIndustry
  - 値: 33
  - 表示名: "電気業"
- GasIndustry
  - 値: 34
  - 表示名: "ガス業"
- HeatSupplyIndustry
  - 値: 35
  - 表示名: "熱供給業"
- WaterSupplyIndustry
  - 値: 36
  - 表示名: "水道業"
- CommunicationIndustry
  - 値: 37
  - 表示名: "通信業"
- BroadcastingIndustry
  - 値: 38
  - 表示名: "放送業"
- InformationServices
  - 値: 39
  - 表示名: "情報サービス業"
- InternetRelatedServices
  - 値: 40
  - 表示名: "インターネット附随サービス業"
- AudioVisualTextInformationProduction
  - 値: 41
  - 表示名: "映像・音声・文字情報制作業"
- RailwayIndustry
  - 値: 42
  - 表示名: "鉄道業"
- RoadPassengerTransportIndustry
  - 値: 43
  - 表示名: "道路旅客運送業"
- RoadFreightTransportIndustry
  - 値: 44
  - 表示名: "道路貨物運送業"
- WaterTransportIndustry
  - 値: 45
  - 表示名: "水運業"
- AviationTransportIndustry
  - 値: 46
  - 表示名: "航空運輸業"
- WarehousingIndustry
  - 値: 47
  - 表示名: "倉庫業"
- TransportRelatedServices
  - 値: 48
  - 表示名: "運輸に附帯するサービス業"
- PostalIndustry
  - 値: 49
  - 表示名: "郵便業（信書便事業を含む）"
- VariousCommodityWholesaleIndustry
  - 値: 50
  - 表示名: "各種商品卸売業"
- TextileClothingWholesaleIndustry
  - 値: 51
  - 表示名: "繊維・衣服等卸売業"
- FoodBeverageWholesaleIndustry
  - 値: 52
  - 表示名: "飲食料品卸売業"
- ConstructionMineralMetalMaterialWholesaleIndustry
  - 値: 53
  - 表示名: "建築材料、 鉱物・金属材料等卸売業"
- MachineryEquipmentWholesaleIndustry
  - 値: 54
  - 表示名: "機械器具卸売業"
- OtherWholesaleIndustry
  - 値: 55
  - 表示名: "その他の卸売業"
- VariousCommodityRetailIndustry
  - 値: 56
  - 表示名: "各種商品小売業"
- TextileClothingGeneralMerchandiseRetailIndustry
  - 値: 57
  - 表示名: "織物・衣服・身の回り品小売業"
- FoodBeverageGeneralMerchandiseRetailIndustry
  - 値: 58
  - 表示名: "飲食料品小売業"
- MachineryEquipmentGeneralMerchandiseRetailIndustry
  - 値: 59
  - 表示名: "機械器具小売業"
- OtherRetailIndustry
  - 値: 60
  - 表示名: "その他の小売業"
- NonstoreRetailIndustry
  - 値: 61
  - 表示名: "無店舗小売業"
- BankingIndustry
  - 値: 62
  - 表示名: "銀行業"
- CooperativeFinancialIndustry
  - 値: 63
  - 表示名: "協同組織金融業"
- MoneyLendingCreditCardNondepositoryCreditInstitutionIndustry
  - 値: 64
  - 表示名: "貸金業、クレジットカード業等非預金信用機関"
- FinancialCommodityFuturesTradingIndustry
  - 値: 65
  - 表示名: "金融商品取引業、商品先物取引業"
- AncillaryFinancialIndustry
  - 値: 66
  - 表示名: "補助的金融業等"
- InsuranceIndustry
  - 値: 67
  - 表示名: "保険業（保険媒介代理業、保険サービス業を含む）"
- RealEstateIndustry
  - 値: 68
  - 表示名: "不動産取引業"
- RealEstateLeasingManagementIndustry
  - 値: 69
  - 表示名: "不動産賃貸業・管理業"
- GoodsLeasingIndustry
  - 値: 70
  - 表示名: "物品賃貸業"
- AcademicResearchInstitutions
  - 値: 71
  - 表示名: "学術・開発研究機関"
- SpecializedServices
  - 値: 72
  - 表示名: "専門サービス業（他に分類されないもの）"
- AdvertisingIndustry
  - 値: 73
  - 表示名: "広告業"
- TechnicalServices
  - 値: 74
  - 表示名: "技術サービス業（他に分類されないもの）"
- AccommodationIndustry
  - 値: 75
  - 表示名: "宿泊業"
- Restaurants
  - 値: 76
  - 表示名: "飲食店"
- TakeoutDeliveryFoodServicesIndustry
  - 値: 77
  - 表示名: "持ち帰り・配達飲食サービス業"
- LaundryBarberBeautyBathIndustry
  - 値: 78
  - 表示名: "洗濯・理容・美容・浴場業"
- OtherLifestyleServicesIndustry
  - 値: 79
  - 表示名: "その他の生活関連サービス業"
- EntertainmentIndustry
  - 値: 80
  - 表示名: "娯楽業"
- SchoolEducation
  - 値: 81
  - 表示名: "学校教育"
- OtherEducationLearningSupportIndustry
  - 値: 82
  - 表示名: "その他の教育、学習支援業"
- MedicalIndustry
  - 値: 83
  - 表示名: "医療業"
- HealthSanitation
  - 値: 84
  - 表示名: "保健衛生"
- SocialInsuranceWelfareNursingCareIndustry
  - 値: 85
  - 表示名: "社会保険・社会福祉・介護事業"
- PostOffice
  - 値: 86
  - 表示名: "郵便局"
- Cooperatives
  - 値: 87
  - 表示名: "協同組合（他に分類されないもの）"
- WasteDisposalIndustry
  - 値: 88
  - 表示名: "廃棄物処理業"
- AutomobileMaintenanceIndustry
  - 値: 89
  - 表示名: "自動車整備業"
- MachineryRepairIndustry
  - 値: 90
  - 表示名: "機械等修理業（別掲を除く）"
- OtherBusinessServicesIndustry
  - 値: 91
  - 表示名: "その他の事業サービス業"
- PoliticalEconomicCulturalOrganizations
  - 値: 92
  - 表示名: "政治・経済・文化団体"
- Religion
  - 値: 93
  - 表示名: "宗教"
- OtherServicesIndustry
  - 値: 94
  - 表示名: "その他のサービス業"
- ForeignPublicService
  - 値: 95
  - 表示名: "外国公務"
- NationalPublicService
  - 値: 96
  - 表示名: "国家公務"
- LocalPublicService
  - 値: 97
  - 表示名: "地方公務"
- UnclassifiableIndustry
  - 値: 98
  - 表示名: "分類不能の産業"

### 入力タイプ（InputType）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- Title
  - 値: 1
  - 表示名: "タイトル"
- TextBox
  - 値: 2
  - 表示名: "テキストボックス"
- TextArea
  - 値: 3
  - 表示名: "テキストエリア"
- Int
  - 値: 4
  - 表示名: "整数"
- Decimal
  - 値: 5
  - 表示名: "実数"
- DateTime
  - 値: 6
  - 表示名: "日付"
- Radio
  - 値: 7
  - 表示名: "選択式：ラジオボタン"
- DropDownList
  - 値: 8
  - 表示名: "選択式：ドロップダウンリスト"
- UserSelector
  - 値: 9
  - 表示名: "選択式：ドロップダウンリスト(社員)"
- Checkbox
  - 値: 10
  - 表示名: "チェックボックス"

### 設置場所（InstallationLocation）

- Undefined
  - 表示名: "未定義"
- Office
  - 表示名: "本社"
- Other
  - 表示名: "その他"

### 接触カテゴリグループ（InteractionCategoryGroup）

- Undefined
  - 表示名: "未定義"
- BusinessUnderstandingAndRelationLevel
  - 表示名: "事業性理解・リレーションレベル"
- BusinessModel
  - 表示名: "理解状況(商流図・SWOT等)"
- BusinessUnderstandingInternalDiscussion
  - 表示名: "社内協議の管理"
- BusinessUnderstandingExternalDiscussion
  - 表示名: "社外協議の管理"
- OurPolicyUnderstanding
  - 表示名: "当社の方針・施策をご理解いただく"
- CustomerIdeasUnderstanding
  - 表示名: "お客さまの考え方を理解する"
- SharingOfFinance
  - 表示名: "財務の共有"
- HypotheticalDiscussionOfIssue
  - 表示名: "課題の仮説協議"
- ToDo
  - 表示名: "ToDo"

### 課題項目（IssueItem）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- BusinessStrategy
  - 値: 1
  - 表示名: "経営戦略"
- CostManagement
  - 値: 2
  - 表示名: "原価管理"
- ICTUtilization
  - 値: 3
  - 表示名: "ICT利活用"
- OfficeBPR
  - 値: 4
  - 表示名: "事務BPR"
- PersonnelSystem
  - 値: 5
  - 表示名: "人事制度"
- TalentDevelopment
  - 値: 6
  - 表示名: "人材育成"
- BusinessSuccession
  - 値: 7
  - 表示名: "事業承継"
- SubsidyUtilization
  - 値: 9
  - 表示名: "補助金活用"
- InternationalBusiness
  - 値: 10
  - 表示名: "海外ビジネス"
- InternalControlAndGovernance
  - 値: 11
  - 表示名: "内部統制・ガバナンス"
- ESG_SDGs
  - 値: 12
  - 表示名: "ESG/SDGs"
- DigitalMarketing
  - 値: 13
  - 表示名: "デジタルマーケティング"
- LecturesAndSeminars
  - 値: 14
  - 表示名: "講演・セミナー"
- OwnerTransactions
  - 値: 15
  - 表示名: "オーナー取引"
- CorporatePension_401k
  - 値: 16
  - 表示名: "企業年金・401k"
- CorporateSurplusManagement
  - 値: 17
  - 表示名: "法人余資運用"
- BPO
  - 値: 18
  - 表示名: "BPO"
- Other
  - 値: 99
  - 表示名: "その他"

### 課題プロジェクトチーム（IssueProjectTeam）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- Strategy
  - 値: 1
  - 表示名: "経営戦略"
- ICT
  - 値: 2
  - 表示名: "ICT"
- HumanResourcesStrategy
  - 値: 3
  - 表示名: "人事戦略"
- BusinessSuccessionMnA
  - 値: 4
  - 表示名: "事業承継M&A"
- OverseasBusiness
  - 値: 5
  - 表示名: "海外ビジネス"
- CCISingapore
  - 値: 6
  - 表示名: "CCIシンガポール"
- CCIVietnam
  - 値: 7
  - 表示名: "CCIベトナム"
- CCIThailand
  - 値: 8
  - 表示名: "タイCCI"
- CCIShenzhen
  - 値: 9
  - 表示名: "深圳CCI"
- Solution
  - 値: 10
  - 表示名: "ソリューション"
- DCTeam
  - 値: 11
  - 表示名: "DCチーム"
- Marketing
  - 値: 12
  - 表示名: "マーケティング"
- FDAlco
  - 値: 13
  - 表示名: "FDAlco"
- BPO
  - 値: 14
  - 表示名: "BPO"
- CCIAfrica
  - 値: 15
  - 表示名: "CCIアフリカ"
- Other
  - 値: 99
  - 表示名: "その他"

### 課題プロジェクトタスクステータス（IssueProjectTaskStatus）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- NotStarted
  - 値: 1
  - 表示名: "着手前"
- InProgress
  - 値: 2
  - 表示名: "着手中"
- Completed
  - 値: 3
  - 表示名: "完了済み"

### 課題ステータス（IssueStatus）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- BeforeProposal
  - 値: 1
  - 表示名: "提案前"
- UnderProposal
  - 値: 2
  - 表示名: "提案中"
- Accepted
  - 値: 3
  - 表示名: "応諾済み"
- UnderConsulting
  - 値: 4
  - 表示名: "コンサル中"
- Finished
  - 値: 5
  - 表示名: "完了"
- CancelBeforeProposal
  - 値: 6
  - 表示名: "提案前取下"
- CancelAfterProposal
  - 値: 7
  - 表示名: "提案後失注"
- CancelWithOurCircumstance
  - 値: 8
  - 表示名: "謝絶"

### リース案件種別（LeaseCaseType）

- Undefined
  - 表示名: "未定義"
- FromBank
  - 表示名: "銀行→総合リース"
- OnlyLease
  - 表示名: "総合リース単独"

### リース状況（LeaseSituation）

- Undefined
  - 表示名: "未定義"
- SmallAmount
  - 表示名: "少額案件応諾済"
- SwitchFromOther
  - 表示名: "他社満了からの切替案件"

### お客様への次のアクション（NextActionforCustomer）

- Undefined
  - 値: 0
  - 表示名: "未定義"
- InvestigatingInformationAndNeeds
  - 値: 1
  - 表示名: "情報の深堀、ニーズ確認"
- ImplementingProposals
  - 値: 2
  - 表示名: "提案実施"
- ReviewingProposalProgress
  - 値: 3
  - 表示名: "提案検討状況確認"
- ContractProcessing
  - 値: 4
  - 表示名: "契約手続き"
- ProvidingExecutionSupport
  - 値: 5
  - 表示名: "実行支援中"
- Other
  - 値: 99
  - 表示名: "その他"

### 通知タイプ（NotificationType）

- Undefined
  - 表示名: "未定義"
- Mention
  - 表示名: "メンション"
- Post
  - 表示名: "投稿"
- Reply
  - 表示名: "返信"
- Update
  - 表示名: "更新"

### 支払サイクル（PaymentCycle）

- Undefined
  - 表示名: "未定義"
- EndOfNextMonth
  - 表示名: "検収翌月末"
- Shortest
  - 表示名: "最短支払"
- Special
  - 表示名: "手形支払等（特殊）"

### 物件カテゴリ（PropertyCategory）

- Undefined
  - 表示名: "未定義"
- General
  - 表示名: "一般"
- Car
  - 表示名: "自動車"

### 物件契約タイプ（PropertyContractType）

- Undefined
  - 表示名: "未定義"
- ResidualValueSetting
  - 表示名: "残価設定"
- Maintenance
  - 表示名: "メンテナンス"
- DeferredPayment
  - 表示名: "延払（割賦）"
- Subsidy
  - 表示名: "補助金関連"
- TransferOfOwnership
  - 表示名: "所有権移転"

### 物件ステータス（PropertyStatus）

- Undefined
  - 表示名: "未定義"
- New
  - 表示名: "新品"
- Used
  - 表示名: "中古"

### 提案結果（ProposalResult）

- Undefined
  - 表示名: "未定義"
- Acceptance
  - 表示名: "応諾"
- LostOrder
  - 表示名: "失注"

### テンプレート目的タイプ（PurposeTypeOfTemplate）

- Undefined
  - 表示名: "未定義"
- Internal
  - 表示名: "社内協議"
- External
  - 表示名: "社外協議"
- InternalOrExternal
  - 表示名: "社内外協議"

### 見積調達（QuotationProcurement）

- Undefined
  - 表示名: "未定義"
- ByUser
  - 表示名: "ユーザ調達"
- ByOurCompany
  - 表示名: "自社調達"

### リアクション種別（ReactionType）

- Undefined
  - 値: 0
  - 表示名: ""
- Good
  - 値: 1
  - 表示名: "いいね・確認しました"
- Congratulations
  - 値: 2
  - 表示名: "おめでとう・契約お疲れ様"
- OK
  - 値: 3
  - 表示名: "OK"
- Surprise
  - 値: 4
  - 表示名: "驚き"

### 返済方法タイプ（RepaymentMethodType）

- Undefined
  - 表示名: "未定義"
- LumpSum
  - 表示名: "期日一括"
- Installments
  - 表示名: "分割"
- Other
  - 表示名: "その他"

### 残価設定（ResidualValueSetting）

- Undefined
  - 表示名: "未定義"
- None
  - 表示名: "なし（フルペイ扱い）"
- AppraisedValue
  - 表示名: "オープン（査定額と約同額）"
- AboutFive
  - 表示名: "オープン（約５％）"
- Close
  - 表示名: "クローズ（車両のみ選択可）"

### 件名タイプ（SubjectType）

- Undefined
  - 表示名: "未定義"
- DeedLoanByOurCompany
  - 表示名: "証貸(プロパー）"
- DeedLoanByGuaranteeCorporation
  - 表示名: "証貸（協会）"
- Overdraft
  - 表示名: "当座貸越"
- BillDiscount
  - 表示名: "商手・でんさい割引"
- PaymentAuthorization
  - 表示名: "支払承諾"
- Other
  - 表示名: "その他"

### スレッド名タイプ（ThreadNameType）

- Undefined
  - 表示名: "未定義"
- AmountApplication
  - 表示名: "金額申請"
- InterestRateApplication
  - 表示名: "金利申請"
- PeriodApplication
  - 表示名: "期間申請"
- NewCustomer
  - 表示名: "新規先"
- Other
  - 表示名: "その他"

### 流入元（TrafficSource）

- Undefined
  - 表示名: "未定義"
- FieldSales
  - 表示名: "対面（FS）"
- Forms
  - 表示名: "Forms"
- InsideSales
  - 表示名: "受電（IS）・メール"
- OtherDepartment
  - 表示名: "他部署連携"

### 資金使途タイプ（UseOfFundsType）

- Undefined
  - 表示名: "未定義"
- WorkingCapital
  - 表示名: "運転資金"
- CapitalExpenditure
  - 表示名: "設備資金"
- Other
  - 表示名: "その他"

### 作業ステータス（WorkStatus）

- Undefined
  - 表示名: "未定義"
- InProgress
  - 表示名: "作業中"
- Complete
  - 表示名: "完了"

## 3. 集約（Aggregates）

### 案件集約（CaseAggregate）

**ルートエンティティ**: 案件（Case）

**集約に含まれるエンティティ**:
- 案件ファイル（CaseFile） - 案件に添付されるファイル情報
- 案件リンク（CaseLink） - 案件に関連するURLリンク情報  
- 案件更新情報（CaseUpdateInformation） - 案件の最終更新情報
- 案件お気に入り情報（CaseFavoriteInformation） - 案件のお気に入り情報

**集約の責務**:
- 案件の作成、更新、削除のライフサイクル管理
- 案件に関連するファイル、リンク、更新情報の整合性保持
- 案件削除時の関連エンティティの一括削除

### 課題プロジェクト集約（IssueProjectAggregate）

**ルートエンティティ**: 課題プロジェクト（IssueProject）

**集約に含まれるエンティティ**:
- 課題情報（IssueInformation） - 課題プロジェクトの課題詳細情報
- プロジェクト情報（ProjectInformation） - 課題プロジェクトのプロジェクト詳細情報
- 期限管理（DeadlineManagement） - 課題プロジェクトの期限管理情報
- 課題プロジェクトリンク（IssueProjectLink） - 課題プロジェクトに関連するURLリンク情報

**集約の責務**:
- 課題プロジェクトの作成、更新、削除のライフサイクル管理
- 課題情報、プロジェクト情報、期限管理、リンク情報の整合性保持
- 課題プロジェクト削除時の関連エンティティの一括削除

### チーム集約（TeamAggregate）

**ルートエンティティ**: チーム（Team）

**集約に含まれるエンティティ**:
- チームメンバー（TeamMember） - チームに所属するメンバー情報

**集約の責務**:
- チームの作成、更新、削除のライフサイクル管理
- チームメンバーの追加、更新、削除管理
- チーム削除時の関連メンバーの一括削除

### 一般取引種別集約（GeneralTransactionTypeAggregate）

**ルートエンティティ**: 一般取引種別（GeneralTransactionType）

**集約に含まれるエンティティ**:
- なし（単一エンティティ集約）

**集約の責務**:
- 一般取引種別の作成、更新、削除のライフサイクル管理
- 一般取引種別名の重複チェック
- 表示順序の管理

### 課題プロジェクトタスク集約（IssueProjectTaskAggregate）

**ルートエンティティ**: 課題プロジェクトタスク（IssueProjectTask）

**集約に含まれるエンティティ**:
- タスクファイル（TaskFile） - タスクに添付されるファイル情報

**集約の責務**:
- 課題プロジェクトタスクの作成、更新、削除のライフサイクル管理
- タスクファイルの添付、更新、削除管理
- タスク削除時の関連ファイルの一括削除
- タスクステータス変更時の通知処理

### 課題プロジェクト協議スレッド集約（IssueProjectDiscussionThreadAggregate）

**ルートエンティティ**: 課題プロジェクト協議スレッド（IssueProjectDiscussionThread）

**集約に含まれるエンティティ**:
- 課題プロジェクト協議スレッドファイル（IssueProjectDiscussionThreadFile） - スレッドに添付されるファイル情報
- 課題プロジェクト協議コメント（IssueProjectDiscussionComment） - スレッドに対するコメント
- 課題プロジェクト協議コメントファイル（IssueProjectDiscussionCommentFile） - コメントに添付されるファイル情報

**集約の責務**:
- 課題プロジェクト協議スレッドの作成、更新、削除のライフサイクル管理
- スレッドファイル、コメント、コメントファイルの管理
- スレッド削除時の関連エンティティの一括削除
- メンション機能と通知処理
- 稟議スレッドの返信制御（返信済み稟議スレッドの更新・削除制限）

### 案件協議スレッド集約（CaseDiscussionThreadAggregate）

**ルートエンティティ**: 案件協議スレッド（CaseDiscussionThread）

**集約に含まれるエンティティ**:
- 案件協議スレッドファイル（CaseDiscussionThreadFile） - スレッドに添付されるファイル情報

**集約の責務**:
- 案件協議スレッドの作成、更新、削除のライフサイクル管理
- スレッドファイルの添付、更新、削除管理
- スレッド削除時の関連ファイルの一括削除
- メンション機能と通知処理
- 案件種別に応じたスレッド内容の表示形式管理

### 案件協議返信集約（CaseDiscussionReplyAggregate）

**ルートエンティティ**: 案件協議返信（CaseDiscussionReply）

**集約に含まれるエンティティ**:
- 案件協議返信ファイル（CaseDiscussionReplyFile） - 返信に添付されるファイル情報

**集約の責務**:
- 案件協議返信の作成、更新、削除のライフサイクル管理
- 返信ファイルの添付、更新、削除管理
- 返信削除時の関連ファイルと本文コンテンツの一括削除
- メンション機能と通知処理
- 返信本文のURL変換処理

### 課題プロジェクト協議コメント集約（IssueProjectDiscussionCommentAggregate）

**ルートエンティティ**: 課題プロジェクト協議コメント（IssueProjectDiscussionComment）

**集約に含まれるエンティティ**:
- 課題プロジェクト協議コメントファイル（IssueProjectDiscussionCommentFile） - コメントに添付されるファイル情報

**集約の責務**:
- 課題プロジェクト協議コメントの作成、更新、削除のライフサイクル管理
- コメントファイルの添付、更新、削除管理
- コメント削除時の関連ファイルと本文コンテンツの一括削除
- メンション機能と通知処理
- コメント本文のURL変換処理

### 案件協議返信反応集約（CaseDiscussionReplyReactionAggregate）

**ルートエンティティ**: 案件協議返信反応（CaseDiscussionReplyReaction）

**集約に含まれるエンティティ**:
- なし（単一エンティティ集約）

**集約の責務**:
- 案件協議返信反応の作成、更新のライフサイクル管理
- リアクションタイプの変更管理

### 案件協議スレッド反応集約（CaseDiscussionThreadReactionAggregate）

**ルートエンティティ**: 案件協議スレッド反応（CaseDiscussionThreadReaction）

**集約に含まれるエンティティ**:
- なし（単一エンティティ集約）

**集約の責務**:
- 案件協議スレッド反応の作成、更新のライフサイクル管理
- リアクションタイプの変更管理

### 案件お気に入り情報集約（CaseFavoriteInformationAggregate）

**ルートエンティティ**: 案件お気に入り情報（CaseFavoriteInformation）

**集約に含まれるエンティティ**:
- なし（単一エンティティ集約）

**集約の責務**:
- 案件お気に入り情報の作成、更新のライフサイクル管理
- ユーザーごとの案件お気に入り状態の管理

### 課題プロジェクト協議コメント反応集約（IssueProjectDiscussionCommentReactionAggregate）

**ルートエンティティ**: 課題プロジェクト協議コメント反応（IssueProjectDiscussionCommentReaction）

**集約に含まれるエンティティ**:
- なし（単一エンティティ集約）

**集約の責務**:
- 課題プロジェクト協議コメント反応の作成、更新、削除のライフサイクル管理
- リアクションタイプの変更管理

### 課題プロジェクト協議フォームテンプレート集約（IssueProjectDiscussionFormTemplateAggregate）

**ルートエンティティ**: 課題プロジェクト協議フォームテンプレート（IssueProjectDiscussionFormTemplate）

**集約に含まれるエンティティ**:
- 課題プロジェクト協議種別（IssueProjectDiscussionType）

**集約の責務**:
- 課題プロジェクト協議フォームテンプレートの作成、更新のライフサイクル管理
- 協議種別とテンプレートの一貫性保持
- テンプレートバージョン管理
- 協議種別とテンプレート構成の一括管理

### 課題プロジェクト協議スレッド反応集約（IssueProjectDiscussionThreadReactionAggregate）

**ルートエンティティ**: 課題プロジェクト協議スレッド反応（IssueProjectDiscussionThreadReaction）

**集約に含まれるエンティティ**:
- なし（単一エンティティ集約）

**集約の責務**:
- 課題プロジェクト協議スレッド反応の作成、更新のライフサイクル管理
- リアクションタイプの変更管理

### 課題プロジェクトリンク集約（IssueProjectLinkAggregate）

**ルートエンティティ**: 課題プロジェクトリンク（IssueProjectLink）

**集約に含まれるエンティティ**:
- なし（単一エンティティ集約）

**集約の責務**:
- 課題プロジェクトリンクの作成、更新、削除のライフサイクル管理
- 外部リンク情報とメタデータの管理

## 6. 業務ルール（Business Rules）

TODO
