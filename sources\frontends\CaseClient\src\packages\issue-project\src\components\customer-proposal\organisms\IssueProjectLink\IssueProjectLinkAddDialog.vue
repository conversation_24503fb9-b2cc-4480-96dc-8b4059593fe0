<template>
  <v-dialog
    v-model="dialog"
    width="50%"
    persistent
    @click:outside="cancel"
    @keydown.esc="cancel"
  >
    <v-progress-linear v-if="loading" indeterminate color="green" />
    <app-simple-dialog-tmpl
      :dialog-title="title"
      action-button-text="保存"
      :loading="loading"
      @cancel="cancel"
      @action="save"
    >
      <template #default>
        <v-row>
          <v-col>
            <div>
              <app-textarea
                v-model="targetData.title"
                :error-messages="errorMessages?.title"
                label="タイトル"
                rows="1"
                required-mark
                @blur="validateItem('title')"
                @update:model-value="validateItem('title')"
              />

              <app-textarea
                v-model="targetData.url"
                :error-messages="errorMessages?.url"
                label="URL"
                rows="3"
                required-mark
                @blur="validateItem('url')"
                @update:model-value="validateItem('url')"
              />
            </div>
          </v-col>
        </v-row>
      </template>
    </app-simple-dialog-tmpl>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import { defaultInstance } from '@hox/base/src/utils/shared/zodDefaultInstance'
import {
  type IssueProjectLinkForCreate,
  type IssueProjectLinkForUpdate,
  issueProjectLinkSchemaForCreate,
  issueProjectLinkSchemaForUpdate
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectLink'

type DialogType = 'add' | 'update'

const dialog = ref(false)
const loading = ref(false)
const title = ref('')
const type = ref<DialogType>('add')

let resolvePromise: ((value: { isOk: boolean; data?: IssueProjectLinkForCreate | IssueProjectLinkForUpdate }) => void) | null = null

const schemaForView = issueProjectLinkSchemaForCreate.partial()

function createDefaultViewData() {
  return reactive(defaultInstance<typeof schemaForView>(schemaForView))
}

const targetData = createDefaultViewData()
const targetDataRef = computed(() => {
  return targetData
})

const { errorMessages, validate, validateItem } = useValidation(
  issueProjectLinkSchemaForCreate,
  targetDataRef
)

const { init, hasChanged } = useWatchDataChanges()

function open(data: IssueProjectLinkForCreate | IssueProjectLinkForUpdate, dialogType: DialogType):
Promise<{ isOk: boolean; data?: IssueProjectLinkForCreate | IssueProjectLinkForUpdate }> {
  type.value = dialogType
  Object.assign(targetData, createDefaultViewData())
  Object.assign(targetData, data)

  if (dialogType === 'add') {
    delete targetData.title
    delete targetData.url
  }
  
  if (dialogType === 'add') {
    title.value = 'リンクの登録'
  }
  if (dialogType === 'update') {
    title.value = 'リンクの編集'
  }

  errorMessages.value = {}
  
  init(targetData)
  
  dialog.value = true

  return new Promise((resolve) => {
    resolvePromise = resolve
  })
}

function cancel(): void {
  if (hasChanged.value) {
    const confirmResult = window.confirm(
      '編集中のものは保存されませんが、よろしいですか？'
    )
    if (!confirmResult) return
  }

  errorMessages.value = {}
  dialog.value = false
  
  if (resolvePromise) {
    resolvePromise({ isOk: false })
  }
}

async function save(): Promise<void> {
  const { success } = await validate()
  if (!success) {
    return
  }

  if (resolvePromise) {
    resolvePromise({
      isOk: true,
      data: Object.assign({}, targetData as IssueProjectLinkForCreate | IssueProjectLinkForUpdate),
    })
  }

  dialog.value = false
}

defineExpose({
  open
})
</script>
