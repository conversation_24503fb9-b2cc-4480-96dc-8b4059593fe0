<template>
  <IdentifiedCaseTable
    v-model:page-index="pagination.pageIndex"
    v-model:sort="pagination.sort"
    :page-size="pagination.pageSize"
    :items="data?.items"
    :total="data?.total"
    :loading="loading"
    :search-result-message="searchResultMessage"
    @search="search"
    @add="add"
    @edit="toDetail"
  >
    <template #criteria>
      <IdentifiedCaseSearchCriteria
        v-model:search-condition="searchCondition"
        :case-options="caseOptions"
        :loading="loading"
        :error-messages="errorMessages"
        @update:search-condition="updateSearchCondition"
      />
    </template>
  </IdentifiedCaseTable>
  <SelectCaseTypeDialog ref="selectCaseTypeDialog" />
</template>

<script setup lang="ts">
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useIdentifiedCaseSearch } from '@ibp/case-work-flow-optimization/src/composables/case/useIdentifiedCaseSearch'
import { useIdentifiedCaseOptions } from '@ibp/case-work-flow-optimization/src/composables/case/useIdentifiedCaseOptions'
import { useIdentifiedCaseValidation } from '@ibp/case-work-flow-optimization/src/composables/case/useIdentifiedCaseValidation'
import { useIdentifiedCaseNavigation } from '@ibp/case-work-flow-optimization/src/composables/case/useIdentifiedCaseNavigation'
import { isUndefinedOrNull } from '@hox/base/src/utils/shared/is'
import type { SelectCaseTypeDialogType } from '@ibp/case-work-flow-optimization/src/components/customer-case/SelectCaseTypeDialog.vue'
import IdentifiedCaseTable from './IdentifiedCaseTable.vue'
import IdentifiedCaseSearchCriteria from './IdentifiedCaseSearchCriteria.vue'

// =====================================================================================================================
// Propsインターフェースの定義
// =====================================================================================================================
interface Props {
  customerIdentificationId: string
}

const props = defineProps<Props>()

// =====================================================================================================================
// 画面の定義
// =====================================================================================================================
useHead({
  title: '案件一覧',
})

definePageMeta({})

const customerIdentificationId = ref<string>(props.customerIdentificationId)
const selectCaseTypeDialog = ref<SelectCaseTypeDialogType>()

// オプションデータ取得用コンポーザブル
const {
  options: caseOptions,
  loading: optionsLoading,
  loadAllOptions,
} = useIdentifiedCaseOptions()

// バリデーション関連機能を取得
const validationInstance = useIdentifiedCaseValidation(toRef(() => searchCondition))
const { errorMessages, validateAll } = validationInstance

// 検索関連機能を取得
const {
  searchCondition,
  pagination,
  data,
  searchResultMessage,
  loading: searchLoading,
  criteriaHistory,
  hasState,
  search,
  restoreCriteria,
} = useIdentifiedCaseSearch(customerIdentificationId, validateAll)

// データ整形機能を取得
const { error: errorToast } = useAppToasts()

/**
 * 検索条件更新用の関数
 */
function updateSearchCondition(newValue: any) {
  // 検索条件の各プロパティを更新
  Object.assign(searchCondition, newValue)
}

// 複数のフラグでローディング状態を管理
const { hasTrue: loading, addFlag } = useFlagCondition()

// 初期化時のローディング状態
const initializeLoading = ref(false)
addFlag(initializeLoading)

// 検索ローディング状態を追加
addFlag(searchLoading)

// オプション読み込み中のローディング状態を追加
addFlag(optionsLoading)

// =====================================================================================================================
// 画面初期化処理
// =====================================================================================================================

onMounted(async () => {
  initializeLoading.value = true

  try {
    const hasCriteriaHistory = hasState.value && !isUndefinedOrNull(criteriaHistory.value)

    // オプション情報を取得
    await loadAllOptions()

    if (hasCriteriaHistory) {
      // 検索条件を復元して検索実行
      await restoreCriteria(criteriaHistory.value)
      search({
        noStoreCriteria: true,
      })
    } else {
      // 初期化
      data.value = {
        items: [],
        total: 0,
      }
    }
  } catch (error) {
    errorToast('初期データの読み込みに失敗しました。')
  } finally {
    initializeLoading.value = false
  }
})

// =====================================================================================================================
// 画面で使用するメソッドの定義
// =====================================================================================================================

// ナビゲーション関連機能を取得
const {
  navigateToEdit,
  getAddDialogParams,
  isControlPressed,
} = useIdentifiedCaseNavigation(customerIdentificationId.value)

/**
 * 案件追加処理
 */
function add() {
  const dialogParams = getAddDialogParams()
  selectCaseTypeDialog.value?.open(dialogParams)
}

/**
 * 画面状態をリセットする
 */
function resetScreenState() {
  data.value = { items: [], total: 0 }
  initializeLoading.value = true
}

/**
 * 案件編集画面に遷移
 */
function toDetail(event: any) {
  // Ctrlキーが押されていない場合のみ画面状態をリセット（同一タブでの遷移時のみ）
  if (!isControlPressed.value) {
    resetScreenState()
  }
  navigateToEdit(event)
}
</script>

<style scoped>
.case-container ::v-deep .highlight-row {
  background-color: #f6cfd2;
}
</style>
