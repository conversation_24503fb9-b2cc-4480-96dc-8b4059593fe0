using FluentAssertions;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Domain.Entities;
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.GetEntity;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.GetEntity;

/// <summary>
/// GetEntitySpecificationの単体テストクラス
/// </summary>
/// <remarks>
/// SpecificationTestHelperを使用してSpecificationの動作を検証します。
/// フィルタリング条件、NoTracking設定、データ絞り込みロジックを包括的にテストし、
/// コードカバレッジ100%を目指します。
/// </remarks>
public class GetEntitySpecificationTest
{
    #region NoTracking確認

    [Fact]
    public void AsNoTrackingが指定されている()
    {
        // Arrange
        // TODO: 実際のクエリプロパティに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = "test-id"
            // 他のプロパティも必要に応じて設定
        };
        var spec = new GetEntitySpecification(query);

        // Act & Assert
        SpecificationTestHelper.HasNoTracking(spec).Should().BeTrue();
    }

    #endregion

    #region 基本条件での絞り込み

    [Fact]
    public void IDが指定されている場合はIDで絞り込まれる()
    {
        // Arrange
        var targetId = "target-id";
        // TODO: 実際のクエリクラスに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = targetId
        };
        var spec = new GetEntitySpecification(query);

        // TODO: TestDataクラスのメソッド名を実際のものに変更してください
        var source = new List<Entity>
        {
            // TODO: TestData.Entity.CreateValidメソッドを使用してテストデータを作成してください
            TestData.Entity.CreateValid(targetId, "Target Entity"),
            TestData.Entity.CreateValid("other-id", "Other Entity")
        };

        // Act
        var actual = SpecificationTestHelper.ApplyTo(source, spec);

        // Assert
        actual.Should().HaveCount(1);
        actual.Should().Contain(x => x.Id == targetId);
        actual.Should().NotContain(x => x.Id == "other-id");
    }

    #endregion

    #region 複数条件での絞り込み（必要に応じて）

    [Fact]
    public void IDとStatusが指定されている場合はIDとStatusで絞り込まれる()
    {
        // Arrange
        var targetId = "target-id";
        var targetStatus = Status.Active; // TODO: 実際のStatus enum名に変更してください
        // TODO: 実際のクエリクラスに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = targetId,
            Status = targetStatus
        };
        var spec = new GetEntitySpecification(query);

        // TODO: TestDataクラスのメソッド名を実際のものに変更してください
        var source = new List<Entity>
        {
            TestData.Entity.CreateValid(targetId, "Target Entity", targetStatus),
            TestData.Entity.CreateValid(targetId, "Same ID Different Status", Status.Inactive),
            TestData.Entity.CreateValid("other-id", "Different ID Same Status", targetStatus)
        };

        // Act
        var actual = SpecificationTestHelper.ApplyTo(source, spec);

        // Assert
        actual.Should().HaveCount(1);
        actual.Should().Contain(x => x.Id == targetId && x.Status == targetStatus);
    }

    #endregion

    #region 日付範囲での絞り込み（日付条件がある場合）

    [Fact]
    public void FromDateが指定されている場合はFromDate以降で絞り込まれる()
    {
        // Arrange
        var fromDate = new DateTime(2024, 1, 1);
        // TODO: 実際のクエリクラスに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = "test-id",
            FromDate = fromDate
        };
        var spec = new GetEntitySpecification(query);

        // TODO: TestDataクラスのメソッド名を実際のものに変更してください
        var source = new List<Entity>
        {
            TestData.Entity.CreateValid("test-id", "Within Range", new DateTime(2024, 1, 5)), // FromDate以降
            TestData.Entity.CreateValid("test-id", "Before Range", new DateTime(2023, 12, 31)) // FromDate以前
        };

        // Act
        var actual = SpecificationTestHelper.ApplyTo(source, spec);

        // Assert
        actual.Should().HaveCount(1);
        actual.Should().Contain(x => x.CreatedAt >= fromDate);
    }

    [Fact]
    public void ToDateが指定されている場合はToDate以前で絞り込まれる()
    {
        // Arrange
        var toDate = new DateTime(2024, 12, 31);
        // TODO: 実際のクエリクラスに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = "test-id",
            ToDate = toDate
        };
        var spec = new GetEntitySpecification(query);

        // TODO: TestDataクラスのメソッド名を実際のものに変更してください
        var source = new List<Entity>
        {
            TestData.Entity.CreateValid("test-id", "Within Range", new DateTime(2024, 6, 1)), // ToDate以前
            TestData.Entity.CreateValid("test-id", "After Range", new DateTime(2025, 1, 1)) // ToDate以降
        };

        // Act
        var actual = SpecificationTestHelper.ApplyTo(source, spec);

        // Assert
        actual.Should().HaveCount(1);
        actual.Should().Contain(x => x.CreatedAt <= toDate);
    }

    [Fact]
    public void FromDateとToDateが指定されている場合は期間で絞り込まれる()
    {
        // Arrange
        var fromDate = new DateTime(2024, 1, 1);
        var toDate = new DateTime(2024, 12, 31);
        // TODO: 実際のクエリクラスに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = "test-id",
            FromDate = fromDate,
            ToDate = toDate
        };
        var spec = new GetEntitySpecification(query);

        // TODO: TestDataクラスのメソッド名を実際のものに変更してください
        var source = new List<Entity>
        {
            TestData.Entity.CreateValid("test-id", "Within Range", new DateTime(2024, 6, 1)), // 期間内
            TestData.Entity.CreateValid("test-id", "Before Range", new DateTime(2023, 12, 31)), // 期間外（前）
            TestData.Entity.CreateValid("test-id", "After Range", new DateTime(2025, 1, 1)) // 期間外（後）
        };

        // Act
        var actual = SpecificationTestHelper.ApplyTo(source, spec);

        // Assert
        actual.Should().HaveCount(1);
        actual.Should().Contain(x => x.CreatedAt >= fromDate && x.CreatedAt <= toDate);
    }

    #endregion

    #region 除外条件での絞り込み（ExcludeStatusesなどがある場合）

    [Fact]
    public void ExcludeStatusesが指定されている場合は指定されたStatus以外で絞り込まれる()
    {
        // Arrange
        var excludeStatuses = new List<Status> { Status.Deleted, Status.Archived }; // TODO: 実際のStatus enum名に変更してください
        // TODO: 実際のクエリクラスに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = "test-id",
            ExcludeStatuses = excludeStatuses
        };
        var spec = new GetEntitySpecification(query);

        // TODO: TestDataクラスのメソッド名を実際のものに変更してください
        var source = new List<Entity>
        {
            TestData.Entity.CreateValid("test-id", "Active Entity", Status.Active), // 除外対象外
            TestData.Entity.CreateValid("test-id", "Deleted Entity", Status.Deleted), // 除外対象
            TestData.Entity.CreateValid("test-id", "Archived Entity", Status.Archived) // 除外対象
        };

        // Act
        var actual = SpecificationTestHelper.ApplyTo(source, spec);

        // Assert
        actual.Should().HaveCount(1);
        actual.Should().Contain(x => x.Status == Status.Active);
        actual.Should().NotContain(x => excludeStatuses.Contains(x.Status));
    }

    #endregion

    #region 関連エンティティでの絞り込み（外部キーがある場合）

    [Fact]
    public void ParentIdが指定されている場合はParentIdで絞り込まれる()
    {
        // Arrange
        var targetParentId = "parent-id";
        // TODO: 実際のクエリクラスに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = "test-id",
            ParentId = targetParentId
        };
        var spec = new GetEntitySpecification(query);

        // TODO: TestDataクラスのメソッド名を実際のものに変更してください
        var source = new List<Entity>
        {
            TestData.Entity.CreateValid("test-id", "Target Entity", targetParentId),
            TestData.Entity.CreateValid("test-id", "Different Parent", "other-parent-id")
        };

        // Act
        var actual = SpecificationTestHelper.ApplyTo(source, spec);

        // Assert
        actual.Should().HaveCount(1);
        actual.Should().Contain(x => x.ParentId == targetParentId);
    }

    #endregion

    #region 複合条件の組み合わせテスト

    [Fact]
    public void 全ての条件が指定されている場合は全条件で絞り込まれる()
    {
        // Arrange
        var targetId = "target-id";
        var targetStatus = Status.Active; // TODO: 実際のStatus enum名に変更してください
        var fromDate = new DateTime(2024, 1, 1);
        var toDate = new DateTime(2024, 12, 31);
        var excludeStatuses = new List<Status> { Status.Deleted };
        
        // TODO: 実際のクエリクラスに合わせて設定してください
        var query = new GetEntityQuery()
        {
            Id = targetId,
            Status = targetStatus,
            FromDate = fromDate,
            ToDate = toDate,
            ExcludeStatuses = excludeStatuses
        };
        var spec = new GetEntitySpecification(query);

        // TODO: TestDataクラスのメソッド名を実際のものに変更してください
        var source = new List<Entity>
        {
            // 全条件に合致
            TestData.Entity.CreateValid(targetId, "Perfect Match", targetStatus, new DateTime(2024, 6, 1)),
            // IDが不一致
            TestData.Entity.CreateValid("other-id", "Wrong ID", targetStatus, new DateTime(2024, 6, 1)),
            // Statusが除外対象
            TestData.Entity.CreateValid(targetId, "Excluded Status", Status.Deleted, new DateTime(2024, 6, 1)),
            // 日付が範囲外
            TestData.Entity.CreateValid(targetId, "Out of Range", targetStatus, new DateTime(2025, 1, 1))
        };

        // Act
        var actual = SpecificationTestHelper.ApplyTo(source, spec);

        // Assert
        actual.Should().HaveCount(1);
        actual.Should().Contain(x => 
            x.Id == targetId &&
            x.Status == targetStatus &&
            x.CreatedAt >= fromDate &&
            x.CreatedAt <= toDate &&
            !excludeStatuses.Contains(x.Status));
    }

    #endregion
}
