import type { IssueProjectDiscussionCommentReactionForDelete } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import { useDeleteIssueProjectDiscussionCommentReaction } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import type { FindIssueProjectDiscussionThreadResultItem } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'

export const useDeleteCommentReaction = () => {
  const { error: errorToast, success: successToast } = useAppToasts()
  // データの変更を監視して、画面離脱時に変更があった場合に確認ダイアログを表示します。
  const { init, hasChanged, restart } = useWatchDataChanges()
  const watcher = { init, hasChanged, restart }

  const removeCommentReaction = async (
    reaction: IssueProjectDiscussionCommentReactionForDelete,
    threadId: string,
    commentId: string,
    refThreadsData: FindIssueProjectDiscussionThreadResultItem[],
  ) => {
    const { executeWithResult: useDeleteCommentReaction } =
      useDeleteIssueProjectDiscussionCommentReaction(
        ref<IssueProjectDiscussionCommentReactionForDelete>(reaction),
      )

    const result = await useDeleteCommentReaction().catch((e) => {
      // 失敗だった場合の処理を行います。
      if (!e.hasProblemDetails) {
        errorToast('リアクションの削除に失敗しました。')
      }
      if (e.data.type === '/validation-error') {
        errorToast(e.data.errors)
      } else if (e.data.type === '/conflict') {
        errorToast(
          'すでに別のユーザーがデータを登録・変更しているためリアクションを削除できませんでした。',
        )
      } else {
        errorToast('リアクションの削除に失敗しました。')
      }
      return false
    })

    if (!result) return

    // スレッド検索
    const targetThreadIndex = refThreadsData.findIndex((x) => x.id === threadId)

    // スレッド検索
    const targetThread = refThreadsData[targetThreadIndex]
    const targetCommentIndex = targetThread.comments.findIndex(
      (x) => x.id === commentId,
    )

    // targetDataから削除
    const reactionIndex = refThreadsData[targetThreadIndex].comments[
      targetCommentIndex
    ].reactions.findIndex((x) => x.id === reaction.id)

    refThreadsData[targetThreadIndex].comments[
      targetCommentIndex
    ].reactions.splice(reactionIndex, 1)

    successToast('リアクションを削除しました。')
    watcher.init(refThreadsData)
  }

  return {
    removeCommentReaction,
  }
}
