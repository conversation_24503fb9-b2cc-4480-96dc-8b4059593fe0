// https://nuxt.com/docs/api/configuration/nuxt-config
import child_process from 'child_process'
import vuetify, { transformAssetUrls } from 'vite-plugin-vuetify'

const env = process.env.NODE_ENV || 'development'
// nuxt build コマンドは NODE_ENV を強制的に production にする。上書きすると壊す可能性があるため、別の環境変数を利用する
// FORCE_NODE_ENV が設定されている場合、config内で指定できる設定はこちらの値が利用される
let forceEnv = process.env.FORCE_NODE_ENV || ''
if (env === 'test') {
  forceEnv = 'development'
}

const useDevEnv = env === 'development' || forceEnv === 'development'

// eslint-disable-next-line no-console
console.log(`run nuxt.config.ts: NODE_ENV=${env}, FORCE_NODE_ENV=${forceEnv}, useDevEnv=${useDevEnv}`)

const tryDefault = (process: any, defaultVaue: string) => {
  try {
    return process()
  } catch (e) {
    // eslint-disable-next-line no-console
    console.log('tryDefault: catched error', e)
    return defaultVaue
  }
}
// git のコミットハッシュを取得する
const gitCommitHash = tryDefault(() => {
  return child_process.execSync('git rev-parse HEAD').toString().trim()
}, '')
// minver のバージョンを取得する
const minverVersion = tryDefault(() => {
  return child_process
    .execSync('dotnet tool run minver -p preview.0 -t v -v e')
    .toString()
    .trim()
}, '')

// eslint-disable-next-line no-console
console.log(`versions: commithash=${gitCommitHash}, appversion=${minverVersion}`)

export default defineNuxtConfig({
  app: {
    baseURL: '/case/', // baseURLの設定
    buildAssetsDir: '/ibp-case-app/_nuxt',
  },
  devtools: { enabled: true },
  devServer: {
    port: Number(process.env.DEV_SERVER_PORT || 3210),
  },
  ssr: false,
  srcDir: 'src/',
  extends: ['./src/packages/common-case', './src/packages/issue-project', './src/packages/case-work-flow-optimization', './src/packages/lease'],
  css: ['@hox/base/src/assets/styles/main.scss',
    '@ibp/issue-project/src/assets/styles/main.scss',
    '@fortawesome/fontawesome-svg-core/styles.css',
    '@vueup/vue-quill/dist/vue-quill.snow.css',
    'quill/dist/quill.core.css',
    'quill/dist/quill.snow.css',
  ],
  runtimeConfig: {
    // 環境変数から取得 https://nuxt.com/docs/guide/going-further/runtime-config#environment-variables
    // nuxt 3 は.env を自動で読み込んでくれる
    public: {
      isDev: useDevEnv,
      isStaging: process.env.NUXT_PUBLIC_IS_STAGING,
      useDevEnv,
      appInfo: {
        applicationName: 'IB Platform',
        commitHash: gitCommitHash,
        appVersion: minverVersion,
        topPagePath: '/', // トップページのパスです。ヘッダーのロゴやエラー画面のトップに戻るボタンなどで利用されます。
      },
      error: {
        transferToErrorPage: true, // エラーが発生した際にエラー画面に遷移するかどうかを設定します。
      },
      endpoints: {
        default: {
          // デフォルトのエンドポイント。各種 pulgin でのデフォルトのキーとして利用されているため、可能な限り設定すること
          baseurl: '',
          scope: '',
        },
        // 空の指定。スポット的なAPIで、エンドポイントもスコープも指定しない場合に利用する。
        empty: {},
      },
      auth: {
        clientId: '',
        authority: '',
        redirectUri: '',
        endpointKey: 'default', // endpoints で利用するキー。オプション
        userInfoPath: '/me', // ユーザー情報を取得する endpoint で取得された baseurl からの相対パス。オプション
      },
      logging: {
        level: 'debug',
        console: {
          enabled: true,
          level: 'debug',
        },
        server: {
          enabled: true,
          endpointKey: 'default', // endpoints で利用するキー。オプション
          path: '/clientlog', // ログを送信する endpoint で取得された baseurl からの相対パス。オプション
          level: 'warn',
          apiKey: '', // 送信先で認証するためのAPIキー。オプション
        },
      },
    },
  },
  components: [
    {
      path: '~/packages/common-case/src/components',
      pathPrefix: false,
    },
    {
      path: '~/packages/issue-project/src/components',
      pathPrefix: false,
    },
    {
      path: '~/packages/case-work-flow-optimization/src/components',
      pathPrefix: false,
    },
    {
      path: '~/packages/lease/src/components',
      pathPrefix: false,
    },
  ],
  build: {
    transpile: ['vuetify'],
  },
  modules: [
    '@nuxt/eslint',
    '@nuxt/test-utils/module', // Nuxtモジュールの登録
    (_options, nuxt) => {
      nuxt.hooks.hook('vite:extendConfig', (config: any) => {
        config.plugins?.push(vuetify({ autoImport: true }))
      })
    },
  ],
  typescript: {
    tsConfig: {
      compilerOptions: {
        types: ['vitest/globals'], // globalsのTypeScriptサポート
      },
    },
  },
  // FORCE_NODE_ENV が development の場合、sourcemap を有効にする
  sourcemap: useDevEnv ? { server: true, client: true } : undefined,
  vite: {
    // FORCE_NODE_ENV が development の場合、mode を設定する
    mode: useDevEnv ? 'development' : 'production',
    // server は開発時にしか利用しないため、useDevEnv で判定する
    server: useDevEnv
      ? {
          proxy: {
            '/api/': {
              target: 'http://localhost:62354/',
              secure: false,
              rewrite: (path) => path.replace(/^\/api\//, ''),
            },
          },
        }
      : undefined,
    vue: {
      template: {
        transformAssetUrls,
      },
      // FORCE_NODE_ENV の値を利用する
      isProduction: !useDevEnv,
    },
  },
  nitro: {
    compressPublicAssets: true,
  },
  compatibilityDate: '2024-07-17',
})
