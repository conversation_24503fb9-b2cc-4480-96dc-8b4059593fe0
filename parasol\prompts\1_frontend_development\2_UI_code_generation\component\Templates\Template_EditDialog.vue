<template>
  <v-dialog v-model="dialog" @click:outside="cancel" @keydown.esc="cancel">
    <!-- TODO: dialog-title にダイアログのタイトルを設定します。 -->
    <app-simple-dialog-tmpl
      :loading="loading"
      dialog-title="ダイアログ"
      dialog-icon="mdi-pencil"
      @action="action"
      @cancel="cancel"
    >
      <!-- TODO: 必要な項目を追加します。 -->
      <app-text-field
        v-model="targetData.name"
        label="資格名"
        :error-messages="errorMessages?.name"
        @update:model-value="validators.validateItem('name')"
        @blur="validators.validateItem('name')"
      />
    </app-simple-dialog-tmpl>
  </v-dialog>
</template>
<script lang="ts">
import { ref } from 'vue'
import { z } from 'zod'
import { useValidation } from '@/composables/shared/useValidation'

const component = defineComponent({
  setup(_, { expose }) {
    // ダイアログの表示非表示

    const dialog = ref(false)
    const targetData = ref({
      // 入力する項目を定義します。
      name: '',
    })

    const inputSchema = z.object({
      // 入力する項目のスキーマを定義します。
      name: z.string().min(1).max(100),
    })

    const { errorMessages, validate, validateItem } = useValidation(
      inputSchema,
      targetData,
    )
    const validators = {
      validate,
      validateItem,
    }

    const loading = ref(false)
    const resolve = ref()

    function open(data: {
      // TODO: 設定する項目に対応するプロパティを定義します。
      name: string
    }): Promise<{ isOk: boolean, data?: { name: string } }> {
      // 各種状態を初期化します。
      targetData.value = Object.assign({}, data ?? {})
      errorMessages.value = {}
      // ダイアログを表示します。
      dialog.value = true
      // 呼び出し元に Promise を返します。
      return new Promise((_resolve) => {
        resolve.value = _resolve
      })
    }

    // アクションボタンがクリックされたときの処理をします。
    async function action() {
      // バリデーションを実行します。バリデーションが不要な場合は削除してください。
      const { success } = await validators.validate()
      if (!success) {
        return
      }

      // 処理結果を resolve にしてデータを返します。
      resolve.value({
        isOk: true,
        data: Object.assign({}, targetData.value || {}),
      })
      // ダイアログを閉じます。
      dialog.value = false
    }
    // 選択がされずに閉じられる/キャンセルボタンがクリックされたときの処理をします。
    function cancel() {
      resolve.value({ isOk: false })
      // ダイアログを閉じます。
      dialog.value = false
    }

    expose({
      open,
    })

    return {
      dialog,
      targetData,
      loading,
      action,
      cancel,
      errorMessages,
      validate,
      validateItem,
      validators,
    }
  },
})

// 利用側で expose したメソッドを認識しないため、こちらで型定義を行って export する
export type QualificationEditDialogType = {
  // TODO: 定義した open メソッドと一致するように型を定義します。
  open(data?: {
    // TODO: 表示するときの引数を定義します。
    name: string
  }): Promise<{
    isOk: boolean
    // TODO: 結果のデータを定義します。
    data?: { name: string }
  }>
}

export default component
</script>
