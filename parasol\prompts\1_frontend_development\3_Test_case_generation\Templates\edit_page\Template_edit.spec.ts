import { describe, test, expect, vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { mockNuxtImport, mountSuspended } from '@nuxt/test-utils/runtime'

import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { format } from 'date-fns'
import {
  setupMockUseLayout,
  setupMockUseDisplay,
  getMockUseRouteDefaultImpl,
  patchOFetchOnMsw,
  getRequestJsonFromMswHandler,
  getAllInputValuesAsObject,
  setTestUser,
  clearTestUser,
} from '../../testSupport'
// TODO: テスト対象コンポーネントのパスを実際のエンティティに合わせて変更してください
// 例: import edit from '@/pages/your-entity/edit.vue'
import edit from '@/pages/employee/edit.vue'
import { useAppToasts } from '~/composables/shared/useAppToasts'
import * as obju from '@/utils/shared/obju'
import { defu } from 'defu'

// =====================================================================================================================
// 定数定義
// =====================================================================================================================
// TODO: ベースURLを実際のエンティティに合わせて変更してください
// 例: const BASE_URL = 'http://localhost:3000/your-entity/edit'
const BASE_URL = 'http://localhost:3000/employee/edit'
const window = globalThis.window

// =====================================================================================================================
// テスト用処理設定
// =====================================================================================================================
// APIのモックサーバー定義
const server = setupServer()

// onBeforeRouteLeaveのモック化
// confirmBeforeUnload で利用している
mockNuxtImport('onBeforeRouteLeave', () => {
  return () => {}
})

// useRoute のモック化
mockNuxtImport('useRoute', () => {
  return () => {
    return getMockUseRouteDefaultImpl()
  }
})

// navigateToのモック化
const { mockNavigateTo } = vi.hoisted(() => ({
  mockNavigateTo: vi.fn(),
}))
mockNuxtImport('navigateTo', () => mockNavigateTo)

// 共通モック設定処理
function setUpMocks() {
  setupMockUseLayout()
  setupMockUseDisplay()
}

// =====================================================================================================================
// 前処理/後処理
// =====================================================================================================================
// テスト開始前処理
beforeAll(() => {
  // ユーザー情報を設定
  setTestUser('テストユーザー', 'test_user', ['admin'])
})
// テスト終了後処理
afterAll(() => {
  // APIモックサーバーを閉じる
  server.close()
  // ユーザー情報をクリア
  clearTestUser()
})
// テストケース開始前処理
beforeEach(() => {
  // デフォルトURL設定
  window.location.href = BASE_URL
  setUpMocks()
})
// テストケース終了時後処理
afterEach(() => {
  // APIのモックハンドラをリセット
  server.resetHandlers()
  // モック化したモジュールを元の実装に戻す
  vi.restoreAllMocks()
  // トーストが表示されている場合はクリア
  const { forceClear } = useAppToasts()
  forceClear()
})

// =====================================================================================================================
// APIデータ定義
// =====================================================================================================================
const responseSourceData = {
  // TODO: 実際のAPIのレスポンスに合わせて変更してください
  id: '1',
  nameKanji: 'nameKanji_1',
  nameKana: 'nameKana_1',
  birthDate: new Date(2000, 0, 1),
  address: 'address_1',
  branchId: '1',
  branchName: 'branchName_1',
  version: '1001',
}
const qualifications = [
  { id: '1', name: 'qualificationName_1' },
  { id: '2', name: 'qualificationName_2' },
]

// =====================================================================================================================
// テスト実装
// =====================================================================================================================
describe('employee/edit.vue test', () => {
  test('画面初期化処理：パラメータなしの場合、編集画面が追加モードで表示され各入力フィールドに値が設定されずに表示される', async () => {
    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 保持データの確認
    expect(vm.addMode).toBe(true)
    // 保持されている値が初期状態かどうかを確認します。
    // ここでは responseSourceData で指定されているプロパティがすべて null または配列の場合は空と一致しているかで確認しています。
    expect(vm.targetData).toEqual(obju.clear(responseSourceData, {
      qualifications: [],
    }))

    // TODO: 各入力フィールドの内容を確認
    const inputs = getAllInputValuesAsObject(wrapper)
    expect(inputs).toEqual({
      'ID': undefined,
      '名前(漢字)': undefined,
      '名前(カナ)': undefined,
      '住所': undefined,
      '誕生日': '',
      '部署': undefined,
    })
  })

  test('画面初期化処理：パラメータありの場合、編集画面の各入力フィールドに値が設定されて表示される', async () => {
    // URL設定を上書き
    window.location.href = BASE_URL + '?id=1'

    // APIのモックEndpointを登録
    const getHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.get('/v1.0/employee/1', getHandler))
    const qualificationsHandler = vi.fn(() => HttpResponse.json(qualifications))
    server.use(http.get('/v1.0/employee/1/qualifications', qualificationsHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 保持データの確認
    expect(vm.addMode).toBe(false)
    // 取得できた値が responseSourceData と一致しているかどうかで確認している
    expect(Object.assign(vm.targetData, {
      qualifications: vm.targetData.qualifications.map(v => {
        delete v._tempKey
        return v
      }),
    })).toEqual(defu({}, responseSourceData, { qualifications }))

    // TODO: 各入力フィールドの内容を確認
    const inputs = getAllInputValuesAsObject(wrapper)
    expect(inputs).toEqual({
      'ID': `${responseSourceData.id}`,
      '名前(漢字)': `${responseSourceData.nameKanji}`,
      '名前(カナ)': `${responseSourceData.nameKana}`,
      '住所': `${responseSourceData.address}`,
      '誕生日': `${format(responseSourceData.birthDate, 'yyyy/MM/dd')}`,
      '部署': `${responseSourceData.branchName}`,
    })
  })

  test('登録処理：入力値に問題がない場合登録APIが呼び出される', async () => {
    // APIのモックEndpointを登録
    const qualificationsHandler = vi.fn(() => HttpResponse.json(qualifications))
    server.use(http.get('/v1.0/employee/*/qualifications', qualificationsHandler))
    const postHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.post('/v1.0/employee', postHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockConfirmDialog.mockReturnValue(true)

    // 基本情報入力値の設定
    const inputData = {
      ...responseSourceData,
    } as any
    inputData.qualifications = [
      {
        name: 'qualificationName_3',
        _tempKey: '123456',
        updated: false,
      },
    ]
    Object.assign(vm.targetData, inputData)
    // 保存処理の実行
    await vm.save()

    // 登録APIの呼び出し確認
    expect(postHandler).toBeCalledTimes(1)
    // 登録APIのリクエスト確認
    const requestBody = await getRequestJsonFromMswHandler(postHandler)
    // APIに渡された値と入力されている値が一致しているかどうかで、APIに渡されるデータが正しいかどうかを検証
    expect(requestBody).toEqual({
      ...inputData,
      birthDate: inputData.birthDate.toISOString(),
    })
    // 登録完了メッセージが表示されているか
    const { messages } = useAppToasts()
    expect(messages.value[0]).toMatchObject({
      message: '1 を保存しました。',
      color: 'primary',
    })
  })

  test('登録処理：入力値にエラーがある場合、登録APIが呼び出されない', async () => {
    // APIのモックEndpointを登録
    const postHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.post('/v1.0/employee', postHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any
    // 確認ダイアログをモック化
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockConfirmDialog.mockReturnValue(true)
    // ブラウザ側でのバリデーションでエラーにする。
    vi.spyOn(vm.validators, 'validate').mockResolvedValue({ success: false })

    await vm.save()

    // APIが呼び出されていないことを確認
    expect(postHandler).toBeCalledTimes(0)
  })

  test('更新処理：入力値に問題がない場合、更新APIが呼び出される', async () => {
    // URL設定を上書き
    window.location.href = BASE_URL + '?id=1'

    // APIのモックEndpointを登録
    const getHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.get('/v1.0/employee/1', getHandler))
    const qualificationsHandler = vi.fn(() => HttpResponse.json(qualifications))
    server.use(http.get('/v1.0/employee/1/qualifications', qualificationsHandler))
    const putHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.put('/v1.0/employee', putHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockConfirmDialog.mockReturnValue(true)

    // 基本情報入力値の設定
    const inputData = {
      ...responseSourceData,
    } as any
    inputData.qualifications = [
      {
        name: 'qualificationName_3',
        _tempKey: '123456',
        updated: false,
      },
    ]
    Object.assign(vm.targetData, inputData)

    // 更新処理を実行
    await vm.save()

    // 更新APIの呼び出し確認
    expect(putHandler).toBeCalledTimes(1)
    // 更新APIのリクエスト確認
    const requestBody = await getRequestJsonFromMswHandler(putHandler)
    // APIに渡された値と入力されている値が一致しているかどうかで、APIに渡されるデータが正しいかどうかを検証
    expect(requestBody).toEqual({
      ...inputData,
      birthDate: inputData.birthDate.toISOString(),
    })

    // 登録完了メッセージが表示されているか
    const { messages } = useAppToasts()
    expect(messages.value[0]).toMatchObject({
      message: '1 を保存しました。',
      color: 'primary',
    })
  })

  // 登録と更新が違うコードで実装している場合は、例外ケースのテストを追加してください。

  test('削除処理：削除APIが呼び出される', async () => {
    // URL設定を上書き
    window.location.href = BASE_URL + '?id=1'

    // APIのモックEndpointを登録
    const getHandler = vi.fn(() => HttpResponse.json(responseSourceData))
    server.use(http.get('/v1.0/employee/1', getHandler))
    const qualificationsHandler = vi.fn(() => HttpResponse.json(qualifications))
    server.use(http.get('/v1.0/employee/1/qualifications', qualificationsHandler))
    const deleteHandler = vi.fn(() => new HttpResponse(null, { status: 204 }))
    server.use(http.delete('/v1.0/employee', deleteHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象コンポーネントをマウント
    const wrapper = await mountSuspended(edit)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをモック化
    const mockConfirmDialog = vi.spyOn(vm.confirmDialog.value, 'open')

    // 削除処理を実行（確認ダイアログで「はい」）
    mockConfirmDialog.mockReturnValue(true)
    await vm.remove()

    // 削除APIの呼び出し確認
    expect(deleteHandler).toBeCalledTimes(1)
    // 削除APIのリクエスト確認
    const requestBody = await getRequestJsonFromMswHandler(deleteHandler)
    expect(requestBody).toEqual({ id: '1', version: '1001' })
    // 更新完了メッセージの確認
    const { messages } = useAppToasts()
    expect(messages.value[0]).toMatchObject({
      message: '1 を削除しました。続いてデータを新規登録できます。',
      color: 'primary',
    })
  })
})
