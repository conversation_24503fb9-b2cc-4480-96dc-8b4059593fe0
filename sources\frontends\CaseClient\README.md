# Client

## プロジェクトのセットアップ

アプリケーションを開発するために、ソースコードをテンプレートから展開した場合、もしくはリポジトリから取得した場合など、新しくソースコードを入手した際には次のセットアップを行う必要があります。

### npmパッケージの認証

次のコマンド使って Azure Artifacts認証用のトークンを取得します

```shell
vsts-npm-auth -config .npmrc
```

### node_modules の復元

次のコマンド使って node_modules を復元してください。

```shell
npm ci
```

### .env ファイルの設定

アプリケーションを動作させるための認証などをはじめとした設定を .env ファイルに行います。

```
NUXT_PUBLIC_AUTH_CLIENT_ID=XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
NUXT_PUBLIC_AUTH_AUTHORITY=https://login.microsoftonline.com/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX

NUXT_PUBLIC_ENDPOINTS_DEFAULT_SCOPE=api://XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/XXXXXX
NUXT_PUBLIC_ENDPOINTS_DEFAULT_BASEURL=http://localhost:5000
NUXT_PUBLIC_AUTH_REDIRECT_URI=/auth/redirect

NUXT_PUBLIC_LOGGING_LEVEL = debug
NUXT_PUBLIC_LOGGING_SERVER_ENABLED = true
```

### 起動

以下のコマンドで起動する

```shell
npm run dev
```

### テスト

以下のコマンドで各レイヤーをテストする

```shell
npm run test
```

## レイヤーごとのpackages.jsonの役割

### workspaceでのシンボリックリンクの命名

workspaceを使うことでnode_modulesにシンボリックリンクが定義され、レイヤー間の参照を行うときにパスの指定が容易になります。
nameプロパティでシンボリックリンクの命名を行います。

### パッケージのインストール

複数またはすべてのレイヤーで使用するパッケージはルートでインストールを行います。
単体のレイヤーでしか使用しないパッケージはそのレイヤーでインストールを行います。
どのレイヤーでインストールを行っても、node_modulesはルートに作成されます。
