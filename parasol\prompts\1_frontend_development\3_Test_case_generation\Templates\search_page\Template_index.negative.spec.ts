import { describe, test, expect, vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { mountSuspended, mockNuxtImport } from '@nuxt/test-utils/runtime'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import {
  setupMockUseLayout,
  setupMockUseDisplay,
  getMockUseRouteDefaultImpl,
  patchOFetchOnMsw,
  setTestUser,
  clearTestUser,
} from '../../testSupport'

// TODO: テスト対象コンポーネントのパスを実際のエンティティに合わせて変更してください
// 例: import index from '@/pages/your-entity/index.vue'
import index from '@/pages/employee/index.vue'
import { useAppToasts } from '~/composables/shared/useAppToasts'

// =====================================================================================================================
// 定数定義
// =====================================================================================================================
// TODO: ベースURLを実際のエンティティに合わせて変更してください
// 例: const BASE_URL = 'http://localhost:3000/your-entity'
const BASE_URL = 'http://localhost:3000/employee'
const window = globalThis.window

// =====================================================================================================================
// テスト用処理設定
// =====================================================================================================================
// APIのモックサーバー定義
const server = setupServer()

// useRoute のモック化
mockNuxtImport('useRoute', () => {
  return () => {
    return getMockUseRouteDefaultImpl()
  }
})

// navigateToのモック化
const { mockNavigateTo } = vi.hoisted(() => ({
  mockNavigateTo: vi.fn(),
}))
mockNuxtImport('navigateTo', () => mockNavigateTo)

function setUpMocks() {
  setupMockUseLayout()
  setupMockUseDisplay()
}

// =====================================================================================================================
// 前処理/後処理
// =====================================================================================================================
// テスト開始前処理
beforeAll(() => {
  // ユーザー情報を設定
  setTestUser('テストユーザー', 'test_user', ['admin'])
})
// テスト終了後処理
afterAll(() => {
  // APIモックサーバーを閉じる
  server.close()
  // ユーザー情報をクリア
  clearTestUser()
})
// テストケース開始前処理
beforeEach(() => {
  // デフォルトURL設定
  window.location.href = BASE_URL
  setUpMocks()
})
// テストケース終了時後処理
afterEach(() => {
  // APIのモックハンドラをリセット
  server.resetHandlers()
  // mockNuxtImportのモックを初期状態に
  mockNavigateTo.mockReset()
  // モック化したモジュールを元の実装に戻す
  vi.restoreAllMocks()
  // トーストが表示されている場合はクリア
  const { forceClear } = useAppToasts()
  forceClear()
})

// =====================================================================================================================
// テストデータ定義
// =====================================================================================================================

// TODO: APIから返されるテストデータを実際のエンティティの構造に合わせて変更してください
// APIから返されるダミーデータの元の値を作成します。
const responseSourceData = [...Array(3).keys()].map(index => {
  return {
    id: index,
    version: `100${index}`,
    nameKanji: `nameKanji_${index}`,
    nameKana: `nameKana_${index}`,
    birthDate: new Date(2000, 0, index),
    address: `address_${index}`,
    branchName: `branchName_${index}`,
    qualificationCount: index,
  }
})

// =====================================================================================================================
// テスト実装
// =====================================================================================================================
describe('Entity Search Page Negative Test', () => {
  test('削除処理：APIにて排他エラーが発生した場合、例外が発生せずに処理が中断される', async () => {
    // TODO: 削除APIのエンドポイントを実際のエンティティに合わせて変更してください
    // 例: server.use(http.post('/v1.0/your-entities/:deleteAll', deleteAllHandler))
    const deleteAllHandler = vi.fn(() => HttpResponse.json({
      type: '/conflict',
      title: 'Raise conflict',
      status: 409,
      detail: 'error message',
    }, { status: 409 }))
    server.use(http.post('/v1.0/employee/:deleteAll', deleteAllHandler))
    server.listen({ onUnhandledRequest: 'error' })
    patchOFetchOnMsw()

    // テスト対象をマウント
    const wrapper = await mountSuspended(index)
    const vm = wrapper.getCurrentComponent().proxy as any

    // 確認ダイアログをspyして「はい」を返すように設定
    const mockDialog = vi.spyOn(vm.confirmDialog.value, 'open')
    mockDialog.mockReturnValue(true)

    // 一覧で選択されている値を設定
    vm.listSelectedItems.value = responseSourceData
    // 削除処理を実行
    const flg = await vm.remove()

    // トーストが表示されていること
    const { messages } = useAppToasts()
    expect(messages.value[0]).toMatchObject({
      color: 'error',
    })

    // 削除APIが呼び出されたか検証
    expect(deleteAllHandler).toHaveBeenCalled()
    // 削除APIが失敗の場合falseを返却することを確認
    expect(flg).toBe(false)
  })
})
