# 顧客案件編集ページ定義

## 基本情報

- ページ名 : 案件編集
- UIパターン : 編集画面（edit）

## 実現対象ハイレベルユースケース

- 案件カテゴリに応じた専用編集フォームによる案件データの編集・登録
- 案件種別別の詳細項目編集機能
- 他ドメイン案件の適切な専用画面への振り分け機能

## 共通処理（ミドルウェア）定義

- [ ] アクセスログ出力
- [ ] アクセス可否チェック
- [ ] 次画面パスチェック

## パラメータ定義

| パラメータ名 | 種別 | 必須 | 型 | 説明 |
|---|---|---|---|---|
| id | query | | 文字列 | 案件ID（未指定の場合は新規作成モード） |
| customerIdentificationId | query | ○ | 文字列 | 顧客識別ID |
| caseCategory | query | | 文字列 | 案件カテゴリ（新規作成時のみ） |
| from | query | | 文字列 | 遷移元画面識別子（パンくずリスト構築用） |
| businessUnderstandingId | query | | 文字列 | 事業性理解ID（事業性理解からの遷移時） |

## UI定義

### 編集フォーム構成
案件カテゴリに応じて以下の専用編集フォームを動的に表示：

#### GeneralTransaction（一般取引案件）編集フォーム
- **案件情報セクション**
    - 案件名
        - 入力形式 : テキストボックス
        - 必須項目 : ○
        - 文字数制限 : 50文字
    - 総合取引種別
        - 入力形式 : プルダウンリスト
        - 選択肢 : 総合取引種別マスター
    - ステータス
        - 入力形式 : プルダウンリスト
        - 必須項目 : ○
    - 案件概要
        - 入力形式 : テキストエリア
        - 文字数制限 : 500文字
- **ファイルセクション**
    - ファイルアップロード・ダウンロード機能
- **リンクセクション**
    - 関連リンク管理機能
- **サイドアクション**
    - 協議ボタン（編集モード時のみ）
    - お気に入りボタン（編集モード時のみ）

#### NewLoan（新規融資案件）編集フォーム
- **融資案件情報セクション**
    - 案件名
        - 入力形式 : テキストボックス
        - 必須項目 : ○
        - 文字数制限 : 50文字
    - ステータス
        - 入力形式 : プルダウンリスト
        - 必須項目 : ○
    - 期日
        - 入力形式 : 日付選択
        - 表示形式 : "yyyy/MM/dd"
    - 担当者
        - 入力形式 : プルダウンリスト
        - 必須項目 : ○
    - 科目
        - 入力形式 : プルダウンリスト
        - 必須項目 : ○
    - 金額
        - 入力形式 : 数値入力
        - 必須項目 : ○
        - 単位 : 千円
    - 期間（年・月）
        - 入力形式 : 数値入力
        - 必須項目 : ○
    - 金利
        - 入力形式 : 数値入力
        - 必須項目 : ○
        - 単位 : %
    - 資金使途
        - 入力形式 : プルダウンリスト + カスタム入力
    - ESG関連フラグ
        - 入力形式 : チェックボックス
    - 返済方法
        - 入力形式 : プルダウンリスト + カスタム入力
    - 担保・保証
        - 入力形式 : チェックボックス + カスタム入力
    - 不成約事由（顧客キャンセル時のみ）
        - 入力形式 : プルダウンリスト
        - 必須項目 : ○（条件付き）
- **信用保証協会セクション**
    - 保証協会利用フラグ
        - 入力形式 : チェックボックス
    - 関連項目（利用時のみ表示）
- **ファイルセクション**
    - ファイルアップロード・ダウンロード機能
- **リンクセクション**
    - 関連リンク管理機能
- **サイドアクション**
    - 協議ボタン（編集モード時のみ）
    - お気に入りボタン（編集モード時のみ）
    - 条件登録ボタン（資金調達支援機能有効時）

#### NewLease（新規リース案件）編集フォーム
- **リース案件情報セクション**
    - 案件名
        - 入力形式 : テキストボックス
        - 必須項目 : ○
        - 文字数制限 : 50文字
    - ステータス
        - 入力形式 : プルダウンリスト
        - 必須項目 : ○
    - 金額
        - 入力形式 : 数値入力
        - 必須項目 : ○
        - 単位 : 千円
    - 総合リース担当者
        - 入力形式 : プルダウンリスト
    - 案件担当者
        - 入力形式 : プルダウンリスト
        - 必須項目 : ○
- **作業状況セクション（北国総合リース使用欄）**
    - 相談票作成対象フラグ
    - 見積作成者、見積精査者
    - 顧客応諾確認者、個別申請者
- **物件情報セクション**
    - 物件詳細情報入力項目群
- **見積依頼セクション（北国総合リース使用欄）**
    - 見積依頼関連項目群
- **契約進捗情報セクション（北国総合リース使用欄）**
    - 契約進捗関連項目群
- **ファイルセクション**
    - ファイルアップロード・ダウンロード機能
- **リンクセクション**
    - 関連リンク管理機能
- **サイドアクション**
    - 協議ボタン（編集モード時のみ）
    - お気に入りボタン（編集モード時のみ）

#### OtherLoan（その他融資案件）編集フォーム
- NewLoanと類似構成だが、その他融資特有の項目を含む
- **融資案件情報セクション**（基本項目）
- **ファイルセクション**
- **リンクセクション**
- **サイドアクション**

#### ExternallyCompletedLease（外部完了リース案件）編集フォーム
- NewLeaseと類似構成だが、外部完了特有の項目を含む
- **リース案件情報セクション**（基本項目）
- **完了情報セクション**（外部完了特有）
- **ファイルセクション**
- **リンクセクション**
- **サイドアクション**

#### GeneralCase（汎用案件）編集フォーム
- その他の案件カテゴリに対応する汎用フォーム
- **案件情報セクション**（基本項目）
- **ファイルセクション**
- **リンクセクション**
- **サイドアクション**

### 他ドメイン振り分け対象
- **IssueProject（課題案件）**
    - 動作 : 課題案件編集画面（/customer-proposal/case/issue-project/edit）に自動リダイレクト
- **資金調達支援対象カテゴリ**
    - 動作 : 資金調達支援専用編集画面に自動リダイレクト（機能フラグ有効かつ本番環境時）

### 共通アクションボタン
各フォームで共通して提供される機能
- **保存ボタン**
    - 機能 : 新規登録・更新処理
    - 配置 : AppEditPageTmplのデフォルト位置
- **削除ボタン**
    - 機能 : 案件削除処理
    - 表示条件 : 編集モード時のみ
- **サイドアクション**
    - 協議ボタン : 案件協議画面への遷移
    - お気に入りボタン : お気に入り登録・解除
    - 条件登録ボタン : 資金調達支援への遷移（対象フォームのみ）

### ローディング表示
- 初期読み込み中はスケルトンローダー表示
- 案件カテゴリ判定完了後に該当編集フォーム表示

## イベント処理定義

### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | 案件カテゴリを判定し適切な編集フォームを表示する | ライフサイクルフック created |
| 保存ボタン押下 | 案件データを登録・更新する | 各フォームコンポーネント内 |
| 削除ボタン押下 | 案件データを削除する | 各フォームコンポーネント内 |
| キャンセル・戻るボタン押下 | 元の画面に戻る | 各フォームコンポーネント内 |

### 初期表示（created）
1. 顧客担当者情報をストアから取得・確認する。
2. 顧客担当者情報が存在しない場合：
   - 顧客情報から担当者IDを取得する
   - ユーザー検索APIで担当者詳細情報を取得する
   - 取得した担当者情報をストアに設定する
3. 案件IDが指定されている場合（編集モード）：
   - 案件カテゴリ取得APIを呼び出し、案件カテゴリを判定する
4. 案件IDが未指定の場合（新規作成モード）：
   - URLパラメータから案件カテゴリを取得する
5. 取得した案件カテゴリに基づいて処理を分岐：
   - **IssueProject**: 課題案件編集画面にリダイレクト
   - **資金調達支援対象**: 資金調達支援編集画面にリダイレクト（条件付き）
   - **その他**: 該当する編集フォームコンポーネントを表示

### 編集フォーム処理（各コンポーネント内）
#### 保存処理
1. 入力データのバリデーションを実行する。
2. 案件登録/更新APIを呼び出す。
3. 成功時はトースト通知を表示し、適切な画面に遷移する。

#### 削除処理
1. 削除確認ダイアログを表示する。
2. 確認後、案件削除APIを呼び出す。
3. 成功時はトースト通知を表示し、一覧画面に戻る。

### 自動リダイレクト処理
#### 課題案件編集への遷移
- 遷移先: `/customer-proposal/case/issue-project/edit`
- 引き継ぎパラメータ: id, customerIdentificationId, from
- 実行条件: 案件カテゴリが「IssueProject」の場合

#### 資金調達支援編集への遷移
- 遷移先: `/funding-support/case/edit` または `/funding-support/case/{kebab-case-category}/edit`
- 引き継ぎパラメータ: id, customerIdentificationId, from
- 実行条件: 資金調達支援対象カテゴリ かつ 機能フラグ有効 かつ 本番環境
