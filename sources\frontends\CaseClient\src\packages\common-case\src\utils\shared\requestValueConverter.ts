import { parseISO } from 'date-fns'

/**
 * URLのクエリから取得した値(文字列)を数値型の配列に変換
 */
export const convertStringArrayToNumberArray = (
  val: Array<string> | string | undefined,
) => {
  if (!val) return undefined

  // 文字列を数値の配列に変換
  if (typeof val === 'string') {
    return [Number(val)]
  }

  // 文字列の配列を数値の配列に変換
  return [...val].map(Number)
}

/**
 * URLのクエリから取得した値(文字列)を文字列型の配列に変換
 */
export const convertToStringArray = (
  val: Array<string> | string | undefined,
) => {
  if (!val) return undefined

  // 文字列を文字列の配列に変換
  if (typeof val === 'string') {
    return [val]
  }

  // 文字列の配列はそのまま返す
  return val
}

/**
 * URLのクエリから取得した値(文字列)をDate型に変換
 */
export const convertToDate = (val: string | undefined) => {
  return typeof val === 'string' ? parseISO(val) : undefined
}
