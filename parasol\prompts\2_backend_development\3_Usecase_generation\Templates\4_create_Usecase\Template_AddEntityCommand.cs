using MediatR;
using Nut.Results;

// TODO: namespace をディレクトリにあわせて変更してください。
namespace SampleService.UseCases.Entity.AddEntity;

[WithDefaultBehaviors]
public record AddEntityCommand(
    string Name,
    DateTimeOffset PeriodFrom,
    DateTimeOffset PeriodTo,
    int Amount,
    bool IsAssumption,
    List<AddEntityCommandDetail> Members,
    string UpdaterId,     // TODO: 更新者IDを設定します
    string UpdaterName    // TODO: 更新者名を設定します
) : IRequest<Result<string>>; // 追加したデータのキーを返します。

public record AddEntityCommandDetail(
    string MemberId,
    int UnitPricePerHour);
