<template>
  <!-- 照会期間 -->
  <IssueProjectDiscussionDate
    @add-thread="openThreadEditor"
    @update:from-date="updateFromDate"
    @update:to-date="updateToDate"
    @update:discussion-type="updateDiscussionType"
  />
  <!-- スレッド -->
  <IssueProjectDiscussionThread
    v-for="thread in sortedThreads"
    :id="`thread-${thread.id}`"
    ref="threadRef"
    :key="thread.id"
    :thread="thread"
    type="customer-identified"
    @download-thread-file="downloadThreadFile"
    @add-thread-reaction="addThreadReaction"
    @update-thread-reaction="updateThreadReaction"
    @delete-thread-reaction="deleteThreadReaction"
    @remove-thread="removeThread($event)"
    @update-thread="openThreadEditor"
    @add-comment-reaction="addCommentReaction"
    @update-comment-reaction="updateCommentReaction"
    @delete-comment-reaction="deleteCommentReaction"
    @remove-comment="removeComment"
    @add-comment="openCommentEditorWhenAdd"
    @download-comment-file="downloadCommentFile"
    @update-comment="openCommentEditorWhenUpdate"
  />

  <!-- スレッドエディタ -->
  <IssueProjectDiscussionThreadEditor
    v-if="teamData && dataRef.staffs"
    ref="threadEditorDialog"
    :teams="teamData"
    :staffs="dataRef.staffs"
  />

  <!-- コメントエディタ -->
  <IssueProjectDiscussionCommentEditor
    v-if="teamData && dataRef.staffs"
    ref="commentEditorDialog"
    :staffs="dataRef.staffs"
    :teams="teamData"
  />

  <!-- 確認ダイアログ -->
  <app-confirm-dialog ref="confirmDialog" />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { parseISO } from 'date-fns'
import { stringify } from 'qs'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import {
  useFindIssueProjectDiscussionThread,
  useDeleteIssueProjectDiscussionThread,
  usePutIssueProjectDiscussionThread,
  usePostIssueProjectDiscussionThread,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import type {
  FindIssueProjectDiscussionThreadCriteria,
  FindIssueProjectDiscussionThreadResultItem,
  issueProjectDiscussionThreadReactionType,
  DeleteIssueProjectDiscussionThreadType,
  IssueProjectDiscussionThread,
  issueProjectDiscussionCommentReactionType,
  issueProjectDiscussionCommentType,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThread'
import { useFindUser } from '@ibp/common-case/src/apiclient/myCareer/user'
import { useGetAllTeam } from '@ibp/common-case/src/apiclient/customerProposal/team'
import type { Team } from '@ibp/common-case/src/apiclient/customerProposal/team'
import { useGetIssueProjectDiscussionOptions } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionType'
import {
  useGetOptions,
  usePostIssueProjectDiscussionThreadReaction,
  usePutIssueProjectDiscussionThreadReaction,
  useDeleteIssueProjectDiscussionThreadReaction,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThreadReaction'
import type {
  IssueProjectDiscussionThreadReactionForCreate,
  IssueProjectDiscussionThreadReactionForSave,
  IssueProjectDiscussionThreadReactionForDelete,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThreadReaction'
import {
  useGetIssueProjectOptions,
  useGetIssueProject,
} from '@ibp/issue-project/src/apiclient/customerProposal/issueProject'
import { useDownloadIssueProjectDiscussionThreadFile } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThreadFile'
import type { downloadIssueProjectDiscussionThreadFile } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionThreadFile'
import { isArray } from '@hox/base/src/utils/shared/is'
import type { AppConfirmDialogType } from '@hox/base/src/components/shared/molecules/AppConfirmDialog.vue'
import { usePostInteractionLog } from '@ibp/common-case/src/apiclient/customerInteraction/interactionLog'
import type {
  InteractionLog,
  GetInteractionLogResult,
} from '@ibp/common-case/src/apiclient/customerInteraction/interactionLog'
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import { usePostIssueProjectTask } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectTask'
import type { FileModelItem, DeleteItem } from '@ibp/common-case/src/models/share/FileUploadType'
import { usePostCommentReaction } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/reaction/usePostCommentReaction'
import { usePutCommentReaction } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/reaction/usePutCommentReaction'
import { useDeleteCommentReaction } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/reaction/useDeleteCommentReaction'
import type { IssueProjectDiscussionCommentReactionForDelete } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentReaction'
import { useThreadShareData } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/thread/useThreadShareData'
import { useDeleteComment } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/useDeleteComment'
import { usePostComment } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/usePostComment'
import { useDownloadIssueProjectDiscussionCommentFile } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentFile'
import type { downloadIssueProjectDiscussionCommentFile } from '@ibp/issue-project/src/apiclient/customerProposal/issueProjectDiscussionCommentFile'
import { usePutComment } from '@ibp/issue-project/src/composables/customerProposal/case/issueProjectDiscussion/comment/usePutComment'
import type IssueProjectDiscussionThreadComponent from '@ibp/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThread.vue'
import type IssueProjectDiscussionThreadEditorComponent from '@ibp/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionThreadEditor.vue'
import type IssueProjectDiscussionCommentEditorComponent from '@ibp/issue-project/src/components/customer-proposal/organisms/Issue-project-discussion/IssueProjectDiscussionCommentEditor.vue'
import {
  defaultSelectedCaseStatusesWithIssueProject,
  PATHS,
} from '@ibp/issue-project/src/constants/path'
import { OriginOfTransitions } from '@ibp/case-work-flow-optimization/src/constants/common/query'
import {
  type Breadcrumb,
  useBreadcrumbs,
} from '@hox/base/src/composables/shared/useBreadcrumbs'
import { DEFAULT_SELECTED_CASE_STATUSES_WITH_ISSUE_PROJECT } from '@ibp/common-case/src/constants/domain/case'
import { setTOCToCookie } from '@ibp/common-case/src/utils/vueCookie'
import { useGetCustomer } from '@ibp/base/src/composables/customer/useGetCustomer'
import { useGetCustomerLayout } from '@ibp/base/src/composables/layout/useGetCustomerLayout'

useHead({
  title: '課題案件協議',
})
definePageMeta({
  middleware: 'check-accessible',
  layout: 'customer',
  title: '課題案件協議',
})

const router = useRouter()
const route = useRoute()
const { error: errorToast, success: successToast } = useAppToasts()

const { threadDataRef } = useThreadShareData()

const customerIdentificationId = route.query.customerIdentificationId

// ヘッダーに手動通知を表示
const { customer } = useGetCustomer(customerIdentificationId as string)
const { toggleManualNotification } = useGetCustomerLayout()
toggleManualNotification(true)

type staffType = {
  id: string
  value: string
  romaji: string
}

type descriptionsType = {
  key: string
  value: string | number | Date | null | undefined
}

type targetDataType = {
  id: string
  version: string
  discussionType: string
  mentionTargetsHtml: string | null | undefined
  templateVersion: number
  purpose: string
  person: string | null | undefined
  isPersonOfPower: boolean | null | undefined
  purposeTypeOfTemplate: string
  descriptions: Array<descriptionsType>
  descriptionJson: string | undefined
  mentionTargetUserIds: Array<string> | null | undefined
  mentionTargetTeamMemberUserIds: Array<string> | null | undefined
  mentionTargetTeamIds: Array<string>
  filesToUpload: File[] | undefined
  filesToRemove: DeleteItem[] | undefined
  uploadFiles: Array<FileModelItem> | undefined
}

const dataRef = ref<{
  staffs: Array<staffType>
  bpoTeam: Team | undefined
  bpoTeamId: string
  bpoTeamStaffIds: Array<string>
  issueProjectId: string
  issueItemName: string | undefined
  threadsData: FindIssueProjectDiscussionThreadResultItem[]
  dialogTitle: string
  fromDate: Date | undefined
  toDate: Date | undefined
  discussionType: string | undefined
}>({
  staffs: [],
  bpoTeam: undefined,
  bpoTeamId: '01HG84PND8SKX843WCA2PSNDSN',
  bpoTeamStaffIds: [],
  issueProjectId: route.query.issueProjectId as string, // 課題案件Id
  issueItemName: undefined,
  threadsData: reactive([]),
  dialogTitle: 'スレッドの削除確認',
  fromDate: undefined,
  toDate: undefined,
  discussionType: undefined,
})

// タスク自動起案関連
const TaskAutomaticCreation = {
  cloudSign: '4', // 契約締結稟議申請兼捺印申請：クラウドサイン
  writtenContract: '5', // 契約締結稟議申請兼捺印申請：書面契約
}

// データの変更を監視して、画面離脱時に変更があった場合に確認ダイアログを表示します。
const { init, hasChanged, restart } = useWatchDataChanges()
const watcher = { init, hasChanged, restart }

// =====================================================================================================================
// 画面初期化処理
// =====================================================================================================================

// 画面変更の検知を開始します。
watcher.init(dataRef.value.threadsData)

onMounted(async () => {
  createBreadcrumbs()
  setTOCToCookie()
  await findThreads()

  // ドロップダウンリストの設定
  const optionsResult = await getDiscussionOptions()
  if (!optionsResult) return

  const reactionTypeResult = await getReaction()
  if (!reactionTypeResult) return

  // スレッドの読み込み完了を待つ
  while (threadDataRef.value.loading) {
    await new Promise((resolve) => setTimeout(resolve, 100)) // 100ミリ秒待機
  }

  // v-forのレンダリングが完了するまで待機
  await nextTick()

  // スクロール
  if (route.query.threadId) {
    scrollToThread(route.query.threadId as string)
    await expandThread(route.query.threadId as string)
    await highlightThread(route.query.threadId as string)
  }

  // bpoチーム取得
  dataRef.value.bpoTeam = teamData.value.find(
    (x) => x.id === dataRef.value.bpoTeamId,
  )

  if (dataRef.value.bpoTeam) {
    dataRef.value.bpoTeamStaffIds = dataRef.value.bpoTeam.teamMembers.map(
      (x) => x.staffId,
    )
  }

  // 課題案件の課題項目名を取得
  const getIssueProjectOptionResult = await getIssueProjectOptions()
  const getIssueProjectResult = await getIssueProject().catch(() => {
    return null
  })

  if (!getIssueProjectResult) {
    // 課題案件ごと削除されていたら課題案件一覧に遷移
    toIssueProjectSearchPage()
  }

  if (getIssueProjectOptionResult && getIssueProjectResult) {
    dataRef.value.issueItemName =
        issueProjectOptionsData.value.issueItemList?.find(
          (item) =>
            item.value === issueProjectData.value.issueInformation?.issueItem,
        )?.text
  }
})

onUnmounted(() => {
  removeTOCFromCookie()
})

// =====================================================================================================================
// APIクライアントの定義(スレッド検索)
// =====================================================================================================================
// 課題Id
const issueProjectId = computed(() => {
  const route = useRoute()
  return route.query.issueProjectId
})

// 開始日
const computedFromDate = computed<string | undefined>(() => {
  return dataRef.value.fromDate?.toISOString()
})

// 終了日
const computedToDate = computed<string | undefined>(() => {
  return dataRef.value.toDate?.toISOString()
})

// 協議種別
const computedDiscussionType = computed<string | undefined>(() => {
  return dataRef.value.discussionType
})

// =====================================================================================================================
// APIクライアントの定義(Myキャリア)
// =====================================================================================================================
const { data: myCareerUserData, executeWithResult: findUser } = useFindUser()

// =====================================================================================================================
// APIクライアントの定義(チーム)
// =====================================================================================================================
const { data: teamData, executeWithResult: getAllTeam } = useGetAllTeam()

// =====================================================================================================================
// APIクライアントの定義(協議タイプ)
// =====================================================================================================================
const { data: DiscussionTypeData, executeWithResult: getDiscussionOptions } =
    useGetIssueProjectDiscussionOptions()

// =====================================================================================================================
// APIクライアントの定義(協議リアクションタイプ)
// =====================================================================================================================
const { executeWithResult: getReaction } = useGetOptions()

// =====================================================================================================================
// 画面遷移
// =====================================================================================================================

/**
   * 顧客特定済みの課題案件一覧に遷移する
   */
const toIssueProjectSearchPage = () => {
  const customerIdentificationId = route.query.customerIdentificationId
  const defaultStatuses = stringify({
    q: {
      caseStatuses: defaultSelectedCaseStatusesWithIssueProject,
    }, // 案件一覧タブ用
  })
  router.push(
      `/customer-proposal/case?${defaultStatuses}&q[tab]=issue-project&q[customerIdentificationId]=${customerIdentificationId}&q[from]=customer-identifying&issueProjectIsNothing=true`,
  )
}

// =====================================================================================================================
// ユーザー関連
// =====================================================================================================================
const { $auth } = useNuxtApp()
const authDisplayUserName = $auth.getUser()!.displayName
const authUserId = $auth.getUser()!.userId

// =====================================================================================================================
// スレッド関連
// =====================================================================================================================

/*
   **  APIクライアントの定義(課題案件)
   */
// useGetIssueProjectOptions
const {
  data: issueProjectOptionsData,
  executeWithResult: getIssueProjectOptions,
} = useGetIssueProjectOptions()

// Idで課題案件を取得
const { data: issueProjectData, executeWithResult: getIssueProject } =
    useGetIssueProject(ref<string>(dataRef.value.issueProjectId))

/**
   * 指定条件に合致するスレッド(とコメント)を取得する
   */
const findThreads = async () => {
  try {
    threadDataRef.value.loading = true

    const { data: threadResultData, executeWithResult: findThread } =
        useFindIssueProjectDiscussionThread(
          ref<FindIssueProjectDiscussionThreadCriteria>({
            issueProjectId: issueProjectId.value as string,
            fromDate: computedFromDate.value,
            toDate: computedToDate.value,
            discussionTypeId: computedDiscussionType.value,
          }),
        )

    // スレッド検索
    const threadResult = await findThread()
    if (!threadResult) return

    dataRef.value.threadsData = Object.assign([], threadResultData.value)

    // ユーザー検索
    await findUser()
    dataRef.value.staffs = myCareerUserData.value.map((x) => {
      return {
        id: x.userId,
        value: x.nameKanji,
        romaji: x.nameRomaji,
      }
    })

    // チーム取得
    await getAllTeam()
  } finally {
    threadDataRef.value.loading = false
  }
}

const sortedThreads = computed<FindIssueProjectDiscussionThreadResultItem[]>(
  () => {
    if (dataRef.value.threadsData) {
      const threads = Object.assign([], dataRef.value.threadsData)
      return threads.sort(
        (
          a: FindIssueProjectDiscussionThreadResultItem,
          b: FindIssueProjectDiscussionThreadResultItem,
        ) =>
          parseISO(b.registeredDateTime).getTime() -
          parseISO(a.registeredDateTime).getTime(),
      )
    }
    return []
  },
)

/**
   * 社内協議のスレッドの添付ファイルのダウンロード
   */
const downloadThreadFile = async (threadId: string, fileName: string) => {
  threadDataRef.value.loading = true

  // APIクライアントの定義(スレッドのファイルダウンロード)
  const { data: downloadData, executeWithResult: downloadThreadFile } =
      useDownloadIssueProjectDiscussionThreadFile(
        ref<downloadIssueProjectDiscussionThreadFile>({
          threadId,
          fileName,
        }),
      )

  const result = await downloadThreadFile()

  // APIがエラーだった場合は処理を中断します。
  if (!result) {
    return
  }

  // Blobオブジェクトの作成
  const blob = new Blob([downloadData.value])
  const link = document.createElement('a')
  // リンク生成
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  // リンククリック実行
  link.click()
  // Blobへの参照を削除する
  URL.revokeObjectURL(link.href)

  threadDataRef.value.loading = false
}

/**
   * スレッド削除
   */
const removeThread = async (
  thread: FindIssueProjectDiscussionThreadResultItem,
) => {
  const confirmResult = await confirmDialog.value?.open('remove')
  if (!confirmResult) return false

  const sendData = {
    id: thread.id,
    registrantId: thread.registrantId,
    version: thread.version,
  }

  // ApiClientの定義
  const { executeWithResult: deleteThread } =
      useDeleteIssueProjectDiscussionThread(
        ref<DeleteIssueProjectDiscussionThreadType>(sendData),
      )

  const result = await deleteThread().then(
    // 成功時
    () => {
      successToast('指定されたスレッドを削除しました。')
      return true
    },
    // エラー時
    (e: any) => {
      if (!e.hasProblemDetails) {
        errorToast('指定されたデータの削除に失敗しました。')
      }
      if (e.data.type === '/conflict') {
        errorToast(
          'すでに別のユーザーがデータを登録・変更しているため削除できませんでした。',
        )
      } else {
        errorToast('指定されたデータの削除に失敗しました。')
      }
      return false
    },
  )

  // APIが失敗の場合は false を返して処理を中断します
  if (!result) return false

  // スレッド検索
  const targetThreadIndex = dataRef.value.threadsData.findIndex(
    (x) => x.id === thread.id,
  )
  // threadsDataから削除
  dataRef.value.threadsData.splice(targetThreadIndex, 1)
}

/**
   * スレッドエディタを開く
   */

// =====================================================================================================================
// スレッドダイアログの定義
// =====================================================================================================================
// 更新・削除確認ダイアログを定義します。
const confirmDialog = ref<AppConfirmDialogType>()

// スレッドのダイアログを定義します。
const threadEditorDialog =
    ref<InstanceType<typeof IssueProjectDiscussionThreadEditorComponent>>()

const openThreadEditor = async (
  thread: FindIssueProjectDiscussionThreadResultItem,
  isAdd: boolean,
) => {
  const threadResult = await threadEditorDialog.value
    ?.open(thread)
    .then((result: { isOk: boolean, data?: any } | undefined) => {
      if (result?.isOk) {
        return result.data
      }
    })

  // キャンセルの場合
  if (!threadResult) return

  threadResult.uploadFiles = []
  threadResult.filesToUpload = []
  threadResult.filesToRemove = []

  threadResult.uploadFiles =
      await threadEditorDialog.value?.getUploadTargetFiles()
  threadResult.filesToUpload =
      threadEditorDialog.value?.dataRef.targetData.filesToUpload
  threadResult.filesToRemove =
      threadEditorDialog.value?.dataRef.targetData.filesToRemove

  // 保存処理
  const saveThreadResult = await saveThread(threadResult, isAdd)
  if (!saveThreadResult) return
  threadEditorDialog.value?.close()
}

/**
   * スレッドの登録/更新
   */
const saveThread = async (thread: targetDataType, isAdd: boolean) => {
  try {
    threadEditorDialog.value?.setLoading(true)
    threadDataRef.value.loading = true

    const sendData = createSendThreadData(thread, isAdd)

    // スレッド更新のAPIクライアントの定義
    const { data: updateThreadData, executeWithResult: updateThread } =
        usePutIssueProjectDiscussionThread(ref<FormData>(sendData))

    // スレッド登録のAPIクライアントの定義
    const { data: addThreadData, executeWithResult: addThread } =
        usePostIssueProjectDiscussionThread(ref<FormData>(sendData))

    const result = await (isAdd ? addThread : updateThread)().catch(
      (e: any) => {
        // バックエンド到達前の通信系エラー
        if (e.cause?.message === 'Failed to fetch') {
          errorToast('保存に失敗しました。添付ファイルを開いていないか確認をお願いします。開いていない場合は通信エラーの可能性がありますので、時間をおいて再度実行してください。')
          return false
        }

        // 失敗だった場合の処理を行います。
        if (!e.hasProblemDetails) {
          errorToast(
            '保存に失敗しました。',
          )
        }
        if (e.data.type === '/validation-error') {
          errorToast(e.data.errors)
        } else if (e.data.type === '/conflict') {
          errorToast(
            'すでに別のユーザーがデータを登録・変更しているため保存できませんでした。',
          )
        } else {
          errorToast(
            '保存に失敗しました。',
          )
        }
        return false
      },
    )

    // APIがエラーだった場合は処理を中断します。
    if (!result) return false

    if (isAdd) {
      isArray(dataRef.value.threadsData)
        ? dataRef.value.threadsData.push(addThreadData.value)
        : (dataRef.value.threadsData = [addThreadData.value])
    } else {
      const targetThreadIndex = dataRef.value.threadsData.findIndex(
        (x) => x.id === updateThreadData.value.id,
      )
      dataRef.value.threadsData.splice(
        targetThreadIndex,
        1,
        updateThreadData.value,
      )
    }

    // 行動記録の保存
    const interactionCategory = isAdd
      ? `IssueProjectThread_${addThreadData.value.purpose}_Add`
      : `IssueProjectThread_${updateThreadData.value.purpose}_Update`
    const saveInteractLogResult = await saveInteractionLog(
      isAdd ? addThreadData.value : updateThreadData.value,
      route.query.customerIdentificationId as string,
      interactionCategory,
      dataRef.value.threadsData,
      dataRef.value.issueItemName,
    )

    if (saveInteractLogResult) {
      successToast('スレッドを保存しました。')
    }

    watcher.init(dataRef.value.threadsData)

    // 契約締結稟議申請兼捺印申請（クラウドサイン・書面契約）の場合、タスクを自動起案する
    if (
      isAdd &&
      (thread.discussionType === TaskAutomaticCreation.cloudSign ||
        thread.discussionType === TaskAutomaticCreation.writtenContract)
    ) {
      const description = thread.descriptions.find(
        (x) => x.key === '契約日・契約書締結予定日',
      )

      const sendTaskData = createSendTaskData(description)

      // APIクライアントの定義 課題案件タスク
      const { executeWithResult: addIssueProjectTask } =
          usePostIssueProjectTask(ref<FormData>(sendTaskData))

      const result = await addIssueProjectTask().catch((err: any) => {
        if (!err.hasProblemDetails) {
          errorToast('タスクを保存できませんでした')
        }
        const data = err.data || {}

        if (data.type === '/validation-error') {
          errorToast(err.data.errors)
        } else if (data.type === '/conflict') {
          errorToast(
            'すでに別のユーザーがデータを登録・変更しているためタスクを保存できませんでした。',
          )
        } else {
          errorToast('タスクを保存できませんでした')
        }
        return false
      })

      if (!result) return

      successToast('タスクを保存しました。')
    }

    return true
  } finally {
    threadEditorDialog.value?.setLoading(false)
    threadDataRef.value.loading = false
  }
}

/**
* スレッド編集内容を送信用データ(FormData)に変換する
*/
const createSendThreadData = (threadData: targetDataType, isAdd: boolean) => {
  const formData = new FormData()

  const discussionType = DiscussionTypeData.value.filter(
    (x) => x.value === threadData.discussionType, // 型を修正
  )

  const discussionTypeName = discussionType[0].text ?? ''

  if (isAdd) {
    formData.append('RegisteredDateTime', new Date().toISOString())
    formData.append('DiscussionTypeId', threadData.discussionType)
    formData.append('PurposeTypeOfTemplate', threadData.purposeTypeOfTemplate)
    formData.append('IssueProjectId', route.query.issueProjectId as string)
    formData.append('Registrant', authDisplayUserName ?? '')
    formData.append('TemplateVersion', threadData.templateVersion.toString())
    // 更新の場合
  } else {
    formData.append('Id', threadData.id)
    formData.append('Version', threadData.version)
  }

  // 登録、更新の共通
  formData.append('DiscussionTypeName', discussionTypeName)
  formData.append('RegistrantId', authUserId ?? '')
  formData.append('DescriptionJson', threadData.descriptionJson as string)
  formData.append(
    'customerIdentificationId',
    route.query.customerIdentificationId as string,
  )
  formData.append('customerName', customer.value?.name ?? '')

  formData.append('Purpose', threadData.purpose)

  if (threadData.person) formData.append('person', threadData.person)

  if (threadData.isPersonOfPower) {
    formData.append('IsPersonOfPower', threadData.isPersonOfPower.toString())
  }

  if (threadData.mentionTargetUserIds) {
    threadData.mentionTargetUserIds.forEach((value) => {
      formData.append('mentionTargetUserIds[]', value)
    })
  }

  if (threadData.mentionTargetTeamIds) {
    threadData.mentionTargetTeamIds.forEach((value) => {
      formData.append('mentionTargetTeamIds[]', value)
    })
  }

  if (threadData.mentionTargetTeamMemberUserIds) {
    threadData.mentionTargetTeamMemberUserIds.forEach((value) => {
      formData.append('mentionTargetTeamMemberUserIds[]', value)
    })
  }

  if (threadData.mentionTargetsHtml) {
    formData.append('mentionTargetsHtml', threadData.mentionTargetsHtml)
  }

  // ファイル
  formData.append('threadId', threadData.id)
  formData.append('updaterId', authUserId ?? '')
  formData.append('updater', authDisplayUserName ?? '')
  formData.append('updaterName', authDisplayUserName ?? '')

  if (threadData.filesToUpload?.length) {
    for (const file of threadData.filesToUpload) {
      formData.append('uploadFiles', file)

      formData.append(
        'versions',
        threadData.uploadFiles?.find((f) => f.fileName === file.name)
          ?.version ?? '',
      )
    }
  }

  if (threadData.filesToRemove?.length) {
    for (const fileToRemove of threadData.filesToRemove) {
      formData.append('filesToRemove', fileToRemove.deleteItemName)
    }
  }

  return formData
}

// =====================================================================================================================
// スレッドのリアクション関連
// =====================================================================================================================
/**
*  スレッドのリアクション追加
*/
const addThreadReaction = async (
  newReactionType: number,
  sendThreadId: string,
) => {
  const sendData = {
    threadId: sendThreadId,
    staffId: authUserId,
    staffName: authDisplayUserName ?? '',
    reactionType: newReactionType,
    updatedDateTime: new Date(),
  }

  // リアクション追加のAPIクライアントの定義
  const { data: reactionData, executeWithResult: addReaction } =
      usePostIssueProjectDiscussionThreadReaction(
        ref<IssueProjectDiscussionThreadReactionForCreate>(sendData),
      )

  const result = await addReaction().catch((e: any) => {
    // 失敗だった場合の処理を行います。
    if (!e.hasProblemDetails) {
      errorToast('リアクションの登録に失敗しました。')
    }
    if (e.data.type === '/validation-error') {
      errorToast(e.data.errors)
    } else if (e.data.type === '/conflict') {
      errorToast(
        'すでに別のユーザーがデータを登録・変更しているため削除できませんでした。',
      )
    } else {
      errorToast('リアクションの登録に失敗しました。')
    }
    return false
  })
  // APIがエラーだった場合は処理を中断します。
  if (!result) return

  // スレッド検索
  const targetThreadIndex = dataRef.value.threadsData.findIndex(
    (x) => x.id === reactionData.value.threadId,
  )

  // 追加リアクションをthreadsDataに入れる
  isArray(dataRef.value.threadsData[targetThreadIndex].reactions)
    ? dataRef.value.threadsData[targetThreadIndex].reactions.push(
      reactionData.value,
    )
    : (dataRef.value.threadsData[targetThreadIndex].reactions = [
        reactionData.value,
      ])

  successToast('リアクションを登録しました。')
}

/**
*  スレッドのリアクション更新
*/
const updateThreadReaction = async (
  reaction: issueProjectDiscussionThreadReactionType,
  newReactionType: number,
) => {
  const sendData = {
    id: reaction.id,
    reactionType: newReactionType,
    updatedDateTime: new Date(),
    version: reaction.version,
  }

  // リアクション更新のAPIクライアントの定義
  const { data: reactionData, executeWithResult: updateReaction } =
      usePutIssueProjectDiscussionThreadReaction(
        ref<IssueProjectDiscussionThreadReactionForSave>(sendData),
      )

  const result = await updateReaction().catch((e: any) => {
    // 失敗だった場合の処理を行います。
    if (!e.hasProblemDetails) {
      errorToast('リアクションの更新に失敗しました。')
    }
    if (e.data.type === '/validation-error') {
      errorToast(e.data.errors)
    } else if (e.data.type === '/conflict') {
      errorToast(
        'すでに別のユーザーがデータを登録・変更しているため更新できませんでした。',
      )
    } else {
      errorToast('リアクションの更新に失敗しました。')
    }
    return false
  })
  // APIがエラーだった場合は処理を中断します。
  if (!result) return

  // スレッド検索
  const targetThreadIndex = dataRef.value.threadsData.findIndex(
    (x) => x.id === reactionData.value.threadId,
  )

  const reactionIndex =
      dataRef.value.threadsData[targetThreadIndex].reactions?.findIndex(
        (x) => x.id === reactionData.value.id,
      ) ?? -1

  // 更新リアクションをthreadsDataに入れる
  if (reactionIndex !== -1) {
    dataRef.value.threadsData[targetThreadIndex].reactions?.splice(
      reactionIndex,
      1,
      reactionData.value,
    )
  }

  successToast('リアクションを更新しました。')
}

/**
   *  スレッドのリアクション削除
   */
const deleteThreadReaction = async (
  reaction: IssueProjectDiscussionThreadReactionForDelete,
  threadId: string,
) => {
  // リアクション削除のAPIクライアントの定義
  const { executeWithResult: deleteReaction } =
      useDeleteIssueProjectDiscussionThreadReaction(
        ref<IssueProjectDiscussionThreadReactionForDelete>(reaction),
      )

  const result = await deleteReaction().catch((e: any) => {
    // 失敗だった場合の処理を行います。
    if (!e.hasProblemDetails) {
      errorToast('リアクションの削除に失敗しました。')
    }
    if (e.data.type === '/validation-error') {
      errorToast(e.data.errors)
    } else if (e.data.type === '/conflict') {
      errorToast(
        'すでに別のユーザーがデータを登録・変更しているため削除できませんでした。',
      )
    } else {
      errorToast('リアクションの削除に失敗しました。')
    }
    return false
  })
  // APIがエラーだった場合は処理を中断します。
  if (!result) return

  // スレッド検索
  const targetThreadIndex = dataRef.value.threadsData.findIndex(
    (x) => x.id === threadId,
  )

  // threadsDataから削除
  const reactionIndex =
      dataRef.value.threadsData[targetThreadIndex].reactions?.findIndex(
        (x) => x.id === reaction.id,
      ) ?? -1

  if (reactionIndex !== -1) {
    dataRef.value.threadsData[targetThreadIndex].reactions?.splice(
      reactionIndex,
      1,
    )
  }

  successToast('リアクションを削除しました。')
}

/**
* タスク送信データの作成
*/
const createSendTaskData = (description: descriptionsType | undefined) => {
  const formData = new FormData()
  formData.append(
    'customerIdentificationId',
    route.query.customerIdentificationId as string,
  )
  formData.append('issueProjectId', route.query.issueProjectId as string)

  formData.append('requestedAt', new Date().toISOString())
  description?.value instanceof Date
    ? description.value.setDate(description.value?.getDate() - 7)
    : new Date()

  const expiredAt = ref<string>()
  if (typeof description?.value === 'string') {
    expiredAt.value = parseISO(description.value).toISOString()
  } else if (description?.value instanceof Date) {
    expiredAt.value = description.value.toISOString()
  }
  formData.append('expiredAt', expiredAt.value as string)
  formData.append('staffId', dataRef.value.bpoTeamId)
  formData.append('overview', '契約手続')
  formData.append(
    'detail',
    '契約書作成\r\n入金予定明細表記載\r\n契約書DB番号採番\r\nメール作成\r\nクラウドサイン申請or捺印・お渡し\r\n契約書格納\r\n入金予定明細表追加記入\r\n請求書メール送信',
  )
  formData.append('issueProjectTaskStatus', '1')
  formData.append('customerName', customer.value?.name ?? '')

  formData.append('UpdatedDateTime', new Date().toISOString())
  if (dataRef.value.bpoTeam) {
    for (const bpoTeamStaffId of dataRef.value.bpoTeamStaffIds) {
      formData.append('teamStaffIds', bpoTeamStaffId)
    }
    formData.append('teamId', dataRef.value.bpoTeam.id)
  }
  return formData
}

// =====================================================================================================================
// 行動記録関連
// =====================================================================================================================

/**
* 行動記録を登録する
*/
const saveInteractionLog = async (
  savedData: IssueProjectDiscussionThread | any, // IssueProjectDiscussionThread以外の型が入る可能性もあるのでany
  customerIdentificationId: string,
  interactionCategory: string,
  threadData: FindIssueProjectDiscussionThreadResultItem[],
  issueItemName: string | undefined,
): Promise<GetInteractionLogResult | boolean> => {
  const isThread = [
    'IssueProjectThread_Internal_Add',
    'IssueProjectThread_Internal_Update',
    'IssueProjectThread_External_Add',
    'IssueProjectThread_External_Update',
  ].includes(interactionCategory)

  const isComment = [
    'IssueProjectComment_Internal_Add',
    'IssueProjectComment_Internal_Update',
    'IssueProjectComment_External_Add',
    'IssueProjectComment_External_Update',
  ].includes(interactionCategory)

  // 共通の登録項目をセット
  let sendInteractionLogData = {
    customerIdentificationId,
    interactionCategory,
    sourceId: savedData.id,
    creatorId: authUserId,
    creatorName: authDisplayUserName,
    createdDateTime: new Date(),
    description1: issueItemName,
  }

  // コメント・スレッドに応じた項目をセット
  if (isThread) {
    sendInteractionLogData = Object.assign(sendInteractionLogData, {
      threadId: savedData.id,
      description2: savedData.title,
    })
  } else if (isComment) {
    const targetThread = threadData.find((x) => x.id === savedData.id)
    sendInteractionLogData = Object.assign(sendInteractionLogData, {
      threadId: targetThread?.id,
      description2: targetThread?.title,
    })
  }

  // APIクライアントの定義・行動記録の登録
  const { data: interactionLogData, executeWithResult: addInteractionLog } =
      usePostInteractionLog(ref<InteractionLog>(sendInteractionLogData))

  await addInteractionLog().catch(() => {
    // 行動の登録に失敗した場合はメッセージを表示
    errorToast('データの保存には成功しましたが、行動記録の登録に失敗しました。')
    return false
  })

  return interactionLogData.value
}

// =====================================================================================================================
// 照会期間関連
// =====================================================================================================================
const updateFromDate = (fromDate: Date) => {
  dataRef.value.fromDate = fromDate
  findThreads()
}

const updateToDate = (toDate: Date) => {
  dataRef.value.toDate = toDate
  findThreads()
}

const updateDiscussionType = (discussionType: string) => {
  dataRef.value.discussionType = discussionType
  findThreads()
}

// =====================================================================================================================
// コメント関連
// =====================================================================================================================
const { postCommentReaction } = usePostCommentReaction()
const { putCommentReaction } = usePutCommentReaction()
const { removeCommentReaction } = useDeleteCommentReaction()

/**
   *  コメントのリアクション追加
   */
const addCommentReaction = (
  newReactionType: number,
  threadId: string,
  sendCommentId: string,
) => {
  postCommentReaction(
    newReactionType,
    threadId,
    sendCommentId,
    dataRef.value.threadsData,
  )
}

/**
*  コメントのリアクション更新
*/
const updateCommentReaction = (
  reaction: issueProjectDiscussionCommentReactionType,
  newReactionType: number,
  threadId: string,
) => {
  putCommentReaction(
    reaction,
    newReactionType,
    threadId,
    dataRef.value.threadsData,
  )
}

/**
*  コメントのリアクション削除
*/
const deleteCommentReaction = (
  reaction: IssueProjectDiscussionCommentReactionForDelete,
  threadId: string,
  commentId: string,
) => {
  removeCommentReaction(
    reaction,
    threadId,
    commentId,
    dataRef.value.threadsData,
  )
}

/**
*  コメント削除
*/
const { deleteComment } = useDeleteComment()

const removeComment = async (
  emitComment: issueProjectDiscussionCommentType,
) => {
  const dialogResult = await confirmDialog.value?.open('remove')
  if (!dialogResult) return false

  const sendData = {
    id: emitComment.id,
    registrantId: emitComment.registrantId,
    version: emitComment.version,
  }

  deleteComment(sendData, emitComment, dataRef.value.threadsData)
}

/**
* コメントの添付ファイルのダウンロード
*/
const downloadCommentFile = async (commentId: string, fileName: string) => {
  threadDataRef.value.loading = true

  const { data: commentFileData, executeWithResult: downloadCommentFile } =
      useDownloadIssueProjectDiscussionCommentFile(
        ref<downloadIssueProjectDiscussionCommentFile>({
          commentId,
          fileName,
        }),
      )

  await downloadCommentFile().catch((e) => {
    // 失敗だった場合の処理を行います。
    if (!e.hasProblemDetails) {
      errorToast('指定されたファイルのダウンロードに失敗しました。')
    }
    return false
  })

  if (!commentFileData) return

  // Blobオブジェクトの作成
  const blob = new Blob([commentFileData.value])
  const link = document.createElement('a')
  // リンク生成
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  // リンククリック実行
  link.click()
  // Blobへの参照を削除する
  URL.revokeObjectURL(link.href)

  threadDataRef.value.loading = false
}

/**
* コメントエディタを開く
* */

// コメント追加のcomposableの定義
const { addComment } = usePostComment()

export type FileObjectType = {
  filesToUpload: File[]
  filesToRemove: DeleteItem[]
}

// コメントのダイアログを定義します。
const commentEditorDialog =
    ref<InstanceType<typeof IssueProjectDiscussionCommentEditorComponent>>()

// コメント追加
const openCommentEditorWhenAdd = async (
  threadId: string,
  setShownComment: any,
  changeLatestCommentShownDetailState: (isShownDetail: boolean) => any,
) => {
  let comment = null
  comment = await commentEditorDialog.value
    ?.open(threadId, null)
    .then((result) => {
      if (result.isOk) {
        return result.data
      }
    })
    // キャンセルの場合
  if (!comment) {
    return
  }

  const fileObject: FileObjectType = {
    filesToUpload: [],
    filesToRemove: [],
  }

  setShownComment()
  fileObject.filesToUpload =
      commentEditorDialog.value?.dataRef.filesToUpload ?? []
  fileObject.filesToRemove =
      commentEditorDialog.value?.dataRef.filesToRemove ?? []

  const addResult = await addComment(
    comment,
    route.query.customerIdentificationId as string,
    dataRef.value.threadsData,
    saveInteractionLog,
    dataRef.value.issueItemName,
    fileObject,
    customer.value?.name ?? '',
  )
  if (!addResult) {
    return
  }

  changeLatestCommentShownDetailState(true)
  commentEditorDialog.value?.close()
}

/**
* コメントエディタを開く、更新
* */

// コメント更新のcomposableの定義
const { updateComment } = usePutComment()

const openCommentEditorWhenUpdate = async (
  comment: issueProjectDiscussionCommentType,
) => {
  const commentResult = await commentEditorDialog.value
    ?.open('', comment)
    .then((result) => {
      if (result.isOk) {
        const resultObject = {
          data: result.data,
          mentionTargetTeamMemberUserIds:
              result.mentionTargetTeamMemberUserIds ?? [],
        }
        return resultObject
      }
    })
    // キャンセルの場合
  if (!commentResult) {
    return
  }

  const fileObject: FileObjectType = {
    filesToUpload: [],
    filesToRemove: [],
  }

  fileObject.filesToUpload =
      commentEditorDialog.value?.dataRef.filesToUpload ?? []
  fileObject.filesToRemove =
      commentEditorDialog.value?.dataRef.filesToRemove ?? []

  const updateResult = await updateComment(
    commentResult.data,
    route.query.customerIdentificationId as string,
    dataRef.value.threadsData,
    saveInteractionLog,
    dataRef.value.issueItemName,
    commentResult.mentionTargetTeamMemberUserIds,
    fileObject,
    customer.value?.name ?? '',
  )
  if (!updateResult) {
    return
  }
  commentEditorDialog.value?.close()
}

// =====================================================================================================================
// スクロール関連
// =====================================================================================================================
/**
   * スクロール
   */
const scrollToThread = (threadId: string) => {
  const element = document.getElementById(`thread-${threadId}`)
  if (element) {
    // 照会期間などのアイテムでスレッドタイトルが隠れるため、オフセットを設定してスクロールする
    const targetPosition =
        element.getBoundingClientRect().top + window.scrollY - 100
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth',
    })
  } else {
    errorToast('こちらの協議、コメントはすでに削除されています。')
  }
}

  /**
   * スレッドを展開する
   */
  type ThreadComponent = InstanceType<
    typeof IssueProjectDiscussionThreadComponent
  >

const threadRef = ref<ThreadComponent[]>([])

const expandThread = async (threadId: string) => {
  if (!threadId) return

  // コンポーネントを選択
  const threadComponent = await threadRef.value.find(
    (component: ThreadComponent) => {
      return component.definedProps.thread.id === threadId
    },
  )

  // コンポーネントが存在する場合、返信の展開と詳細表示を実行
  if (threadComponent) {
    threadComponent.toggleShownCommentsState()
    threadComponent.toggleShownDetailState()
  }
}

/**
* スレッドをハイライト表示する
*/
const highlightThread = async (threadId: string) => {
  if (!threadId) return

  // コンポーネントを選択
  const threadComponent = await threadRef.value.find(
    (component: ThreadComponent) =>
      component.definedProps.thread.id === threadId,
  )
  // 選択されたコンポーネントが存在する場合、ハイライトを実行
  if (threadComponent) {
    threadComponent.highlight()
  }
}

// =====================================================================================================================
// パンくずリスト
// ====================================================================================================================
const { replaceHistories, clearHistories } = useBreadcrumbs()

const createBreadcrumbs = () => {
  const query = route.query as { [key: string]: string }
  clearHistories()
  switch (query.from) {
    // Topから遷移
    case OriginOfTransitions.TOP__ISSUE_PROJECT_DISCUSSION: {
      const breadcrumb: Breadcrumb[] = [
        {
          title: '案件一覧',
          path: '/case/case-work-flow-optimization?q[tab]=issue-project-discussion',
          isExternal: false,
        },
      ]
      replaceHistories(
        breadcrumb,
      )
      break
    }

    /** 顧客検索  >  ポータル  >  案件一覧  >  課題案件編集の協議ボタン */
    case OriginOfTransitions.CUSTOMER_IDENTIFYING__ISSUE_PROJECT_DISCUSSION: {
      const breadcrumb: Breadcrumb[] = [
        {
          title: 'ポータル',
          path: PATHS({
            customerIdentificationId: query.customerIdentificationId,
          }).PORTAL,
          shortPath: '',
          isExternal: true,
        },
        {
          title: '案件一覧',
          path: `/case/case-work-flow-optimization?q[tab]=issue-project&q[from]=${OriginOfTransitions.CUSTOMER_IDENTIFYING}&q[customerIdentificationId]=${query.customerIdentificationId}&q[caseStatuses]=${DEFAULT_SELECTED_CASE_STATUSES_WITH_ISSUE_PROJECT}`,
          shortPath: '',
          isExternal: false,
        },
        {
          title: '課題案件編集',
          path: `/case/case-work-flow-optimization/issue-project/edit?id=${query.issueProjectId}&customerIdentificationId=${query.customerIdentificationId}&from=${OriginOfTransitions.CUSTOMER_IDENTIFYING__ISSUE_PROJECT}`,
          shortPath: '',
          isExternal: false,
        },
      ]
      replaceHistories(breadcrumb)
      break
    }

    // TOP → 課題案件タブ → 課題案件編集から遷移
    case OriginOfTransitions.TOP__ISSUE_PROJECT_TASK_EDIT: {
      const breadcrumb: Breadcrumb[] = [
        {
          title: '案件一覧',
          path: `/case/case-work-flow-optimization?q[tab]=issue-project&q[caseStatuses]=${DEFAULT_SELECTED_CASE_STATUSES_WITH_ISSUE_PROJECT}`,
          isExternal: false,
        },
        {
          title: '課題案件編集',
          path: `/case/case-work-flow-optimization/issue-project/edit?id=${query.issueProjectId}&customerIdentificationId=${query.customerIdentificationId}&from=${OriginOfTransitions.CUSTOMER_IDENTIFYING__ISSUE_PROJECT}`,
          isExternal: false,
        },
      ]
      replaceHistories(breadcrumb)
      break
    }

    // タイムラインから遷移
    case OriginOfTransitions.TIMELINE: {
      const breadcrumb: Breadcrumb[] = [
        {
          title: '課題案件編集',
          path: `/case/case-work-flow-optimization/issue-project/edit?id=${query.issueProjectId}&customerIdentificationId=${query.customerIdentificationId}`,
          isExternal: false,
        },
      ]
      replaceHistories(breadcrumb)
      break
    }

    // Teamsなど遷移元ページが指定されない直リンクから遷移
    default: {
      const breadcrumb: Breadcrumb[] = [
        {
          title: '課題案件編集',
          path: `/case/case-work-flow-optimization/issue-project/edit?id=${query.issueProjectId}&customerIdentificationId=${query.customerIdentificationId}`,
          isExternal: false,
        },
      ]
      replaceHistories(breadcrumb)
    }
  }
}
</script>
