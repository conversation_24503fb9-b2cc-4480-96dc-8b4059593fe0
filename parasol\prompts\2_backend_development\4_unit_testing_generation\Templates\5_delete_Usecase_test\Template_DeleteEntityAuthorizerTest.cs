using FluentAssertions;
using Nut.Results.FluentAssertions;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.DeleteEntity;
// using Shared.Services;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.DeleteEntity;

/// <summary>
/// DeleteEntityAuthorizerの単体テストクラス
/// </summary>
/// <remarks>
/// Delete操作の認可ロジックを包括的にテストします。
/// 主にエンティティの所有者・作成者・削除権限を持つユーザーのみが削除可能であることを検証します。
/// Delete操作は不可逆的な処理であるため、認可チェックは特に重要です。
/// Update AuthorizerTestと同様の構造ですが、削除権限の特殊性を考慮したテストケースを追加します。
/// コードカバレッジを100%に近づけるため、成功・失敗・例外パターンを網羅的にテストします。
/// </remarks>
public class DeleteEntityAuthorizerTest : IAsyncLifetime
{
    private readonly TestCurrentUserService _currentUserService;

    public DeleteEntityAuthorizerTest()
    {
        // TODO: 実際のテストユーザー設定に合わせて調整してください
        _currentUserService = new TestCurrentUserService();
    }

    public Task InitializeAsync()
        => Task.CompletedTask;

    public Task DisposeAsync() => Task.CompletedTask;

    #region 異常系テスト（null チェック）

    [Fact]
    public void Ctor_引数にnullを渡すと例外が発生する()
    {
        // TODO: 実際のコンストラクタ引数に合わせて調整してください
        var nullCurrentUserService = (ICurrentUserService)null!;
        var act = () => new DeleteEntityAuthorizer(nullCurrentUserService);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public async Task Authorize_Requestがnullの場合は例外が発生する()
    {
        // Arrange
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act & Assert
        var nullCommand = (DeleteEntityCommand)null!;
        var act = () => authorizer.AuthorizeAsync(nullCommand, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    #endregion

    #region 認可成功テスト

    [Fact]
    public async Task Authorize_所有者による削除で成功する()
    {
        // Arrange
        var ownerId = "owner-user-001";
        _currentUserService.UpdateUserName(ownerId);
        
        // TODO: 実際のDeleteEntityCommandの構造に合わせて調整してください
        var command = CreateValidCommand() with { UserId = ownerId };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
    }

    [Fact]
    public async Task Authorize_作成者による削除で成功する()
    {
        // Arrange
        var creatorId = "creator-user-001";
        _currentUserService.UpdateUserName(creatorId);
        
        // TODO: 実際のエンティティに作成者フィールドがある場合は調整してください
        var command = CreateValidCommand() with { CreatedBy = creatorId };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
    }

    [Theory]
    [InlineData("0000001")] // テストで使用される標準ユーザーID
    [InlineData("admin-user-001")]
    [InlineData("authorized-user-002")]
    public async Task Authorize_権限を持つユーザーIDで成功する(string authorizedUserId)
    {
        // Arrange
        _currentUserService.UpdateUserName(authorizedUserId);
        
        var command = CreateValidCommand() with { UserId = authorizedUserId };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
    }

    [Fact]
    public async Task Authorize_管理者権限で他人のエンティティ削除が成功する()
    {
        // TODO: ロールベース認可がある場合は実装してください
        // Arrange
        var adminUserId = "admin-user-001";
        var adminRoles = new[] { "Admin", "SystemAdmin", "SuperUser" };
        _currentUserService.UpdateUser(CurrentUser.CreateCurrentUser(adminUserId, adminRoles));
        
        var command = CreateValidCommand() with { UserId = "other-user-001" }; // 他人のエンティティでも削除可能
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
    }

    [Fact]
    public async Task Authorize_削除権限ロールで成功する()
    {
        // TODO: 削除専用ロールがある場合は実装してください
        // Arrange
        var deleterId = "deleter-user-001";
        var deleteRoles = new[] { "EntityDeleter", "DataManager", "Moderator" };
        _currentUserService.UpdateUser(CurrentUser.CreateCurrentUser(deleterId, deleteRoles));
        
        var command = CreateValidCommand() with { UserId = "target-user-001" }; 
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
    }

    [Fact]
    public async Task Authorize_同一チームメンバーによる削除で成功する()
    {
        // TODO: チームベース認可がある場合は実装してください
        // Arrange
        var teamMemberId = "team-member-001";
        _currentUserService.UpdateUserName(teamMemberId);
        
        var command = CreateValidCommand() with 
        { 
            UserId = "original-user-001",
            TeamId = "team-001" 
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
    }

    #endregion

    #region 認可失敗テスト

    [Fact]
    public async Task Authorize_所有者以外による削除で失敗する()
    {
        // Arrange
        var unauthorizedUserId = "unauthorized-user-001";
        _currentUserService.UpdateUserName(unauthorizedUserId);
        
        var command = CreateValidCommand() with { UserId = "owner-user-001" }; // 別のユーザーが所有
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Theory]
    [InlineData("dummy")]
    [InlineData("userId")] // 分析で見つかった失敗パターン
    [InlineData("invalid-user")]
    [InlineData("deleted-user")]
    [InlineData("suspended-user")]
    public async Task Authorize_無効なユーザーIDで失敗する(string invalidUserId)
    {
        // Arrange
        _currentUserService.UpdateUserName(invalidUserId);
        
        var command = CreateValidCommand() with { UserId = "0000001" }; // 有効なターゲット
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Fact]
    public async Task Authorize_削除権限不足のユーザーで失敗する()
    {
        // Arrange
        var limitedUserId = "limited-user-001";
        var limitedRoles = new[] { "ReadOnly", "Guest", "Viewer" };
        _currentUserService.UpdateUser(CurrentUser.CreateCurrentUser(limitedUserId, limitedRoles));
        
        var command = CreateValidCommand() with { UserId = "owner-user-001" };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Fact]
    public async Task Authorize_別チームのユーザーで失敗する()
    {
        // TODO: チームベース認可がある場合は実装してください
        // Arrange
        var otherTeamUserId = "other-team-user-001";
        _currentUserService.UpdateUserName(otherTeamUserId);
        
        var command = CreateValidCommand() with 
        { 
            UserId = "team-user-001",
            TeamId = "team-001" // 異なるチーム
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Fact]
    public async Task Authorize_保護されたエンティティの削除で失敗する()
    {
        // TODO: エンティティ保護機能がある場合は実装してください
        // Arrange
        var validUserId = "valid-user-001";
        _currentUserService.UpdateUserName(validUserId);
        
        var command = CreateValidCommand() with 
        { 
            UserId = validUserId,
            IsProtected = true // 保護されたエンティティ
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Fact]
    public async Task Authorize_システムエンティティの削除で失敗する()
    {
        // TODO: システムエンティティの保護がある場合は実装してください
        // Arrange
        var validUserId = "valid-user-001";
        _currentUserService.UpdateUserName(validUserId);
        
        var command = CreateValidCommand() with 
        { 
            UserId = validUserId,
            IsSystemEntity = true // システムエンティティ
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    #endregion

    #region 特殊ケース・エッジケーステスト

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("  ")]
    [InlineData("\t")]
    [InlineData("\n")]
    public async Task Authorize_空文字や空白のUserIdで失敗する(string invalidUserId)
    {
        // Arrange
        _currentUserService.UpdateUserName("valid-user-001");
        
        var command = CreateValidCommand() with { UserId = invalidUserId };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Fact]
    public async Task Authorize_CurrentUserが未設定で失敗する()
    {
        // Arrange
        var emptyCurrentUserService = new TestCurrentUserService(); // CurrentUserが設定されていない
        var command = CreateValidCommand();
        var authorizer = new DeleteEntityAuthorizer(emptyCurrentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Fact]
    public async Task Authorize_大文字小文字の違いで失敗する()
    {
        // Arrange - 大文字小文字を区別する場合のテスト
        var userId = "User-001";
        _currentUserService.UpdateUserName(userId);
        
        var command = CreateValidCommand() with { UserId = "user-001" }; // 小文字
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        // TODO: システムが大文字小文字を区別するかどうかに応じて調整してください
        result.Should().BeError(); // 区別する場合
        // result.Should().BeOk(); // 区別しない場合
    }

    [Theory]
    [InlineData("user-001", "USER-001")]
    [InlineData("Admin", "admin")]  
    [InlineData("TestUser", "testuser")]
    public async Task Authorize_大文字小文字違いのユーザーID比較パターン(string currentUser, string commandUser)
    {
        // TODO: システムの大文字小文字の扱いに応じて期待結果を調整してください
        // Arrange
        _currentUserService.UpdateUserName(currentUser);
        
        var command = CreateValidCommand() with { UserId = commandUser };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        // システムが大文字小文字を区別する場合は BeError()
        // システムが大文字小文字を区別しない場合は BeOk()
        result.Should().BeError(); // デフォルトでは区別するものとして設定
    }

    #endregion

    #region Delete特有の認可テスト

    [Fact]
    public async Task Authorize_削除済みエンティティの削除で失敗する()
    {
        // TODO: 削除済み状態の確認が実装されている場合は調整してください
        // Arrange
        var validUserId = "valid-user-001";
        _currentUserService.UpdateUserName(validUserId);
        
        var command = CreateValidCommand() with 
        { 
            UserId = validUserId,
            IsDeleted = true // 既に削除済み
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Fact]
    public async Task Authorize_子エンティティが存在する場合の削除で失敗する()
    {
        // TODO: 子エンティティ存在チェックがある場合は実装してください
        // Arrange
        var validUserId = "valid-user-001";
        _currentUserService.UpdateUserName(validUserId);
        
        var command = CreateValidCommand() with 
        { 
            UserId = validUserId,
            HasChildren = true // 子エンティティが存在
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    [Fact]
    public async Task Authorize_参照されているエンティティの削除で失敗する()
    {
        // TODO: 外部参照チェックがある場合は実装してください
        // Arrange
        var validUserId = "valid-user-001";
        _currentUserService.UpdateUserName(validUserId);
        
        var command = CreateValidCommand() with 
        { 
            UserId = validUserId,
            IsReferenced = true // 他のエンティティから参照されている
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    #endregion

    #region 複合条件テスト

    [Fact]
    public async Task Authorize_複数権限チェック条件が満たされる場合に成功する()
    {
        // TODO: 複合的な認可ルールがある場合は実装してください
        // 例: 所有者であり、かつ削除権限を持ち、かつ保護されていない等
        
        // Arrange
        var validUserId = "multi-condition-user-001";
        var validRoles = new[] { "EntityDeleter", "DataManager" };
        _currentUserService.UpdateUser(CurrentUser.CreateCurrentUser(validUserId, validRoles));
        
        var command = CreateValidCommand() with 
        { 
            UserId = validUserId,
            IsProtected = false,
            IsSystemEntity = false
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();
    }

    [Fact]
    public async Task Authorize_一部条件が満たされない場合に失敗する()
    {
        // TODO: 複合的な認可ルールがある場合は実装してください
        
        // Arrange
        var userId = "partial-condition-user-001";
        var insufficientRoles = new[] { "ReadOnly" }; // 権限不足
        _currentUserService.UpdateUser(CurrentUser.CreateCurrentUser(userId, insufficientRoles));
        
        var command = CreateValidCommand() with 
        { 
            UserId = userId, // ユーザーは一致するが
            IsProtected = true // 保護されているため削除不可
        };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);

        // Act
        var result = await authorizer.AuthorizeAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    #endregion

    #region キャンセレーション・非同期テスト

    [Fact]
    public async Task Authorize_CancellationTokenでキャンセルされる場合の動作確認()
    {
        // Arrange
        var validUserId = "cancel-test-user-001";
        _currentUserService.UpdateUserName(validUserId);
        
        var command = CreateValidCommand() with { UserId = validUserId };
        var authorizer = new DeleteEntityAuthorizer(_currentUserService);
        var cts = new CancellationTokenSource();
        cts.Cancel(); // 即座にキャンセル

        // Act & Assert
        var act = () => authorizer.AuthorizeAsync(command, cts.Token);
        
        // TODO: Authorizerの実装がキャンセレーションをサポートしているかに応じて調整
        // キャンセレーション対応している場合
        await act.Should().ThrowAsync<OperationCanceledException>();
        
        // キャンセレーション対応していない場合（多くの場合はこちら）
        // var result = await act.Should().NotThrowAsync();
        // result.Should().NotBeNull();
    }

    #endregion

    #region ヘルパーメソッド

    /// <summary>
    /// テスト用の有効なDeleteEntityCommandを作成します
    /// </summary>
    /// <returns>有効なDeleteEntityCommand</returns>
    private static DeleteEntityCommand CreateValidCommand()
    {
        // TODO: 実際のDeleteEntityCommandの構造に合わせて調整してください
        
        // パターン1: ID + Version のみ（認可不要）
        // return new DeleteEntityCommand(
        //     Id: "entity-001",
        //     Version: "version-1"
        // );
        
        // パターン2: ID + UserId + Version（認可必要）
        return new DeleteEntityCommand(
            Id: "entity-001",
            UserId: "default-user-001", // 認可テストで重要なフィールド
            Version: "version-1"
        );
        
        // パターン3: 追加フィールドがある場合
        // return new DeleteEntityCommand(
        //     Id: "entity-001",
        //     UserId: "default-user-001",
        //     Version: "version-1",
        //     CreatedBy: null,        // 作成者情報
        //     TeamId: null,          // チームベース認可
        //     IsProtected: false,     // 保護フラグ
        //     IsSystemEntity: false,  // システムエンティティフラグ
        //     IsDeleted: false,      // 削除済みフラグ
        //     HasChildren: false,     // 子エンティティ存在フラグ
        //     IsReferenced: false     // 外部参照フラグ
        // );
    }

    #endregion
}
