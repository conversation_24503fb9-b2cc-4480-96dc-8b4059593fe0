# バックエンド共通処理 利用方法サンプル

このドキュメントは、app-architecture-template-backend-sharedで提供される共通処理の利用方法を示すサンプル集です。各ライブラリの主要なクラスとインターフェースの使用方法を、実際のコード例とともに説明します。

## 目次

- [ドメイン層の実装](#ドメイン層の実装)
- [リポジトリパターンの利用](#リポジトリパターンの利用)
- [仕様パターンの利用](#仕様パターンの利用)
- [UseCaseの実装（MediatRベース）](#usecaseの実装mediatrベース)
- [メッセージングの利用](#メッセージングの利用)
- [バリデーションの利用](#バリデーションの利用)
- [ユーザー情報の取得](#ユーザー情報の取得)
- [オブジェクトストレージの利用](#オブジェクトストレージの利用)
- [拡張メソッドの利用](#拡張メソッドの利用)
- [例外処理の利用](#例外処理の利用)

## ドメイン層の実装

### エンティティの定義

```csharp
using Shared.Domain;

// 基本的なエンティティ
public class Employee : IEntity<int>
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

// アグリゲートルート
public class Department : IAggregateRoot<int>
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public List<Employee> Employees { get; set; } = new();
}

// バージョン管理可能なエンティティ
public class Project : IEntity<int>, IVersionableEntity
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
}

// アーキテクチャガイド推奨のIRootEntity<T>を使用したエンティティ
public class User : IRootEntity<int>
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
    public bool IsDeleted { get; set; }
}
```

### 値オブジェクトの定義

```csharp
using Shared.Domain;

// 強く型付けされた値オブジェクト
[StronglyTypedValue(typeof(string))]
public partial struct EmailAddress
{
    static partial void Validate(string value, List<DomainConstraintViolation> violations)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            violations.Add(new DomainConstraintViolation("メールアドレスは必須です"));
        }
        else if (!value.Contains("@"))
        {
            violations.Add(new DomainConstraintViolation("有効なメールアドレス形式ではありません"));
        }
    }
}

// 使用例
var emailResult = EmailAddress.From("<EMAIL>");
if (emailResult.IsSuccess)
{
    var email = emailResult.Get();
    // 値オブジェクトを使用
}
```

## リポジトリパターンの利用

### リポジトリの実装

```csharp
using Shared.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

public class EmployeeRepository : EntityFrameworkRepository<Employee, int>, IEmployeeRepository
{
    public EmployeeRepository(DbContext context) : base(context)
    {
    }

    // カスタムクエリの実装
    public async Task<Result<List<Employee>>> FindByDepartmentAsync(int departmentId)
    {
        return await Result.Try(async () =>
        {
            return await Context.Set<Employee>()
                .Where(e => e.DepartmentId == departmentId)
                .ToListAsync();
        });
    }
}

// インターフェースの定義
public interface IEmployeeRepository : IRepository<Employee, int>
{
    Task<Result<List<Employee>>> FindByDepartmentAsync(int departmentId);
}
```

### リポジトリの使用

```csharp
public class EmployeeService
{
    private readonly IEmployeeRepository _repository;

    public EmployeeService(IEmployeeRepository repository)
    {
        _repository = repository;
    }

    public async Task<Result<Employee>> GetEmployeeAsync(int id)
    {
        return await _repository.GetAsync(id);
    }

    public async Task<Result<List<Employee>>> GetAllEmployeesAsync()
    {
        return await _repository.AllAsync();
    }

    public async Task<Result> CreateEmployeeAsync(Employee employee)
    {
        return await _repository.AddAsync(employee);
    }
}
```

## 仕様パターンの利用

### 仕様の定義

```csharp
using Shared.Spec;
using Shared.Spec.Builder;
using Shared.Spec.SpecificationParts;

// アーキテクチャガイド推奨の命名規則に従ったSpecification
public class FindEmployeeSpecification : BaseSpecification<Employee>
{
    public FindEmployeeSpecification()
    {
        Query = new SpecificationBuilder<Employee>();
    }

    public FindEmployeeSpecification ByDepartment(int departmentId)
    {
        Query.Where(e => e.DepartmentId == departmentId);
        return this;
    }

    public FindEmployeeSpecification ByName(string name)
    {
        if (!string.IsNullOrWhiteSpace(name))
        {
            Query.Where(e => e.Name.Contains(name));
        }
        return this;
    }

    public FindEmployeeSpecification ActiveOnly()
    {
        Query.Where(e => e.IsActive);
        return this;
    }

    public FindEmployeeSpecification OrderByName()
    {
        Query.OrderBy(e => e.Name);
        return this;
    }

    public FindEmployeeSpecification WithPagination(int pageIndex, int pageSize)
    {
        Query.Skip((pageIndex - 1) * pageSize);
        Query.Take(pageSize);
        return this;
    }
}
```

### 仕様の使用

```csharp
public class EmployeeService
{
    private readonly IEmployeeRepository _repository;

    public async Task<Result<List<Employee>>> SearchEmployeesAsync(
        int? departmentId, 
        string? name, 
        int pageIndex = 1, 
        int pageSize = 20)
    {
        var spec = new FindEmployeeSpecification()
            .ByName(name)
            .OrderByName()
            .WithPagination(pageIndex, pageSize);

        if (departmentId.HasValue)
        {
            spec.ByDepartment(departmentId.Value);
        }

        return await _repository.FindAsync(spec);
    }

    public async Task<Result<PaginatedResult<Employee>>> SearchEmployeesWithPaginationAsync(
        int? departmentId, 
        string? name, 
        PaginationOption pageOption)
    {
        var spec = new FindEmployeeSpecification()
            .ByName(name);

        if (departmentId.HasValue)
        {
            spec.ByDepartment(departmentId.Value);
        }

        // アーキテクチャガイド推奨の拡張メソッドを使用
        return await _repository.FindWithPaginationAsync(spec, pageOption);
    }
}
```

## UseCaseの実装（MediatRベース）

### Command（更新・追加・削除）の実装

```csharp
using MediatR;
using Shared.Messaging;

// Command Request（更新・追加・削除）
public class CreateEmployeeCommand : IRequest<Result<int>>, ISendRequest<int>
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int DepartmentId { get; set; }
}

// Command Handler
public class CreateEmployeeHandler : IRequestHandler<CreateEmployeeCommand, Result<int>>
{
    private readonly IEmployeeRepository _repository;
    private readonly IMessagePublisher<EmployeeCreatedEvent> _eventPublisher;

    public CreateEmployeeHandler(
        IEmployeeRepository repository,
        IMessagePublisher<EmployeeCreatedEvent> eventPublisher)
    {
        _repository = repository;
        _eventPublisher = eventPublisher;
    }

    public async Task<Result<int>> Handle(CreateEmployeeCommand request, CancellationToken cancellationToken)
    {
        // ドメイン制約の検証
        var employee = new Employee
        {
            Name = request.Name,
            Email = request.Email,
            DepartmentId = request.DepartmentId,
            CreatedAt = DateTime.UtcNow
        };

        var constraint = EmployeeValidator.Validate(employee);
        var validationResult = constraint.Validate();

        if (!validationResult.IsValid)
        {
            return Result.Error(new DomainViolationException(validationResult.Violations));
        }

        // 保存処理
        var addResult = await _repository.AddAsync(employee);
        if (addResult.IsFailure)
        {
            return addResult.MapError<int>();
        }

        // イベントの発行
        var @event = new EmployeeCreatedEvent
        {
            EmployeeId = employee.Id,
            EmployeeName = employee.Name,
            CreatedAt = employee.CreatedAt
        };

        await _eventPublisher.PublishAsync(@event);

        return Result.Ok(employee.Id);
    }
}
```

### Query（参照）の実装

```csharp
using MediatR;

// Query Request（参照）
public class FindEmployeeQuery : IRequest<Result<List<Employee>>>
{
    public int? DepartmentId { get; set; }
    public string? Name { get; set; }
    public PaginationOption PaginationOption { get; set; } = new();
}

// Query Handler
public class FindEmployeeHandler : IRequestHandler<FindEmployeeQuery, Result<List<Employee>>>
{
    private readonly IEmployeeRepository _repository;

    public FindEmployeeHandler(IEmployeeRepository repository)
    {
        _repository = repository;
    }

    public async Task<Result<List<Employee>>> Handle(FindEmployeeQuery request, CancellationToken cancellationToken)
    {
        var spec = new FindEmployeeSpecification()
            .ByName(request.Name);

        if (request.DepartmentId.HasValue)
        {
            spec.ByDepartment(request.DepartmentId.Value);
        }

        return await _repository.FindAsync(spec);
    }
}
```

### Controllerでの使用

```csharp
using MediatR;
using Microsoft.AspNetCore.Mvc;

[ApiController]
[Route("api/[controller]")]
public class EmployeeController : ControllerBase
{
    private readonly IMediator _mediator;

    public EmployeeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> Find([FromQuery] FindEmployeeQuery query)
    {
        var result = await _mediator.Send(query);
        return result.Map(r => Ok(r)).ToApiResult();
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateEmployeeCommand command)
    {
        var result = await _mediator.Send(command);
        return result.Map(r => Ok(new { Id = r })).ToApiResult();
    }
}
```

## メッセージングの利用

### イベントの定義

```csharp
using Shared.Messaging;

[EventName("EmployeeCreated")]
public class EmployeeCreatedEvent : IPublishEvent
{
    public int EmployeeId { get; set; }
    public string EmployeeName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

// リクエストの定義
public class CreateEmployeeRequest : ISendRequest<int>
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int DepartmentId { get; set; }
}
```

### メッセージの送信

```csharp
using Shared.Messaging;

public class EmployeeService
{
    private readonly IMessagePublisher<EmployeeCreatedEvent> _eventPublisher;
    private readonly IMessageSender<CreateEmployeeRequest, int> _messageSender;

    public async Task<Result> CreateEmployeeAsync(Employee employee)
    {
        // イベントの発行
        var @event = new EmployeeCreatedEvent
        {
            EmployeeId = employee.Id,
            EmployeeName = employee.Name,
            CreatedAt = employee.CreatedAt
        };

        var publishResult = await _eventPublisher.PublishAsync(@event);
        if (publishResult.IsFailure)
        {
            return publishResult;
        }

        return Result.Ok();
    }

    public async Task<Result<int>> SendCreateRequestAsync(CreateEmployeeRequest request)
    {
        return await _messageSender.SendAsync(request);
    }
}
```

## バリデーションの利用

### ドメイン制約の定義

```csharp
using Shared.Domain;
using Shared.Domain.Constraints;

public class EmployeeValidator
{
    public static DomainConstraint<Employee> Validate(Employee employee)
    {
        var constraint = new DomainConstraint<Employee>(employee);

        constraint
            .Required(e => e.Name)
            .MaxLength(e => e.Name, 100)
            .Required(e => e.Email)
            .Pattern(e => e.Email, @"^[^@]+@[^@]+\.[^@]+$", "有効なメールアドレス形式ではありません")
            .GreaterThan(e => e.CreatedAt, DateTime.MinValue);

        return constraint;
    }
}
```

### バリデーションの実行

```csharp
public class EmployeeService
{
    public async Task<Result> CreateEmployeeAsync(Employee employee)
    {
        // ドメイン制約の検証
        var constraint = EmployeeValidator.Validate(employee);
        var validationResult = constraint.Validate();

        if (!validationResult.IsValid)
        {
            return Result.Error(new DomainViolationException(validationResult.Violations));
        }

        // 保存処理
        return await _repository.AddAsync(employee);
    }
}
```

## ユーザー情報の取得

### 現在のユーザー情報の取得

```csharp
using Shared.Services;

public class EmployeeService
{
    private readonly ICurrentUserService _currentUserService;

    public async Task<Result<Employee>> GetCurrentEmployeeAsync()
    {
        var currentUser = await _currentUserService.GetAsync();
        
        if (currentUser.Unknown)
        {
            return Result.Error(new UnauthorizedException());
        }

        return await _repository.GetAsync(currentUser.UserId);
    }
}
```

### ClaimsPrincipalからのユーザーID取得

```csharp
using Shared.Services;
using System.Security.Claims;

public class EmployeeController : ControllerBase
{
    [HttpGet("me")]
    public async Task<IActionResult> GetCurrentEmployee()
    {
        var userId = User.GetApplicationUserId();
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        // ユーザーIDを使用した処理
        return Ok(new { UserId = userId });
    }
}
```

## オブジェクトストレージの利用

### Azure Blob Storageの利用

```csharp
using Shared.AzureBlob;
using Shared.ObjectStorage;

public class FileService
{
    private readonly IObjectStorageClient _storageClient;

    public FileService(IObjectStorageClient storageClient)
    {
        _storageClient = storageClient;
    }

    public async Task<Result> UploadFileAsync(string fileName, Stream fileStream)
    {
        return await _storageClient.PostAsync(fileName, fileStream, "application/octet-stream");
    }

    public async Task<Result<Stream>> DownloadFileAsync(string fileName)
    {
        return await _storageClient.GetAsync(fileName);
    }

    public async Task<Result<bool>> FileExistsAsync(string fileName)
    {
        return await _storageClient.ExistsAsync(fileName);
    }

    public async Task<Result> DeleteFileAsync(string fileName)
    {
        return await _storageClient.DeleteAsync(fileName);
    }
}
```

## 拡張メソッドの利用

### LINQ拡張メソッド

```csharp
using Shared.Linq;
using Shared.Application;

public class EmployeeService
{
    public async Task<PaginatedResult<Employee>> GetEmployeesWithPaginationAsync(
        int pageIndex, 
        int pageSize, 
        string? searchName = null)
    {
        var query = _context.Employees.AsQueryable();

        // 条件付きフィルタリング
        query = query.WhereIf(!string.IsNullOrWhiteSpace(searchName), 
            e => e.Name.Contains(searchName));

        // ページング結果の取得
        return await query.ToPaginatedAsync(pageIndex, pageSize);
    }

    public async Task<PaginatedResult<Employee>> GetEmployeesWithSortingAsync(
        int pageIndex, 
        int pageSize, 
        IList<Sort> sortDefinitions)
    {
        var employees = await _repository.AllAsync();
        
        // ソート条件の適用
        var sortedEmployees = employees.SortBy(sortDefinitions);
        
        // ページング結果の取得
        return await sortedEmployees.ToPaginatedAsync(pageIndex, pageSize);
    }
}
```

### リポジトリ拡張メソッド

```csharp
using Shared.Application;

public class EmployeeService
{
    public async Task<PaginatedResult<Employee>> SearchEmployeesAsync(
        ISpecification<Employee> spec, 
        PaginationOption pageOption)
    {
        // ページング付き検索の実行
        return await _repository.FindWithPaginationAsync(spec, pageOption);
    }
}
```

### 環境判定の拡張メソッド

```csharp
using Shared.Application;

public class ConfigurationService
{
    private readonly IApplicationEnvironment _environment;

    public ConfigurationService(IApplicationEnvironment environment)
    {
        _environment = environment;
    }

    public string GetApiEndpoint()
    {
        if (_environment.IsDevelopment())
        {
            return "https://dev-api.example.com";
        }
        else if (_environment.IsStaging())
        {
            return "https://staging-api.example.com";
        }
        else if (_environment.IsProduction())
        {
            return "https://api.example.com";
        }

        return "https://localhost:5001";
    }
}
```

### 時間処理の拡張メソッド

```csharp
using Shared.Time;
using Microsoft.Extensions.Time.Testing;

public class TimeService
{
    private readonly TimeProvider _timeProvider;

    public TimeService(TimeProvider timeProvider)
    {
        _timeProvider = timeProvider;
    }

    public DateTimeOffset GetCurrentJstTime()
    {
        return _timeProvider.GetJstNow();
    }
}

// テストでの使用例
[Test]
public void GetCurrentJstTime_ShouldReturnJstTime()
{
    var fakeTime = new FakeTimeProvider(DateTimeOffset.UtcNow);
    var service = new TimeService(fakeTime);
    
    var result = service.GetCurrentJstTime();
    
    Assert.That(result.Offset, Is.EqualTo(TimeSpan.FromHours(9)));
}
```

## 例外処理の利用

### 標準例外の使用

```csharp
using Shared.Results.Errors;

public class EmployeeService
{
    public async Task<Result<Employee>> GetEmployeeAsync(int id)
    {
        var employee = await _repository.GetAsync(id);
        
        if (employee.IsFailure)
        {
            // データが見つからない場合
            if (employee.Error is DataNotFoundException)
            {
                return Result.Error(new DataNotFoundException($"従業員ID {id} が見つかりません"));
            }
            
            return employee;
        }

        return employee;
    }

    public async Task<Result> UpdateEmployeeAsync(Employee employee)
    {
        try
        {
            // 更新処理
            return await _repository.UpdateAsync(employee);
        }
        catch (Exception ex)
        {
            // 変更競合の処理
            if (ex.Message.Contains("concurrency"))
            {
                return Result.Error(new ChangeConflictException("データが他のユーザーによって変更されています"));
            }
            
            throw;
        }
    }
}
```

### カスタム例外の定義

```csharp
using Shared.Application;

public class EmployeeNotFoundException : DataNotFoundException
{
    public EmployeeNotFoundException(int employeeId) 
        : base($"従業員ID {employeeId} が見つかりません")
    {
        EmployeeId = employeeId;
    }

    public int EmployeeId { get; }
}

public class InvalidEmployeeDataException : ValidationException
{
    public InvalidEmployeeDataException(string message) : base(message)
    {
    }
}
```

## 初期化処理の利用

### サービスの初期化

```csharp
using Shared.ServiceModule;

public class EmployeeDataInitializer : IInitializer
{
    private readonly IEmployeeRepository _repository;
    private readonly ILogger<EmployeeDataInitializer> _logger;

    public EmployeeDataInitializer(
        IEmployeeRepository repository,
        ILogger<EmployeeDataInitializer> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task InitializeAsync()
    {
        _logger.LogInformation("従業員データの初期化を開始します");

        try
        {
            // 初期データの投入
            var initialEmployees = new List<Employee>
            {
                new Employee { Name = "管理者", Email = "<EMAIL>" },
                new Employee { Name = "一般ユーザー", Email = "<EMAIL>" }
            };

            foreach (var employee in initialEmployees)
            {
                await _repository.AddAsync(employee);
            }

            _logger.LogInformation("従業員データの初期化が完了しました");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "従業員データの初期化中にエラーが発生しました");
            throw;
        }
    }
}
```

## 設定とDI登録

### サービスの登録

```csharp
using Microsoft.Extensions.DependencyInjection;
using Shared.EntityFrameworkCore;
using Shared.AzureBlob;

public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // Entity Framework Coreの設定
        services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection"));
            options.UseRepositoryFunctions(); // リポジトリ関数の有効化
        });

        // リポジトリの登録
        services.AddScoped<IEmployeeRepository, EmployeeRepository>();
        services.AddScoped<IDepartmentRepository, DepartmentRepository>();

        // Azure Blob Storageの設定
        services.Configure<BlobSettings>(Configuration.GetSection("BlobStorage"));
        services.AddScoped<IObjectStorageClient, BlobStorageClient>();

        // 初期化サービスの登録
        services.AddScoped<IInitializer, EmployeeDataInitializer>();

        // MediatRの設定
        services.AddMediatR(typeof(Startup).Assembly);
    }
}
```

### 設定ファイルの例

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=EmployeeDB;Trusted_Connection=true;"
  },
  "BlobStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=mykey;EndpointSuffix=core.windows.net"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  }
}
```

## まとめ

このドキュメントでは、app-architecture-template-backend-sharedで提供される主要な共通処理の利用方法を示しました。これらの機能を活用することで、以下のような利点が得られます：

1. **一貫性**: 標準化されたパターンによる一貫した実装
2. **再利用性**: 共通機能の再利用による開発効率の向上
3. **保守性**: 統一されたアーキテクチャによる保守性の向上
4. **テスト容易性**: 依存関係の分離によるテストの容易化
5. **拡張性**: 柔軟な設計による将来の拡張への対応

各機能の詳細な使用方法については、各ライブラリのドキュメントを参照してください。
