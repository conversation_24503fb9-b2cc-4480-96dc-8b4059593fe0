import { defineVitestConfig } from '@nuxt/test-utils/config'
import { configDefaults, coverageConfigDefaults } from 'vitest/config'

export default defineVitestConfig({
  test: {
    environment: 'nuxt',
    pool: 'forks',
    globals: true,
    reporters: ['junit', 'default'],
    outputFile: {
      junit: '../testresult/test/test-results.xml',
    },
    exclude: [
      ...configDefaults.exclude,
      '**/node_modules/**',
      '**/.nuxt/**',
      '../virtual:nuxt:**',
      // templateパッケージを除外
      '**/packages/template/**',
    ],
    coverage: {
      enabled: true,
      provider: 'istanbul',
      reporter: ['cobertura', 'html'],
      reportsDirectory: '../testresult/coverages',
      exclude: [
        ...coverageConfigDefaults.exclude,
        'node_modules/**',
        '**/.nuxt/**',
        '../virtual:nuxt:**',
        '**/shared/**',
      ],
    },
  },
})
