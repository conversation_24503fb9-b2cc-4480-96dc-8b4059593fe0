﻿import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { OriginOfTransitions } from '@/packages/case-work-flow-optimization/src/constants/common/query'

/**
 * 融資リース案件の編集・画面遷移を管理するコンポーザブル
 * 編集画面への遷移や別タブ表示などの処理を担当
 */
export const useLeaseNavigation = () => {
  // ルーター
  const router = useRouter()
  // Ctrlキーの状態を追跡
  const isControlPressed = ref(false)

  // キーイベントリスナー
  onMounted(() => {
    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('keyup', handleKeyUp)
  })

  onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown)
    window.removeEventListener('keyup', handleKeyUp)
  })

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Control') {
      isControlPressed.value = true
    }
  }

  const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key === 'Control') {
      isControlPressed.value = false
    }
  }
  /**
   * 案件の編集画面に遷移する
   * @param item 選択された案件データ
   * @param options オプション設定
   * @returns 遷移先URL情報
   */
  function getEditUrl(item: any) {
    return {
      path: '/customer-case/edit',
      query: {
        id: item.id,
        customerIdentificationId: item.customerIdentificationId,
        from: OriginOfTransitions.NEW_LEASE_CASE,
      },
    }
  }

  /**
   * 編集処理 - 案件一覧のアイテムをクリックした時の処理
   * @param e イベントオブジェクト
   * @param onBeforeNavigate 遷移前に実行するコールバック関数
   */
  function navigateToEdit(e: any, onBeforeNavigate?: () => void) {
    const item = e.row?.item || e.target
    const url = getEditUrl(item)

    // Ctrlキーの状態を確認
    if (isControlPressed.value) {
      // Ctrlキーを押しながらクリックした場合は別タブで開く
      const resolvedRoute = router.resolve(url)
      window.open(resolvedRoute.href, item.id)
    } else {
      // 遷移前に実行する処理があれば実行
      if (onBeforeNavigate) {
        onBeforeNavigate()
      }
      // 遷移処理
      navigateTo(url)
    }
  }
  return {
    navigateToEdit,
    getEditUrl,
    isControlPressed,
  }
}
