<template>
  <!-- バインドするデータが null / undefined の可能性も考慮しておくこと -->
  <app-search-page-tmpl
    v-model:pageIndex="pagination.pageIndex"
    v-model:sort="pagination.sort"
    v-model:listSelectedItems="listSelectedItems"
    :page-size="pagination.pageSize"
    :list-item-key="'id'"
    :list-headers="headers"
    :list-items="data?.items"
    :list-items-total="data?.total"
    :loading="loading"
    :on-search="search"
    :on-add="add"
    :on-edit="edit"
    :on-remove="remove"
  >
    <template #criteria>
      <!-- TODO: ここに検索条件項目を追加します。 -->
      <!-- //#if(!isInTemplateExpanding || includeSample ) -->
      <v-row>
        <v-col>
          <app-text-field
            v-model="searchCondition.name"
            label="名前"
            data-testid="name"
          />
        </v-col>
        <v-col>
          <app-text-field
            v-model="searchCondition.address"
            label="住所"
            data-testid="address"
          />
        </v-col>
      </v-row>
      <!-- //#else -->
      <!-- <v-row>
          <v-col>
            <app-text-field
              v-model="searchCondition.name"
              label="名前"
              data-testid="name"
            />
          </v-col>
          <v-col>
            <app-text-field
              v-model="searchCondition.address"
              label="住所"
              data-testid="address"
            />
          </v-col>
        </v-row> -->
      <!-- //#endif -->
    </template>
  </app-search-page-tmpl>
  <app-confirm-dialog ref="confirmDialog" type="remove" />
</template>
<script setup lang="ts">
import { reactive, computed, ref, onMounted } from 'vue'
import { format } from 'date-fns'
import type { SearchOptions } from '@hox/base/src/components/shared/templates/AppSearchPageTmpl.vue'
import { useCriteriaUrlState } from '@hox/base/src/composables/shared/useCriteriaUrlState'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import type { AppConfirmDialogType } from '@hox/base/src/components/shared/molecules/AppConfirmDialog.vue'
import type { ConvertStringDeep } from '@hox/base/src/utils/shared/typeUtils'
// TODO: 利用するAPIクライアントと型をインポートします。
import type { Pagination } from '@hox/base/src/apiclient/shared/types'
import type { SortItem } from '@hox/base/src/components/shared/types'
import type {
  FindEmployeeCriteria as FindApiCriteria,
  FindEmployeeResultItem as FindApiResultItem,
} from './../apiclient/employee_template'
import {
  useFindEmployee as useFindApi,
  useDeleteAllEmployee as useDeleteAllApi,
} from './../apiclient/employee_template'

// =====================================================================================================================
// 画面の定義
// =====================================================================================================================

// タイトルを指定します。(windowのタイトルに設定されます。)
useHead({
  // TODO: 画面のタイトルを設定してください。
  title: '検索',
})
// タイトルを指定します。(パンくずリストに設定されます。)
definePageMeta({
  // TODO: 画面のタイトルを設定してください。
  title: '検索',
})

// 画面で入力する検索条件型の定義(APIで要求されているPagination部分を除いたもの)
// TODO: 検索条件の型に変更してください。
type Criteria = Omit<FindApiCriteria, keyof Pagination>

// 検索条件を設定する画面項目とバインドするデータを定義します。
const searchCondition = reactive<Criteria>({
  // TODO: ここに検索条件にバインドするプロパティを追加します。
  name: '',
  address: '',
})

// 検索条件を定義します。
// ページングやソートの変更時に、最初に検索されたときの条件を利用するために
// 画面項目とバインドするデータとは独立して定義します。
const criteria = ref<Criteria | null>(null)

// ページング
const pagination = reactive({
  pageIndex: 1,
  pageSize: 10,
  sort: [] as SortItem[],
})

// 検索結果
const headers = [
  // TODO: ここに検索結果の項目を設定します。
  // https://vuetifyjs.com/ja/api/v-data-table/#headers
  { title: 'ID', key: 'id' },
  { title: '名前(漢字)', key: 'nameKanji', sortable: true },
  { title: '名前(カナ)', key: 'nameKana', sortable: true },
  { title: '住所', key: 'address', sortable: true },
  {
    title: '誕生日',
    key: 'birthDate',
    value: (val: any) => format(val.birthDate, 'yyyy/MM/dd'),
  },
  { title: '部署名', key: 'branchName' },
  { title: '所持資格数', key: 'qualificationCount', sortable: true },
]

// 処理完了などをトースト表示するために利用します。
const { primary: primaryToast, error: errorToast } = useAppToasts()
// 複数のフラグでローディング状態を管理する場合は useFlagCondition を利用します。
const { hasTrue: loading, addFlag } = useFlagCondition()

// 指定されているクエリの中から検索条件以外のものを抜き出して保持しておきます。
// ※検索条件の履歴に積むときに消えないように追加するためです。
// e.g. 画面表示時にクエリパラメーターで val=a が指定されている場合、検索条件を変更してクエリパラメーターに保持されても val=a は消えないようになります。
const additionalQueryParameter: { [key: string]: string } = (() => {
  const route = useRoute()
  const query = route.query as { [key: string]: string }
  const excludeKeys = Object.keys(searchCondition).concat([
    'q',
    'pageIndex',
    'pageSize',
    'sort',
  ])
  return Object.fromEntries(
    Object.entries(query).filter(([key]) => !excludeKeys.includes(key)),
  )
})()

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

// 検索クエリを定義します。
const query = computed(() => {
  const sort = pagination.sort.reduce((prev, curr) => {
    prev.push(`${curr.key}${curr.order === 'desc' ? ' desc' : ''}`)
    return prev
  }, [] as string[])
  const paginationQuery = {
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
    sort,
  }
  return { ...(criteria.value ?? {}), ...paginationQuery }
})

// api の定義 パラメーターには reactive(ref / reactive) な値を使います。
// 検索APIクライアントを定義します。
const {
  data,
  executeWithResult: findData,
  inProgress: findInProgress,
} = useFindApi(query)
addFlag(findInProgress)

// -- 一括削除APIを実行するための定義
// 削除するデータを定義します。
const listSelectedItems = ref<FindApiResultItem[]>([])
const deleteAllBody = computed(() => {
  return (listSelectedItems.value ?? []).map((item: any) => {
    return { id: item.id, version: item.version }
  })
})
// 一括削除APIクライアントを定義します。
const { executeWithResult: removeData, inProgress: removeInProgress } =
  useDeleteAllApi(deleteAllBody)
addFlag(removeInProgress)

// 検索履歴を積む設定を取得します。
const {
  state: criteriaHistory,
  push: pushCriteria,
  onChange,
  hasState,
} = useCriteriaUrlState<FindApiCriteria>()

// 検索履歴が変わった(戻るが押された)際の処理を記述します。
onChange((state) => {
  if (!state) {
    // 初回アクセスに戻った場合に undefined になる
    data.value = {
      // データを初期化します。
      items: [],
      total: 0,
    }
    return
  }
  // 検索条件を復元し検索を実行する
  restoreCriteria(state)
  search({ noStoreCriteria: true })
})

// =====================================================================================================================
// 画面初期化処理
// =====================================================================================================================

onMounted(() => {
  // 画面直接アクセスの場合などに検索履歴がある場合は復元する
  if (hasState.value && criteriaHistory.value) {
    restoreCriteria(criteriaHistory.value)
    search({ noStoreCriteria: true })
  } else {
    // 初期化
    data.value = { items: [], total: 0 }
  }
})

// =====================================================================================================================
// 画面で使用するメソッドの定義
// =====================================================================================================================

// 検索の処理を行います。
async function search(options: SearchOptions = {}) {
  // ソートの変更かページングの変更の場合で、かつ検索条件が空(まだ一度も検索されていない)の場合は検索しない
  if (
    (options.isSortChangeRequest || options.isPaginationRequest) &&
    !criteria.value
  ) {
    return true
  }
  // ソート/ページング以外の場合は検索条件を更新する
  if (!options.isSortChangeRequest && !options.isPaginationRequest) {
    // reactive / ref の状態を要調査
    criteria.value = Object.assign({}, searchCondition)
  }
  await findData()
  if (!options.noStoreCriteria) {
    // 検索条件を履歴に積みます。
    // 検索条件以外のパラメーターも消えないように追加します。
    pushCriteria({ ...query.value, ...additionalQueryParameter })
  }
  return true
}

// 追加の処理を行います。通常は edit に遷移します。
function add() {
  // 新規追加で edit に遷移する
  return navigateTo({ path: '/employee/edit' })
}

// 検索結果のデータを編集します。通常は edit に遷移します。
function edit({ item }: { item: any }) {
  // 更新で edit に遷移する
  return navigateTo({ path: '/employee/edit', query: { id: item.id } })
}

// 一括削除時の確認ダイアログを定義します。
const confirmDialog = ref<AppConfirmDialogType>()

async function remove() {
  if (listSelectedItems.value.length === 0) {
    return false
  }
  const confirmResult = await confirmDialog.value?.open()
  if (!confirmResult) return false

  const result = await removeData().then(
    () => {
      primaryToast('指定されたデータを削除しました。')
      return true
    },
    (e: any) => {
      if (!e.hasProblemDetails) {
        throw e
      }
      if (e.data.type === '/conflict') {
        errorToast(
          'すでに別のユーザーがデータを登録・変更しているため削除できませんでした。',
        )
      } else {
        errorToast('指定されたデータの削除に失敗しました。')
      }
      return false
    },
  )
  // APIが失敗の場合は false を返します。
  if (!result) return false
  return true
}

// URLのクエリに指定されていた検索条件を復元します。
// 多くの場合はそのまま利用できますが、検索条件の型が変わっている場合は適宜修正してください。
function restoreCriteria(
  // 検索条件の型を設定します。
  criteriaHistory: ConvertStringDeep<FindApiCriteria>,
) {
  // TODO: 検索条件を復元します。
  searchCondition.name = criteriaHistory.name ?? ''
  searchCondition.address = criteriaHistory.address ?? ''
  // ソート条件を保持されているカタチからオブジェクトのカタチに変換します。
  function convertToSortFormat(querySort: string[] | string | undefined) {
    const sort = Array.isArray(querySort)
      ? querySort
      : !querySort
          ? []
          : [querySort]
    return (sort ?? []).map((s) => {
      const [key, order] = s.split(' ')
      return {
        key,
        order: order === 'desc' ? 'desc' : ('asc' as 'asc' | 'desc'),
      }
    })
  }
  pagination.pageIndex = parseFloat(criteriaHistory.pageIndex) || 1
  pagination.pageSize = parseFloat(criteriaHistory.pageSize) || 10
  pagination.sort = convertToSortFormat(criteriaHistory.sort)
}
</script>
