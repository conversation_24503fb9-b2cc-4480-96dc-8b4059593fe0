<template>
  <div>
    <quill-editor
      ref="quillEditor"
      v-model:content="inputtedValue"
      content-type="html"
      :options="quillEditorOption"
      :class="
        isForMention
          ? 'override-quill-editor-mentions'
          : 'override-quill-editor'
      "
      :enable="!disabled"
      @blur="blur"
    />
    <div>{{ hintText }}</div>
    <VRow v-if="errorMessage && errorMessage.length">
      <VCol
        class="mt-1"
        style="color: #ff5722; caret-color: #ff5722; font-size: 12px"
      >
        {{ errorMessage[0] }}
      </VCol>
    </VRow>
  </div>
</template>

<script setup lang="ts">
import 'quill-mention'
import { QuillEditor } from '@vueup/vue-quill'
import type { Team } from '@ibp/common-case/src/apiclient/customerProposal/team'

const toolbar = [
  ['bold', 'italic', 'underline', 'strike'],
  ['blockquote', 'code-block'],
  [{ header: 1 }, { header: 2 }],
  // インデントが深くなると表示が崩れるため、無効化
  // [{ list: 'ordered' }, { list: 'bullet' }],
  [{ script: 'sub' }, { script: 'super' }],
  [{ indent: '-1' }, { indent: '+1' }],
  [{ direction: 'rtl' }],
  [{ size: ['small', false, 'large', 'huge'] }],
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  [{ color: [] }, { background: [] }],
  [{ align: [] }],
  ['clean'],
  ['link', 'image', 'video'],
]

const formats = [
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'code',
  'background',
  'color',
  'link',
  'size',
  'script',
  'header',
  'indent',
  // インデントが深くなると表示が崩れるため、無効化
  // "list"
  'align',
  'direction',
  'code-block',
  'image',
  'video',
  'mention',
]

const commercialDistributionToolbar = [
  ['bold', 'underline', { color: [] }, { indent: '-1' }, { indent: '+1' }],
]

export type MentionTarget = {
  id: string
  value: string
  romaji?: string
  teamName?: string
  mentionType: 'staff' | 'team'
}

export type StaffType = {
  id: string
  value: string
  romaji: string
}

const props = defineProps({
  modelValue: {
    type: String as PropType<string | undefined | null>,
    default: null,
  },
  placeholder: {
    type: String,
    default: null,
  },
  /**  ツールバーを表示させるか */
  showToolbar: {
    type: Boolean,
    default: false,
  },
  /** メンションできるか */
  canMention: {
    type: Boolean,
    default: false,
  },
  hintText: {
    type: String,
    default: null,
  },
  staffs: {
    type: Array as PropType<StaffType[]>,
    default: () => [],
  },
  teams: {
    type: Array as PropType<Team[]>,
    default: () => [],
  },
  errorMessage: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  isCommercialDistribution: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['update:modelValue', 'blur'])

const inputtedValue = computed({
  get: () => props.modelValue,
  set: (newValue) => {
    emits('update:modelValue', newValue)
  },
})

const isForMention = computed(() => {
  return props.canMention && !props.showToolbar
})

const quillEditorOption = computed(() => {
  const opt = {
    theme: 'snow',
    formats,
    modules: {
      toolbar: props.isCommercialDistribution
        ? commercialDistributionToolbar
        : props.showToolbar && toolbar,
      // メンション設定
      mention: props.canMention && {
        allowedChars: /.*/,
        maxChars: 100,
        offsetTop: 3,
        renderItem: (item: MentionTarget) => {
          return item.value + (item.romaji ? `( ${item.romaji} )` : '')
        },
        mentionDenotationChars: ['@', '＠'],
        source: (searchTerm: string, renderList: any) => {
          const targets: MentionTarget[] = props.staffs.map((x) => ({
            id: x.id,
            value: x.value,
            romaji: x.romaji,
            mentionType: 'staff',
          }))

          props.teams.forEach((x) => {
            targets.push({ id: x.id, value: x.teamName, mentionType: 'team' })
          })

          if (searchTerm.length === 0) {
            renderList(targets, searchTerm)
          } else {
            const matches = []
            for (let i = 0; i < targets.length; i++) {
              if (
                ~targets[i].value
                  .toLowerCase()
                  .indexOf(searchTerm.toLowerCase())
              ) {
                matches.push(targets[i])
                continue
              }

              // ローマ字検索
              if (targets[i].romaji) {
                if (~targets[i].romaji!.indexOf(searchTerm.toLowerCase())) {
                  matches.push(targets[i])
                }
              }
            }
            renderList(matches, searchTerm)
          }
        },
        defaultMenuOrientation: 'top',
        dataAttributes: [
          'id',
          'value',
          'denotationChar',
          'link',
          'target',
          'disabled',
          'mentionType',
        ],
      },
    },
    placeholder: props.placeholder
      ? props.placeholder
      : props.canMention
        ? 'メンション先を指定(@〇〇)'
        : null,
  }
  return opt
})

const blur = () => {
  emits('blur')
}
</script>

<style lang="scss">
.override-quill-editor-mentions {
  position: relative;
  .quill-editor {
    width: 100%;
    height: 45px;
    margin-right: 0;
    border: 1px solid #aaa;
    border-radius: 5px;
  }
  .ql-editor {
    padding: 8px;
    padding-right: 0;
  }
  p {
    margin-bottom: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
  }
  .selected {
    background: #dcedc8;
  }
  .ql-mention-list-container {
    overflow: auto;
    max-height: 100px;
    background: white;
    border: 1px solid #aaa;
    border-radius: 5px;
  }
  .ql-mention-list {
    margin-top: 4px;
    margin-right: 4px;
    padding-top: 2px;
    padding-bottom: 2px;
    padding-right: 4px;
    padding-left: 24px;
  }
}

.override-quill-editor {
  position: relative;
  .quill-editor {
    width: 100%;
    min-height: 80px;
    margin-right: 0;
    border: 1px solid #aaa;
    border-radius: 5px;
  }
  .ql-editor {
    padding: 12px;
    padding-top: 10px;
    padding-right: 0;
    white-space: pre;
  }
  p {
    margin-bottom: 0;
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    line-height: 1.75rem;
  }
  .selected {
    background: #dcedc8;
  }
  .ql-mention-list-container {
    overflow: auto;
    max-height: 100px;
    background: white;
    border: 1px solid #aaa;
    border-radius: 5px;
  }
  .ql-mention-list {
    margin-top: 4px;
    margin-right: 4px;
    padding-top: 2px;
    padding-bottom: 2px;
    padding-right: 4px;
    padding-left: 24px;
  }
}
</style>
