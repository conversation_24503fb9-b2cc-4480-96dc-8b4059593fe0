import { z } from 'zod'

export const issueProjectLinkSchemaForCreate = z.object({
  title: z.string().trim().max(100),
  url: z.string().trim().max(1000),
  updater: z.string(),
  updaterId: z.string(),
  issueProjectId: z.string()
})

export const issueProjectLinkSchemaForUpdate = issueProjectLinkSchemaForCreate.extend({
  id: z.string(),
  version: z.string()
})

export const issueProjectLinkSchemaForGet = z.object({
  id: z.string(),
  title: z.string(),
  url: z.string(),
  updater: z.string(),
  updaterId: z.string(),
  updatedDateTime: z.coerce.date(),
  issueProjectId: z.string(),
  version: z.string()
})

export type IssueProjectLinkForCreate = z.infer<typeof issueProjectLinkSchemaForCreate>
export type IssueProjectLinkForUpdate = z.infer<typeof issueProjectLinkSchemaForUpdate>
export type IssueProjectLinkForGet = z.infer<typeof issueProjectLinkSchemaForGet>