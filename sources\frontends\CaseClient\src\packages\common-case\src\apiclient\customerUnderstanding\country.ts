import { useNuxtApp } from 'nuxt/app'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'

export type CountryMaster = {
  countryCode: string
  countryName: string
}

// =====================================================================================================================
// APIクライアントの定義 (取得)
// =====================================================================================================================

/**
 * データを取得する
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetAllCountry() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Array<CountryMaster>>(
    useFetch(
      () => $endpoints.default.get('/ibp-customer-understanding/v1.0/country/get-all'),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
