using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Nut.Results;
using Nut.Results.FluentAssertions;
using Shared.Domain;
using Shared.ObjectStorage;
using Shared.Results.Errors;
using Shared.Spec;
using Xunit;
// TODO: プロジェクト固有のusingを追加してください
// using SampleService.Domain;
// using SampleService.Infrastructure.Persistence;
// using SampleService.Infrastructure.Storage;
// using SampleService.Services.Domain;
// using SampleService.Tests.TestUtil;
// using SampleService.UseCases.Entity.DeleteEntity;
// using Entities = SampleService.Domain.Entities;

// TODO: 長いジェネリック型の短縮形エイリアス（可読性向上のため）
using EntityRepository = Shared.Domain.IRepository<SampleService.Domain.Entities.Entity, string>;

// TODO: namespace をテスト対象のプロジェクトに合わせて変更してください。
namespace SampleService.Tests.UseCases.Entity.DeleteEntity;

/// <summary>
/// DeleteEntityHandlerの単体テストクラス
/// </summary>
/// <remarks>
/// 統合テスト（SQLiteインメモリDB使用）を中心に、
/// Delete処理のテストを包括的に実装し、コードカバレッジ100%を目指します。
/// 物理削除・楽観的ロック・ファイルクリーンアップ・エラーハンドリングを網羅的にテストします。
/// DeleteUseCaseは他のUseCaseと比べてシンプルですが、削除の安全性と関連リソースの確実なクリーンアップが重要です。
/// </remarks>
public class DeleteEntityHandlerTest : IAsyncLifetime
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TestCurrentUserService _currentUserService;
    private readonly TestCurrentDateTimeService _currentDateTimeService;
    
    // TODO: 実際のファイル削除サービス依存関係に合わせて以下のモックを調整してください
    private readonly Mock<IQuillImageService> _quillImageServiceMock;
    private readonly Mock<IObjectStorageClientProvider> _objectStorageClientProviderMock;
    private readonly Mock<IObjectStorageClient> _objectStorageClientMock;
    private readonly Mock<IFileProcessingService> _fileProcessingServiceMock;
    private readonly Mock<IImageCleanupService> _imageCleanupServiceMock;
    
    // TODO: 実際のストレージ設定に合わせて変更してください
    private readonly string _containerName = "entity-storage";
    private readonly string _folderPrefix = "entity";

    public DeleteEntityHandlerTest()
    {
        _currentUserService = new TestCurrentUserService();
        _currentDateTimeService = new TestCurrentDateTimeService();

        _dbContext = TestDbContextBuilder.Build(_currentUserService, _currentDateTimeService);

        // TODO: 実際のファイル削除サービスに合わせてモックを初期化してください
        _quillImageServiceMock = new Mock<IQuillImageService>();
        _quillImageServiceMock.Setup(x => x.DeleteAllContentsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Ok("cleanup-completed"));

        // オブジェクトストレージ関連のモック初期化
        _objectStorageClientMock = new Mock<IObjectStorageClient>();
        _objectStorageClientMock.Setup(m => m.GetObjectInformationsAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok(CreateTestObjectInformations()));
        _objectStorageClientMock.Setup(m => m.DeleteAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok());

        _objectStorageClientProviderMock = new Mock<IObjectStorageClientProvider>();
        _objectStorageClientProviderMock.Setup(m => m.CreateAsync())
            .ReturnsAsync(_objectStorageClientMock.Object);

        // ファイル処理サービスのモック初期化
        _fileProcessingServiceMock = new Mock<IFileProcessingService>();
        _fileProcessingServiceMock.Setup(x => x.DeleteEntityFilesAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok());

        // 画像クリーンアップサービスのモック初期化
        _imageCleanupServiceMock = new Mock<IImageCleanupService>();
        _imageCleanupServiceMock.Setup(x => x.CleanupImagesAsync(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Ok());
    }

    public Task InitializeAsync()
        => TestData.CreateAsync(_dbContext);

    public Task DisposeAsync() => Task.CompletedTask;

    #region テスト対象とテストデータの準備

    /// <summary>
    /// テスト対象のハンドラーを作成します
    /// </summary>
    /// <returns>DeleteEntityHandler インスタンス</returns>
    private DeleteEntityHandler CreateHandler()
    {
        // TODO: プロジェクトのパターンに合わせてハンドラーの生成方法を調整してください
        var unitOfWork = new UnitOfWork(_dbContext);
        return new DeleteEntityHandler(
            unitOfWork,
            _objectStorageClientProviderMock.Object,
            _quillImageServiceMock.Object,
            _fileProcessingServiceMock.Object,
            _imageCleanupServiceMock.Object
        );
    }

    /// <summary>
    /// 削除対象の既存エンティティからコマンドを作成します
    /// </summary>
    /// <param name="entityId">削除対象のエンティティID</param>
    /// <returns>DeleteEntityCommand インスタンス</returns>
    private async Task<DeleteEntityCommand> CreateValidCommandAsync(string entityId)
    {
        var currentEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        if (currentEntity == null)
            throw new InvalidOperationException($"Entity with ID {entityId} not found for test setup");

        // TODO: 実際のDeleteEntityCommandの構造に合わせて調整してください
        return new DeleteEntityCommand(
            Id: entityId,
            UserId: currentEntity.CreatedBy, // 削除者ID（認可で使用される場合）
            Version: currentEntity.Version
        );
    }

    /// <summary>
    /// テスト用の既存エンティティIDを取得します
    /// </summary>
    /// <returns>テスト用エンティティID</returns>
    private static string GetTestEntityId()
    {
        // TODO: TestDataクラスで作成される実際のエンティティIDに合わせて変更してください
        return "entity-1";
    }

    /// <summary>
    /// テスト用のオブジェクト情報を作成します
    /// </summary>
    /// <returns>テスト用オブジェクト情報のリスト</returns>
    private IEnumerable<ObjectInformation> CreateTestObjectInformations()
    {
        return new List<ObjectInformation>
        {
            new ObjectInformation($"{_folderPrefix}/entity-1/test-file-1.txt", "text/plain", 1024),
            new ObjectInformation($"{_folderPrefix}/entity-1/test-image-1.jpg", "image/jpeg", 2048),
            new ObjectInformation($"{_folderPrefix}/entity-1/test-document-1.pdf", "application/pdf", 4096)
        };
    }

    #endregion

    #region 異常系テスト（null チェック）

    [Fact]
    public void Ctor_引数にnullを渡すと例外が発生する()
    {
        var unitOfWork = new UnitOfWork(_dbContext);

        // TODO: 実際のコンストラクタ引数に合わせて全パターンのnullチェックを実装してください
        Assert.Throws<ArgumentNullException>("unitOfWork", 
            () => new DeleteEntityHandler(
                null!, 
                _objectStorageClientProviderMock.Object, 
                _quillImageServiceMock.Object, 
                _fileProcessingServiceMock.Object, 
                _imageCleanupServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("objectStorageClientProvider", 
            () => new DeleteEntityHandler(
                unitOfWork, 
                null!, 
                _quillImageServiceMock.Object, 
                _fileProcessingServiceMock.Object, 
                _imageCleanupServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("quillImageService", 
            () => new DeleteEntityHandler(
                unitOfWork, 
                _objectStorageClientProviderMock.Object, 
                null!, 
                _fileProcessingServiceMock.Object, 
                _imageCleanupServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("fileProcessingService", 
            () => new DeleteEntityHandler(
                unitOfWork, 
                _objectStorageClientProviderMock.Object, 
                _quillImageServiceMock.Object, 
                null!, 
                _imageCleanupServiceMock.Object));
        
        Assert.Throws<ArgumentNullException>("imageCleanupService", 
            () => new DeleteEntityHandler(
                unitOfWork, 
                _objectStorageClientProviderMock.Object, 
                _quillImageServiceMock.Object, 
                _fileProcessingServiceMock.Object, 
                null!));
    }

    [Fact]
    public async Task Handle_Requestがnullの場合は例外が発生する()
    {
        var handler = CreateHandler();

        var nullCommand = (DeleteEntityCommand)null!;
        var act = () => handler.Handle(nullCommand, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    #endregion

    #region データ不存在・楽観的ロックテスト（Delete特有）

    [Fact]
    public async Task Handle_指定されたデータが存在しない場合はDataNotFoundErrorが返る()
    {
        // Arrange
        var command = new DeleteEntityCommand(
            Id: "NONEXISTENT_ID",
            UserId: "test-user",
            Version: "dummy-version"
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    [Fact]
    public async Task Handle_Versionが違う場合は楽観的ロックエラーが返る()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var currentEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        var command = new DeleteEntityCommand(
            Id: entityId,
            UserId: currentEntity.CreatedBy,
            Version: currentEntity.Version + "_INVALID" // 異なるVersion
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
        // TODO: 実際の楽観的ロックエラー型に合わせて調整してください
        // result.Should().BeError().And.BeOfType<ChangeConflictError>();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("INVALID_VERSION_123")]
    public async Task Handle_無効なVersionで楽観的ロックエラーが返る(string invalidVersion)
    {
        // Arrange
        var entityId = GetTestEntityId();
        var currentEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        var command = new DeleteEntityCommand(
            Id: entityId,
            UserId: currentEntity.CreatedBy,
            Version: invalidVersion
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError();
    }

    #endregion

    #region 正常系テスト（物理削除）

    [Fact]
    public async Task Handle_指定されたエンティティが物理削除される()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        var handler = CreateHandler();

        // 削除前の存在確認
        var beforeDelete = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();
        beforeDelete.Should().NotBeNull();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // 削除後の確認：エンティティが物理削除されていることを確認
        var afterDelete = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();
        afterDelete.Should().BeNull();
    }

    [Fact]
    public async Task Handle_関連ファイルが正しく削除される()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // ファイル削除サービスの呼び出し確認
        _quillImageServiceMock.Verify(x => x.DeleteAllContentsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);
        
        // オブジェクトストレージの削除確認
        _objectStorageClientMock.Verify(m => m.GetObjectInformationsAsync(It.IsAny<string>()), Times.Once);
        _objectStorageClientMock.Verify(m => m.DeleteAsync(It.IsAny<string>()), Times.AtLeast(1));
        
        // ファイル処理サービスの削除確認
        _fileProcessingServiceMock.Verify(x => x.DeleteEntityFilesAsync(entityId), Times.Once);
        
        // 画像クリーンアップサービスの確認
        _imageCleanupServiceMock.Verify(x => x.CleanupImagesAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_カスケード削除される関連エンティティを確認()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        var handler = CreateHandler();

        // TODO: 関連エンティティの削除前カウント確認（必要な場合）
        // var relatedCountBefore = await _dbContext.RelatedEntities
        //     .Where(r => r.EntityId == entityId)
        //     .CountAsync();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // TODO: DB制約によるカスケード削除が設定されている場合は関連データの削除確認
        // var relatedCountAfter = await _dbContext.RelatedEntities
        //     .Where(r => r.EntityId == entityId)
        //     .CountAsync();
        // relatedCountAfter.Should().Be(0);
    }

    [Fact]
    public async Task Handle_親エンティティの更新日時は更新されない()
    {
        // TODO: 親エンティティがある場合の検証を実装してください
        // Arrange
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        var handler = CreateHandler();

        // 削除前の親エンティティの更新日時を記録
        // var parentId = await _dbContext.Entities
        //     .Where(e => e.Id == entityId)
        //     .Select(e => e.ParentEntityId)
        //     .FirstOrDefaultAsync();
        
        // var parentUpdateTimeBefore = await _dbContext.ParentEntities
        //     .Where(p => p.Id == parentId)
        //     .Select(p => p.UpdatedDateTime)
        //     .FirstOrDefaultAsync();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // 親エンティティの更新日時が変更されていないことを確認
        // var parentUpdateTimeAfter = await _dbContext.ParentEntities
        //     .Where(p => p.Id == parentId)
        //     .Select(p => p.UpdatedDateTime)
        //     .FirstOrDefaultAsync();
        
        // parentUpdateTimeAfter.Should().Be(parentUpdateTimeBefore);
        
        // 実装されていない場合はパスするためのアサーション
        Assert.True(true, "TODO: 親エンティティの更新日時チェックを実装してください");
    }

    #endregion

    #region エラーハンドリングテスト

    [Fact]
    public async Task Handle_リポジトリからのエラーはそのまま返る()
    {
        // Arrange
        var unitOfWorkMock = new Mock<IUnitOfWork>();
        var repositoryMock = new Mock<EntityRepository>();
        
        unitOfWorkMock.Setup(x => x.GetRepository<Entity, string>())
            .Returns(repositoryMock.Object);

        repositoryMock.Setup(x => x.SingleAsync(It.IsAny<ISpecification<Entity>>()))
            .ReturnsAsync(Result.Error<Entity>(new Error("Repository Error")));

        var handler = new DeleteEntityHandler(
            unitOfWorkMock.Object,
            _objectStorageClientProviderMock.Object,
            _quillImageServiceMock.Object,
            _fileProcessingServiceMock.Object,
            _imageCleanupServiceMock.Object
        );
        
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Repository Error");
    }

    [Fact]
    public async Task Handle_QuillImageServiceからのエラーはそのまま返る()
    {
        // Arrange
        _quillImageServiceMock.Setup(x => x.DeleteAllContentsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Error<string>(new Error("QuillImage Service Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("QuillImage Service Error");
    }

    [Fact]
    public async Task Handle_ObjectStorageからのエラーはそのまま返る()
    {
        // Arrange
        _objectStorageClientMock.Setup(m => m.GetObjectInformationsAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Error<IEnumerable<ObjectInformation>>(new Error("Storage Access Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Storage Access Error");
    }

    [Fact]
    public async Task Handle_ファイル削除エラーはそのまま返る()
    {
        // Arrange
        _objectStorageClientMock.Setup(m => m.DeleteAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Error(new Error("File Deletion Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("File Deletion Error");
    }

    [Fact]
    public async Task Handle_FileProcessingServiceからのエラーはそのまま返る()
    {
        // Arrange
        _fileProcessingServiceMock.Setup(x => x.DeleteEntityFilesAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Error(new Error("File Processing Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("File Processing Error");
    }

    [Fact]
    public async Task Handle_ImageCleanupServiceからのエラーはそのまま返る()
    {
        // Arrange
        _imageCleanupServiceMock.Setup(x => x.CleanupImagesAsync(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Error(new Error("Image Cleanup Error")));

        var handler = CreateHandler();
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<Error>().And.WithMessage("Image Cleanup Error");
    }

    #endregion

    #region ファイルクリーンアップ詳細テスト

    [Fact]
    public async Task Handle_複数ファイルが存在する場合すべて削除される()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // 複数ファイルを設定
        var multipleFiles = new List<ObjectInformation>
        {
            new ObjectInformation($"{_folderPrefix}/{entityId}/file1.txt", "text/plain", 100),
            new ObjectInformation($"{_folderPrefix}/{entityId}/file2.jpg", "image/jpeg", 200),
            new ObjectInformation($"{_folderPrefix}/{entityId}/file3.pdf", "application/pdf", 300),
            new ObjectInformation($"{_folderPrefix}/{entityId}/file4.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", 400),
        };
        
        _objectStorageClientMock.Setup(m => m.GetObjectInformationsAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok(multipleFiles.AsEnumerable()));

        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // 各ファイルの削除が呼び出されたことを確認
        _objectStorageClientMock.Verify(m => m.DeleteAsync(It.IsAny<string>()), Times.Exactly(multipleFiles.Count));
        
        foreach (var file in multipleFiles)
        {
            _objectStorageClientMock.Verify(m => m.DeleteAsync(file.Key), Times.Once);
        }
    }

    [Fact]
    public async Task Handle_ファイルが存在しない場合でも正常に削除される()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);

        // ファイルが存在しない場合を設定
        _objectStorageClientMock.Setup(m => m.GetObjectInformationsAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok(Enumerable.Empty<ObjectInformation>()));

        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeOk();

        // ファイル削除は呼び出されないことを確認
        _objectStorageClientMock.Verify(m => m.DeleteAsync(It.IsAny<string>()), Times.Never);
        
        // エンティティ自体は削除されることを確認
        var deletedEntity = await _dbContext.Entities.AsNoTracking()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();
        deletedEntity.Should().BeNull();
    }

    #endregion

    #region 境界値・エッジケーステスト

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("INVALID_ID")]
    public async Task Handle_無効なIDでDataNotFoundErrorが返る(string invalidId)
    {
        // Arrange
        var command = new DeleteEntityCommand(
            Id: invalidId,
            UserId: "test-user",
            Version: "test-version"
        );
        var handler = CreateHandler();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    [Fact]
    public async Task Handle_削除済みエンティティの再削除でDataNotFoundErrorが返る()
    {
        // Arrange
        var entityId = GetTestEntityId();
        var command = await CreateValidCommandAsync(entityId);
        var handler = CreateHandler();

        // 一度削除を実行
        await handler.Handle(command, CancellationToken.None);

        // Act - 同じエンティティを再度削除しようとする
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeError().And.BeOfType<DataNotFoundError>();
    }

    #endregion
}
