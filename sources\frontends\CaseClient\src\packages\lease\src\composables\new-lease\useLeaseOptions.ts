import { ref, reactive } from 'vue'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useBranchMaster } from '@/packages/common-case/src/composables/customerIdentifying/useBranchMaster'
import { getStaffAndTeamOptions } from '@/packages/common-case/src/utils/ddlOptions'
import { DEFAULT_SELECTED_CASE_STATUSES } from '@/packages/common-case/src/constants/domain/case'

/**
 * リース案件オプションデータ取得用のコンポーザブル
 * マスターデータやスタッフ情報など、検索条件で利用する選択肢の取得を行う
 */
export const useLeaseOptions = () => {
  // 処理完了などをトースト表示するために利用
  const { error: errorToast } = useAppToasts()

  // マスタデータ取得用コンポーザブル
  const { branchMasterOptions, fetchBranches } = useBranchMaster()

  // スタッフと担当者のローディング状態
  const loadingStaffAndTeam = ref(false)

  // 選択肢
  const options = reactive({
    staffs: [] as any[],
    staffAndTeams: [] as any[],
  })

  /**
   * すべての選択肢データを取得する
   * @param hasCriteriaHistory 検索条件履歴の有無
   * @param searchCondition 検索条件オブジェクト（初期値設定用）
   * @returns 処理成功フラグ
   */
  async function loadAllOptions(hasCriteriaHistory: boolean, searchCondition: any) {
    loadingStaffAndTeam.value = true
    try {
      const result = await setOptions()
      if (searchCondition && !hasCriteriaHistory) {
        searchCondition.caseStatuses = DEFAULT_SELECTED_CASE_STATUSES || []
      }
      return result
    } finally {
      loadingStaffAndTeam.value = false
    }
  }

  /**
   * マスタデータとスタッフ・チーム情報を取得する
   * @returns 処理成功フラグ
   */
  async function setOptions() {
    try {
      const [
        getStaffAndTeamOptionsResult,
      ] = await Promise.all([
        getStaffAndTeamOptions(),
        fetchBranches(), // 店番情報を取得
      ])

      if (getStaffAndTeamOptionsResult.length === 0) {
        errorToast('検索条件の選択肢が取得できませんでした。リロードしてください。')
        return false
      }

      options.staffAndTeams = getStaffAndTeamOptionsResult
      return true
    } catch (error) {
      errorToast('オプションデータの取得に失敗しました。')
      return false
    }
  }

  return {
    // マスタデータ
    branchMasterOptions,

    // スタッフと担当者
    options,
    loadingStaffAndTeam,

    // メソッド
    loadAllOptions,
    setOptions,
  }
}
