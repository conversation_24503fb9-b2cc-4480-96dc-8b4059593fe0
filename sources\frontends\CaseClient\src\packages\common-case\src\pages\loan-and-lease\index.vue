<template>
  <CaseTable
    v-model:pageIndex="pagination.pageIndex"
    v-model:sort="pagination.sort"
    :items="data?.items"
    :total="data?.total"
    :loading="loading"
    :page-size="pagination.pageSize"
    :search-result-message="searchResultMessage"
    @search="search"
    @edit="edit"
  >
    <template #criteria>
      <SearchCriteria
        :search-condition="searchCondition"
        :branch-master-options="branchMasterOptions"
        :industry-master-options="industryMasterOptions"
        :segment-options="segmentOptions"
        :staff-and-teams="options.staffAndTeams"
        :loading-staff-and-team="loadingStaffAndTeam"
        :error-messages="errorMessages"
        :results-count="data?.total || 0"
        @update:search-condition="updateSearchCondition"
        @validate-item="validateItem"
      />
    </template>
  </CaseTable>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useLoanAndLeaseValidation } from '@/packages/common-case/src/composables/loan-and-lease/useLoanAndLeaseValidation'
import { useLoanAndLeaseSearch } from '@/packages/common-case/src/composables/loan-and-lease/useLoanAndLeaseSearch'
import { useLoanAndLeaseOptions } from '@/packages/common-case/src/composables/loan-and-lease/useLoanAndLeaseOptions'
import { useLoanAndLeaseNavigation } from '@/packages/common-case/src/composables/loan-and-lease/useLoanAndLeaseNavigation'
import SearchCriteria from '@/packages/common-case/src/components/loan-and-lease/SearchCriteria.vue'
import CaseTable from '@/packages/common-case/src/components/loan-and-lease/CaseTable.vue'
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { isUndefinedOrNull } from '@hox/base/src/utils/shared/is'

// タイトルとメタデータの設定
useHead({
  title: '融資リース案件一覧',
})

definePageMeta({
  title: '融資リース案件一覧',
})

// オプションデータ取得用コンポーザブル
const {
  branchMasterOptions,
  industryMasterOptions,
  segmentOptions,
  options,
  loadingStaffAndTeam,
  loadAllOptions,
} = useLoanAndLeaseOptions()

// バリデーションの設定
const validationInstance = useLoanAndLeaseValidation(toRef(() => searchCondition))
const { errorMessages, validateItem, validateAll } = validationInstance

// 検索関連機能を取得
const {
  searchCondition,
  pagination,
  data,
  searchResultMessage,
  loading,
  criteriaHistory,
  hasState,
  search,
  restoreCriteria,
} = useLoanAndLeaseSearch(validateAll)
const { error: errorToast } = useAppToasts()

// 検索条件更新用の関数
function updateSearchCondition(newValue: any) {
  // 検索条件の各プロパティを更新
  Object.assign(searchCondition, newValue)
}

// 初期データの読み込み
onMounted(async () => {
  try {
    const hasCriteriaHistory = hasState && !isUndefinedOrNull(criteriaHistory.value)

    // マスタデータとオプション情報を取得
    await loadAllOptions(hasCriteriaHistory, searchCondition)

    if (hasCriteriaHistory) {
      // 検索条件を復元して検索実行
      await restoreCriteria(criteriaHistory.value)
      search({ noStoreCriteria: true })
    } else {
      // 初期化
      data.value = { items: [], total: 0 }
    }
  } catch (error) {
    errorToast('初期データの読み込みに失敗しました。')
  }
})

// ナビゲーション関連機能を取得
const { navigateToEdit } = useLoanAndLeaseNavigation()

// 編集処理
function edit(e: any) {
  // 遷移前に画面をクリア・ローディング状態にする
  const onBeforeNavigate = () => {
    data.value = { items: [], total: 0 }
    loading.value = true
  }

  // 編集画面に遷移
  navigateToEdit(e, onBeforeNavigate)
}
</script>

<style>
.loan-lease-highlight-row {
  background-color: #f6cfd2 !important;
}
</style>
