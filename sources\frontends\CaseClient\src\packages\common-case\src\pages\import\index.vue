<template>
  <div>
    <v-row>
      <v-col>
        <v-card outlined class="pa-5 mb-3 mx-2">
          <v-row>
            <v-col cols="9" class="pl-10">
              <app-autocomplete
                v-model="caseCategory"
                :items="allCaseCategories"
                label="追加対象の案件カテゴリ"
                data-testid="category"
              />
            </v-col>
            <v-col class="d-flex justify-end">
              <app-main-btn
                :disabled="!caseCategory"
                prepend-icon="mdi-upload"
                @click="openDialog()"
              >
                ファイルアップロード
              </app-main-btn>
              <CaseCsvUploadDialog
                ref="caseCsvImportDialog"
                :case-category="caseCategory"
                @csv-uploaded="csvUploaded = true"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col class="pl-10">
              <h4 class="title">&lt;注意事項&gt;</h4>
              <li>
                ファイル形式は、 Excelで保存する際に
                <b>CSV(コンマ区切り)(*.csv)</b>
                を選択してください。
              </li>
              <li>
                {{ categoryIndicateText }}
              </li>
            </v-col>
          </v-row>
          <v-row v-if="caseCategory">
            <v-col cols="12" class="pl-10 py-0">
              <div class="description_title">&lt;レイアウト例&gt;</div>
            </v-col>
            <v-col cols="12" class="pl-10 py-0">
              <div class="csvLayoutSample">
                <table>
                  <thead>
                    <tr>
                      <th v-for="{ key, name } in csvLayoutSample" :key="key">
                        {{ key }}
                        <span style="user-select: none"><br >{{ name }}</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td v-for="{ key, sample } in csvLayoutSample" :key="key">
                        {{ sample }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <CaseImportHistoryCard
          :csv-uploaded="csvUploaded"
          @history-fetched="historyFetched()"
        />
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
import CaseCsvUploadDialog from '@ibp/common-case/src/components/import/CaseCsvUploadDialog.vue'
import CaseImportHistoryCard from '@ibp/common-case/src/components/import/CaseImportHistoryCard.vue'
import { CASE_CATEGORIES } from '@ibp/common-case/src/constants/domain/case'

// =====================================================================================================================
// 画面全体で利用するデータの定義
// =====================================================================================================================

// =====================================================================================================================
// 画面の定義
// =====================================================================================================================

// タイトルを指定します。(windowのタイトルに設定されます。)
useHead({
  title: '案件データ取込',
})

// タイトルを指定します。(パンくずリストに設定されます。)
definePageMeta({
  title: '案件データ取込',
})

// =====================================================================================================================
// リアクティブデータの定義
// =====================================================================================================================

const csvUploaded = ref<boolean>(false)
const caseCategory = ref<string | undefined>(undefined)

// =====================================================================================================================
// 計算プロパティの定義
// =====================================================================================================================

const categoryIndicateText = computed(() => {
  const caseCategoryText = allCaseCategories.value.find(
    ({ value }) => value === caseCategory.value,
  )?.title
  return caseCategoryText
    ? `${caseCategoryText}は、店番、CIF番号、案件名、ステータスおよび担当者IDを必ずデータに含むようにしてください。`
    : 'アップロード実施する前、必ずアップロード案件のカテゴリを選択してください。'
})

const allCaseCategories = computed(() => {
  return CASE_CATEGORIES.filter((c) =>
    [
      'NewLoan',
      'OtherLoan',
      'UnorganizedLoan',
      'ExternallyCompletedLease',
      'GeneralTransaction',
    ].includes(c.value),
  )
})

const csvLayoutSample = computed(() => {
  const common = [
    { key: 'BranchNumber', name: '店番', sample: '101' },
    { key: 'CifNumber', name: 'CIF', sample: '12345678' },
    { key: 'CaseName', name: '案件名', sample: '案件名' },
    { key: 'CaseStatus', name: 'ステータス', sample: 'BeforeProposal' },
    { key: 'ExpiredAt', name: '期日', sample: '2024/01/01' },
    { key: 'StaffId', name: '案件担当者ID', sample: '1234567' },
    { key: 'CaseOutline', name: '案件概要', sample: '案件の概要' },
    {
      key: 'CaseLinkTitle',
      name: 'リンクタイトル',
      sample: 'リンクタイトル',
    },
    {
      key: 'CaseLinkUrl',
      name: 'リンクURL',
      sample: 'https://www.hokkokubank.co.jp',
    },
  ]

  // 融資案件（新規）
  if (caseCategory.value === 'NewLoan') {
    return [
      ...common,
      { key: 'SubjectType', name: '科目', sample: 'DeedLoanByOurCompany' },
      { key: 'Amount', name: '金額(千円)', sample: '100' },
      { key: 'InterestRate', name: '金利(%)', sample: '0.01' },
      { key: 'UseOfFundsType', name: '資金使途', sample: 'WorkingCapital' },
      { key: 'RepaymentMethodType', name: '返済方法', sample: 'LumpSum' },
      { key: 'CollateralOrGuarantee', name: '担保・保証人', sample: '1' },
      { key: 'RelatedEsg', name: 'ESG関連', sample: '1' },
      {
        key: 'PreConsultationStandardTarget',
        name: '事前相談基準対象',
        sample: '1',
      },
      { key: 'IsEarthquakeRelated', name: '震災関連', sample: '1' },
    ]
  }

  // 融資案件（その他）
  if (caseCategory.value === 'OtherLoan') {
    return [
      ...common,
      {
        key: 'PreConsultationStandardTarget',
        name: '事前相談基準対象',
        sample: '1',
      },
      { key: 'IsEarthquakeRelated', name: '震災関連', sample: '1' },
    ]
  }

  // 融資案件（未整理）
  if (caseCategory.value === 'UnorganizedLoan') {
    return [...common]
  }

  // リース案件（他社満了）
  if (caseCategory.value === 'ExternallyCompletedLease') {
    return [
      ...common,
      { key: 'IsEarthquakeRelated', name: '震災関連', sample: '1' },
    ]
  }

  // 総合取引案件
  if (caseCategory.value === 'GeneralTransaction') {
    return [
      ...common,
      {
        key: 'GeneralTransactionType',
        name: '案件項目',
        sample: '案件項目名',
      },
    ]
  }

  return []
})

// =====================================================================================================================
// Refの定義
// =====================================================================================================================
const caseCsvImportDialog = ref<InstanceType<typeof CaseCsvUploadDialog>>()

// =====================================================================================================================
// 画面で使用するメソッドの定義
// =====================================================================================================================

function openDialog() {
  return caseCsvImportDialog.value?.open()
}

function historyFetched() {
  csvUploaded.value = false
}
</script>

<style scoped lang="scss">
.csvLayoutSample {
  overflow-x: auto;

  & > table {
    width: 100%;
    border-collapse: collapse;

    td,
    th {
      padding: 0.25rem;
      font-size: 0.8rem;
      font-weight: normal;
      border: 1px solid black;
      white-space: nowrap;
      text-align: center;
    }
  }
}
</style>
