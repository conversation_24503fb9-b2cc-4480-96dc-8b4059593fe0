<template>
  <div>
    <VRow dense>
      <VCol dense cols="3">
        <AppAutocomplete
          v-model="localSearchCondition.branchNumbers"
          :items="branchMasterOptions || []"
          label="店番"
          clearable
          multiple
          chips
          closable-chips
        />
      </VCol>
      <VCol dense cols="2">
        <AppTextField
          v-model="localSearchCondition.cifNumber"
          label="CIF番号"
          type="number"
          counter="8"
          clearable
          :error-messages="errorMessages?.cifNumber"
          @blur="validateItem('cifNumber')"
          @update:model-value="validateItem('cifNumber')"
        />
      </VCol>
      <VCol dense cols="3">
        <AppTextField
          v-model="localSearchCondition.customerName"
          label="氏名（漢字 または カナ）"
          style="white-space: break-spaces"
          hint="部分一致検索ができます。"
          persistent-hint
          clearable
        />
      </VCol>
      <VCol dense cols="4">
        <AppAutocomplete
          v-model="localSearchCondition.caseStatuses"
          :items="CASE_STATUSES"
          label="ステータス"
          clearable
          multiple
          chips
          closable-chips
        />
      </VCol>
    </VRow>
    <VRow dense>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.leaseStaffIds"
          :items="staffAndTeams || []"
          label="総合リース担当者"
          clearable
          multiple
          chips
          closable-chips
          :loading="loadingStaffAndTeam"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.staffIds"
          :items="staffAndTeams || []"
          label="案件担当者"
          clearable
          multiple
          chips
          closable-chips
          :loading="loadingStaffAndTeam"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.quotationCreateStaffIds"
          :items="staffAndTeams || []"
          label="見積作成者"
          clearable
          multiple
          chips
          closable-chips
          :loading="loadingStaffAndTeam"
        />
      </VCol>
      <VCol dense>
        <AppAutocomplete
          v-model="localSearchCondition.quotationScrutinizeStaffIds"
          :items="staffAndTeams || []"
          label="見積精査者"
          clearable
          multiple
          chips
          closable-chips
          :loading="loadingStaffAndTeam"
        />
      </VCol>
      <VCol cols="2">
        <VCheckbox
          v-model="localSearchCondition.isFavorite"
          label="お気に入り"
          true-value="true"
          false-value="false"
          :error-messages="errorMessages?.isFavorite"
        />
      </VCol>
    </VRow>
  </div>
</template>

<script setup lang="ts">
/**
 * リース案件検索条件コンポーネント
 *
 * 機能:
 * 1. 検索条件入力フォームの提供
 * 2. 入力値のバリデーション
 * 3. 検索条件変更の親コンポーネントへの通知
 */

// === 外部インポート ===
import { ref, watch } from 'vue'

// === マスタデータ・ドメイン定数 ===
import {
  CASE_STATUSES, // 案件ステータス一覧
} from '@/packages/common-case/src/constants/domain/case'

// === プロパティ定義 ===
const props = defineProps({
  // 検索条件オブジェクト
  searchCondition: {
    type: Object,
    required: true,
  },
  // マスタデータ選択肢
  branchMasterOptions: {
    type: Array,
    default: () => [],
  },
  // スタッフ・チーム情報
  staffAndTeams: {
    type: Array,
    default: () => [],
  },
  loadingStaffAndTeam: {
    type: Boolean,
    default: false,
  },
  // バリデーション
  errorMessages: {
    type: [Object, null],
    default: null,
  },
})

// === イベント定義 ===
const emit = defineEmits([
  'update:searchCondition', // 検索条件更新イベント
  'validateItem', // 項目検証イベント
])

// === 検索条件管理 ===
/**
 * 検索条件のローカルコピーを作成し、v-modelでバインドする
 * 親コンポーネントとの双方向バインディングを実現
 */
const localSearchCondition = ref({ ...props.searchCondition })

// 検索条件が変更されたら親コンポーネントに通知
watch(localSearchCondition, (newValue) => {
  emit('update:searchCondition', newValue)
}, { deep: true })

// 親コンポーネントからの変更を監視して反映
watch(() => props.searchCondition, (newValue) => {
  // ディープコピーを作成して参照を切り替える
  localSearchCondition.value = { ...newValue }
}, { deep: true })

/**
 * 項目のバリデーション実行
 * 親コンポーネントに検証イベントを通知
 * @param field バリデーション対象フィールド名
 */
function validateItem(field: string) {
  emit('validateItem', field)
}
</script>
