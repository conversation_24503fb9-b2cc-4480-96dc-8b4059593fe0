/**
 * APIクライアントテンプレート
 * 
 * 使用技術:
 * - フレームワーク: Nuxt 3
 * - 言語: TypeScript
 * - バリデーション: Zod
 * - 日付処理: date-fns
 * - HTTPクライアント: Nuxt useFetch
 * 
 * ファイル名規則:
 * - [ドメインモデル物理名].ts（camelCase形式）
 * - 例: employee.ts, orderDetail.ts, customerAccount.ts
 * 
 * 命名規則:
 * - スキーマ名: [モデル名]Schema (PascalCase + Schema)
 * - 型名: [モデル名] (PascalCase)
 * - 関数名: use[操作][モデル名] (use + 操作動詞 + PascalCase)
 * - 変数名・プロパティ名: camelCase
 * - エンドポイントのリソース名: 複数形で全て小文字（連結文字不要）
 */

// =====================================================================================================================
// インポート文
// =====================================================================================================================
// 必須インポート（削除・変更不可）
import { useNuxtApp } from 'nuxt/app'
import { z } from 'zod'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type { ApiResult, Pagination } from '@hox/base/src/apiclient/shared/types'

// スキーマ定義のインポート
// 関連するエンティティのスキーマをインポート
// 基本スキーマ定義
import { employeeSchema } from '../constants/domain/entities/employeeEntity'
// 基本データ型の定義
import type { Employee} from '../constants/domain/entities/employeeEntity'
// TODO: 上記のemployeeSchema、Employeeを[モデル名]Schema、[モデル名]に変更してください

// 条件付きインポート
// 明細のスキーマ定義のインポート
// 注意: 明細がある場合のみ定義してください
import { employeeQualificationSchema } from '../constants/domain/entities/employeeQualificationEntity'
// 明細のデータ型
import type { EmployeeQualification} from '../constants/domain/entities/employeeQualificationEntity'
// TODO: 明細がある場合は、employeeQualificationを[モデル名][明細名]に変更してください
//       例: OrderDetail, CustomerAddress, ProductCategory

// 日付型のプロパティがある場合のみインポート
import { parseISO } from 'date-fns'
// TODO: 日付型（Date型、Zodのz.date()）のプロパティがある場合は、上記をコメントアウト解除してください

// 文字列判定が必要な場合のみインポート
import { isString } from '@hox/base/src/utils/shared/is'
// TODO: データ変換で文字列判定が必要な場合は、上記をコメントアウト解除してください

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

// リクエスト先ベースURL
// TODO: API定義にAPIの所属マイクロサービスが明示されている場合、`[マイクロサービスパス][ベースURL]`形式で定義
// TODO: API定義にAPIの所属マイクロサービスが明示されていない場合、`[ベースURL]`形式で定義
// TODO: API定義ファイルが存在しない場合、`/v1.0/[取り扱い集約名(camelCase)]`形式で定義
const BASE_URL = '/ibp-sample/v1.0/employees'

// 検索条件の型定義
// Paginationとの交差型として定義すること
export type FindEmployeeCriteria = {
  // TODO: 検索条件となるプロパティを追加してください
  // 検索条件は基本的にオプショナル（?）にしてください
  // 文字列検索の例: name?: string
  // 部分一致検索の例: nameLike?: string
  // 範囲検索の例: ageFrom?: number, ageTo?: number
  // 真偽値検索の例: isActive?: boolean
  // 列挙型検索の例: status?: 'active' | 'inactive' | 'pending'
  // 複数選択の例: statuses?: string[]
  name?: string
  address?: string
} & Pagination // 必須: ページング情報を含める

// TODO: 上記のFindEmployeeCriteriaをFind[モデル名]Criteriaに変更してください

// 検索結果項目の型定義
// 検索結果で不要なプロパティがある場合はOmitで除外
export type FindEmployeeResultItem = Omit<Employee, 'branchId' | 'qualifications'>
// TODO: 検索結果に含めないプロパティがある場合は、Omitを使用して除外してください
//       例: export type Find[モデル名]ResultItem = Omit<[モデル名], '除外プロパティ1' | '除外プロパティ2'>
//       全て含める場合: export type Find[モデル名]ResultItem = [モデル名]

// 検索結果の型定義（ページング情報を含む）
export type FindEmployeeResult = ApiResult<FindEmployeeResultItem>
// TODO: 上記のFindEmployeeResult、FindEmployeeResultItemをFind[モデル名]Result、Find[モデル名]ResultItemに変更してください

/**
 * データを検索する
 * エンドポイント: GET /v1.0/[リソース名]
 * @param query Ref<T> 検索条件（リアクティブ）
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindEmployee(query: Ref<FindEmployeeCriteria>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindEmployeeResult>(
    useFetch(
      // TODO: API定義に記載されたURLとしてください
      () => $endpoints.default.get(`${BASE_URL}`),
      withDefaultFetchOptions({
        method: 'GET',
        query, // クエリパラメータとして検索条件を送信
        scope: $endpoints.default.scope, // 必須: スコープの指定
        transform(data: FindEmployeeResult) {
          // TODO: 日付型のプロパティがある場合は、文字列から日付型への変換を実装してください
          //       検索結果は配列なので、data.itemsをループして処理
          //       各日付プロパティに対してisString判定とparseISO変換を行う
          if (Array.isArray(data.items)) {
            for (const item of data.items) {
              if (isString(item.birthDate)) {
                item.birthDate = parseISO(item.birthDate)
              }
              // TODO: 他の日付型プロパティがある場合は同様の処理を追加
            }
          }
          return data
        },
      }),
    ),
  )
}
// TODO: 関数名をuseFind[モデル名]に変更してください
//       型パラメータのFindEmployeeResultもFind[モデル名]Resultに変更してください

/**
 * 単一データを取得する
 * エンドポイント: GET /v1.0/[リソース名]/{id}
 * @param id Ref<string> 取得するデータのID（リアクティブ）
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetEmployee(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Employee>(
    useFetch(
      // TODO: API定義に記載されたURLとしてください
      () => $endpoints.default.get(`${BASE_URL}/${id.value}`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope, // 必須: スコープの指定
        transform(data: Employee) {
          // TODO: 日付型のプロパティがある場合は、文字列から日付型への変換を実装してください
          if (isString(data.birthDate)) {
            data.birthDate = parseISO(data.birthDate)
          }
          // TODO: 他の日付型プロパティがある場合は同様の処理を追加
          return data
        },
      }),
    ),
  )
}
// TODO: 関数名をuseGet[モデル名]に変更してください
//       型パラメータのEmployeeも[モデル名]に変更してください

/**
 * 明細データを取得する
 * エンドポイント: GET /v1.0/[リソース名]/{id}/[明細リソース名]
 * 注意: 明細がある場合のみ実装してください
 * @param id Ref<string> 親エンティティのID（リアクティブ）
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetEmployeeQualifications(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<EmployeeQualification[]>(
    useFetch(
      // TODO: API定義に記載されたURLとしてください
      () => $endpoints.default.get(`${BASE_URL}/${id.value}/qualifications`),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope, // 必須: スコープの指定
        transform(data: EmployeeQualification[]) {
          // TODO: 明細に日付型のプロパティがある場合は、変換処理を実装してください
          //       配列の各要素に対して日付変換を行う
          // if (Array.isArray(data)) {
          //   for (const item of data) {
          //     if (isString(item.dateProperty)) {
          //       item.dateProperty = parseISO(item.dateProperty)
          //     }
          //   }
          // }
          return data
        },
      }),
    ),
  )
}
// TODO: 明細がある場合は、関数名をuseGet[モデル名][明細名]に変更してください
//       型パラメータのEmployeeQualification[]も[明細名][]に変更してください

// 明細保存用スキーマ
// 明細は更新時に全件送信するため、更新用のプロパティを追加
export const employeeQualificationSchemaForSave = employeeQualificationSchema.merge(z.object({
  id: z.string().optional(), // 新規作成時は未設定
  updated: z.boolean().optional(), // 更新フラグ
}))
// 明細保存用の型
export type EmployeeQualificationForSave = z.infer<typeof employeeQualificationSchemaForSave>
// TODO: 明細がある場合は、上記を[明細名]SchemaForSaveと[明細名]ForSaveに変更してください

// 保存用スキーマ定義
// 基本スキーマを継承し、必要に応じて調整
export const employeeSchemaForSave = employeeSchema.merge(z.object({
  // 明細がある場合は、保存用の明細スキーマの配列を定義
  qualifications: z.array(employeeQualificationSchemaForSave).min(1),
}))
// TODO: 保存時にスキーマが変わる場合（明細の追加など）は、mergeで必要なプロパティを追加してください
//       変更がない場合は以下のようにしてください:
//       export const [モデル名]SchemaForSave = [モデル名]Schema

// 保存用の型
export type EmployeeForSave = z.infer<typeof employeeSchemaForSave>
// TODO: 上記のemployeeSchemaForSave、EmployeeForSaveを[モデル名]SchemaForSave、[モデル名]ForSaveに変更してください

// 作成用スキーマ定義
// 保存用スキーマからid、versionを除外（サーバー側で生成されるため）
export const employeeSchemaForCreate = employeeSchemaForSave.omit({ id: true, version: true })
// 作成用の型
export type EmployeeForCreate = z.infer<typeof employeeSchemaForCreate>
// TODO: 上記のemployeeSchemaForCreate、EmployeeForCreateを[モデル名]SchemaForCreate、[モデル名]ForCreateに変更してください

/**
 * 新規作成処理を行う
 * エンドポイント: POST /v1.0/[リソース名]
 * @param body Ref<T> 作成するデータ（リアクティブ）
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostEmployee(body: Ref<EmployeeForCreate>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Employee>(
    useFetch(
      // TODO: API定義に記載されたURLとしてください
      () => $endpoints.default.get(`${BASE_URL}`),
      withDefaultFetchOptions({
        method: 'POST',
        body, // リクエストボディとして送信
        scope: $endpoints.default.scope, // 必須: スコープの指定
        transform(data: Employee) {
          // TODO: 日付型のプロパティがある場合は、文字列から日付型への変換を実装してください
          if (isString(data.birthDate)) {
            data.birthDate = parseISO(data.birthDate)
          }
          // TODO: 他の日付型プロパティがある場合は同様の処理を追加
          return data
        },
      }),
    ),
  )
}
// TODO: 関数名をusePost[モデル名]に変更してください
//       パラメータの型EmployeeForCreateを[モデル名]ForCreateに変更してください
//       型パラメータのEmployeeも[モデル名]に変更してください

/**
 * 更新処理を行う
 * エンドポイント: PUT /v1.0/[リソース名]
 * @param body Ref<T> 更新するデータ（リアクティブ）
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutEmployee(body: Ref<EmployeeForSave>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Employee>(
    useFetch(
      // TODO: API定義に記載されたURLとしてください
      () => $endpoints.default.get(`${BASE_URL}`),
      withDefaultFetchOptions({
        method: 'PUT',
        body, // リクエストボディとして送信（id、versionを含む）
        scope: $endpoints.default.scope, // 必須: スコープの指定
        transform(data: Employee) {
          // TODO: 日付型のプロパティがある場合は、文字列から日付型への変換を実装してください
          if (isString(data.birthDate)) {
            data.birthDate = parseISO(data.birthDate)
          }
          // TODO: 他の日付型プロパティがある場合は同様の処理を追加
          return data
        },
      }),
    ),
  )
}
// TODO: 関数名をusePut[モデル名]に変更してください
//       パラメータの型EmployeeForSaveを[モデル名]ForSaveに変更してください
//       型パラメータのEmployeeも[モデル名]に変更してください

/**
 * 削除処理を行う
 * エンドポイント: DELETE /v1.0/[リソース名]
 * @param body Ref<T> 削除対象の識別情報（id、version）（リアクティブ）
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteEmployee(body: Ref<Pick<Employee, 'id' | 'version'>>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      // TODO: API定義に記載されたURLとしてください
      () => $endpoints.default.get(`${BASE_URL}`),
      withDefaultFetchOptions({
        method: 'DELETE',
        body, // リクエストボディとしてidとversionを送信（楽観的ロック）
        scope: $endpoints.default.scope, // 必須: スコープの指定
      }),
    ),
  )
}
// TODO: 関数名をuseDelete[モデル名]に変更してください
//       パラメータの型Pick<Employee, 'id' | 'version'>をPick<[モデル名], 'id' | 'version'>に変更してください

/**
 * 一括削除処理を行う
 * エンドポイント: POST /v1.0/[リソース名]/:deleteAll
 * @param body Ref<T[]> 削除対象の識別情報の配列（リアクティブ）
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteAllEmployee(body: Ref<Pick<Employee, 'id' | 'version'>[]>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      // TODO: API定義に記載されたURLとしてください
      () => $endpoints.default.get(`${BASE_URL}/:deleteAll`),
      withDefaultFetchOptions({
        method: 'POST', // 一括処理はPOSTメソッドを使用
        body, // リクエストボディとして削除対象の配列を送信
        scope: $endpoints.default.scope, // 必須: スコープの指定
      }),
    ),
  )
}
// TODO: 関数名をuseDeleteAll[モデル名]に変更してください
//       パラメータの型Pick<Employee, 'id' | 'version'>[]をPick<[モデル名], 'id' | 'version'>[]に変更してください

/**
 * 実装チェックリスト:
 * 
 * ■ 基本的な置換作業
 * [ ] ファイル名を[ドメインモデル物理名].tsに変更（camelCase形式）
 * [ ] 全ての"employee"を対象のモデル名（camelCase）に置換
 * [ ] 全ての"Employee"を対象のモデル名（PascalCase）に置換
 * [ ] 全ての"qualification"を明細名（camelCase）に置換（明細がある場合）
 * [ ] 全ての"Qualification"を明細名（PascalCase）に置換（明細がある場合）
 * 
 * ■ スキーマ定義
 * [ ] 基本スキーマにドメインモデルのプロパティを追加
 * [ ] 各プロパティに適切なバリデーションを設定（max, min, email等）
 * [ ] 明細がある場合、明細スキーマを定義
 * [ ] 保存用スキーマの調整（明細の追加など）
 * 
 * ■ 型定義
 * [ ] 検索条件の型（Find[モデル名]Criteria）に必要なプロパティを追加
 * [ ] 検索結果項目型（Find[モデル名]ResultItem）で不要なプロパティをOmitで除外
 * 
 * ■ エンドポイント
 * [ ] 全てのエンドポイントパスを/v1.0/[リソース名]形式に変更
 * [ ] リソース名は複数形で全て小文字（連結文字不要）を使用
 * 
 * ■ データ変換
 * [ ] 日付型プロパティがある場合、parseISOのインポートを有効化
 * [ ] 日付型プロパティがある場合、isStringのインポートを有効化
 * [ ] 各API関数のtransform内で日付変換処理を実装
 * 
 * ■ クリーンアップ
 * [ ] 不要なTODOコメントを削除
 * [ ] 明細がない場合、明細関連のコードを削除
 * 
 * ■ 検証
 * [ ] TypeScriptの型エラーがないことを確認
 * [ ] 全てのAPI関数が正しく実装されていることを確認
 * [ ] 命名規則が一貫していることを確認
 */