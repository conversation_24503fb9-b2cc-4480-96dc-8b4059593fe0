# ページ定義

## 基本情報

- ページ名 : 課題案件協議
- UIパターン : 複数集約表示画面（multi）

## 実現対象ハイレベルユースケース

- 課題案件に関するチーム内でのスレッド形式協議
- スレッドとコメントによるリアルタイムコミュニケーション
- ファイル共有とリアクション機能による協議効率化

## 共通処理（ミドルウェア）定義

- [ ] アクセスログ出力
- [ ] アクセス可否チェック
- [ ] 次画面パスチェック

## パラメータ定義

| パラメータ名 | 種別 | 必須 | 型 | 説明 |
|---|---|---|---|---|
| issueProjectId | query | ○ | 文字列 | 課題案件ID |
| customerIdentificationId | query | ○ | 文字列 | 顧客識別ID |
| threadId | query | | 文字列 | スレッドID（特定スレッドへの直接アクセス時） |
| from | query | | 文字列 | 遷移元画面識別子（パンくずリスト構築用） |

## UI定義

### 検索・フィルタリング機能
- 照会期間
    - 開始日・終了日の日付範囲選択
    - 変更時にリアルタイム検索実行
- 協議種別
    - プルダウンリスト（単一選択）
    - 変更時にリアルタイム検索実行

### スレッド表示エリア
- スレッド一覧
    - 表示順 : 登録日時の降順（最新が上）
    - 各スレッドの表示項目
        - 協議種別、タイトル、登録者、登録日時
        - 本文内容、添付ファイル一覧
        - リアクション（いいね等）
        - コメント一覧（折りたたみ可）
- スレッド機能
    - 新規投稿、編集、削除
    - ファイルアップロード・ダウンロード
    - リアクション追加・更新・削除
    - コメント追加・編集・削除
    - メンション機能（ユーザー・チーム指定）

### コメント機能
- コメント表示
    - 各コメントの表示項目
        - コメント内容、投稿者、投稿日時
        - 添付ファイル一覧、リアクション
- コメント機能
    - 追加、編集、削除
    - ファイルアップロード・ダウンロード
    - リアクション追加・更新・削除

### アクションボタン
- 新しい投稿ボタン
    - 配置 : 画面右上
    - 機能 : スレッド作成ダイアログを開く

### 特別機能
- 自動タスク起案
    - 協議種別が「契約締結稟議申請兼捺印申請」の場合、自動でタスクを作成
    - 対象 : クラウドサイン・書面契約
- スレッド直接アクセス
    - URLパラメータでthreadId指定時、該当スレッドまで自動スクロール・展開・ハイライト表示

## イベント処理定義

### 一覧
| イベント処理 | 処理内容 | 処理定義先 |
|---|---|---|
| 初期表示 | スレッド一覧と選択肢を取得し表示する | ライフサイクルフック created |
| 検索条件変更 | 条件に合致するスレッドを検索・表示する | function findThreads |
| 新しい投稿ボタン押下 | スレッド作成ダイアログを開く | function openThreadEditor |
| スレッド編集 | スレッド編集ダイアログを開く | function openThreadEditor |
| スレッド削除 | 確認後スレッドを削除する | function removeThread |
| コメント追加 | コメント作成ダイアログを開く | function openCommentEditorWhenAdd |
| コメント編集 | コメント編集ダイアログを開く | function openCommentEditorWhenUpdate |
| コメント削除 | 確認後コメントを削除する | function removeComment |
| リアクション追加/更新/削除 | スレッド・コメントのリアクションを操作する | function addThreadReaction 等 |
| ファイルダウンロード | 添付ファイルをダウンロードする | function downloadThreadFile 等 |

### 初期表示（created）
1. スレッド一覧を取得・表示する。
2. 協議種別選択肢を取得する。
3. リアクション種別選択肢を取得する。
4. ユーザー・チーム一覧を取得する（メンション機能用）。
5. URLパラメータでthreadIdが指定されている場合、該当スレッドまでスクロールし展開・ハイライト表示する。
6. 課題案件の課題項目名を取得する（行動記録用）。

### 検索条件変更（findThreads）
1. 入力された検索条件（照会期間、協議種別）でスレッド検索APIを呼び出す。
2. 検索結果を登録日時の降順でソートして表示する。
3. ユーザー・チーム情報を合わせて取得・設定する。

### スレッド作成・更新（saveThread）
1. スレッドエディタから入力データを取得する。
2. ファイルアップロード処理を含むFormDataを作成する。
3. スレッド登録/更新APIを呼び出す。
4. 行動記録を登録する。
5. 協議種別が契約締結稟議申請の場合、自動タスク起案を実行する。
6. 成功時はトースト通知を表示し、スレッド一覧を更新する。

### コメント作成・更新（addComment/updateComment）
1. コメントエディタから入力データを取得する。
2. ファイルアップロード処理を含むFormDataを作成する。
3. コメント登録/更新APIを呼び出す。
4. 行動記録を登録する。
5. 成功時はトースト通知を表示し、対象スレッドのコメント一覧を更新する。

### リアクション操作（addThreadReaction等）
1. リアクション種別とターゲット（スレッドまたはコメント）を特定する。
2. リアクション追加/更新/削除APIを呼び出す。
3. 成功時はトースト通知を表示し、対象のリアクション表示を更新する。

### ファイルダウンロード（downloadThreadFile等）
1. 指定されたファイル情報でダウンロードAPIを呼び出す。
2. レスポンスをBlobオブジェクトに変換する。
3. 動的にダウンロードリンクを作成してクリック実行する。
4. メモリリークを防ぐためBlobオブジェクトの参照を削除する。
