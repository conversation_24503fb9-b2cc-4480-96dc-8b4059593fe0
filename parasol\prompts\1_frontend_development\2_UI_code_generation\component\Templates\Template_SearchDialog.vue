<template>
  <v-dialog v-model="dialog">
    <!-- TODO: list-item-key には一覧のキーとなる項目名を指定してください。 -->
    <!-- TODO: dialog-title にダイアログのタイトルを設定します。 -->
    <app-search-dialog-tmpl
      v-model:page-index="pagination.page"
      v-model:sort="pagination.sort"
      :list-item-key="'id'"
      :list-items="data?.items"
      :list-items-total="data?.total"
      :can-pagination="true"
      :list-headers="headers"
      :page-size="pagination.pageSize"
      :on-search="search"
      :on-select="select"
      :loading="loading"
      dialog-title="検索ダイアログ"
    >
      <template #criteria>
        <!-- TODO: ここに検索条件を設定します。 -->
        <app-text-field v-model="searchCondition.branchName" label="部署名" />
      </template>
    </app-search-dialog-tmpl>
  </v-dialog>
</template>
<script lang="ts">
import { reactive, computed, ref } from 'vue'
import type { SearchOptions } from '@/components/shared/templates/AppSearchDialogTmpl.vue'
import { useFlagCondition } from '@/composables/shared/useFlagCondition'
// TODO: 利用するAPIクライアントと型をインポートします。
import type { FindBranchResult as FindApiResult } from '@/apiclient/branch'
import { useFindBranch as useFindApi } from '@/apiclient/branch'

const component = defineComponent({
  setup(_, { expose }) {
    // 検索条件を設定する画面項目とバインドするデータを定義します。
    const searchCondition = reactive({
      // TODO: ここに検索条件にバインドするプロパティを追加します。
      branchName: '',
    })

    // ダイアログの表示非表示
    const dialog = ref(false)

    // 検索結果
    const headers = [
      // TODO: ここに検索結果の項目を設定します。
      { title: 'Id', key: 'id', sortable: true },
      { title: '部署名', key: 'name', sortable: true },
    ]

    // ページング
    const pagination = reactive({
      page: 1,
      pageSize: 10,
      sort: [] as SortItem[],
    })

    // 検索条件の型を取得します。
    type CriteriaType = typeof searchCondition | null
    const criteria = ref<CriteriaType>(null)

    const inProgress = ref(false)
    // 複数のフラグでローディング状態を管理する場合は useFlagCondition を利用します。
    const { hasTrue: loading /* , addFlag */ } = useFlagCondition(inProgress)
    // 状態を追加する場合は addFlag を利用します。
    // addFlag(additionalConditionFlagRef)

    // =====================================================================================================================
    // APIクライアントの定義
    // =====================================================================================================================

    // 検索クエリを定義します。
    const query = computed(() => {
      const sort = pagination.sort.reduce((prev, curr) => {
        prev.push(`${curr.key}${curr.order === 'desc' ? ' desc' : ''}`)
        return prev
      }, [] as string[])
      const paginationQuery = {
        pageIndex: pagination.page,
        pageSize: pagination.pageSize,
        sort,
      }
      return {
        ...(criteria.value ?? {}),
        ...paginationQuery,
      }
    })

    // 検索APIクライアントを定義します。
    // TODO: 利用するAPIクライアントに変更してください。
    const { data, refresh } = useFindApi(query)

    // 検索の処理を行います。
    async function search(options: SearchOptions) {
      // ソートの変更かページングの変更の場合で、かつ検索条件が空(まだ一度も検索されていない)の場合は検索しない
      if (
        (options.isSortChangeRequest || options.isPaginationRequest)
        && !criteria.value
      ) {
        return true
      }
      inProgress.value = true
      try {
        // ソート/ページング以外の場合は検索条件を更新する
        if (!options.isSortChangeRequest && !options.isPaginationRequest) {
          // reactive / ref の状態を要調査
          criteria.value = Object.assign({}, searchCondition)
        }
        await refresh()
        return true
      } finally {
        inProgress.value = false
      }
    }

    // 検索結果から選択したときの処理を行います。
    function select(options: { item: any }) {
      // ダイアログを閉じます。
      dialog.value = false
      // 呼び出し元に検索結果を返します。
      resolve.value?.({
        isOk: true,
        data: {
          id: options.item.id,
          name: options.item.name,
        },
      })
      return true
    }

    const resolve = ref<((value: any) => void) | undefined>()
    // ダイアログを開きます。
    function open() {
      searchCondition.branchName = ''
      // 検索条件を初期化します。
      // searchCondition.name = ''
      // 検索結果を初期化します。
      data.value = {} as FindApiResult // TODO: 結果の型にキャストします。
      dialog.value = true
      return new Promise((_resolve) => {
        resolve.value = _resolve
      })
    }
    expose({
      open,
    })
    return {
      dialog,
      headers,
      pagination,
      searchCondition,
      data,
      loading,
      open,
      search,
      select,
    }
  },
})

// 利用側で expose したメソッドを認識しないため、こちらで型定義を行って export する
export type BranchSearchDialogType = {
  // TODO: 定義した open メソッドと一致するように型を定義します。
  open(): Promise<{
    isOk: boolean
    data: {
      // 返す結果の型を定義します。
      id: string
      name: string
    }
  }>
}

export default component
</script>
