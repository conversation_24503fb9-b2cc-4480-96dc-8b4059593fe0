import { QuillDeltaToHtmlConverter } from 'quill-delta-to-html'
import { Quill } from '@vueup/vue-quill'

/**
 * quill-editorのデルタ形式をhtmlに変換します。
 * @param deltaString
 * @returns html
 */
export const quillDeltaToHtml = (deltaString: string) => {
  const delta = JSON.parse(deltaString)
  const cfg = {}
  const converter = new QuillDeltaToHtmlConverter(delta.ops, cfg)
  converter.renderCustomWith((customOp: any) => {
    // Conditionally renders blot of mention type
    if (customOp.insert.type === 'mention') {
      // Get Mention Blot Data
      const mention = customOp.insert.value

      // Template Return Data
      const mentionHtml = ''
      return mentionHtml
        .concat('<span class="mention"')
        .concat(` data-index="${mention.index}"`)
        .concat(` data-denotation-char="${mention.denotationChar}"`)
        .concat(` data-id="${mention.id}"`)
        .concat(` data-link="${mention.link}"`)
        .concat(` data-value='${mention.value}'`)
        .concat(` data-mention-type='${mention.mentionType}'`)
        .concat('>')
        .concat(`<span contenteditable="false">${mention.value}</span>`)
        .concat('</span>')
    }
    return ''
  })
  return converter.convert()
}

/**
  htmlをquill-editorのDELTA形式に変換します。
  変換用のパッケージ(node_quill_converter)もありましたが、メンテナンスが数年前で止まっているため不採用としました。
  下記リンクのissueを参考に実装しています。
  https://github.com/quilljs/quill/issues/1551
 * @param html
 * @returns デルタ形式
 */
export const htmlToQuillDelta = (html: string | undefined) => {
  const div = document.createElement('div')
  div.setAttribute('id', 'htmlToDelta')
  div.innerHTML = `<div id="quillEditor" style="display:none">${html}</div>`
  document.body.appendChild(div)
  const quill = new Quill('#quillEditor', {
    theme: 'snow',
  })
  const delta = quill.getContents()
  document.getElementById('htmlToDelta')?.remove()
  return delta
}

/**
 * htmlからタグを除いた文字列を返します
 * @param html
 * @returns htmlからタグを除いた文字列
 */
export const htmlToPlainText = (html: string) => {
  const div = document.createElement('div')
  div.innerHTML = html

  if (!div.textContent && !div.innerText) {
    return undefined
  }
  return div.textContent || div.innerText || ''
}

/**
 * Html形式のメンション対象からメンション対象のIDを取得します。
 * @param mentionTargetsHtml  Html形式のメンション対象
 * @param mentionType メンション種別
 * @returns メンション対象IDのリスト
 */
export const getMentionTargetIds = (
  mentionTargetsHtml: string,
  mentionType: 'staff' | 'team',
) => {
  const delta = htmlToQuillDelta(mentionTargetsHtml)
  const targets = []
  const deltaOpsLength = delta?.ops?.length ?? 0
  for (let index = 0; index < deltaOpsLength; index++) {
    if (delta.ops[index].insert?.mention) {
      if (delta.ops[index].insert?.mention.mentionType === mentionType) {
        targets.push(delta.ops[index].insert?.mention)
      }
    }
  }
  return targets?.map((x) => x.id)
}

/**
 * Html形式のメンション対象からメンション対象リストを取得します。
 * @param mentionTargetsHtml
 * @returns メンション対象リスト
 */
export const getMentionTargets = (mentionTargetsHtml?: string | null) => {
  if (!mentionTargetsHtml) return []

  const mentionTargetsDelta = htmlToQuillDelta(mentionTargetsHtml)

  const targets = []
  const deltaOpsLength = mentionTargetsDelta?.ops?.length ?? 0
  for (let index = 0; index < deltaOpsLength; index++) {
    if (mentionTargetsDelta.ops[index].insert?.mention) {
      targets.push(mentionTargetsDelta.ops[index].insert?.mention)
    }
  }
  return targets
}
