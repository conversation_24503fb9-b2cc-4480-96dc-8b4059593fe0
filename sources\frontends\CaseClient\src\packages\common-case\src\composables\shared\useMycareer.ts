export const useMycareer = () => {
  // 設定
  const config = useRuntimeConfig()

  // ログインユーザー取得
  const { hasLoggedInUser } = useUser()

  /**
   * Myキャリアが使えるかどうか
   * @returns boolean
   */
  const getIsMyCareerAccessSucceed = () => {
    // userIDを持っていなくて && 本番環境だったら使えない
    if (
      !hasLoggedInUser.value &&
      !config.public.isDev &&
      !config.public.isStaging
    ) {
      return false
    }
    return true
  }

  return {
    getIsMyCareerAccessSucceed,
  }
}
