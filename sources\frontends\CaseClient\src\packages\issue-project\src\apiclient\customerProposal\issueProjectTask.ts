import { useNuxtApp } from 'nuxt/app'
import { parseISO } from 'date-fns'
import { z } from 'zod'
import { isString } from '@hox/base/src/utils/shared/is'
import {
  createReturnableExecuter,
  withDefaultFetchOptions,
} from '@hox/base/src/apiclient/shared'
import type {
  Pagination,
  ApiResult,
} from '@hox/base/src/apiclient/shared/types'

// ファイルのスキーマ
export const issueProjectTaskFileSchema = z.object({
  id: z.string().nullish(),
  fileName: z.string().nullish(),
  filePath: z.string().nullish(),
  issueProjectTaskId: z.string().nullish(),
})
export const issueProjectTaskFilesSchema = z.array(issueProjectTaskFileSchema)

// ファイルの型を定義します
export type IssueProjectTaskFile = z.infer<typeof issueProjectTaskFileSchema>
export type IssueProjectTaskFiles = z.infer<typeof issueProjectTaskFilesSchema>

// 主として扱うデータのスキーマを定義します
export const issueProjectTaskSchema = z.object({
  id: z.string(),
  customerIdentificationId: z.string().uuid(),
  requestedAt: z.date().nullish(),
  expiredAt: z.date(),
  staffId: z.string(),
  overview: z.string().max(30),
  detail: z.string().nullish(),
  issueProjectTaskStatus: z.number(),
  issueProjectId: z.string(),
  taskFiles: issueProjectTaskFilesSchema.nullish(),
  version: z.string(),
})

// 主として扱うデータのデータ型を定義します。
export type IssueProjectTask = z.infer<typeof issueProjectTaskSchema>

// =====================================================================================================================
// APIクライアントの定義 (検索)
// =====================================================================================================================

// 検索するデータのスキーマを定義します
export const issueProjectTaskSchemaForSearch = z.object({
  id: z.string(),
  issueProjectId: z.string(),
  requestedAt: z.string().datetime().nullish(),
  customerIdentificationId: z.string().uuid(),
  customerName: z.string().nullish(),
  IssueInformationOverview: z.string().nullish(),
  expiredAt: z.string().nullish(),
  issueProjectTaskStatus: z.number(),
  issueProjectStaffId: z.string().nullish(),
  issueProjectStaffName: z.string().nullish(),
  staffId: z.string(),
  staffName: z.string().nullish(),
  overview: z.string(),
  isAccessRestricted: z.boolean(),
  version: z.string(),
})

// 検索するデータのデータ型を定義します
export type IssueProjectTaskForSearch = z.infer<
  typeof issueProjectTaskSchemaForSearch
>

// 検索条件の型を定義します。
export type FindIssueProjectTaskCriteria = {
  issueProjectId?: string
  staffIds?: string[] | null
  issueProjectTaskStatus?: number[]
  customerStaffIds?: string[] | null
  issueProjectStaffIds?: string[] | null
  registrantIds?: string[] | null
  expiredAtFrom?: string
  expiredAtTo?: string
  customerName?: string | null
} & Pagination

export type FindIssueProjectTaskResultItem = IssueProjectTaskForSearch
// 検索結果の型にページングの情報を追加します。
export type FindIssueProjectTaskResult = ApiResult<FindIssueProjectTaskResultItem>

/**
 * データを検索する
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useFindIssueProjectTask(
  query: Ref<FindIssueProjectTaskCriteria>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<FindIssueProjectTaskResult>(
    useFetch(
      () =>
        $endpoints.default.get('/ibp-customer-proposal/v1.0/issueprojecttask'),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (取得)
// =====================================================================================================================

/**
 * データを取得する
 * @param id Ref<T> 取得するデータのキー
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useGetIssueProjectTask(id: Ref<string>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectTask>(
    useFetch(
      () =>
        $endpoints.default.get(
          `/ibp-customer-proposal/v1.0/issueprojecttask/${id.value}`,
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          if (isString(data.requestedAt)) {
            data.requestedAt = parseISO(data.requestedAt)
          }
          if (isString(data.expiredAt)) {
            data.expiredAt = parseISO(data.expiredAt)
          }

          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (追加)
// =====================================================================================================================

// IFormFileのスキーマ
export const IFormFileSchema = z.object({
  contentType: z.string(),
  length: z.number().int(),
  name: z.string(),
  fileName: z.string(),
})

// 追加するデータのスキーマを定義します。
export const issueProjectTaskSchemaForCreate = z.object({
  customerIdentificationId: z.string(),
  requestedAt: z.date().nullish(),
  expiredAt: z.date(),
  staffId: z.string(),
  overview: z.string().max(30),
  detail: z.string().max(200).nullish(),
  issueProjectTaskStatus: z.number(),
  issueProjectId: z.string(),
  customerName: z.string(),
  uploadFiles: issueProjectTaskFilesSchema.nullish(),
  updatedDateTime: z.date(),
  teamStaffIds: z.array(z.string()).nullish(),
  teamId: z.string().nullish(),
})

// 追加するデータの型を作成します。
export type IssueProjectTaskForCreate = z.infer<
  typeof issueProjectTaskSchemaForCreate
>

/**
 * 作成処理を行う
 * @param body Ref<T> 対象となる型からサービス側で生成されるプロパティを除外した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePostIssueProjectTask(body: Ref<FormData>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectTask>(
    useFetch(
      () =>
        $endpoints.default.get('/ibp-customer-proposal/v1.0/issueprojecttask'),
      withDefaultFetchOptions({
        method: 'POST',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          if (isString(data.requestedAt)) {
            data.requestedAt = parseISO(data.requestedAt)
          }
          if (isString(data.expiredAt)) {
            data.expiredAt = parseISO(data.expiredAt)
          }
          if (isString(data.updatedDateTime)) {
            data.updatedDateTime = parseISO(data.updatedDateTime)
          }
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (更新)
// =====================================================================================================================

// 更新用のデータのスキーマを定義します。
export const issueProjectTaskSchemaForUpdate = z.object({
  id: z.string(),
  requestedAt: z.date().nullish(),
  expiredAt: z.date(),
  staffId: z.string(),
  overview: z.string().max(30),
  detail: z.string().max(200).nullish(),
  customerName: z.string().nullish(),
  issueProjectTaskStatus: z.number(),
  uploadFiles: issueProjectTaskFilesSchema.nullish(),
  updatedDateTime: z.date().nullish(),
  teamStaffIds: z.array(z.string()).nullish(),
  version: z.string(),
})

// 更新用の型を作成します。
export type IssueProjectTaskForUpdate = z.infer<
  typeof issueProjectTaskSchemaForUpdate
>

/**
 * 更新処理を行う
 * @param body Ref<T> 対象となる型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function usePutIssueProjectTask(body: Ref<FormData>) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<IssueProjectTask>(
    useFetch(
      () =>
        $endpoints.default.get('/ibp-customer-proposal/v1.0/issueprojecttask'),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          if (isString(data.requestedAt)) {
            data.requestedAt = parseISO(data.requestedAt)
          }
          if (isString(data.expiredAt)) {
            data.expiredAt = parseISO(data.expiredAt)
          }
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (削除)
// =====================================================================================================================

// 削除のスキーマを定義します
export const issueProjectTaskSchemaForDelete = issueProjectTaskSchema.pick({
  id: true,
  version: true,
})

export type IssueProjectTaskForDelete = z.infer<
  typeof issueProjectTaskSchemaForDelete
>

/**
 * 削除処理を行う
 * @param body Ref<T> 対象となる型から削除に必要な情報を抽出した型
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDeleteIssueProjectTask(
  body: Ref<IssueProjectTaskForDelete>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter(
    useFetch(
      () =>
        $endpoints.default.get('/ibp-customer-proposal/v1.0/issueprojecttask'),
      withDefaultFetchOptions({
        method: 'DELETE',
        body,
        scope: $endpoints.default.scope,
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (getOptions)
// =====================================================================================================================

export type IssueProjectTaskStatusType = {
  text: string
  value: string
}

export type GetOptionsResult = {
  issueProjectTaskStatusList: Array<IssueProjectTaskStatusType>
}

/**
 * データを取得する
 */
export function useGetOptions() {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<GetOptionsResult>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojecttask/get-options',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (ダウンロード)
// =====================================================================================================================

// ダウンロードの型を定義します
export type downloadIssueProjectTaskFile = {
  issueProjectTaskId: string
  fileName: string
}

/**
 * ダウンロードする
 * @param query Ref<T> 取得するデータの抽出条件
 * @returns useFetchの戻り値に対して、createReturnableExecuterを適用したもの
 */
export function useDownloadIssueProjectTaskFile(
  query: Ref<downloadIssueProjectTaskFile>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<Blob>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojecttask/download',
        ),
      withDefaultFetchOptions({
        method: 'GET',
        query,
        scope: $endpoints.default.scope,
        transform(data: any) {
          // データの変換などを行います。日付文字列から日付型への変換などが必要な場合はここで行います。
          return data
        },
      }),
    ),
  )
}

// =====================================================================================================================
// APIクライアントの定義 (担当者更新)
// =====================================================================================================================

// BatchUpdateStaffCommandの型
export type IssueProjectTaskBatchUpdateStaffCommand = {
  customerIdentificationIdList: string[]
  currentStaffId: string
  changeStaffId: string
}

export function useIssueProjectTaskBatchUpdateCustomerStaff(
  body: Ref<IssueProjectTaskBatchUpdateStaffCommand>,
) {
  const { $endpoints } = useNuxtApp()
  return createReturnableExecuter<string>(
    useFetch(
      () =>
        $endpoints.default.get(
          '/ibp-customer-proposal/v1.0/issueprojecttask/batch-update-staff',
        ),
      withDefaultFetchOptions({
        method: 'PUT',
        body,
        scope: $endpoints.default.scope,
        transform(data: any) {
          return data
        },
      }),
    ),
  )
}
