import * as z from 'zod'
// 利用する列挙型のインポート
import { accountEnum } from '../enums/accountEnum'
// 内部エンティティのインポート
import { settingSchema } from './settingEntity'

// エンティティの定義
export const sampleEntitySchema = z.object({
  /** 
   * ID (Id)
   * 仕様: `GUID`
   */
  id: z.string(),

  /**
   * 氏名
   * 仕様: `KANJI_MAX_128`
   * TODO: 文字列の場合はMINとMAXには桁数を設定すること
   */
  name: z.string().max(128).nullish(),

  /**
   * SNS
   * 仕様: `URI_FORMAT`
   */
  sns: z.string().nullish(),

  /**
   * 年齢
   * 仕様: `NUMERIC_MIN_0_MAX_3`
   * TODO: 数値の場合はMINとMAXにはその桁の最小値及び最大値を設定すること
   */
  age: z.number().min(0).max(999).nullish(),


  /**
   * 会員コード
   * 仕様: `ALPHANUMERIC_MAX_10`
   */
  memberCode: z.string().max(10).nullish(),

  /**
   * 登録日
   * 仕様: `DATE_FORMAT`
   */
  registrationDate: z.date().nullish(),

  /**
   * 郵便番号
   * 仕様: `POSTAL_CODE_FORMAT`
   */
  postalCode: z.number().size(7).nullish(),

  /**
   * 仮登録フラグ
   * 仕様: `BOOLEAN`
   */
  isTemporary: z.boolean().nullish(),
  
  /**
   * アカウント種別
   * 仕様: 列挙型：アカウント種別
   */
  accountType: z.enum(accountEnum).nullish(),

  /**
   * 設定情報
   * 仕様： エンティティ：設定
   */
  setting: settingSchema,

})

// エンティティ型の定義
export type sampleEntity = z.infer<typeof sampleEntitySchema>
