### Shared.AspNetCore

## 概要

Shared.AspNetCoreライブラリは、ASP.NET Coreアプリケーション向けの拡張機能を提供するライブラリです。WebアプリケーションやWeb APIで共通利用されるミドルウェア、フィルター、認証、診断機能などを提供し、ASP.NET Coreアプリケーションの開発効率と品質向上を支援します。

## 主要機能

### ミドルウェア
- **ユーザー情報付与**: HTTPリクエストごとにユーザー情報をログに自動付与
- **トークン転送**: クッキーからAuthorizationヘッダーへのトークン転送
- **パイプライン拡張**: 共通処理のミドルウェア化

### 診断・ヘルスチェック
- **例外ハンドリング**: API例外の一元処理
- **ヘルスチェック**: IETFドラフト準拠のJSON形式レスポンス
- **設定デバッグ**: 設定値の可視化・トラブルシュート

### MVC拡張
- **API隠蔽**: 内部APIの非表示化（Backdoor制御）
- **モデルバインディング**: フォームデータ、ソート条件の柔軟なバインド
- **フィルター**: 開発用・リソース応答フィルター

### 認証・セキュリティ
- **開発用認証**: ローカル・検証環境での認証テスト
- **証明書認証**: Key Vault等の証明書検証
- **APIM連携**: サブスクリプションキー、ユーザー情報の付与

### YARP拡張
- **API Gateway連携**: リバースプロキシでの認証情報転送
- **ヘッダー変換**: 認証・ユーザー情報のヘッダー付与

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| AppendUserInfoToLogMiddleware | ミドルウェア | HTTPリクエストごとに現在のユーザー情報をログへ自動付与するミドルウェア | 監査ログ・アクセスログのユーザー特定、セキュリティ監査、トレーシング |
| ApiExceptionHandler | 例外ハンドラ | API例外の一元処理 | エラーハンドリング標準化 |
| HealthResponseWriter | ヘルスチェック | ヘルスチェックの結果をIETFドラフト準拠のJSON形式でHTTPレスポンスに出力 | システム監視・外部監視連携・運用自動化 |
| WebApiExtensions | 拡張メソッド | `Nut.Results.Result`型をASP.NET Coreの`IActionResult`へ変換する拡張メソッド群 | APIの標準化・エラーハンドリングの一元化・RESTfulレスポンス実装 |
| IApplicationBuilderExtensions / IServiceCollectionExtensions | 拡張メソッド | `IApplicationBuilder`/`IServiceCollection`の拡張 | ミドルウェアの組み込み・サービス登録の簡素化、拡張性向上 |
| BackdoorHideApiConvention | MVC拡張 | コントローラーの名前空間に"Backdoor"が含まれる場合、そのアクションをApiExplorerで非表示にするMVC拡張 | 内部/開発用APIの非公開化、APIドキュメントのセキュリティ強化 |
| FormDataJsonModelBinder / SortModelBinder / SortModelBinderProvider | モデルバインダ | フォームデータやソート条件をC#モデルにバインドするためのカスタムバインダ群 | 複雑な入力値の自動バインド、APIの柔軟なパラメータ受け入れ |
| DevTimeOnlyBackdoorFilter / ResourceDummyTextResponseFilterAttribute | フィルタ | 開発用・リソース応答フィルタ | テスト・デバッグ |
| ApiExceptionHandlerOptions | 設定 | 例外ハンドラの設定 | エラー応答制御 |
| ConfigurationRootExtensions | 設定拡張 | `IConfigurationRoot`の拡張メソッド | 設定値のデバッグ・運用時の設定確認 |
| ApimCertificateAuthenticationSetup / ApimDefaults / ApimCertificateAuthenticationOptions | API管理 | APIM認証設定 | API認証強化 |
| DevAuthAuthenticationHandler | 認証 | 開発用の認証を行う認証ハンドラ | ローカル・検証環境での認証テスト、開発効率化 |
| DevAuthAuthenticationSchemeOptions | 認証設定 | `DevAuthAuthenticationHandler`の動作を制御するためのオプション | 開発用認証のカスタマイズ |
| DevAuthDefaults | 定数 | 開発用認証で利用するデフォルト値を定義 | 開発用認証の標準化・共通化 |
| AuthenticationBuilderExtensions | 認証拡張 | 開発用認証（DevAuth）を`AuthenticationBuilder`へ簡単に追加する拡張メソッド | DevAuth認証の導入簡素化 |
| CertificateValidationService | 証明書認証 | Key Vault等に格納されたX.509証明書の有効性・サムプリント検証を行うサービス | APIやサービス間通信の証明書認証、セキュリティ強化 |
| CertificateValidationServiceOptions | 設定 | `CertificateValidationService`の動作設定 | サービスごとの証明書検証要件の柔軟な設定 |
| CertValidation | 設定 | 証明書検証に利用するサムプリントリスト等の設定クラス | サムプリントによる証明書制御 |
| AbstractAppendKeyAuthTransformFactory / AppendApimSubscriptionKeyTransformFactory | YARP拡張 | サブスクリプションキー付与 | API Gateway連携 |
