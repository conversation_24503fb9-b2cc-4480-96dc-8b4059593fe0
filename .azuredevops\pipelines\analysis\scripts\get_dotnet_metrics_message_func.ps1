# 行数の閾値を設定(error, warning)
$sourceLineThreshold = [int[]](100, 70)

# 複雑度の閾値を設定(error, warning)
$cyclomaticComplexityThreshold = [int[]](20, 10)

# 対象件数を設定
$topN = 5

# アイコンを設定
$errorIcon = "❌"
$warningIcon = "⚠️"
$safeIcon = "✅"

<#
.SYNOPSIS
    指定された値が閾値を上回っているかどうかを判定し、エラーまたは警告のアイコンを返します。
.PARAMETER value
    判定する値。
.PARAMETER thresholds
    エラーおよび警告の閾値を含む配列。
.PARAMETER errorOrWarningStatus
    現在のエラーまたは警告のステータス。
.RETURNS
    アイコンと更新されたエラーまたは警告のステータスを返します。
#>

function Get-ErrorOrWarningIcon-Greater-Than {
    param(
        [int]$value,
        [int[]]$thresholds,
        [int]$errorOrWarningStatus
    )

    if ($value -gt $thresholds[0]) {
        # エラーの場合は、エラーアイコンと、引数で渡された$errorOrWarningStatusの値または2の大きい方を返す
        return $errorIcon, [math]::Max($errorOrWarningStatus, 2)
    }
    elseif ($value -gt $thresholds[1]) {
        # 警告の場合は、警告アイコンと、引数で渡された$errorOrWarningStatusの値または1の大きい方を返す
        return $warningIcon, [math]::Max($errorOrWarningStatus, 1)
    }
    else {
        # エラーでも警告でもない場合は、引数で渡された$errorOrWarningStatusの値を返す
        return "", $errorOrWarningStatus
    }
}

<#
.SYNOPSIS
    変更されたファイルのコードメトリクスを解析し、結果のメッセージを生成します。
.PARAMETER changeStats
    変更されたファイルの統計情報を含むオブジェクト。
.PARAMETER analysisResultDirectory
    静的分析結果が保存されているディレクトリのパス。
.RETURNS
    生成されたコードメトリクスメッセージを返します。
#>

function Get-DotNet-Metrics-Message {
    param(
        [PSCustomObject]$changeStats,
        [string]$analysisResultDirectory
    )

    # 変更されたファイルの一覧を取得
    $modifiedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "A" -or $_.ShortStatus -eq "M" } | Select-Object -ExpandProperty FileName
    $renamedFiles = $changeStats.Values | Where-Object { $_.ShortStatus -eq "R" } | Select-Object -ExpandProperty NewFileName
    $targetFiles = $modifiedFiles + $renamedFiles

    $metricsReportPath = Join-Path $analysisResultDirectory "metrics"

    $methods = @()

    Get-ChildItem -Path $metricsReportPath -Filter "*.Metrics.xml" | ForEach-Object {
        # MetricsReport(Xml)を読み込む
        $metricsReportXml = [xml](Get-Content $_.FullName)

        foreach ($targetFile in $targetFiles) {
            $targetPath = Convert-Path $targetFile

            # MetricsReport(Xml)のFileが変更されたファイルのパスと一致するメソッドをリストに格納
            $methods += $metricsReportXml.CodeMetricsReport.Targets.Target.Assembly.Namespaces.Namespace.Types.NamedType.Members.Method | 
            Where-Object { $_.File -ieq $targetPath } | 
            Select-Object *,
            @{Name = "ClassName"; Expression = { $_.ParentNode.ParentNode.Name } }, 
            @{Name = "TargetFile"; Expression = { $targetFile } }
        }
    }

    $sourceLinesMetrixMessage = @()
    $errorOrWarningStatusSourceLines = [int]0

    # $methodsを行数でソートしてtopN件を取得する
    $topNSourceLinesMethods = $methods | Sort-Object { [int]$_.Metrics.Metric[3].Value } -Descending | Select-Object -First $topN

    foreach ($method in $topNSourceLinesMethods) {
        $sourceLines = [int]$($method.Metrics.Metric | Where-Object { $_.Name -ieq "SourceLines" } | Select-Object -ExpandProperty Value) 
        $executableLines = [int]$($method.Metrics.Metric | Where-Object { $_.Name -ieq "ExecutableLines" } | Select-Object -ExpandProperty Value)

        $sourceLinesIcon, $errorOrWarningStatusSourceLines = Get-ErrorOrWarningIcon-Greater-Than $sourceLines $sourceLineThreshold $errorOrWarningStatusSourceLines

        # $sourceLinesIconが空の場合はスキップ
        if ([string]::IsNullOrEmpty($sourceLinesIcon)) {
            continue
        }

        # 戻り値、パラメータを除去してメソッド名のみを抽出する
        $methodName = $method.Name -replace ".*\.(.*)\((.*)", '$1'

        $sourceLinesMetrixMessage += "1. [$($method.ClassName)](?_a=files&path=/$([URI]::EscapeUriString($method.TargetFile))).$($methodName)($($sourceLines)$sourceLinesIcon, $($executableLines))`r`n"
    }

    $complexityMetrixMessage = @()
    $errorOrWarningStatusComplexity = [int]0

    # $methodsをサイクロマティック複雑度でソートしてtopN件を取得する
    $topNComplexityMethods = $methods | Sort-Object { [int]$_.Metrics.Metric[1].Value } -Descending | Select-Object -First $topN

    foreach ($method in $topNComplexityMethods) {
        $cyclomaticComplexity = [int]$($method.Metrics.Metric | Where-Object { $_.Name -ieq "CyclomaticComplexity" } | Select-Object -ExpandProperty Value) 
        $maintainabilityIndex = [int]$($method.Metrics.Metric | Where-Object { $_.Name -ieq "MaintainabilityIndex" } | Select-Object -ExpandProperty Value)
        $classCoupling = [int]$($method.Metrics.Metric | Where-Object { $_.Name -ieq "ClassCoupling" } | Select-Object -ExpandProperty Value) 

        $cyclomaticComplexityIcon, $errorOrWarningStatusComplexity = Get-ErrorOrWarningIcon-Greater-Than $cyclomaticComplexity $cyclomaticComplexityThreshold $errorOrWarningStatusComplexity

        # $cyclomaticComplexityIconが空の場合はスキップ
        if ([string]::IsNullOrEmpty($cyclomaticComplexityIcon)) {
            continue
        }

        # 戻り値、パラメータを除去してメソッド名のみを抽出する
        $methodName = $method.Name -replace ".*\.(.*)\((.*)", '$1'

        $complexityMetrixMessage += "1. [$($method.ClassName)](?_a=files&path=/$([URI]::EscapeUriString($method.TargetFile))).$($methodName)($($cyclomaticComplexity)$cyclomaticComplexityIcon, $($maintainabilityIndex), $($classCoupling))`r`n"
    }

    $resultSourceLinesMessage = @()

    if ([string]::IsNullOrEmpty($sourceLinesMetrixMessage)) {
        $resultSourceLinesMessage = @"
Waring, NG なし

"@
    }
    else {
        $resultSourceLinesMessage = @"
<details>
<summary>詳細</summary>

$sourceLinesMetrixMessage
</details>

"@
    }

    $resultComplexityMessage = @()

    if ([string]::IsNullOrEmpty($complexityMetrixMessage)) {
        $resultComplexityMessage = @"
Waring, NG なし

"@
    }
    else {
        $resultComplexityMessage = @"
<details>
<summary>詳細</summary>

$complexityMetrixMessage
</details>

"@
    }

    return @"
### コードメトリクス(行数, 実行可能行数) $(($safeIcon, $warningIcon, $errorIcon)[$errorOrWarningStatusSourceLines])
$resultSourceLinesMessage
### コードメトリクス(複雑度, 保守容易性, クラス結合) $(($safeIcon, $warningIcon, $errorIcon)[$errorOrWarningStatusComplexity]) 
$resultComplexityMessage
"@
}
