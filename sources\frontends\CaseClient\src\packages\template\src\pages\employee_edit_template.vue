<template>
  <app-edit-page-tmpl
    :sections="sections"
    :loading="loading"
    :on-save="save"
    :on-remove="remove"
    :can-remove="!addMode"
  >
    <!--スロットは sections プロパティに設定したセクションのキーを指定してください。
          スロット名は section- から始めて、キーを指定してください。
          各セクションのルートにはほとんどの場合 AppEditItemGroupTmpl を利用します。-->
    <!-- TODO: 必要なセクションを追加し編集項目を設定します。 -->
    <!--
      <template #section-basic>
        <app-edit-item-group-tmpl>
          <template #title>基本情報</template>
          <v-container fluid>
            <app-text-field
              v-model="targetData.id"
              label="ID"
              :disabled="true"
              data-testid="id"
            />
            <app-text-field
              v-model="targetData.nameKanji"
              label="名前(漢字)"
              data-testid="nameKanji"
              required-mark
              :error-messages="errorMessages?.nameKanji"
              @update:model-value="validators.validateItem('nameKanji')"
              @blur="validators.validateItem('nameKanji')"
            />
          </v-container>
        </app-edit-item-group-tmpl>
      </template>
       -->
    <!-- //#if(!isInTemplateExpanding || includeSample ) -->
    <template #section-basic>
      <app-edit-item-group-tmpl>
        <template #title>基本情報</template>
        <v-container fluid>
          <app-text-field
            v-model="targetData.id"
            label="ID"
            :disabled="true"
            data-testid="id"
          />
          <app-text-field
            v-model="targetData.nameKanji"
            label="名前(漢字)"
            data-testid="nameKanji"
            required-mark
            :error-messages="errorMessages?.nameKanji"
            @update:model-value="validators.validateItem('nameKanji')"
            @blur="validators.validateItem('nameKanji')"
          />
          <app-text-field
            v-model="targetData.nameKana"
            label="名前(カナ)"
            required-mark
            data-testid="nameKana"
            :error-messages="errorMessages?.nameKana"
            @update:model-value="validators.validateItem('nameKana')"
            @blur="validators.validateItem('nameKana')"
          />
          <app-text-field
            v-model="targetData.address"
            label="住所"
            required-mark
            data-testid="address"
            :error-messages="errorMessages?.address"
            @update:model-value="validators.validateItem('address')"
            @blur="validators.validateItem('address')"
          />
          <app-popup-date-picker
            v-model="targetData.birthDate"
            label="誕生日"
            display-text-format="yyyy/MM/dd"
            required-mark
            data-testid="birthDate"
            :error-messages="errorMessages?.birthDate"
            @update:model-value="validators.validateItem('birthDate')"
            @blur="validators.validateItem('birthDate')"
          />
          <app-text-field
            v-model="targetData.branchName"
            readonly
            label="部署"
            required-mark
            clearable
            append-icon="mdi-magnify"
            :error-messages="errorMessages?.branchId"
            @update:model-value="validators.validateItem('branchId')"
            @blur="validators.validateItem('branchId')"
            @click:append="showBranchSearchDialog"
            @click:clear="(targetData as any).branchId = undefined"
          />
        </v-container>
      </app-edit-item-group-tmpl>
    </template>
    <template #section-qualifications>
      <app-edit-item-group-tmpl>
        <app-list-edit
          :loading="loading"
          :list-item-key="'_tempKey'"
          :list-items="targetData.qualifications"
          :list-headers="qualificationHeaders"
          :error-messages="errorMessages?.qualifications"
          :on-add="addQualification"
          :on-edit="editQualification"
          :on-remove="removeQualifications"
        />
      </app-edit-item-group-tmpl>
    </template>
    <!--//#endif -->
  </app-edit-page-tmpl>
  <!-- //#if(!isInTemplateExpanding || includeSample ) -->
  <branch-search-dialog ref="branchSearch" />
  <qualification-edit-dialog ref="qualificationEdit" />
  <!--//#endif -->
  <app-confirm-dialog ref="confirmDialog" />
</template>
<script setup lang="ts">
import { useAppToasts } from '@hox/base/src/composables/shared/useAppToasts'
import { useFlagCondition } from '@hox/base/src/composables/shared/useFlagCondition'
import type { AppConfirmDialogType } from '@hox/base/src/components/shared/molecules/AppConfirmDialog.vue'
// #if(!isInTemplateExpanding || includeSample)
// #endif
import { useWatchDataChanges } from '@hox/base/src/composables/shared/useWatchDataChanges'
import { confirmBeforeUnload } from '@hox/base/src/utils/shared/confirmBeforeUnload'
// TODO: 利用するAPIクライアントと型をインポートします。
// APIクライアント側に型定義やスキーマがある場合は、それらを利用します。
// 標準で展開されるAPIクライアントはテンプレート展開用のダミークライアントです。アプリケーションコードでは利用できません。
// #if(!isInTemplateExpanding || includeSample)
// //#else
// import type {
//  EmptyForSave as ForSave,
//  EmptyForCreate as ForCreate,
// } from '@/apiclient/shared/empty'
// import {
//  useGetEmpty as useGetApi,
//  usePostEmpty as usePostApi,
//  usePutEmpty as usePutApi,
//  useDeleteEmpty as useDeleteApi,
//  emptySchemaForCreate as schemaForCreate,
//  emptySchemaForSave as schemaForSave,
// } from '@/apiclient/shared/empty'
// #endif

import type { SectionConfig } from '@hox/base/src/components/shared/templates/AppEditPageTmpl.vue'
import { useValidation } from '@hox/base/src/composables/shared/useValidation'
import { defaultInstance } from '@hox/base/src/utils/shared/zodDefaultInstance'
import {
  useGetEmployee as useGetApi,
  useGetEmployeeQualifications,
  usePostEmployee as usePostApi,
  usePutEmployee as usePutApi,
  useDeleteEmployee as useDeleteApi,
  employeeSchemaForCreate as schemaForCreate,
  employeeSchemaForSave as schemaForSave,
} from '@/packages/template/src/apiclient/employee_template'
import type {
  EmployeeForSave as ForSave,
  EmployeeForCreate as ForCreate,
} from '@/packages/template/src/apiclient/employee_template'
import type { QualificationEditDialogType } from './../components/employee/organisms/QualificationEditDialog.vue'
import type { BranchSearchDialogType } from './../components/branch/organisms/BranchSearchDialog.vue'

// import { useRouterEx } from '@hox/base/src/composables/shared/useRouterEx'
// import { useBreadcrumbs } from '~/composables/shared/useBreadcrumbs'
// import type { Breadcrumb } from '~/composables/shared/useBreadcrumbs'

// =====================================================================================================================
// 画面全体で利用するデータの定義
// =====================================================================================================================

const { primary: primaryToast, error: errorToast } = useAppToasts()
const router = useRouter()

// =====================================================================================================================
// 画面の定義
// =====================================================================================================================

// タイトルを指定します。(windowのタイトルに設定されます。)
useHead({
  // TODO: 画面のタイトルを設定してください。
  // #if(!isInTemplateExpanding || includeSample)
  title: '行員編集',
  // //#else
  // title: '編集',
  // #endif
})
// タイトルを指定します。(パンくずリストに設定されます。)
definePageMeta({
  // TODO: 画面のタイトルを設定してください。
  // #if(!isInTemplateExpanding || includeSample)
  title: '行員編集',
  // //#else
  // title: '編集',
  // #endif
})

// パンくずリストは手動でも設定できます。
// ここでは、パンくずリストに履歴が無い場合に、履歴に検索画面を追加する例を示します。
// const { getRouteInfo } = useRouterEx()
// const { histories, replaceHistories } = useBreadcrumbs()
// const routeInfoToBreadcrumbs = (routeInfo: any): Breadcrumb => {
//   return {
//     title: routeInfo.title,
//     path: routeInfo.path,
//     shortPath: routeInfo.path,
//   }
// }
// if (histories.value.length === 0) {
//   replaceHistories([
//     routeInfoToBreadcrumbs(getRouteInfo('employee')),
//   ])
// }

// セクションを定義します。
const sections: SectionConfig[] = [
  // TODO: セクションを定義してください。
  // #if(!isInTemplateExpanding || includeSample)
  { key: 'basic', title: '基本情報' },
  { key: 'qualifications', title: '資格' },
  // #else
  // {
  //   key: 'basic',
  //   title: '基本情報',
  // },
  // #endif
]

// APIに設定するスキーマから画面で利用するスキーマを作ります。空を許容するようにしています。
// TODO: 保存する型に変更してください。
const schemaForView = schemaForSave.partial()

// 画面で使用するデータのデフォルトを作ります。
function createDefaultViewData() {
  // TODO: 表示する型に変更してください。
  const result = reactive(defaultInstance<typeof schemaForView>(schemaForView))
  // #if(!isInTemplateExpanding || includeSample)
  result.qualifications = []
  // #endif
  return result
}

// 画面で使用するデータを定義します。
const targetData = createDefaultViewData()
// APIに渡すためのデータを定義します。
const targetDataRef = computed(() => {
  return targetData
})

const route = useRoute()
// 利用するデータを特定するためのキーを取得します。
const paramId = computed(() => route.query?.id?.toString())
// キーの有無を利用して追加モードかどうかを判定します。
const addMode = computed(() => !paramId.value)

// データの変更を監視して、画面離脱時に変更があった場合に確認ダイアログを表示します。
const { init, hasChanged, restart } = useWatchDataChanges()
const watcher = { init, hasChanged, restart }
confirmBeforeUnload(hasChanged, '')

// 確認ダイアログを定義します。
const confirmDialog = ref<AppConfirmDialogType>()

// 複数のフラグでローディング状態を管理する場合は useFlagCondition を利用する
const inProgress = ref(false)
const { hasTrue: loading, addFlag } = useFlagCondition(inProgress)

// バリデーションの定義
// バリデーションに利用するスキーマはAPIのものが利用できる場合は、そのまま利用することができます。
// 画面固有のものがある場合は、画面側でスキーマを組みます。
const {
  errorMessages: errorMessagesForCreate,
  validate: validateForCreate,
  validateItem: validateItemForCreate,
} = useValidation(schemaForCreate, targetDataRef) // 作成する型に変更してください。
const {
  errorMessages: errorMessagesForSave,
  validate: validateForSave,
  validateItem: validateItemForSave,
} = useValidation(schemaForSave, targetDataRef) // 更新する型に変更してください。

// 追加と更新でバリデーションが違う場合は、それぞれに定義し、コンポーネントへのバインドは computed で切り替えます。
// 同じ場合は、computed で切り替えずに、直接利用します。
const errorMessages: any = computed({
  get() {
    return addMode.value
      ? errorMessagesForCreate.value
      : errorMessagesForSave.value
  },
  set(value) {
    if (addMode.value) {
      errorMessagesForCreate.value = value
    } else {
      errorMessagesForSave.value = value
    }
  },
})

const validate = () => {
  return addMode.value ? validateForCreate() : validateForSave()
}
const validateItem = (key: string) => {
  return addMode.value ? validateItemForCreate(key) : validateItemForSave(key)
}
const validators = { validate, validateItem }

// =====================================================================================================================
// APIクライアントの定義
// =====================================================================================================================

// api の定義 パラメーターには reactive(ref / reactive) な値を使います。
// 更新対象のデータを取得するAPIクライアントを定義します。
// TODO: 利用するAPIクライアントに変更してください。
const { executeWithResult: getData, inProgress: getInProgress } = useGetApi(
  paramId as Ref<string>,
)

addFlag(getInProgress)
// #if(!isInTemplateExpanding || includeSample)
const {
  executeWithResult: getQualifications,
  inProgress: getQualificationInProgress,
} = useGetEmployeeQualifications(paramId as Ref<string>)

addFlag(getQualificationInProgress)
// #endif

// 追加のAPIクライアントを定義します。
// TODO: 利用するAPIクライアントに変更してください。
const { executeWithResult: postData, inProgress: postInProgress } = usePostApi(
  targetDataRef as Ref<ForCreate>,
)
addFlag(postInProgress)

// 更新のAPIクライアントを定義します。
// TODO: 利用するAPIクライアントに変更してください。
const { executeWithResult: putData, inProgress: putInProgress } = usePutApi(
  targetDataRef as Ref<ForSave>,
)
addFlag(putInProgress)

// 削除のAPIクライアントを定義します。
// 削除のパラメーターを定義します。
const deleteBody = computed(() => {
  return { id: targetData.id!, version: targetData.version! }
})
// TODO: 利用するAPIクライアントに変更してください。
const { executeWithResult: removeData, inProgress: deleteInProgress } =
  useDeleteApi(deleteBody)
addFlag(deleteInProgress)

// =====================================================================================================================
// 画面初期化処理
// =====================================================================================================================

// 既存データを取得します。
if (!addMode.value) {
  await load()
}
// 画面変更の検知を開始します。
watcher.init(targetData)

// =====================================================================================================================
// 画面で使用するメソッドの定義
// =====================================================================================================================

// データをロードします。
async function load() {
  if (!paramId.value) return

  inProgress.value = true
  try {
    // 更新対象のデータを取得します。
    const result = await getData()
    // 取得した結果を画面にバインドされたオブジェクトに詰め込みます。
    Object.assign(targetData, result.data)
    // #if(!isInTemplateExpanding || includeSample)
    const qualificationsResult = await getQualifications()
    // TODO: 結果の判定
    const qualifications = prepareQualification(qualificationsResult.data)
    targetData.qualifications = qualifications
    // #endif
  } finally {
    inProgress.value = false
  }
}

// データを保存します。
async function save() {
  // 確認ダイアログを表示します。
  const confirmResult = await confirmDialog.value?.open('save')
  if (!confirmResult) return

  // バリデーションを行います。
  const { success } = await validators.validate()
  if (!success) {
    return
  }
  errorMessages.value = {}
  inProgress.value = true
  try {
    // APIを実行します。
    const result = await (addMode.value ? postData : putData)().catch((e) => {
      // 失敗だった場合の処理を行います。
      if (!e.hasProblemDetails) {
        throw e
      }
      if (e.data.type === '/validation-error') {
        errorMessages.value = e.data.errors
      } else if (e.data.type === '/conflict') {
        errorToast(
          'すでに別のユーザーがデータを登録・変更しているため保存できませんでした。',
        )
      } else {
        throw e
      }
      return false
    })
    if (typeof result === 'boolean') {
      return
    }
    // 成功だった場合の処理を行います。
    const { data } = result
    primaryToast(`${result.data.id} を保存しました。`, { retain: true })
    // 追加モードの場合は、URLを変更します。
    // 更新モードに変わります。
    if (addMode.value) {
      // データ変更の監視を初期化します。
      watcher.init(targetData)
      await router.push({ query: { id: data.id } })
    }
    // #if(!isInTemplateExpanding || includeSample)
    const qualificationsResult = await getQualifications()
    data.qualifications = prepareQualification(qualificationsResult.data)
    // #endif
    // 結果で現在のデータを上書き
    Object.assign(targetData, data)
  } finally {
    // データ変更の監視を初期化します。
    watcher.init(targetData)
    inProgress.value = false
  }
}

// データを削除します。
async function remove() {
  const confirmResult = await confirmDialog.value?.open('remove')
  if (!confirmResult) return

  errorMessages.value = {}
  inProgress.value = true
  try {
    // APIを実行します。
    const result = await removeData()
      .then(() => true)
      .catch((e) => {
        // 失敗だった場合の処理を行います。
        if (!e.hasProblemDetails) {
          throw e
        }
        if (e.data.type === '/validation-error') {
          errorMessages.value = e.data.errors
        } else if (e.data.type === '/conflict') {
          errorToast(
            'すでに別のユーザーがデータを登録・変更しているため削除できませんでした。',
          )
        } else {
          throw e
        }
        return false
      })

    if (!result) {
      return
    }

    primaryToast(
      `${targetData.id} を削除しました。続いてデータを新規登録できます。`,
      { retain: true },
    )
    // デフォルトの値を設定する
    Object.assign(targetData, createDefaultViewData())
    // 追加モードに変更
    router.push({})
  } finally {
    watcher.init(targetData)
    inProgress.value = false
  }
}
// #if(!isInTemplateExpanding || includeSample)
// 部署検索の設定
const branchSearch = ref<BranchSearchDialogType>()
async function showBranchSearchDialog() {
  const result = await branchSearch.value?.open()
  if (result?.data) {
    targetData.branchId = result.data.id
    targetData.branchName = result.data.name
  }
}

// 明細の設定
const qualificationHeaders = ref([
  { title: '資格名', key: 'name', sortable: false },
])
const qualificationEdit = ref<QualificationEditDialogType>()

async function addQualification() {
  const { isOk, data } = await qualificationEdit.value!.open()
  if (isOk) {
    // 一覧に追加する
    const newData = Object.assign({}, data, { updated: false })
    targetData.qualifications?.push(prepareQualification(newData))
  }
  validators.validateItem('qualifications')
}

async function editQualification({ item }: { item: any }) {
  const { isOk, data } = await qualificationEdit.value!.open(item)
  if (isOk) {
    Object.assign(item, data)
    if (item.id) {
      // idがある場合 == 既存データの場合
      item.updated = true
    }
    validators.validateItem('qualifications')
  }
}

function removeQualifications({ selectedItems }: { selectedItems: any[] }) {
  const qualifications = targetData.qualifications ?? []
  // 選択されている一覧から一つずつ処理
  for (const target of selectedItems) {
    // _tempKey が一致する値のインデックスを取得
    const removeIndex = qualifications.findIndex(
      // なぜか item の型が {id, name} にマップされてしまうため any で回避
      (item: any) => item._tempKey === target._tempKey,
    )
    if (removeIndex > -1) {
      // インデックスに該当するデータを削除
      qualifications.splice(removeIndex, 1)
    }
  }
  validators.validateItem('qualifications')
  return true
}

function prepareQualification(data: any): any {
  const isarray = Array.isArray(data)
  if (!isarray) {
    data = [data]
  }
  const key = new Date().getTime()
  const result = data.map((v: any, i: number) => {
    return { ...v, _tempKey: v._tempKey ?? `${key}${i}` }
  })
  return isarray ? result : result[0]
}
// #endif
</script>
