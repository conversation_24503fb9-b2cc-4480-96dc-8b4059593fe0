<template>
  <v-tabs v-model="dataRef.currentTab">
    <v-tab value="case">案件一覧</v-tab>
    <v-tab value="issue-project">課題案件一覧</v-tab>
    <v-tab v-if="dataRef.showTabs" value="issue-project-task"
    >課題案件タスク一覧</v-tab
    >
    <v-tab v-if="dataRef.showTabs" value="issue-project-discussion">
      課題案件協議一覧
    </v-tab>
  </v-tabs>
  <v-window v-model="dataRef.currentTab">
    <!-- 案件一覧 -->
    <v-window-item value="case">
      <IdentifiedCase
        v-if="dataRef.customerIdentificationId"
        :customer-identification-id="dataRef.customerIdentificationId"
      />
      <Case v-else />
    </v-window-item>
    <v-window-item value="issue-project">
      <!-- <IdentifiedIssueProject
        v-if="dataRef.customerIdentificationId"
        :customer-identification-id="dataRef.customerIdentificationId"
      />
      <IssueProject v-else /> -->
      課題案件一覧
    </v-window-item>
    <v-window-item value="issue-project-task">
      <!-- <IssueProjectTask /> -->
      課題案件タスク
    </v-window-item>
    <v-window-item value="issue-project-discussion">
      <IssueProjectDiscussion />
    </v-window-item>
  </v-window>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { parseQuery } from '@ibp/common-case/src/utils/qs'
import { OriginOfTransitions } from '@ibp/case-work-flow-optimization/src/constants/common/query'
// import { useBreadcrumbs } from '@hox/base/src/composables/shared/useBreadcrumbs'
// import type { Breadcrumb } from '@hox/base/src/composables/shared/useBreadcrumbs'

// コンポーネントのインポート
import IdentifiedCase from '@ibp/case-work-flow-optimization/src/components/case/IdentifiedCase.vue'
import Case from '@ibp/case-work-flow-optimization/src/components/case/Case.vue'
import IssueProjectDiscussion from '@ibp/issue-project/src/pages/issue-project/issue-project-discussion/index.vue'

// タイトルを指定します。(windowのタイトルに設定されます。)
useHead({
  title: '案件一覧',
})
// タイトルを指定します。(パンくずリストに設定されます。)
definePageMeta({
  title: '案件一覧',
  breadcrumbs: {
    clear: true, // ルートのページはパンくずをクリアする
  },
  // TODO: レイアウトを動的に変更する
  // layout: computed(() => {
  //   const route = useRoute()
  //   const { customerIdentificationId } = parseQuery(route.query)
  //   return customerIdentificationId ? 'customer-understanding' : 'default'
  // }),
})

const dataRef = ref<{
  customerIdentificationId: string | undefined | null
  currentTab: string | undefined | null
  showTabs: boolean
}>({
  customerIdentificationId: undefined,
  currentTab: undefined,
  showTabs: false,
})

onMounted(() => {
  const route = useRoute()
  const { customerIdentificationId, from, tab } = parseQuery(route.query)

  dataRef.value.customerIdentificationId = customerIdentificationId
  dataRef.value.currentTab = tab
  dataRef.value.showTabs = from !== OriginOfTransitions.CUSTOMER_IDENTIFYING

  // 顧客識別IDを持っている場合はパンくずリストを動的に変更する
  if (dataRef.value.customerIdentificationId) {
    // TODO: useBreadcrumbs API を修正する必要があります
    /*
    useBreadcrumbs().froms.value.unshift({
      title: 'ポータル',
      path: `/portal/business-customer?id=${dataRef.value.customerIdentificationId}`,
    } as Breadcrumb)

    useBreadcrumbs().froms.value.unshift({
      title: '顧客検索',
      path: '/new-customer-identifying/customer',
    } as Breadcrumb)
    */
  }
})
</script>

<style scoped>
.v-tabs {
  margin-bottom: 20px;
}

.v-tab {
  background-color: #eee;
}

.v-btn.v-tab--selected {
  background-color: #fff; /* アクティブなタブの背景色 */
  border-top: 1px solid #00a63c;
  border-right: 1px solid #00a63c;
  border-left: 1px solid #00a63c;
  color: #00a63c;
}

.v-btn.v-tab--selected:hover {
  background-color: #f0f0f0; /* ホバー時の背景色 */
}
</style>
