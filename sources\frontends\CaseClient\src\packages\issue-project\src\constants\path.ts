import { stringify } from 'qs'

const defaultSelectedCaseStatuses = [
  'BeforeProposal',
  'UnderProposal',
  'Accepted',
] as const

export const defaultSelectedCaseStatusesWithIssueProject = [
  ...defaultSelectedCaseStatuses,
  'UnderConsulting',
  'NULL',
] as const

const issueProjectQueryString = (customerIdentificationId: string) =>
  stringify({
    q: {
      customerIdentificationId,
      caseStatuses: defaultSelectedCaseStatuses,
      tab: 'issue-project',
      from: 'customer-identifying',
    },
  })

const caseQueryString = (customerIdentificationId: string) =>
  stringify({
    q: {
      customerIdentificationId,
      caseStatuses: defaultSelectedCaseStatusesWithIssueProject,
      tab: 'case',
      from: 'customer-identifying',
    },
  })

export const PATHS = ({
  customerIdentificationId = '',
  businessUnderstandingId = '',
}: {
  customerIdentificationId?: string
  businessUnderstandingId?: string
}) => {
  const { isDev } = useRuntimeConfig().public

  const MenuUrlOrigin = isDev ? 'https://localhost:3000' : window.origin
  const CustomerUnderstandingsUrlOrigin = isDev
    ? 'https://localhost:3010'
    : window.origin
  const AccessManagementUrlOrigin = isDev
    ? 'https://localhost:3020'
    : window.origin
  const NewCustomerIdentifying = isDev
    ? 'https://localhost:3040'
    : window.origin
  const TimelineUrlOrigin = isDev ? 'https://localhost:3011' : window.origin
  const portalUrl = isDev ? 'https://localhost:3200' : window.origin

  return {
    // Menu
    MENU: MenuUrlOrigin,
    // CustomerUnderstandings
    BUSINESS_UNDERSTANDING: `${CustomerUnderstandingsUrlOrigin}/business-understanding?customerIdentificationId=${customerIdentificationId}`,
    FIND_ISSUE_PROJECT: `${CustomerUnderstandingsUrlOrigin}/customer-proposal/case?${issueProjectQueryString(customerIdentificationId)}`,
    FIND_CASE: `${CustomerUnderstandingsUrlOrigin}/customer-proposal/case?${caseQueryString(customerIdentificationId)}`,
    EDIT_TEAM: `${CustomerUnderstandingsUrlOrigin}/customer-proposal/edit`,
    COMMUNICATION_PLAN: `${CustomerUnderstandingsUrlOrigin}/business-understanding/communication-plan?businessUnderstandingId=${businessUnderstandingId}`,
    // AccessManagement
    EDIT_ACCESS_MANAGER: `${AccessManagementUrlOrigin}/activity-management/logs/access-manager`,
    // NewCustomerIdentifying
    FIND_CUSTOMER: `${NewCustomerIdentifying}/new-customer-identifying/customer`,
    // Timeline
    TIMELINE: `${TimelineUrlOrigin}/customer-connection/customer-interaction/interaction-log?customerIdentificationId=${customerIdentificationId}`,
    PORTAL: `${portalUrl}/portal/business-customer?id=${customerIdentificationId}`,
  }
}
