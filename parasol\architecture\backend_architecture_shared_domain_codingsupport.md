### Shared.Domain.CodingSupport

## 概要

Shared.Domain.CodingSupportライブラリは、ドメイン駆動設計（DDD）の実装を支援するコーディング支援機能を提供するライブラリです。Roslynアナライザー、コード生成、コーディング規約チェックなどの機能を提供し、DDD実装の品質向上と開発効率化を支援します。

## 主要機能

### コード解析
- **Roslynアナライザー**: C#コードの静的解析
- **規約チェック**: コーディング規約違反の検出
- **品質担保**: コード品質の自動チェック

### コード生成
- **ValueConverter生成**: Entity Framework用のValueConverter自動生成
- **JSON変換器生成**: JSONシリアライズ/デシリアライズ用コンバータ生成
- **強く型付けされた値生成**: ドメイン固有の強い型の自動生成

### 開発支援
- **属性生成**: 必要な属性クラスの自動生成
- **テンプレート**: コード生成のためのテンプレート提供
- **効率化**: 手動作成の手間削減

### 拡張性
- **カスタマイズ**: 生成されるコードのカスタマイズ
- **プラグイン**: 機能拡張のためのプラグイン機構
- **統合**: 既存開発環境との統合

## クラス一覧

| クラス名 | 機能 | 概要 | 主な用途 |
|-------------|------|------|----------|
| AvoidNewAnalyzer / AvoidDefaultAnalyzer | コード解析 | C#の`new`/`default`禁止を強制するRoslynアナライザ。コーディング規約違反を検出。 | コーディング規約チェック・品質担保 |
| EfConverterGenerator | EF拡張 | Entity Framework用のValueConverterクラスを自動生成するツール。 | DBマッピング・型変換の効率化 |
| JsonConverterGenerator | JSON拡張 | JSONシリアライズ/デシリアライズ用のコンバータクラスを自動生成。 | JSON拡張・データ変換効率化 |
| StronglyTypedValueGenerator | 型生成 | ドメイン固有の強い型（ValueObject等）を自動生成するツール。 | 型安全性・ドメイン設計強化 |
| Constants / Descriptors / Util | 定数・ユーティリティ | コーディング支援用の定数・ユーティリティ関数群。 | 開発効率化・共通処理 |
| EfValueConverterWriter / EfValueConverterAttributeWriter | EF拡張 | ValueConverterや属性クラスの自動生成ツール。 | DB型変換・マッピング効率化 |
| JsonConverterWriter / JsonConverterAttributeWriter | JSON拡張 | JSON変換用クラスや属性の自動生成ツール。 | シリアライズ効率化・拡張性向上 |
| MainAttributeWriter | 属性生成 | 属性クラスを自動生成するツール。 | コード生成支援・メタデータ付与 |
