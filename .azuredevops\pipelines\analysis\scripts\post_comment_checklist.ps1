<#
.SYNOPSIS
    チェックリストをコメントとして投稿します。
.PARAMETER accessToken
    Azure DevOpsのアクセストークン。
.PARAMETER rootDirectory
    ルートディレクトリのパス。
.PARAMETER threadId
    コメントを投稿するスレッドID。
#>

param(
    [string]$accessToken,
    [string]$rootDirectory,
    [string]$threadId
)

Import-Module (Join-Path $PSScriptRoot "get_file_change_stats_func.ps1") -Force
Import-Module (Join-Path $PSScriptRoot "get_checklist_message_func.ps1") -Force

$fileChangeStats = Get-File-Changes-Stats "origin/$env:SYSTEM_PULLREQUEST_TARGETBRANCHNAME"
$checkListMessage = Get-CheckList-Message $fileChangeStats $rootDirectory

$collectionUri = "$($env:SYSTEM_TEAMFOUNDATIONCOLLECTIONURI)"
$project = "$($env:SYSTEM_TEAMPROJECT)"
$repository = "$($env:BUILD_REPOSITORY_NAME)"
$pullRequestId = "$($env:SYSTEM_PULLREQUEST_PULLREQUESTID)"

$uri = "$collectionUri$project/_apis/git/repositories/$repository/pullRequests/$pullRequestId/threads/$threadId/comments?api-version=7.0"

$message = @"
## チェックリスト
$checkListMessage
"@

# JSON形式のリクエストボディを作成
$body = @{
    "parentCommentId" = 0
    "content"         = $message
    "commentType"     = "text"
} | ConvertTo-Json

# 対象のスレッドにチェックリストコメントを投稿
Invoke-RestMethod -Uri $uri -Method Post -Headers @{
    Authorization = "Bearer $accessToken"
} -Body $body -ContentType "application/json"
