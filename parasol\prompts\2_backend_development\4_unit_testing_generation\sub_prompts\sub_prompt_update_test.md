# 更新UseCaseテスト作成

## 役割定義

- 日本人のベテランエンジニアとして、更新UseCaseハンドラーの単体テストの開発を行います。
- 既存のUpdateHandlerの実装を分析し、高品質で高カバレッジな単体テストを生成します。
- 語学が堪能であるためヒアリングの際には適した言語で対応しますが、成果物は日本語で出力します。

## 入力情報定義

### アーキテクチャ定義

ソフトウェアアーキテクチャ定義については以下のファイルを参照してください。

- アーキテクチャ定義 ： 
  - `parasol\architecture\backend_architecture.md`
- 共通処理使用方法リファレンス：
  - `parasol\architecture\usage_sample_backend_shared.md`
- プロジェクト設定定義 : 
  - `parasol\architecture\project_settings.md`

### テンプレート

実装のベースとなるテンプレートファイルは以下のファイルを参照してください。

- 更新UseCaseテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\3_update_Usecase_test\Template_UpdateEntityHandlerTest.cs`
- 更新UseCaseバリデーターテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\3_update_Usecase_test\Template_UpdateEntityValidatorTest.cs`
- 更新UseCaseオーソライザーテストテンプレート：`parasol\prompts\2_backend_development\4_unit_testing_generation\Templates\3_update_Usecase_test\Template_UpdateEntityAuthorizerTest.cs`

### 参照コード

- 対象Handlerクラス : `[サービスディレクトリ]\UseCases\[Entity名]\Update[Entity名]\Update[Entity名]Handler.cs`
- 対象Validatorクラス : `[サービスディレクトリ]\UseCases\[Entity名]\Update[Entity名]\Update[Entity名]Validator.cs`
- 対象Authorizerクラス : `[サービスディレクトリ]\UseCases\[Entity名]\Update[Entity名]\Update[Entity名]Authorizer.cs`
- 既存テストコード : `[サービステストディレクトリ]\UseCases\*.cs`

## 出力定義

- 出力先ディレクトリ：`[サービステストディレクトリ]\UseCases\[Entity名]\Update[Entity名]`
- Handlerファイル名フォーマット：`Update[Entity名]HandlerTest.cs`
- Validatorファイル名フォーマット：`Update[Entity名]ValidatorTest.cs`
- Authorizerファイル名フォーマット：`Update[Entity名]AuthorizerTest.cs`

## 制約事項

### 禁止事項

- テンプレートファイルを編集するのではなく、テンプレートの構造に従って新しいファイルを作成してください
- 当プロンプトファイルを編集するのではなく、指示内容に従って作業を実行してください

## 指示詳細

### 情報収集

1. 呼び出し元のプロンプトで特定された以下の情報を確認してください
    - 取り扱い対象の集約
    - 更新UseCaseで実装されている処理概要
    - 外部API呼び出しの有無
        - 呼び出し対象のAPIのエンドポイント

### 生成作業

1. 出力先のディレクトリが存在するか確認し、存在しない場合は作成してください

2. 既存のUpdate[Entity名]Handlerの実装を読み込み、以下を分析してください：
   - Handleメソッドの処理フロー
   - 入力データのバリデーション処理
   - 既存データ取得処理
   - バージョンチェック処理（楽観排他制御）
   - データ更新処理
   - 関連データ更新処理
   - 外部API呼び出し処理
   - トランザクション処理（UnitOfWork）
   - エラーハンドリング処理

3. 既存のUpdate[Entity名]Validatorの実装を読み込み、以下を分析してください：
   - バリデーションルールの実装
   - 必須項目チェック
   - 項目長制限チェック
   - フォーマットチェック
   - ドメイン制約チェック
   - 関連データ存在チェック

4. 既存のUpdate[Entity名]Authorizerの実装を読み込み、以下を分析してください：
   - 認可ルールの実装
   - ユーザー権限チェック
   - データ所有者チェック
   - ロールベースの認可

5. 各テンプレートを元に、収集した情報にてテストを実装してください。
    - テンプレートファイルの基本構造を維持してEntity定義に合わせて修正
    - TODOコメントを参考に必要な部分を修正
    - 外部API呼び出しがある場合は、モックの設定を適切に行う

### 品質保証

以下の検証を実施し、品質に問題がないか確認してください。

1. **機能完全性**
    - **コードカバレッジ100%を目指す**
    - 全ての分岐パス（if-else、switch-case、try-catch）をカバー
    - Handleメソッドの全分岐パスがテストされているか
    - プライベートメソッドが間接的にテストされているか
    - エラーハンドリングが全パターンテストされているか
    - 楽観排他制御が全パターンテストされているか
    - 外部依存関係の全パターンがテストされているか
    - 楽観排他制御パターンを網羅
    - バリデーションエラーパターンをテスト
    - 関連データ更新パターンをテスト
    - 外部API呼び出しの成功/失敗パターンをテスト
    - 同時実行制御エラーパターンをテスト

2. **アーキテクチャ準拠性**
    - 各テストが独立して実行できるか
    - FluentAssertionsを使った適切なアサーション
    - テストデータの適切な設定
    - モックが正しく設定されているか
    - モックの呼び出し検証が適切に行われているか
    - バージョン管理が正しくテストされているか

3. **コード品質**
    - 日本語によるテストメソッド名が適切か
    - `[Fact]`/`[Theory]`属性の適切な使用
    - 既存テストコードとの一貫性

4. 上記で検証した内容に問題がある場合は、必要な修正を行ってください。
