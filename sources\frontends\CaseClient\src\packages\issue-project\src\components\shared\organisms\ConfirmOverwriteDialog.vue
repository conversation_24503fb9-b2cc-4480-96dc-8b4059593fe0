<template>
  <v-dialog
    v-model="dialog"
    width="50%"
    @click:outside="cancel"
    @keydown.esc="cancel"
  >
    <app-simple-dialog-tmpl
      :dialog-title="dialogTitle"
      @cancel="cancel"
      @action="action"
    >
      <template #default>
        {{ targetData }}
      </template>
    </app-simple-dialog-tmpl>
  </v-dialog>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'ConfirmOverwriteDialog',
  setup(_, { expose }) {
    const dialog = ref(false)
    const dialogTitle = ref('同じファイル名が存在します。上書きしますか？')
    const targetData = ref<string | null>(null)
    const resolve = ref<(value: { isOk: boolean }) => void>()

    function open(fileName: string): Promise<{ isOk: boolean }> {
      dialog.value = true
      targetData.value = fileName
      return new Promise((_resolve) => {
        resolve.value = _resolve
      })
    }

    function action() {
      resolve.value && resolve.value({ isOk: true })
      dialog.value = false
    }

    function cancel() {
      resolve.value && resolve.value({ isOk: false })
      dialog.value = false
    }

    expose({ open })

    return {
      dialog,
      dialogTitle,
      targetData,
      action,
      cancel,
    }
  },
})
</script>
